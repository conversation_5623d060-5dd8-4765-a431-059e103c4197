import Dialog = require("../widget/Dialog");
import View = require("../view/View");
import SpriteView = require("../view/SpriteView");
import EventEmitter = require("../../core/EventEmitter");
import { StringObjectKV } from "../util/TypeHelper";
/**
 * <p>MotionDialog widget.</p>
 * <p>The MotionDialog is an extension of the Dialog Class. You should use it for dialogs with hand motion.
 *    MotionDialog has one, two or three motion buttons at left top right, bottom is usually using for back.
 * <br>
 * @extends yunos.ui.widget.Dialog
 * @memberof yunos.ui.widget
 * @public
 * @since 4
 *
 */
declare class MotionDialog extends Dialog {
    [key: string]: Object;
    private _takeOverMode;
    private _isAutoTheme;
    private _resolver;
    private _originContentView;
    private _contentView;
    private _colorMode;
    private _blueLeft;
    private _blueTop;
    private _blueRight;
    private _greenLeft;
    private _redRight;
    private _selectDistance;
    private _framewidthTop;
    private _frameheightTop;
    private _framewidthSide;
    private _frameheightSide;
    private _motionDialogBackgroundImage;
    private _im;
    private _motionStartHandle;
    private _motionMoveHandle;
    private _motionEndHandle;
    private _motionStateObj;
    private _frameCount;
    private _closeWhenMoveOutofBounds;
    private _ignoreMoveThreshold;
    private _leftBackgroundView;
    private _rightBackgroundView;
    private _topBackgroundView;
    private _bottomBackgroundView;
    private _screen;
    private _closeIcon;
    protected _buttonArray: View[];
    protected _result: Object;
    protected _selectSound: string;
    protected _selected: boolean;
    /**
     * <p>Create an MotionDialog.</p>
     * @public
     * @since 4
     *
     */
    /**
     * <p>Destructor that destroy this MotionDialog.</p>
     * @param {boolean} recursive - destroy the children in the MotionDialog if the value is true.
     * @override
     * @public
     * @since 4
     *
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds whether the dialog should be closed when tap the buttons of the dialog.</p>
     * <p>default takeOverMode is true </p>
     * @name yunos.ui.widget.MotionDialog#takeOverMode
     * @type {boolean}
     * @throws {TypeError} If this value is not a boolean.
     * @public
     * @since 4
     *
     */
    public takeOverMode: boolean;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.MotionDialog#defaultStyleName
     * @type {string}
     * @default "MotionDialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: "MotionDialog" | "MotionDialogAuto";
    /**
     * <p>set the dialog close when not get motion event.</p>
     * @name yunos.ui.widget.MotionDialog#closeWhenMoveOutofBounds
     * @type {boolean}
     * @default true
     * @private
     */
    private closeWhenMoveOutofBounds: boolean;
    /**
     * <p>Set the Content View.</p>
     * @param {yunos.ui.view.View|null} view - content view to be setted.
     * @public
     * @override
     * @since 4
     *
     */
    public setContentView(view: View): void;
    /**
     * <p>Get the Content View.</p>
     * @returns {yunos.ui.view.View} current content view
     * @public
     * @override
     * @since 4
     *
     */
    public getContentView(): View;
    /**
     * <p>Set Select Item with index.</p>
     * @param {number} index - item index.
     * @param {yunos.ui.view.View|string} target - content view inner select or image uri
     * @public
     * @since 4
     *
     */
    public setMotionSelectView(index: number, target: View | string): void;
    private setIcons(images: string[]): void;
    private setButtonsBackground(index: number, src: string, frameCount: number, colnum: number, rownum: number, frameWidth: number, frameHeight: number): void;
    private playCloseSound(): void;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: StringObjectKV): void;
    private onTap(index: number): void;
    /**
     * <p>Close the dialog, removing it from the screen.</p>
     * @public
     * @override
     * @since 4
     *
     */
    public close(): void;
    /**
     * <p>Defines adaptive enabled state of this view</p>
     * @return {boolean}
     * @override
     * @protected
     * @since 4
     *
     */
    protected adaptiveEnabled(): boolean;
    /**
     * <p>return the layout path that will be load</p>
     * @return {string} the layout path, if your view name is YOURVIEW, default path is "YOURVIEW/YOURVIEW.xml"
     * @override
     * @protected
     * @since 4
     *
     */
    protected adaptiveLayoutFilePath(): string;
    private colorMode: number;
    private updateHeight(): void;
    private checkFireTap(close?: boolean): void;
    private readonly cameraPosition: string;
    /**
     * <p>Call on button selected</p>
     * @param {number} index - button index from 0 to 3, side "left", "top", "right", "bottom"
     * @protected
     * @since 4
     *
     */
    protected onSelect(index: number): void;
    private updateUI(): void;
    private resetUI(): void;
    private static readonly StyleName: {
        Normal: string;
        Auto: string;
    };
    private static readonly TextColor: {
        Normal: string;
        Negative: string;
        Positive: string;
    };
    private static readonly ColorMode: {
        Default: int;
        RedRight: int;
        GreenLeft: int;
        CustomDefind: int;
    };
}
declare namespace MotionDialog {
    class ImageButtonView extends SpriteView {
        private setFrameProcess(value: number): void;
    }
    class MotionHelper extends EventEmitter {
        private _context;
        private _inputs;
        private _calculateCount;
        private _started;
        public constructor(context: Object);
        private calculateCount: number;
        private update(input: Object): number[];
        private process(): number[];
    }
}
export = MotionDialog;
