import YObject = require("../core/YObject");
import Point = require("./Point");
import Segment = require("./path/Segment");
/**
 * <p>Path that represents a path with .</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @private
 */
declare class Path extends YObject {
    private _segments;
    private _startX;
    private _startY;
    private _started;
    private _lengths;
    private _length;
    private _nodesPercentArray;
    private _nodesPercentArrayValid;
    public constructor();
    private lineTo(x: number, y: number): void;
    private moveTo(x: number, y: number): void;
    private arcTo(x: number, y: number, rx: number, ry: number, largeArcFlag: number, sweepFlag: number, xAxisRotation: number): void;
    private quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): void;
    private bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void;
    private closePath(): void;
    private addPath(path: Path): void;
    private addRectangle(x1: number, y1: number, x2: number, y2: number, clockwise: boolean): void;
    private addCircle(x: number, y: number, radius: number, clockwise: boolean): void;
    private addEllipse(x1: number, y1: number, x2: number, y2: number, clockwise: boolean): void;
    private getSegmentCount(): number;
    private getSegmentAt(index: number): Segment;
    private getPointAt(percent: number): Point;
    private getPercentAt(x: number, y: number): number[];
    private getAngleAt(percent: number): void;
    /**
     * <p>Get the total length of the current path.</p>
     * @name yunos.graphics.Path#length
     * @type {number}
     * @private
     */
    private readonly length: number;
    private getPointXYAt(t: number): number[];
    private updateLength(): void;
    private getTangentAtPercent(percent: number): {
        start: number[];
        end: number[];
        index: number;
    };
    private getSegmentIndexAtPercent(percent: number): number;
    private getNodesPercent(): {
        start: number;
        end: number;
    }[];
    private updateNodesPercent(): {
        start: number;
        end: number;
    }[];
    private static mapToEllipse({ x, y }: {
        x: number;
        y: number;
    }, rx: number, ry: number, cosphi: number, sinphi: number, centerx: number, centery: number): {
        x: number;
        y: number;
    };
    private static approxUnitArc(ang1: number, ang2: number): {
        x: number;
        y: number;
    }[];
    private static vectorAngle(ux: number, uy: number, vx: number, vy: number): number;
    private static getArcCenter(px: number, py: number, cx: number, cy: number, rx: number, ry: number, largeArcFlag: number, sweepFlag: number, sinphi: number, cosphi: number, pxp: number, pyp: number): number[];
    private static arcToBezier({ px, py, cx, cy, rx, ry, xAxisRotation, largeArcFlag, sweepFlag }: {
        px: number;
        py: number;
        cx: number;
        cy: number;
        rx: number;
        ry: number;
        xAxisRotation: number;
        largeArcFlag: number;
        sweepFlag: number;
    }): {
        x1: number;
        y1: number;
        x2: number;
        y2: number;
        x: number;
        y: number;
    }[];
}
export = Path;
