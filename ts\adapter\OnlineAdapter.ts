/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets

import BaseAdapter = require("yunos/ui/adapter/BaseAdapter");
import View = require("yunos/ui/view/View");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import OnlineImageView = require("extend/hdt/control/OnlineImageViewBM");
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
const {Visible, None} = require("yunos/ui/view/View").Visibility;
const log = require("../utils/log");
const Utils = require("../utils/Utils");
const Consts = require("../Consts");
import VideoInfo = require("../model/VideoInfo");
const iVideoModel = require("../model/VideoModel").getInstance();
const TAG = "OnlineAdapter";

interface IOnlineView extends View {
    lastPlayed: ImageView;
    viewCount: TextView;
    videoSize: TextView;
    cover: OnlineImageView;
    title: TextView;
}

let Config: {
    ONLINE_ITEM_WIDTH?: number,
    ONLINE_ITEM_HEIGHT?: number
};

class OnlineAdapter extends BaseAdapter {
    private _isSearchType: boolean;

    constructor(isSearchType = false) {
        super();
        Config = {
            ONLINE_ITEM_WIDTH: Utils.getDimen("ONLINE_ITEM_WIDTH"),
            ONLINE_ITEM_HEIGHT: Utils.getDimen("ONLINE_ITEM_HEIGHT")
        };
        this._isSearchType = isSearchType;
    }

    createItem(position: number, convertView: IOnlineView) {
        if (!convertView) {
            convertView = <IOnlineView> LayoutManager.loadSync("online_item");
            convertView.cover = <OnlineImageView> convertView.findViewById("id_cover");
            convertView.title = <TextView> convertView.findViewById("id_title");
            convertView.videoSize = <TextView> convertView.findViewById("id_size");
            convertView.viewCount = <TextView> convertView.findViewById("id_hot_num");
            convertView.lastPlayed = <ImageView> convertView.findViewById("id_last_played");
        }

        let itemData = <VideoInfo> this.data[position];
        if (itemData) {
            log.D(TAG, "createItem", itemData.title, position);
            convertView.title.text = itemData.title ? itemData.title : "";
            convertView.viewCount.text = itemData.viewCount ? Utils.convertViewCount(itemData.viewCount) : "";
            convertView.videoSize.text = itemData.videoSize ? Utils.convertSize(itemData.videoSize) : "";
            if (itemData.thumbnail && itemData.thumbnail.startsWith("http")) {
                try {
                    convertView.cover.sourceSize = [Config.ONLINE_ITEM_WIDTH, Config.ONLINE_ITEM_HEIGHT];
                    convertView.cover.src = itemData.thumbnail;
                } catch (e) {
                    log.E(TAG, "Set thumbnail", e);
                }
            }
            if (this._isSearchType) {
                convertView.lastPlayed.visibility = itemData.lastPlayed ? Visible : None;
            } else {
                let highlightUrl = iVideoModel.loadHighlightUrl(Consts.FromType.ONLINE);
                convertView.lastPlayed.visibility = highlightUrl === itemData.url ? Visible : None;
            }
        }
        return convertView;
    }
}

export = OnlineAdapter;
