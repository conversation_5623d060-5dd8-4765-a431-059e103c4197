/**
 * @friend
 */
import Page = require("yunos/page/Page");
declare class TTS {
    constructor(p: Page);
    content: string;
    type: string;
    tips: string;
    nlgParams: Object;
    bargeIn: boolean;
    play: (v: Object) => void;
    setStopCallback: (callback: Object) => void;
    setCancelCallback: (callback: Object) => void;
    setErrorCallback: (callback: Object) => void;
    destroy: () => void;
}

export = TTS;