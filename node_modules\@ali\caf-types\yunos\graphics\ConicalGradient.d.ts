import Gradient = require("./Gradient");
/**
 * <p>Create a ConicalGradient which draw a conical gradient given by the coordinate represented by the parameters.</p>
 *
 * @example
 *
 * class MyView extends View {
 *   onDraw(ctx) { // override
 *     let conicalGradient = ctx.createConicalGradient(150, 100);
 *     // let conicalGradient = new ConicalGradient(150, 100);
 *     conicalGradient.addColorStop(0, "white");
 *     conicalGradient.addColorStop(0.5, "green");
 *     conicalGradient.addColorStop(1, "red");
 *     ctx.fillStyle = conicalGradient;
 *     ctx.fillRect(0, 0, 300, 300);
 *   }
 * }
 *
 * @extends yunos.graphics.Gradient
 * @memberof yunos.graphics
 * @public
 * @since 3
 *
 */
declare class ConicalGradient extends Gradient {
    private x: number;
    private y: number;
    private angle: number;
    /**
     * <p>Create a conical gradient.</p>
     * @param {number} x - The x axis of the coordinate of the point.
     * @param {number} y - The y axis of the coordinate of the point.
     * @param {number} [angle] - The angle of gradient, the default value is 0.
     * @public
     * @since 3
     *
     */
    public constructor(x: number, y: number, angle?: number);
}
export = ConicalGradient;
