import Matrix3 = require("./Matrix3");
import Matrix4 = require("./Matrix4");
import Quaternion = require("./Quaternion");
/**
 * <p>This class represents a 3D vector.</p>
 * <p>A 3D vecotr is an ordered pair of numbers(labeled x, y and z.</p>
 * @memberof yunos.graphics.3d.math
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class Vector3 {
    private _out;
    /**
     * Constructor that create a 3D vector.
     * @param {number} num1 - the first number of the 3D vector.
     * @param {number} num2 - the second number of the 3D vector.
     * @param {number} num3 - the third number of the 3D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(a?: number, b?: number, c?: number);
    /**
     * Destructor that destroy this 3D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public destroy(): void;
    /**
     * Defines the first value of this 3D vector.
     * @name yunos.graphics.3d.math.Vector3#x
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public x: number;
    /**
     * Defines the second value of this 3D vector.
     * @name yunos.graphics.3d.math.Vector3#y
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public y: number;
    /**
     * Defines the second value of this 3D vector.
     * @name yunos.graphics.3d.math.Vector3#z
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public z: number;
    /**
     * Return the Float32Array of this 3D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public data(): Float32Array;
    /**
     * Add a new Vector to this Vector.
     * @param {yunos.graphics.3d.math.Vector3} v - the new Vector to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public add(v: Vector3): void;
    /**
     * Add the scalar value s to this vector's x, y and z values.
     * @param {number} s - the number to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addScalar(s: number): void;
    /**
     * Add the multiple of v and s to this vector.
     * @param {yunos.graphics.3d.math.Vector3} v - the new vector to add
     * @param {number} s - the number to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addScaledVector(v: Vector3, s: number): void;
    /**
     * Sets this vector to Vector3 + Vector3.
     * @param {yunos.graphics.3d.math.Vector3} a - the new vector to add
     * @param {yunos.graphics.3d.math.Vector3} b - the new vector to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addVectors(a: Vector3, b: Vector3): void;
    /**
     * Applies a rotation specified by an axis and an angle to this vector.
     * @param {yunos.graphics.3d.math.Vector3} axis - A normalized Vector3.
     * @param {yunos.graphics.3d.math.angle} angle - An angle in radians.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public applyAxisAngle(axis: Vector3, angle: number): void;
    /**
     * Multiplies this vector by m
     * @param {yunos.graphics.math.3d.Matrix3} m - the matrix to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public applyMatrix3(m: Matrix3): void;
    /**
     * Multiplies this vector (with an implicit 1 in the 4th dimension) and m, and divides by perspective.
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix4 to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public applyMatrix4(m: Matrix4): void;
    /**
     * Applies a Quaternion transform to this vector.
     * @param {yunos.graphics.3d.math.Quaternion} quaternion - the quaternion to aplly
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public applyQuaternion(quaternion: Quaternion): void;
    /**
     * Returns the angle between this vector and vector v in radians.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to angleTo
     * @return {number} the result of angleTo
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public angleTo(v: Vector3): number;
    /**
     * The x, y and z components of the vector are rounded up to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public ceil(): void;
    /**
     * If this vector's x, y or z value is greater than the max vector's x, y or z value, it is replaced by the corresponding value.
     * If this vector's x, y or z value is less than the min vector's x, y or z value, it is replaced by the corresponding value.
     * @param {yunos.graphics.3d.math.Vector3} min - the minimum x, y, and z values
     * @param {yunos.graphics.3d.math.Vector3} max - the maximum x, y, and z values in the desired range
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clamp(min: Vector3, max: Vector3): void;
    /**
     * If this vector's length is greater than the max value, it is replaced by the max value.
     * If this vector's length is less than the min value, it is replaced by the min value.
     * @param {number} min - the minimum value the length will be clamped to
     * @param {number} max - the maximum value the length will be clamped to
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clampLength(min: number, max: number): void;
    /**
     * If this vector's x, y or z values are greater than the max value, they are replaced by the max value.
     * If this vector's x, y or z values are less than the min value, they are replaced by the min value.
     * @param {number} min - the minimum value the components will be clamped to.
     * @param {number} max - the maximum value the components will be clamped to.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clampScalar(min: number, max: number): void;
    /**
     * Returns a new vector3 with the same x, y and z values as this one.
     * @return {yunos.graphics.3d.math.Vector3} return a new Vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clone(): Vector3;
    /**
     * Copies the values of the passed vector3's x, y and z properties to this vector3.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to copy
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copy(v: Vector3): void;
    /**
     * Sets this vector to cross product of itself and v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to cross
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public cross(v: Vector3): void;
    /**
     * Sets this vector to cross product of a and b.
     * @param {yunos.graphics.3d.math.Vector3} a - the vector to cross
     * @param {yunos.graphics.3d.math.Vector3} b - the vector to cross
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public crossVectors(a: Vector3, b: Vector3): void;
    /**
     * Computes the distance from this vector to v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector distanceTo
     * @return {number} return the distance
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public distanceTo(v: Vector3): number;
    /**
     * Computes the Manhattan distance from this vector to v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector manhattanDistanceTo
     * @return {number} return the manhattanDistance
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public manhattanDistanceTo(v: Vector3): number;
    /**
     * Computes the squared distance from this vector to v. If you are just comparing the distance with another distance, you should compare the distance squared instead as it is slightly more efficient to calculate.
     * @param {yunos.graphics.3d.math.Vector3} v- the vector squared distance to
     * @return {number} return the squared distance
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public distanceToSquared(v: Vector3): number;
    /**
     * Divides this vector by v.
     * @param {yunos.graphics.3d.math.Vector3}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public divide(v: Vector3): void;
    /**
     * <p>Divides this vector by scalar s.</p>
     * <p>Sets vector to ( 0, 0, 0 ) if s = 0.</p>
     * @param {number} s - the scalar to divide
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public divideScalar(s: number): void;
    /**
     * Calculate the dot product of this vector and v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to dot
     * @return {number} the dot result
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public dot(v: Vector3): number;
    /**
     * Checks for strict equality of this vector and v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to equal
     * @return {boolean} the result of equals
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public equals(v: Vector3): boolean;
    /**
     * The components of the vector are rounded down to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public floor(): void;
    /**
     * Sets this vector's x value to be array[ offset + 0 ], y value to be array[ offset + 1 ] and z value to be array[ offset + 2 ].
     * @param {number[]} array - the source array
     * @param {number} offset - offset into the array.default is 0
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public fromArray(array: Array<number>, offset: number): void;
    /**
     * <p>Return the component.</p>
     * <p>If the index equals 0, returns the x value.</p>
     * <p>If the index equals 1, returns the y value.</p>
     * <p>If the index equals 2, returns the z value.</p>
     * @param {number} index - the index of components
     * @return {number} return the component
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getComponent(index: number): number;
    /**
     * Computes the Euclidean length (straight-line length) from (0, 0, 0) to (x, y, z).
     * @return {number} the length
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public length(): number;
    /**
     * Computes the Manhattan length of this vector.
     * @return {number} the manhattanLength
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public manhattanLength(): number;
    /**
     * Computes the square of the Euclidean length (straight-line length) from (0, 0, 0) to (x, y, z).
     * @return {number} the square of the euclidean length
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lengthSq(): number;
    /**
     * Linearly interpolate between this vector and v, where alpha is the distance along the line - alpha = 0 will be this vector, and alpha = 1 will be v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to interpolate towards
     * @param {number} alpha - interpolation factor in the closed interval[0, 1]
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lerp(v: Vector3, alpha: number): void;
    /**
     * Sets this vector to be the vector linearly interpolated between v1 and v2.
     * @param {yunos.graphics.3d.math.Vector3} v1 - the starting Vector3
     * @param {yunos.graphics.3d.math.Vector3} v2 - the vector to interpolate towards
     * @param {number} alpha - interpolation factor in the closed interval[0, 1]
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lerpVectors(v1: Vector3, v2: Vector3, alpha: number): void;
    /**
     * If this vector's x, y or z value is less than v's x, y or z value, replace that value with the corresponding max value.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to max
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public max(v: Vector3): void;
    /**
     * If this vector's x, y or z value is greater than v's x, y or z value, replace that value with the corresponding min value.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to min
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public min(v: Vector3): void;
    /**
     * Multiplies this vector by v.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiply(v: Vector3): void;
    /**
     * Multiplies this vector by scalar s.
     * @param {number} s - the number to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyScalar(s: number): void;
    /**
     * Sets this vector equal to a * b, component-wise.
     * @param {yunos.graphics.3d.math.Vector3} a - the first vector to multiply
     * @param {yunos.graphics.3d.math.Vector3} b - the second vector to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyVectors(a: Vector3, b: Vector3): void;
    /**
     * Inverts this vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public negate(): void;
    /**
     * Convert this vector to a unit vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public normalize(): void;
    /**
     * Projects this vector onto a plane by subtracting this vector projected onto the plane's normal from this vector.
     * @param {yunos.graphics.3d.math.Vector3} planeNormal - A vector representing a plane normal.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public projectOnPlane(planeNormal: Vector3): void;
    /**
     * Projects this vector onto another vector.
     * @param {yunos.graphics.3d.math.Vector3} v - the vector to project
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public projectOnVector(v: Vector3): void;
    /**
     * Reflect the vector off of plane orthogonal to normal.
     * @param {yunos.graphics.3d.math.Vector3} normal - the normal to the reflecting plane.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public reflect(normal: Vector3): void;
    /**
     * The components of the vector are rounded to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public round(): void;
    /**
     * The components of the vector are rounded towards zero (up if negative, down if positive) to an integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public roundToZero(): void;
    /**
     * Sets the x, y and z components of this vector.
     * @param {number} x - the first component of the vector.
     * @param {number} y - the second component of the vector.
     * @param {number} z - the third component of the vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public set(x: number, y: number, z: number): void;
    /**
     * Set the component by index and value.
     * @param {number} index - the index of the component
     * @param {number} value - the value of the component
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setComponent(index: number, value: number): void;
    /**
     * Sets this vector from the cylindrical coordinates radius, theta and y.
     * @param {number} radius - the radius
     * @param {number} theta - the theta
     * @param {number} y - the y
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromCylindricalCoords(radius: number, theta: number, y: number): void;
    /**
     * Sets this vector's x, y and z equal to the column of the matrix specified by the index.
     * @param {yunos.graphics.3d.math.Matrix4} matrix - the matrix
     * @param {number} index - the index
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromMatrixColumn(matrix: Matrix4, index: number): void;
    /**
     * Sets this vector to the position elements of the transformation matrix m.
     * @param {yunos.graphics.3d.math.Matrix4} matrix - the matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromMatrixPosition(m: Matrix4): void;
    /**
     * Sets this vector to the scale elements of the transformation matrix m.
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromMatrixScale(m: Matrix4): void;
    /**
     * Sets this vector from the spherical coordinates radius, phi and theta.
     * @param {number} radius - the radius
     * @param {number} phi - the phi
     * @param {number} theta - the theta
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromSphericalCoords(radius: number, phi: number, theta: number): void;
    /**
     * Set this vector to the vector with the same direction as this one, but length l.
     * @param {number} l - the length
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setLength(l: number): void;
    /**
     * Set the x, y and z values of this vector both equal to scalar.
     * @param {number} scalar - the scalar to set
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setScalar(scalar: number): void;
    /**
     * Subtracts v from this vector.
     * @param {yunos.graphics.3d.math.Vector3} vec - the Vector3 to sub.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public sub(v: Vector3): void;
    /**
     * Subtracts s from this vector's x, y and z compnents.
     * @param {number} s - the scalar to sub.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public subScalar(s: number): void;
    /**
     * Sets this vector to a - b.
     * @param {yunos.graphics.3d.math.Vector3} a - the first vector to sub
     * @param {yunos.graphics.3d.math.Vector3} b - the second vector to sub
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public subVectors(a: Vector3, b: Vector3): void;
    /**
     * Returns an array [x, y, z], or copies x, y and z into the provided array.
     * @param {number[]} array - the provided array
     * @param {number} offset - the offset of the array
     * @return {number[]} the result
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public toArray(array: Array<number>, offset: number): Array<number>;
    /**
     * Transforms the direction of this vector by a matrix (the upper left 3 x 3 subset of a m) and then normalizes the result.
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public transformDirection(m: Matrix4): void;
}
export = Vector3;
