import Formatter = require("./Formatter");
import Locale = require("../Locale");
import Calendar = require("../Calendar");
import TimeZone = require("../TimeZone");
import DateFormatterSolution = require("./dateformatters/DateFormatterSolution");
/**
 * DateFormatter is used to format given dates to strings of configured styles.
 * @extends yunos.util.formatter.Formatter
 * @memberof yunos.util.formatter
 * @public
 * @since 2
 * @relyon YUNOS_SYSCAP_SYSTIME
 */
declare class DateFormatter extends Formatter {
    private _use24HourClock;
    private _dateStyle;
    private _timeStyle;
    private _AMPMSymbols;
    private _regularEraSymbols;
    private _longEraSymbols;
    private _regularWeekdaySymbols;
    private _regularMonthSymbols;
    private _regularQuarterSymbols;
    private _shortWeekdaySymbols;
    private _veryShortMonthSymbols;
    private _veryShortWeekdaySymbols;
    private _shortQuarterSymbols;
    private _shortMonthSymbols;
    private _solution;
    private _calendar;
    private _timeZone;
    private _locale;
    /**
     * Constructor for DateFormatter.
     * @public
     * @since 2
     */
    public constructor();
    /**
     * Create a DateFormatter instance with options.
     * You may have locale, timezone, calendar in options or they would be set as defaults.
     * @param {Object} [options] - options object for creating a formatter
     * @param {boolean} [options.use24HourClock] - whether using 24-hour clock
     * @param {yunos.util.formatter.DateFormatter.StringStyle} [options.dateStyle] - date style of the formatter
     * @param {yunos.util.formatter.DateFormatter.StringStyle} [options.timeStyle] - time style of the formatter
     * @param {string[]} [options.AMPMSymbols] - whether using 24-hour clock
     * @param {yunos.util.Calendar} [options.calendar] - calendar of the formatter
     * @param {yunos.util.Locale} [options.locale] - locale of the formatter
     * @param {yunos.util.TimeZone} [options.timeZone] - timezone of the formatter
     * @param {string[]} [options.longEraSymbols] - long era symbols used for the formatter
     * @param {string[]} [options.regularEraSymbols] - regular era symbols used for the formatter
     * @param {string[]} [options.regularMonthSymbols] - regular month symbols used for the formatter
     * @param {string[]} [options.regularQuarterSymbols] - regular quarter symbols used for the formatter
     * @param {string[]} [options.regularWeekdaySymbols] - regular weekday symbols used for the formatter
     * @param {string[]} [options.shortMonthSymbols] - short month symbols used for the formatter
     * @param {string[]} [options.shortQuarterSymbols] - short quarter symbols used for the formatter
     * @param {string[]} [options.shortWeekdaySymbols] - short weekday symbols used for the formatter
     * @param {string[]} [options.veryShortMonthSymbols] - very short month symbols used for the formatter
     * @param {string[]} [options.veryShortWeekdaySymbols] - very short weekday symbols used for the formatter
     * @return {yunos.util.formatter.DateFormatter} the formatter created with given options
     * @throws {TypeError} If the param is not an object.
     * @public
     * @since 2
     */
    public static create(options?: {
        use24HourClock?: boolean;
        dateStyle?: number;
        timeStyle?: number;
        AMPMSymbols?: string[];
        calendar?: Calendar;
        locale?: Locale;
        timeZone?: TimeZone;
        longEraSymbols?: string[];
        regularEraSymbols?: string[];
        regularMonthSymbols?: string[];
        regularQuarterSymbols?: string[];
        regularWeekdaySymbols?: string[];
        shortMonthSymbols?: string[];
        shortQuarterSymbols?: string[];
        shortWeekdaySymbols?: string[];
        veryShortMonthSymbols?: string[];
        veryShortWeekdaySymbols?: string[];
    }): DateFormatter;
    /**
     * Get a DateFormatter instance with locale, timezone, calendar set as default.
     * @return {yunos.util.formatter.DateFormatter} the formatter with default timezone and locale settings
     * @public
     * @since 2
     */
    public static getDefault(): DateFormatter;
    /**
     * @readonly
     * @private
     */
    private readonly formatsData: Object;
    /**
     * AM/PM symbols for the formatter.
     * This symbol will always appear in the string if you set use24HourClock to be true.
     * @name yunos.util.formatter.DateFormatter#AMPMSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public AMPMSymbols: string[];
    /**
     * Regular era symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.Long.
     * @name yunos.util.formatter.DateFormatter#regularEraSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public regularEraSymbols: string[];
    /**
     * Long era symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.VeryLong.
     * @name yunos.util.formatter.DateFormatter#longEraSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public longEraSymbols: string[];
    /**
     * Regular weekday symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to be either of DateFormatter.StringStyle.VeryLong, DateFormatter.StringStyle.Long, DateFormatter.StringStyle.Regular.
     * @name yunos.util.formatter.DateFormatter#regularWeekdaySymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public regularWeekdaySymbols: string[];
    /**
     * Regular month symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to be either of DateFormatter.StringStyle.VeryLong, DateFormatter.StringStyle.Long, DateFormatter.StringStyle.Regular.
     * @name yunos.util.formatter.DateFormatter#regularMonthSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public regularMonthSymbols: string[];
    /**
     * Regular quarter symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to be either of DateFormatter.StringStyle.VeryLong, DateFormatter.StringStyle.Long, DateFormatter.StringStyle.Regular.
     * @name yunos.util.formatter.DateFormatter#regularQuarterSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public regularQuarterSymbols: string[];
    /**
     * Short weekday symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.Short.
     * @name yunos.util.formatter.DateFormatter#shortWeekdaySymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public shortWeekdaySymbols: string[];
    /**
     * Short month symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.Short.
     * @name yunos.util.formatter.DateFormatter#shortMonthSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public shortMonthSymbols: string[];
    /**
     * Short quarter symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.Short.
     * @name yunos.util.formatter.DateFormatter#shortQuarterSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public shortQuarterSymbols: string[];
    /**
     * Very short weekday symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.VeryShort.
     * @name yunos.util.formatter.DateFormatter#veryShortWeekdaySymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public veryShortWeekdaySymbols: string[];
    /**
     * Very short month symbols for the formatter.
     * This symbol will appear in the string when you set dateStyle to DateFormatter.StringStyle.VeryShort.
     * @name yunos.util.formatter.DateFormatter#veryShortMonthSymbols
     * @type {string[]}
     * @throws {TypeError} If the param is not an array.
     * @public
     * @since 2
     */
    public veryShortMonthSymbols: string[];
    /**
     * Locale for the formatter.
     * @name yunos.util.formatter.DateFormatter#locale
     * @type {yunos.util.Locale}
     * @throws {TypeError} If the param is not an instance of Locale.
     * @public
     * @since 2
     */
    public locale: Locale;
    /**
     * Timezone for the formatter.
     * @name yunos.util.formatter.DateFormatter#timeZone
     * @type {yunos.util.TimeZone}
     * @throws {TypeError} If the param is not an instance of TimeZone.
     * @public
     * @since 2
     */
    public timeZone: TimeZone;
    /**
     * Calendar for the formatter. The formatter use calendar to tell the type of the date string.
     * @name yunos.util.formatter.DateFormatter#calendar
     * @type {yunos.util.Calendar}
     * @throws {TypeError} If the param is not an instance of Calendar.
     * @public
     * @since 2
     */
    public calendar: Calendar;
    /**
     * Determine whether using 24 hour clock style or not.
     * The default value of this property is based on your system settings.
     * @name yunos.util.formatter.DateFormatter#use24HourClock
     * @type {boolean}
     * @public
     * @since 2
     */
    public use24HourClock: boolean;
    /**
     * The style of date in the string.
     * @name yunos.util.formatter.DateFormatter#dateStyle
     * @type {yunos.util.formatter.DateFormatter.StringStyle}
     * @throws {TypeError} If the param is not one of DateFormatter.StringStyle.
     * @public
     * @since 2
     */
    public dateStyle: number;
    /**
     * The style of time in the string.
     * @name yunos.util.formatter.DateFormatter#timeStyle
     * @type {yunos.util.formatter.DateFormatter.StringStyle}
     * @throws {TypeError} If the param is not one of DateFormatter.StringStyle.
     * @public
     * @since 2
     */
    public timeStyle: number;
    /**
     * @private
     */
    /**
    * @private
    */
    private solution: DateFormatterSolution;
    /**
     * Generate a string from a given date.
     * You can specify a template for the string.
     * For example:
     * @example
     * let df = new DateFormatter();
     * df.locale = Locale.getDefault();
     * df.timeZone = TimeZone.getDefault();
     * df.getString(new Date(Date.UTC(2008, 08, 08)), "G yyyy-MM-dd H:m a z")
     * @param {Date} date - the date object that is to be formatted
     * @param {string} [template] - an optional string template that is used to format the date
     * @return {string} formatted string for the given date object
     * @throws {TypeError} If the param date is not a valid Date object, or the param template is not a string.
     * @override
     * @public
     * @since 2
     */
    public getString(date: Date, template?: string): string;
    /**
     * Enum for DateFormatter string styles
     * @enum {number}
     * @default yunos.util.formatter.DateFormatter.StringStyle.VeryLong
     * @readonly
     * @public
     * @since 2
     */
    public static readonly StringStyle: {
        /**
         * None style. Maybe nothing should be shown.
         * @public
         * @since 2
         */
        None: int;
        /**
         * The most long style.
         * @public
         * @since 2
         */
        VeryLong: int;
        /**
         * Long style.
         * @public
         * @since 2
         */
        Long: int;
        /**
         * Regular style.
         * @public
         * @since 2
         */
        Regular: int;
        /**
         * Short style.
         * @public
         * @since 2
         */
        Short: int;
        /**
         * Very short style.
         * @public
         * @since 2
         */
        VeryShort: int;
    };
}
export = DateFormatter;
