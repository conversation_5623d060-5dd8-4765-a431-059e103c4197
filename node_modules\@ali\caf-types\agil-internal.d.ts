
declare class CmdBufferClass {
    Cmd: {[key: string]: number};
    Ev: {[key: string]: number};
    nullHandle: number;
    allocHandle(): number;
    addReleaseCmd(v: number): void;
    releaseScratch(v: number): void;
    createScratch(): number;
    setCurrent(v: number): void;
    mergeScratch(v: number): void;
    flush(v?: boolean): void;

    addCmdI(v1: number, v2: number, v3: number): void;
    addCmdZ(v1: number, v2: number): void;
    addCmdH(v1: number, v2: number, v3: number): void;
    addCmdF(v1: number, v2: number, v3: number): void;
    addCmdU(v1: number, v2: number, v3: number): void;
    addCmdS(v1: number, v2: number, v3: string): void;
    addCmdf(v1: number, v2: number, v3: number[]): void;
    addCmdFFFF(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;
    addCmdIIII(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;
    addCmdUU(v1: number, v2: number, v3: number, v4: number): void;
    addCmdII(v1: number, v2: number, v3: number, v4: number): void;
    addCmdIS(v1: number, v2: number, v3: number, v4: string): void;
    addCmduff(v1: number, v2: number, v3: number[], v4: number[], v5: number[]): void;
    addCmdSi(v1: number, v2: number, v3: string, v4: number[]): void;
    addCmdUF(v1: number, v2: number, v3: number, v4?: number): void;
    addCmdSH(v1: number, v2: number, v3: string, v4?: number): void;
    addCmdUUI(v1: number, v2: number, v3: number, v4: number, v5: number): void;

    // agilng
    Sig: {[key: string]: number};
    releaseScratchForFastRender(v: number): void;
    createScratchForFastRender(v1: number, v2: number, v3: string, v4: string): number;
    addCmdfu(v1: number, v2: number, v3: number[], v4: number[]): void;
    addVarCmd(v1: number, v2: number, v3: number, v4: string[], v5: string, v6: number, v7: number, v8: number): void;
    addCmdSU(v1: number, v2: number, v3: string, v4: number): void;
}

declare class AgilRoot {
    CmdBuffer: CmdBufferClass;
    PlatformBridge: {
        createInitialWindow(...arg: Array<Object>): Array<Object>;
        createWindow(...arg: Array<Object>): Array<Object>;
        destroyWindow(v: number): void;
    }
}

declare class Agil {
    static root(): AgilRoot;
}

export = Agil;
