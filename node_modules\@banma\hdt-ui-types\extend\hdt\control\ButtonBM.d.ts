import { MultiState } from "yunos/ui/util/TypeHelper";
import CompoundButton = require("yunos/ui/widget/CompoundButton");
import { ButtonColorType, ButtonContentType, ButtonIStyle, ButtonType } from "./Types";
declare class ButtonBM extends CompoundButton {
    readonly defaultStyleName: string;
    buttonType: ButtonType;
    colorType: ButtonColorType;
    iconSrc: string | MultiState;
    color: string;
    iconAutoColor: boolean;
    multiStateOverlay: MultiState;
    height: number;
    width: number;
    corner: number;
    contentType: ButtonContentType;
    static readonly ButtonType: typeof ButtonType;
    static readonly ColorType: typeof ButtonColorType;
    static readonly ContentType: typeof ButtonContentType;
    private _defaultHeight;
    private _defaultBorderRadius;
    private _defaultVerticalSpacing;
    private _defaultHorizontalSpacing;
    private _defaultIconWidthHeightL;
    private _defaultIconWidthHeightS;
    private _defaultTypeConfig;
    private _defaultFontSize;
    private _defaultFontWeight;
    private _defaultFontColor;
    private _defaultLeftPadding;
    private _defaultRightPadding;
    private _buttonType;
    private _colorType;
    private _contentType;
    private _corner;
    private _iconAutoColor;
    private _multiStateOverlay;
    private _defaultMultiStateTransition;
    private _iconSrc;
    private _iconView;
    private __height;
    constructor(...args: Object[]);
    destroy(recursive?: boolean): void;
    protected updateStyle(): void;
    protected applyStyle(style: ButtonIStyle): void;
    protected validMultiState(): void;
    private _valid;
    private _validContentType;
    protected _doMultiStateOverlay(): void;
    protected validLayoutDirection(): void;
}
export = ButtonBM;
