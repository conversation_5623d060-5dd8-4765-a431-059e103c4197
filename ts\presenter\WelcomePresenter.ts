/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
import NavigationBar = require("yunos/ui/view/NavigationBar");
import View = require("yunos/ui/view/View");
import CheckBox = require("yunos/ui/widget/CheckBox");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
const RecognitionMode = VoiceCommand.RecognitionMode;
import RelativeLayout = require("yunos/ui/layout/RelativeLayout");
const ScrollableView = require("yunos/ui/view/ScrollableView");
import TextView = require("yunos/ui/view/TextView");
import ButtonBM = require("extend/hdt/control/ButtonBM");
import PopupBM = require("extend/hdt/control/PopupBM");
const iVideoModel = require("../model/VideoModel").getInstance();
const iOnlineModel = require("../model/OnlineModel").getInstance();
import log = require("../utils/log");
const iUserTrackHelper = require("../utils/UserTrackHelper").getInstance();
import Consts = require("../Consts");
const RoutePath = Consts.RoutePath;
import Utils = require("../utils/Utils");
const TAG = "WelcomePresenter";

const MAX_WAIT_TIME = 12000;

class WelcomePresenter extends Presenter {
    private _views: {
        navigationBar: NavigationBar;
        enterBtn: ButtonBM;
        checkbox: CheckBox;
        disclaimerLink: View;
    };
    private _tapAbleViews: View[];
    private _hidden: boolean;
    private _destroyed: boolean;
    private _popup: PopupBM;
    private _pageStartTime: number;

    onCreate() {
        log.I(TAG, "onCreate");
        this._destroyed = false;
        if (iVideoModel.isSkipWelcomePage()) {
            this.destroy();
        }
        this.attachView("welcome");
        iOnlineModel.querySpeedLimit();
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        this._pageStartTime = new Date().getTime();
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_NOTICE);
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_NOTICE, null);
    }

    onPageLink() {
        log.I(TAG, "onPageLink");
        this._pageStartTime = new Date().getTime();
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._setupTapHandler();
        this._addVoiceCommands();
    }

    _setupViews(parentView: View) {
        this._views = {
            navigationBar: <NavigationBar> parentView.findViewById("id_nav"),
            enterBtn: <ButtonBM> parentView.findViewById("id_btn"),
            checkbox: <CheckBox> parentView.findViewById("id_disclaimer_checkbox"),
            disclaimerLink: parentView.findViewById("id_disclaimer_link")
        };

        this._views.navigationBar.leftItem.visibility = View.Visibility.None;
        this._views.navigationBar.titleItem.title.align = TextView.Align.Left;
        this._views.navigationBar.preTitleItem.visibility = View.Visibility.None;

        this._views.checkbox.on("checkedchanged", (checkObj, value: boolean) => {
            log.I(TAG, "checkedchanged", value);
            this._views.enterBtn.enabled = value;
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_NOTICE,
                iUserTrackHelper.NOTICE_TICK, {agree: value});
        });
    }

    _addVoiceCommands() {
        let cmdKeys = ["VOICECMD_WELCOME_START"];
        Utils.registerVoiceCommand(this._views.enterBtn, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            if (!this._views.enterBtn.enabled) {
                log.W(TAG, "voice command, button disabled");
                return;
            }

            log.I(TAG, "voice command, enter button");
            this._enterVideo();
        });

        cmdKeys = ["VOICECMD_WELCOME_AGREE_1", "VOICECMD_WELCOME_AGREE_2"];
        Utils.registerVoiceCommand(this._views.checkbox, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }
            log.I(TAG, "voice command, checkbox");
            this._views.checkbox.checked = true;
        }, true);
    }

    _setupTapHandler() {
        this._tapAbleViews = [];
        Utils.setOnTapListener(this._views.enterBtn, () => {
            log.I(TAG, "enter button pressed!");
            this._enterVideo();
        });
        this._tapAbleViews.push(this._views.enterBtn);

        Utils.setOnTapListener(this._views.disclaimerLink, () => {
            log.I(TAG, "disclaimer link pressed!");
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_NOTICE,
                iUserTrackHelper.NOTICE_DETAIL_CLICK);
            this._showDisclaimerDialog();
        });
        this._tapAbleViews.push(this._views.disclaimerLink);
    }

    _enterVideo() {
        iVideoModel.saveSkipWelcomePage();
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_NOTICE, iUserTrackHelper.NOTICE_START);

        let data = this.context.data;
        if (data) {
            let type = (<{type: string}><object> data).type;
            if (type === "video") {
                let waitTime = new Date().getTime() - this._pageStartTime;
                log.I(TAG, "_enterVideo", waitTime);
                if (waitTime < MAX_WAIT_TIME) {
                    this.context.router.replace(RoutePath.PLAYER, data);
                } else {
                    this.context.router.replace(RoutePath.ONLINE);
                }
            } else if (type === "local") {
                this.context.router.replace(RoutePath.LOCAL, data);
            } else {
                this.context.router.replace(RoutePath.ONLINE);
            }
        } else {
            this.context.router.replace(RoutePath.ONLINE);
        }
    }

    _showDisclaimerDialog() {
        if (this._popup && this._popup.isShowing()) {
            log.I(TAG, "_showDisclaimerDialog, dialog is showing");
            return;
        }
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_NOTICE_DETAIL);

        let scrollableView = new ScrollableView();
        scrollableView.orientation = ScrollableView.Orientation.Vertical;
        scrollableView.height = Utils.getDimen("WELCOME_POPUP_CONTENT_HEIGHT");
        scrollableView.overScroll = false;
        let textview = new TextView();
        textview.id = "textview";
        textview.multiLine = true;
        textview.propertySetName = Consts.WELCOME_DISCLAIMER_CONTENT;
        textview.text = iOnlineModel.speedLimit <= Consts.DEFAULT_SPEED_DISCLAIMER
            ? iRes.getString("DISCLAIMER_STRICT") : iRes.getString("DISCLAIMER_NORMAL");
        let layout = new RelativeLayout();
        layout.setLayoutParam("textview", "align", {
            left: "parent",
            top: "parent",
            right: "parent"
        });
        scrollableView.layout = layout;
        scrollableView.addChild(textview);

        const popup = new PopupBM();
        popup.title = iRes.getString("DISCLAIMER_TITLE");
        popup.setContentView(scrollableView);
        popup.buttons = [iRes.getString("WELCOME_DISCLAIMER_TIPS")];
        popup.once("result", () => {
            if (this._destroyed) {
                return;
            }
            this._views.checkbox.checked = true;
        });
        popup.once("close", () => {
            iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_NOTICE_DETAIL);
            popup.destroy(true);
            this._popup = null;
        });
        popup.show();
        this._popup = popup;
    }

    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }

        log.I(TAG, "_removeVoiceCommands");
        Utils.removeVoiceCommand(this._views.enterBtn);
        Utils.removeVoiceCommand(this._views.checkbox);
    }

    _removeAllListeners() {
        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];
        this._views.checkbox.removeAllListeners("checkedchanged");
        this._views = null;
        log.I(TAG, "_removeAllListeners done.");
    }

    onDestroy() {
        log.D(TAG, "onDestroy");
        this._destroyed = true;
        this._removeAllListeners();
    }
}

export = WelcomePresenter;
