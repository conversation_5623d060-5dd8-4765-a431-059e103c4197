export class SVGNode {

    constructor(densityFactor: number, scale: number);

    load(data: string, width?: number, height?: number, tint?: number):
        { width: number, height: number, handle: number } | undefined;

    getSVGSize(file: string): { width: number, height: number } | undefined;

    destroy(): void;

    beginFrame(inverted?: boolean): void;

    onFrame(): { handle: number, request: boolean } | undefined;

    cancelFrame(): void;
}
