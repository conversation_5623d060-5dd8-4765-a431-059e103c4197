import Page = require("../../page/Page");
import EventEmitter = require("../../core/EventEmitter");
import Manager = require("./core/manager");
import { ThemeCfg } from "./core/define";
/**
 * <p>Theme is a collection of styles which represent the looking of views.</p>
 * @example
 * const Theme = require("yunos/ui/theme/Theme");
 * let theme = Theme.getInstance();
 * theme.getStyle("Slider");
 * @extends yunos.core.EventEmitter
 * @memberof yunos.ui.theme
 * @public
 * @since 4
 */
declare class Theme extends EventEmitter {
    private _domainName;
    private _themeManager;
    private _selectedTheme;
    private _selectedThemeMode;
    private _resource;
    private _theme;
    private _responseToGlobalThemeChange;
    private _responseToResUpdate;
    private _resourceCache;
    private _page;
    private _pendingGlobalThemeChangeName;
    private _themeChangeHandlerAddedForPage;
    private _uniqueId;
    private _propertyWatchHandler;
    private _pageShowHandler;
    private _pageLinkHandler;
    private _resUpdateWatcher;
    private _globalThemeChangeStrategy;
    private _diffStyleCache;
    private _diffSetterCache;
    private _currentGlobalThemeName;
    private globalThemeReference: string;
    private _hasChangedTheme;
    public constructor(domainName: string, themeRef?: string, page?: Page, uniqueId?: string);
    private _watchResUpdate;
    private _unwatchResUpdate;
    private _watchGlobalThemeChange;
    private _unwatchGlobalThemeChange;
    private updateForGlobalTheme;
    private resetCurrentTheme(): void;
    /**
     * <p>Get Theme singleton instance.</p>
     * @return {yunos.ui.theme.Theme} The Theme singleton instance.
     * @example
     * const theme = require("yunos/ui/theme/Theme").getInstance();
     * @public
     * @since 4
     */
    public static getInstance(page?: Page | string, themeRef?: string, uniqueId?: string): Theme;
    /**
     * <p>Destroy Theme singleton instance.</p>
     * <p>There's no need to call this method yourself for the theme instance will be destroyed alone with Page.</p>
     * @override
     * @public
     * @since 4
     */
    public static releaseInstance(page?: Page | string): void;
    /**
     * An Array that contains all available themes name.
     * @name yunos.ui.theme.Theme#available
     * @type {string[]}
     * @readonly
     * @public
     * @since 4
     */
    public readonly available: string[];
    /**
     * The name of selected theme currently.
     * @name yunos.ui.theme.Theme#selected
     * @type {string}
     * @readonly
     * @public
     * @since 4
     */
    public readonly selected: string;
    /**
     * The name of selected mode currently.
     * @name yunos.ui.theme.Theme#selectedMode
     * @type {string}
     * @readonly
     */
    private readonly selectedMode: string;
    /**
     * <p>Set global theme</p>
     * @name yunos.ui.theme.Theme#defaultTheme
     * @type {string}
     * @public
     * @since 4
     */
    public defaultTheme: string;
    private readonly themeManager: Manager;
    /**
     * <p>Set theme.</p>
     * @param {string} theme - theme name.
     * @throws {Error} If specified a theme that doesn't exist.
     * @public
     * @since 4
     */
    public setTheme(theme?: string | ThemeCfg, modeName?: string): void;
    /**
     * <p>Check whether there is a theme.</p>
     * @param  {string}  themeName - Theme's name.
     * @return {boolean} Check result.
     * @public
     * @since 4
     */
    public hasTheme(themeName: string): boolean;
    private setMode(modeName: string): void;
    private _switchTheme;
    private createStyle(styleName: string, cfg: Object, extendStyleName: string): void;
    /**
     * <p>Check current theme has a style or not.</p>
     * @param  {string}  styleName - Name of style.
     * @return {boolean} - Check result.
     * @public
     * @since 4
     */
    public hasStyle(styleName: string, namespace?: string): boolean;
    /**
     * <p>Get a style from current theme.</p>
     * @param  {string} styleName - Name of style.
     * @return {Object} - Result.
     * @public
     * @since 4
     */
    public getStyle(styleName: string, namespace?: string): Object;
    /**
     * <p>Get a string from current theme.</p>
     * @param  {string} stringName - Name of string.
     * @return {string} - Result.
     */
    public getString(stringName: string): string;
    /**
     * <p>Get a image path from current theme.</p>
     * @param  {string} imageName - Name of image.
     * @return {string} - Result.
     * @public
     * @since 4
     */
    public getImage(imageName: string): string;
    /**
     * <p>Get a dimen from current theme.</p>
     * @param  {string} getDimen - Name of dimen.
     * @return {string} - Result.
     * @public
     * @since 6
     */
    public getDimen(dimenName: string): string;
    /**
     * <p>Get a color from current theme.</p>
     * @param  {string} getColor - Name of color.
     * @return {string} - Result.
     * @public
     * @since 4
     */
    public getColor(colorName: string): string;
    private getPixelFromDp(dp: number): number;
    /**
     * <p>Get a property setter from current theme.</p>
     * @param  {string} propertySetName - Name of setter.
     * @return {Object} - Setter.
     * @public
     * @since 4
     */
    public getPropertySet(propertySetName: string): Object;
    private updateResource(resourceName: string, resource: Object, type: string): void;
    private _updateViews;
    private updateDeferViews(): void;
    private _diffStyle;
    private diffSetter(oldSetter: Object, newSetter: Object, setterName: string): Object;
    private _diffSetter(oldSetter: Object, newSetter: Object): Object;
    /**
     * <p>Return global themes name.</p>
     */
    public readonly globalAvailableThemes: string[];
    /**
     * <p>Get current global theme.</p>
     */
    public globalTheme: string;
    /**
     * <p>Defines whether the theme should response to the global theme change.</p>
     * @name yunos.ui.theme.Theme#responseToGlobalThemeChange
     * @type {boolean}
     * @default false
     * @public
     * @since 5
     */
    public responseToGlobalThemeChange: boolean;
    /**
     * <p>Defines whether the theme should reset when page overlay updated.</p>
     * @name yunos.ui.theme.Theme#responseToResUpdate
     * @type {boolean}
     * @default true
     * @public
     * @since 6
     */
    public responseToResUpdate: boolean;
    public globalThemeChangeStrategy: number;
    public static readonly GlobalThemeChangeStrategy: {
        Default: int;
        ASAP: int;
    };
    private readonly resourceCache;
    private log(msg: string, category?: string): void;
    private warn(msg: string): void;
    private clearCache(): void;
}
export = Theme;
