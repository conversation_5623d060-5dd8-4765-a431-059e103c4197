import View = require("yunos/ui/view/View");
import { CanvasContext2D } from "yunos/graphics/CanvasContext2D";
/**
 * <p>CanvasAnimationView</p>
 *
 * @example
 * const CanvasAnimationView = require("yunos/internal/ui/view/CanvasAnimationView");
 * let R = require("yunos/content/resource/Resource").getInstance();
 * let cav = new CanvasAnimationView();
 * cav.width = 400;
 * cav.height = 400;
 * cav.path = R.getRawFileSrc("anim/drinks.json");
 * cav.iterationCount = -2;
 * cav.start();
 * cav.on("complete", () => {
 *     log.E("SimplePage", "cav-complete");
 * });
 * this.window.addChild(cav);
 *
 * @extends yunos.ui.view.View
 * @friend
 */
declare class CanvasAnimationView extends View {
    _path: string;
    _iterationCount: number;
    _animating: boolean;
    _paused: boolean;
    _firstDraw: boolean;
    _loops: number;
    _pendingAnimation: boolean;
    _speed: number;
    _inverted: boolean;
    /**
     * <p>Constructor that create a canvas animation view.</p>
     */
    constructor(...args: Object[]);
    /**
     * @override
     * @ignore
     * @param ctx
     */
    onDraw(ctx: CanvasContext2D): void;
    /**
     * <p>The speed for this animation.</p>
     * @friend
     */
    speed: number;
    /**
     * <p>The repeat count for this animation.</p>
     * <p>Note that 1 means just do this animation one time, -2 means do this animation infinitely.</p>
     * @friend
     */
    iterationCount: number;
    /**
     * <p>Reverse the animation.</p>
     * @friend
     */
    inverted: boolean;
    /**
     * <p>The resource path for this animation.</p>
     * @friend
     */
    path: string;
    /**
     * <p>Starts this animation.</p>
     * @friend
     */
    start(): void;
    /**
     * <p>Update the progress of this animation. The animation will paused and use resume to continue play.</p>
     * @param {boolean} progress - The progress should between 0 and 1.
     * @friend
     */
    updateProgress(progress: number): void;
    /**
     * <p>Stops this animation.</p>
     * @friend
     */
    stop(): void;
    /**
     * indicating whether it is in animation state.
     * @friend
     */
    readonly animating: boolean;
    /**
     * <p>Pauses a running animation.</p>
     * <p>This method should only be called on which the animation was started.</p>
     * <p>If the animation has not yet been started or has since ended, then the call is ignored.</p>
     * @friend
     */
    pause(): void;
    /**
     * <p>Resumes a paused animation, causing the animation to pick up where it left off when it was paused.</p>
     * <p>This method should only be called on which the animation was paused.</p>
     * @friend
     */
    resume(): void;
}
export = CanvasAnimationView;
