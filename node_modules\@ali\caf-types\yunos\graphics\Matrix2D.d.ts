import YObject = require("../core/YObject");
/**
 * A graphics tools class to calculate coordinate change in 2D.
 * @private
 */
declare class Matrix2D extends YObject {
    /**
     * <p>Constructor that create a matrix2D.</p>
     * @private
     */
    /**
     * <p>Constructor that create a matrix2D.</p>
     * @param {yunos.graphics.Matrix2D} matrix - instanceof for yunos.graphics.Matrix2D, assign value to this new instance
     * @private
     */
    private constructor(matrix: Matrix2D);
    private assign(matrix: Matrix2D): this;
    private vectorMul(vector: number[]): number[];
    private mul(matrix: Matrix2D): this;
    private rotate(rad: number): this;
    private scale(sx: number, sy: number): this;
    private skew(sx: number, sy: number): this;
    private translate(tx: number, ty: number): this;
    private at(x: number, y: number): Object;
}
export = Matrix2D;
