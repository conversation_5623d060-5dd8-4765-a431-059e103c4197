import Dialog = require("./Dialog");
import CompositeView = require("yunos/ui/view/CompositeView");
import Rectangle = require("yunos/graphics/Rectangle");
import Point = require("yunos/graphics/Point");
import View = require("yunos/ui/view/View");
import Gradient = require("yunos/graphics/Gradient");
import Bitmap = require("yunos/graphics/Bitmap");
/**
 * <p>The PopupDialog component allows opening pop-up dialog, which is associate with a anchor view or a rectangle.</p>
 * @extends yunos.ui.widget.Dialog
 * @memberof yunos.ui.widget
 * @public
 * @since 4
 *
 */
declare class PopupDialog extends Dialog {
    private _pointerDirection;
    private _bestDirection;
    private _pointerType;
    private _anchorView;
    private _anchorRect;
    private _contentPropChange;
    private _anchorPropChange;
    private _windowPositionChange;
    private _contentBackground;
    private _contentPadding;
    private _cursorUpUri;
    private _cursorDownUri;
    private _cursorLeftUri;
    private _cursorRightUri;
    private _cursorUp;
    private _cursorLeft;
    private _cursorRight;
    private _cursorDown;
    private _cursorWidth;
    private _cursorHeight;
    private _cursorView;
    private _currentWindowPos;
    private _popOffsetX;
    private _popOffsetY;
    private _givenX;
    private _givenY;
    private _isSettingAnchor;
    protected _contentView: CompositeView;
    protected _fontSize: string;
    protected _fontFamily: string;
    protected _cursorRelativePos: {
        dx: number;
        dy: number;
    };
    protected _winRect: {
        left: number;
        top: number;
        right: number;
        bottom: number;
    };
    /**
     * <p>destructor</p>
     * @public
     * @override
     * @since 4
     *
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>The content view which is wrap by PopupDialog.</p>
     * @returns {yunos.ui.view.View|null} return the content view or null
     * @override
     * @public
     * @since 4
     *
     */
    public getContentView(): View | null;
    /**
     * <p>Set the content view of PopupDialog. The frame of content view will wrap to its content.</p>
     * @param {yunos.ui.view.View|null} view - the content view.
     * @throws {TypeError} If the parameter is not fit the required type.
     * @override
     * @public
     * @since 4
     *
     */
    public setContentView(view: View): void;
    /**
     * <p>This property determines whether to cursor icon pointing to the anchor.</p>
     * @name yunos.ui.widget.PopupDialog#pointerType
     * @type {yunos.ui.widget.PopupDialog.PointerType}
     * @default yunos.ui.widget.PopupDialog.PointerType.Visible
     * @public
     * @since 4
     *
     */
    public pointerType: number;
    /**
     * <p>The anchorView associated with PopupDialog. Between anchorView and anchorRect there is only one property should be set.</p>
     * @name yunos.ui.widget.PopupDialog#anchorView
     * @type {yunos.ui.view.View}
     * @public
     * @since 4
     *
     */
    public anchorView: View;
    /**
     * <p>The anchor rectangle which is pointed by PopupDialog. Between anchorRect and anchorView there is only one property should be set.</p>
     * @name yunos.ui.widget.PopupDialog#anchorRect
     * @type {yunos.graphics.Rectangle}
     * @public
     * @since 4
     *
     */
    public anchorRect: Rectangle;
    /**
     * <p>The property should be bitwise number, consist of PopupDialog.PointerDirection.</p>
     * @name yunos.ui.widget.PopupDialog#pointerDirection
     * @type {yunos.ui.widget.PopupDialog.PointerDirection}
     * @default {yunos.ui.widget.PopupDialog.PointerDirection.Any}
     * @public
     * @since 4
     *
     */
    public pointerDirection: number;
    /**
     * <p>Starts to show popup view and display it on screen. If the parameter x, y are both specified then</p>
     * <p>PopupDialog will shows at that position.</p>
     * @param x {number} the x position of the dialog will be showing
     * @param y {number} the y position of the dialog will be showing
     * @public
     * @since 4
     *
     */
    public show(x?: number, y?: number): void;
    /**
     * starts to show popup view with offset and display it on screen.
     * the parameter x, y are both specified
     * case1:the anchorView is not null
     *  PopupDialog's rectangle will have offset x and y on x axios and y axios relative to anchorView rectangle
     * case2:the anchorVIew is null
     *  PopupDialog's rectangle will have offset x and y on x axios and y axios relative to parenWindow rectangle from its point(0,0)
     * @param x {number} the relative offset in x axios
     * @param y {number} the relative offset in y axios
     * @public
     * @since 6
     *
     */
    public showOffset(x: number, y: number): void;
    /**
     * <p>Apply theme style for popup view.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.PopupDialog#defaultStyleName
     * @type {string}
     * @default "PopupDialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * Defines the background color, gradient or image for this view.
     * @name yunos.ui.widget.PopupDialog#background
     * @type {string}
     * @default transparent
     * @fires yunos.ui.widget.PopupDialog#propertychange
     * @throws {TypeError} If parameter is not a string.
     * @throws {TypeError} If parameter is not a valid color, gradient or image.
     * @public
     * @override
     * @since 5
     */
    public background: number | Bitmap | Gradient | string;
    private drawCursor(direction: number, x: number, y: number): void;
    private getTargetPosition(): Point;
    private translateGlobalRectForView(view: View, rect?: Rectangle): Rectangle;
    private calcTargetRect(sourceRect: Rectangle, directions: number): Rectangle;
    private getRectForCursorLeft(srcRect: Rectangle, winRect: Rectangle): Rectangle;
    private getRectForCursorUp(srcRect: Rectangle, winRect: Rectangle): Rectangle;
    private getRectForCursorRight(srcRect: Rectangle, winRect: Rectangle): Rectangle;
    private getRectForCursorDown(srcRect: Rectangle, winRect: Rectangle): Rectangle;
    private getBestRect(srcRect: Rectangle, rects: Map<number, Rectangle>): number;
    private calcCursorPosition(sourceRect: Rectangle, targetRect: Rectangle, direction: number): {
        x: number;
        y: number;
    };
    /**
     * <p>Flags of the allowing cursor direction. The operation of logic or could be used between these flags.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly PointerDirection: {
        /**
         * <p>Indicates that the pointer pointes to left.</p>
         * @public
         * @since 4
         *
         */
        Left: int;
        /**
         * <p>Indicates that the pointer pointes to up.</p>
         * @public
         * @since 4
         *
         */
        Up: int;
        /**
         * <p>Indicates that the pointer points to right.</p>
         * @public
         * @since 4
         *
         */
        Right: int;
        /**
         * <p>Indicates that the pointer points to down.</p>
         * @public
         * @since 4
         *
         */
        Down: int;
        /**
         * <p>Indicates that the pointer direction will be chosen if there is enough space for displaying.</p>
         * @public
         * @since 4
         *
         */
        Any: int;
    };
    /**
     * <p>Enum for the pointer type.</p>
     * <p>The Value set the pointer showing type. The default is Visible.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly PointerType: {
        /**
         * <p>Indicates that the pointer is invisible.</p>
         * @public
         * @since 4
         *
         */
        Hidden: int;
        /**
         * <p>Indicates that the pointer is visible.</p>
         * @public
         * @since 4
         *
         */
        Visible: int;
    };
}
export = PopupDialog;
