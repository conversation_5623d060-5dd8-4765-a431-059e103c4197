import YObject = require("yunos/core/YObject");
/**
 * <p>ForeignKey class, which contains the information of a column which</p>
 * <p>is a foreign key</p>
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class Foreign<PERSON><PERSON> extends YObject {
    private _name;
    private _refTable;
    private _refColumn;
    /**
     * Create a ForeignKey instance.
     *
     * @param {string} name - The column name of the foreign key
     * @param {string} refTable - The table name that the foreign key referring to
     * @param {string} refColumn - The column name that the foreign key referring to
     * @public
     * @since 2
     */
    public constructor(name: string, refTable: string, refColumn: string);
    /**
     * Column name.
     * @name yunos.database.sqlite.ForeignKey#name
     * @type {string}
     * @public
     * @since 2
     */
    public name: string;
    /**
     * The table that the foreign key referring to.
     * @name yunos.database.sqlite.ForeignKey#refTable
     * @type {string}
     * @public
     * @since 2
     */
    public refTable: string;
    /**
     * The table column that the foreign key referring to.
     * @name yunos.database.sqlite.ForeignKey#refColumn
     * @type {string}
     * @public
     * @since 2
     */
    public refColumn: string;
}
export = ForeignKey;
