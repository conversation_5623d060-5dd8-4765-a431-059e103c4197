import StackRouter = require("yunos/appmodel/StackRouter");
import CompositeView = require("yunos/ui/view/CompositeView");
import BasePage = require("../base/BasePage");
declare class BMPage extends BasePage {
    private __baseAnimations;
    readonly carMode: string;
    router: StackRouter;
    readonly modeConfig: import("../system/Types").IModeConfig;
    readonly rootContainer: CompositeView;
    private _rootContainer;
    private mRouter;
    onCreate(): void;
    onStart(): void;
    protected onStop(): void;
    protected getWindowConfig(): {};
    protected isUsingFastRender(): boolean;
    protected usingCommonBackground(): boolean;
    protected useRouter(): boolean;
    protected usingPresenterFallback(): boolean;
    private initRootView;
    private _getAppMargin;
    private _getRootContainerSize;
}
export = BMPage;
