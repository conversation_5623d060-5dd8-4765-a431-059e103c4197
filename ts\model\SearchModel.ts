/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";

import Model = require("yunos/appmodel/Model");
import ConfigStore = require("yunos/content/ConfigStore");
import log = require("../utils/log");
import Consts = require("../Consts");
const TAG = "SearchModel";

class SearchModel extends Model {
    private _configStore: ConfigStore;
    private static _instance: SearchModel;

    constructor() {
        super();
        this._configStore = ConfigStore.getInstance("VIDEO_CONFIGSTORE");
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new SearchModel();
        }
        return this._instance;
    }

    /**
     * 获取搜索历史
     */
    getSearchHistory(accountName?: string) {
        if (!this._configStore) {
            log.W(TAG, "getSearchHistory, configStore is null.");
            return [];
        }

        if (!accountName) {
            log.W(TAG, "getSearchHistory, accountName is null");
            return [];
        }

        let searchHistory = this._configStore.get(accountName, []);
        log.D(TAG, `getSearchHistory ${searchHistory}`);
        return searchHistory;
    }

    /**
     * 清除搜索历史
     */
    clearSearchHistory(accountName: string) {
        if (!this._configStore) {
            log.W(TAG, "clearSearchHistory, configStore is null");
            return;
        }

        if (!accountName) {
            log.W(TAG, "clearSearchHistory, accountName is null");
            return;
        }

        this._configStore.put(accountName, []);
        this._configStore.apply((err) => {
            if (err !== null) {
                log.E(TAG, `searchHistory clear failed`);
            } else {
                log.I(TAG, `searchHistory clear success`);
            }
        });
    }

    /**
     * 添加搜索历史
     */
    addSearchHistory(accountName: string, searchWord: Object) {
        if (!this._configStore) {
            log.W(TAG, "addSearchHistory, configStore is null");
            return;
        }

        if (typeof searchWord !== "string" || searchWord.trim() === "") {
            log.I(TAG, "addSearchHistory, searchWord is not string");
            return;
        }

        if (!accountName) {
            log.W(TAG, "addSearchHistory, accountName is null");
            return;
        }

        let searchHistory = <string[]> this._configStore.get(accountName, []);
        let eltIndex = searchHistory.findIndex((elt) => {
            return elt === searchWord;
        });
        if (eltIndex !== -1) {
            searchHistory.splice(eltIndex, 1);
        }
        searchHistory.unshift(searchWord);
        if (searchHistory.length > Consts.MAX_SEARCH_HISTORY_NUM) {
            searchHistory = searchHistory.slice(0, Consts.MAX_SEARCH_HISTORY_NUM);
        }
        this._configStore.put(accountName, searchHistory);
        this._configStore.apply((err) => {
            if (err !== null) {
                log.D(TAG, `searchHistory ${searchWord} added failed`);
            } else {
                log.D(TAG, `searchHistory ${searchWord} added success`);
            }
        });
    }
}

export = SearchModel;
