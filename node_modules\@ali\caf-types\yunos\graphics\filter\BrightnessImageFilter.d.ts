import ImageFilter = require("./ImageFilter");
/**
 * <p>The BrightnessImageFilter class applies a linear multiplier to the input image, making it appear more or less bright.</p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class BrightnessImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The brightness amount. A value of 0 will create an image that is completely black. A value of 1 leaves the input unchanged.</p>
     * @name yunos.graphics.filter.BrightnessImageFilter#amount
     * @type {number}
     * @default 1
     * @public
     * @since 5
     */
    public amount: number;
}
export = BrightnessImageFilter;
