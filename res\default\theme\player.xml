<partial>
    <property-set name="player">
        <id name="id_warning_dialog">
            <property name="background">{color.PLAYER_WARNING_BG_COLOR}</property>
        </id>
        <id name="id_icon">
            <property name="src">{img(images/ic_warning.png)}</property>
        </id>
        <id name="id_title">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_tips">
            <property name="color">{theme.color.White_3}</property>
        </id>
    </property-set>

    <property-set name="player_title">
        <property name="color">{theme.color.White_2}</property>
    </property-set>

    <property-set name="player_back_multistate">
        <property name="multiState">{config.PLAYER_BACK_MULTISTATE}</property>
    </property-set>

    <property-set name="player_play_multistate">
        <property name="multiState">{config.CTRLBAR_PLAY_BTN_MULTISTATE}</property>
    </property-set>

    <property-set name="player_pause_multistate">
        <property name="multiState">{config.CTRLBAR_PAUSE_BTN_MULTISTATE}</property>
    </property-set>
</partial>
