/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable requireDotNotation
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import LinkClient = require("yunos/net/linkclient/LinkClient");
const iLinkClient = LinkClient.getInstance();
import NetStatus = require("yunos/net/linkclient/NetStatus");
import log = require("../utils/log");
const TAG = "NetworkState";

const stateStrList = ["CONNECTING", "CONNECTED", "SUSPENDED", " DISCONNECTING", "DISCONNECTED", "UNKNOWN"];

class NetworkState {
    private static _instance: NetworkState;
    private _networkConnected: boolean;
    private _trafficCtrlEnabled: boolean;
    private _onTrafficCtrlChanged: (enabled: boolean) => void;
    private _onNetworkStatusChanged: (ns: Object) => void;
    private _networkStateChangeListener: (enabled: boolean) => void;

    constructor() {
        this._init();
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new NetworkState();
        }
        return this._instance;
    }

    _init() {
        // 初始化网络状态变化的回调函数
        this._onNetworkStatusChanged = (ns: NetStatus) => {
            if (!ns) {
                log.E(TAG, "onNetworkStatusChanged failed, ns invalid, ns:", ns);
                return;
            }

            log.D(TAG, "onNetworkStatusChanged, ns:", ns.netType, stateStrList[ns.connState]);
            let networkConnected = false;
            let ans = iLinkClient.getActiveNetworkStatus();
            if (ans) {
                log.D(TAG, "onNetworkStatusChanged, ans:", ans.netType, stateStrList[ans.connState]);
                networkConnected = ans.connState === LinkClient.State.CONNECTED;
            } else {
                networkConnected = ns.connState === LinkClient.State.CONNECTED;
            }

            if (networkConnected !== this._networkConnected || ans && ans.netType !== ns.netType) {
                if (this._networkStateChangeListener) {
                    this._networkStateChangeListener(networkConnected);
                }
            }
            this._networkConnected = networkConnected;
        };
        iLinkClient.addListener(this._onNetworkStatusChanged);

        this._trafficCtrlEnabled = iLinkClient.getTrafficCtrlState();
        log.D(TAG, "trafficCtrlState", this._trafficCtrlEnabled);
        this._onTrafficCtrlChanged = (enable) => {
            log.D(TAG, "_onTrafficCtrlChanged, enable", enable);
            this._trafficCtrlEnabled = enable;
        };
        iLinkClient.on("trafficctrlchanged", this._onTrafficCtrlChanged);
    }

    /**
     * 检查网络是否连接
     */
    get networkConnected() {
        return this._networkConnected;
    }

    /**
     * 检查4G流量开关
     */
    get trafficCtrlEnabled() {
        return this._trafficCtrlEnabled;
    }

    /**
     * 检查当前网络是否为WiFi
     */
    isWiFi() {
        let ans = iLinkClient.getActiveNetworkStatus();
        if (ans) {
            if (ans.connState === LinkClient.State.CONNECTED && ans.netType === LinkClient.NetworkType.TYPE_WIFI) {
                return true;
            }
        }
        return false;
    }

    registerNetworkStateChangeListener(callback: () => void) {
        this._networkStateChangeListener = callback;
    }

    unRegisterNetworkStateChangeListener() {
        this._networkStateChangeListener = null;
    }
}

export = NetworkState;
