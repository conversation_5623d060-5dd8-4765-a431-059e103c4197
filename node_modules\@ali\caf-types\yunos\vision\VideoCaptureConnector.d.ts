import BarcodeDetection = require("./BarcodeDetection");
import SurfaceView = require("../ui/view/SurfaceView");
import VideoCapture = require("../device/VideoCapture");

declare class VideoCaptureConnector {
    constructor(detection: BarcodeDetection, cameraId: number);
    stopDetect: () => void;
    startDetect: () => void;
    startPreview: (view: SurfaceView) => void;
    stopPreview: () => void;
    destroy: () => void;
    getVideoCapture: () => VideoCapture;
}

export = VideoCaptureConnector;