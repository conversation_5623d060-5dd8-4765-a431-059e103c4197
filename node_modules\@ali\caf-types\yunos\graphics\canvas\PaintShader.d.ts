import YObject = require("../../core/YObject");
/**
 * Enum of Shader Tile mode
 * @ignore
 */
export declare enum TileMode {
    /**
     *  replicate the edge color if the shader draws outside of its
     *  original bounds
     * @ignore
     */
    Clamp = 0,
    /**
     *  repeat the shader's image horizontally and vertically
     * @ignore
     */
    Repeat = 1,
    /**
     *  repeat the shader's image horizontally and vertically, alternating
     *  mirror images so that adjacent images always seam
     * @ignore
     */
    Mirror = 2
}
/**
 * Wrapper class of SkShader
 * A Shader specify the source color(s) for what is being drawn.
 * If a Paint has no shader, then the paint's color is used.
 * If the paint has a shader, then the shader's color(s) are use instead,
 *  but they are modulated by the paint's alpha.
 *
 * Current support ColorShader, LinearGradientShader, RadialGradientShader
 * Note: do not create use constructor, use static method instead
 * @example
 *  let shader = PaintShader.CreateLinearGradientShader(Float32Array.from([20.0, 20.0, 100.0, 100.0])
 *               , [new Color('blue'), new Color('yellow')], null, TileMode.Mirror));
 * @ignore
 */
export declare class PaintShader extends YObject {
}
