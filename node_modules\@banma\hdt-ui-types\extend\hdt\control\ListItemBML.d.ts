import SwipeRevealView = require("yunos/ui/view/SwipeRevealView");
import ImageView = require("yunos/ui/view/ImageView");
import { MultiState } from "yunos/ui/util/TypeHelper";
interface IStyle {
    defaultMultiState: MultiState;
    defaultImageMarginStart: number;
    defaultImageWidth: number;
    defaultImageBorderRadius: number;
    defaultEmptyWidth: number;
    defaultContentMultiState: MultiState;
    isShowShader: boolean;
}
declare class ListItemBML extends SwipeRevealView {
    private _defaultMultiState;
    private _imageLeft;
    private _imageRight;
    private _defaultImageWidth;
    private _defaultImageBorderRadius;
    private _defaultEmptyWidth;
    private _defaultContentMultiState;
    private _emptyView;
    constructor();
    destroy(): void;
    readonly leftImage: ImageView;
    readonly rightImage: ImageView;
    addHiddenView(imageLeftMultiState: MultiState, imageRightMultiState?: MultiState): void;
    private createImageView;
    private createEmptyView;
    readonly defaultStyleName: string;
    applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
}
export = ListItemBML;
