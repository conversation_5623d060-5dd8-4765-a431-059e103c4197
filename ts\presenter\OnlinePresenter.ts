/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
const {Visible, Hidden, None} = require("yunos/ui/view/View").Visibility;
import PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
import AnimationGroup = require("yunos/ui/animation/AnimationGroup");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import RelativeLayout = require("yunos/ui/layout/RelativeLayout");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import View = require("yunos/ui/view/View");
import GridView = require("yunos/ui/view/GridView");
import CompositeView = require("yunos/ui/view/CompositeView");
import {IVoiceCommand, IVoiceEvent} from "../Types";
const RecognitionMode = require("yunos/ui/voice/VoiceCommand").RecognitionMode;
import GestureEvent = require("yunos/ui/event/GestureEvent");
import OnlineImageView = require("extend/hdt/control/OnlineImageViewBM");
import ButtonBM = require("extend/hdt/control/ButtonBM");
import PopupBM = require("extend/hdt/control/PopupBM");
import LoadingPage = require("extend/hdt/control/LoadingPageBM");
import Loading = require("extend/hdt/control/LoadingBM");
import CategoryAdapter = require("../adapter/CategoryAdapter");
import OnlineAdapter = require("../adapter/OnlineAdapter");
import Consts = require("../Consts");
const RoutePath = Consts.RoutePath;
import Features = require("../Features");
import VideoInfo = require("../model/VideoInfo");
const iOnlineModel = require("../model/OnlineModel").getInstance();
const iVideoModel = require("../model/VideoModel").getInstance();
const iNetworkState = require("../monitor/NetworkState").getInstance();
const iTrafficHelper = require("../utils/TrafficHelper").getInstance();
const iUserTrackHelper = require("../utils/UserTrackHelper").getInstance();
const iCacheVideo = require("../utils/CacheVideo").getInstance();
import log = require("../utils/log");
import Utils = require("../utils/Utils");
const TAG = "OnlinePresenter";

const PAGE_WIDTH = Utils.getPageWidth();
const USB_FIRST_INDEX = 0;
const USB_SECOND_INDEX = 1;
const USB_ANIM_DURATION = 300;
const LoadingPageType = {
    LOADING: 0,
    NETWORK: 1,
    SERVER: 2,
    EMPTY: 3
};

interface IViews {
    navigationBar?: NavigationBar;
    dlnaIcon?: ImageView;
    searchIcon?: ImageView;
    cpInfoView?: View;
    loadingPage?: LoadingPage;
    empty?: CompositeView;
    onlineList?: GridView;
    topMask?: ImageView;
    bottomMask?: ImageView;
    bannerContainer?: CompositeView;
    usbFirst?: ICompositeView;
    usbSecond?: ICompositeView;
    categoryIcon?: ImageView;
    categoryTitle?: TextView;
    cpLogo?: ImageView;
}

interface ICategoryDialogContent extends CompositeView {
    container?: CompositeView;
    categoryList?: GridView;
    networkError?: CompositeView;
    loading?: Loading;
    retryBtn?: ButtonBM;
}

interface ICategoryData {
    category: string;
    selected: boolean;
    index: number;
}

interface IQrCodeDialogContent extends CompositeView {
    container?: CompositeView;
    loadingContainer?: CompositeView;
    loading?: Loading;
    networkTip?: TextView;
    qrCode?: OnlineImageView;
}

interface IGridView {
    getHeadersHight(length: number): number;
    canScrollVertical(delta: number): boolean;
}

interface ICompositeView extends CompositeView {
    title: TextView;
    icon: ImageView;
    loading: Loading;
    path: string;
}

interface IParamObj {
    result?: Object;
    category_list?: string;
    reason?: Object;
    qrcode_link?: Object;
    category?: Object;
    usb_count?: Object;
    video_count?: number;
    video_title_list?: string;
    video_id_list?: string;
    cpId?: string;
}

interface IPageInfo {
    pageNo: number;
    videoCount: number;
}

interface IConfig {
    PAGE_MARGIN?: number;
    ITEM_SPACE?: number;
    ONLINE_COLUMNS_NUM?: number;
    ONLINE_BANNER_CONTAINER_HEIGHT?: number;
    ONLINE_LIST_BOTTOM_PADDING?: number;
    ONLINE_ITEM_HEIGHT?: number;
    ONLINE_USB_HIDDEN_HEIGHT?: number;
    ONLINE_USB_BTN_HEIGHT?: number;
    ONLINE_USB_LARGE_WIDTH?: number;
    ONLINE_USB_LARGE_TEXT_MAX_WIDTH?: number;
    ONLINE_USB_SMALL_WIDTH?: number;
    ONLINE_USB_SMALL_TEXT_MAX_WIDTH?: number;
    ONLINE_USB_ICON_MARGIN_LEFT?: number;
    ONLINE_USB_ICON_SIZE?: number;
    ONLINE_USB_TITLE_SPACE?: number;
    ONLINE_CATEGORY_POPUP_HEIGHT?: number;
    ONLINE_QR_CODE_POPUP_HEIGHT?: number;
    ONLINE_HEADER_RIGHT_ITEM_NO_DLNA_WIDTH?: number;
    ONLINE_CP_INFO_HEIGHT?: number;
}

const Config: IConfig = {};

class OnlinePresenter extends Presenter {
    private _highlightUrlChanged: (path: string, url: string, index: number) => void;
    private _onlineListChanged: (itemList: VideoInfo[], pageNo: number, videoCount: number) => void;
    private _retryBtnListener: () => void;

    private _destroyed: boolean;
    private _hidden: boolean;
    private _firstload: boolean;
    private _categoryIndex: number;
    private _categoryName: string;
    private _views: IViews;
    private _viewAttached: boolean;
    private _tapAbleViews: View[];
    private _lastScrollY: number;
    private _isScrollUp: boolean;
    private _loadingMoreView: View;
    private _loadingMore: boolean;
    private _loadingMoreTimeout: NodeJS.Timer;
    private _categoryDialog: PopupBM;
    private _categoryDialogContent: ICategoryDialogContent;
    private _qrCodeDialog: PopupBM;
    private _qrCodeDialogContent: IQrCodeDialogContent;

    /**
     * 在线视频记忆页面的逻辑
     * 1.当天进入在线视频需要记忆页码，在下次当天下次打开视频时停留在上次页码
     * 2.每天刷新，即每天第一次页码从头开始
     * 3.页码需要支持分类场景
     * 4.用户可上拉查看之前的内容，下拉加载新内容
     *
     * 实现
     * 1.每次滑动列表，记录本次停留的页码和当前可见的第一个视频位置（本页）
     * 2.再进入视频后先向服务端查询记录页码的视频列表，并根据上次视频的位置来显示
     * 3.未显示的视频，每次下拉加载3个视频
     */
     private _pageInfo: IPageInfo[]; // 页面信息：页码和本页视频数量
     private _pageNo: number; // 本页页码，从1开始
     private _pageFirstVideoPosition: number; // 本页当前可见的第一个视频位置，从0开始
     private _pageUnloadVideosArray: VideoInfo[]; // 本页未加载的视频列表
     private _pageUnloadNum: number; // 未加载的视频页数

    onCreate() {
        log.I(TAG, "onCreate");
        Config.PAGE_MARGIN = Utils.getDimen("PAGE_MARGIN");
        Config.ITEM_SPACE = Utils.getDimen("ITEM_SPACE");
        Config.ONLINE_COLUMNS_NUM = Utils.getDimen("ONLINE_COLUMNS_NUM");
        Config.ONLINE_BANNER_CONTAINER_HEIGHT = Utils.getDimen("ONLINE_BANNER_CONTAINER_HEIGHT");
        Config.ONLINE_LIST_BOTTOM_PADDING = Utils.getDimen("ONLINE_LIST_BOTTOM_PADDING");
        Config.ONLINE_ITEM_HEIGHT = Utils.getDimen("ONLINE_ITEM_HEIGHT");
        Config.ONLINE_USB_BTN_HEIGHT = Utils.getDimen("ONLINE_USB_BTN_HEIGHT");
        Config.ONLINE_USB_HIDDEN_HEIGHT = Config.ONLINE_USB_BTN_HEIGHT + Config.ITEM_SPACE;
        Config.ONLINE_USB_ICON_MARGIN_LEFT = Utils.getDimen("ONLINE_USB_ICON_MARGIN_LEFT");
        Config.ONLINE_USB_ICON_SIZE = Utils.getDimen("ONLINE_USB_ICON_SIZE");
        Config.ONLINE_USB_TITLE_SPACE = Utils.getDimen("ONLINE_USB_TITLE_SPACE");
        Config.ONLINE_USB_LARGE_WIDTH = PAGE_WIDTH - 2 * Config.PAGE_MARGIN;
        Config.ONLINE_USB_LARGE_TEXT_MAX_WIDTH = Config.ONLINE_USB_LARGE_WIDTH
            - 2 * Config.ONLINE_USB_ICON_MARGIN_LEFT
            - Config.ONLINE_USB_ICON_SIZE
            - Config.ONLINE_USB_TITLE_SPACE;
        Config.ONLINE_USB_SMALL_WIDTH = (Config.ONLINE_USB_LARGE_WIDTH
            - Config.ITEM_SPACE) / 2;
        Config.ONLINE_USB_SMALL_TEXT_MAX_WIDTH = Config.ONLINE_USB_SMALL_WIDTH
            - 2 * Config.ONLINE_USB_ICON_MARGIN_LEFT
            - Config.ONLINE_USB_ICON_SIZE
            - Config.ONLINE_USB_TITLE_SPACE;
        Config.ONLINE_CATEGORY_POPUP_HEIGHT = Utils.getDimen("ONLINE_CATEGORY_POPUP_HEIGHT");
        Config.ONLINE_QR_CODE_POPUP_HEIGHT = Utils.getDimen("ONLINE_QR_CODE_POPUP_HEIGHT");
        Config.ONLINE_HEADER_RIGHT_ITEM_NO_DLNA_WIDTH = Utils.getDimen("ONLINE_HEADER_RIGHT_ITEM_NO_DLNA_WIDTH");
        Config.ONLINE_CP_INFO_HEIGHT = Utils.getDimen("ONLINE_CP_INFO_HEIGHT");

        this._destroyed = false;
        this._lastScrollY = -1;
        this.attachView("online");
        this._init();
    }

    _init() {
        if (iTrafficHelper.checkTrafficState(null, false)) {
            iOnlineModel.querySpeedLimit();
            iOnlineModel.queryCPInfo();
            iOnlineModel.queryCategories();
        }
        this._initPageInfo();
        this._loadData();
        this._initListener();
    }

    /**
     * 初始化页面信息
     * 1.当前页码和第一个视频索引
     * 2.视频分类名称和索引
     */
    _initPageInfo() {
        let nowDateString = new Date().toDateString();
        let lastDateString = iVideoModel.getPageRefreshDate();
        log.I(TAG, "_initPageInfo", nowDateString, lastDateString);
        if (nowDateString === lastDateString) {
            let pageInfo = iVideoModel.getPageInfo();
            this._pageNo = pageInfo.pageNo;
            this._pageFirstVideoPosition = pageInfo.videoIndex;
            this._pageUnloadNum = this._pageNo - 1;
            this._categoryIndex = pageInfo.categoryIndex;
            this._categoryName = pageInfo.categoryName;
            if (pageInfo.categoryName) {
                this._views.categoryTitle.text = pageInfo.categoryName;
            }
        } else {
            this._pageNo = 1;
            this._pageFirstVideoPosition = 0;
            this._pageUnloadNum = 0;
            this._categoryIndex = 0;
            this._categoryName = "";
            iVideoModel.savePageRefreshDate(nowDateString);
            iCacheVideo.delAllCachedVideo();
        }
        this._firstload = true;
        this._pageInfo = [];
        this._pageUnloadVideosArray = [];
        log.I(TAG, "_initPageInfo", this._pageNo, this._pageFirstVideoPosition, this._categoryName);
    }

    /**
     * 记录页面信息
     * 1.当前页码和第一个视频索引
     * 2.视频分类名称和索引
     */
    _savePageInfo() {
        let firstPosition = this._views.onlineList.getFirstVisiblePosition();
        let pageNo = 1;
        let videoIndex = 0;
        let lastPageVideosSum = 0;
        let pageVideosSum = 0;
        let unloadVideosNum = this._pageUnloadVideosArray.length;
        log.I(TAG, "_savePageInfo", firstPosition, unloadVideosNum);
        for (let i = 0; i < this._pageInfo.length; i++) {
            // log.I(TAG, "_savePageInfo", this._pageInfo[i].pageNo, "---", this._pageInfo[i].videoCount);
            if (i === 0) {
                pageVideosSum = this._pageInfo[i].videoCount - unloadVideosNum;
            } else {
                pageVideosSum += this._pageInfo[i].videoCount;
            }

            if (pageVideosSum >= firstPosition) {
                pageNo = this._pageInfo[i].pageNo;
                if (i === 0) {
                    videoIndex = firstPosition + unloadVideosNum;
                } else {
                    videoIndex = firstPosition - lastPageVideosSum;
                }
                break;
            } else {
                lastPageVideosSum = pageVideosSum;
            }
        }

        log.I(TAG, "_savePageInfo", pageNo, videoIndex, this._categoryIndex, this._categoryName);
        iVideoModel.savePageInfo(pageNo, videoIndex, this._categoryIndex, this._categoryName);
    }

    /**
     * 查询当前页码的视频数量
     */
    _findPageVideoCount(pageNo: number) {
        for (let i = 0; i < this._pageInfo.length; i++) {
            // log.I(TAG, this._pageInfo[i].pageNo, "---", this._pageInfo[i].videoCount);
            if (this._pageInfo[i].pageNo === pageNo) {
                return this._pageInfo[i].videoCount;
            }
        }
        return 0;
    }

    /**
     * 加载在线视频数据
     * 1.查询缓存的在线视频信息
     * 2.请求cosmo服务端，查询在线视频数据
     */
    _loadData(isNeedLoadCache = true) {
        this._showLoadingPage(LoadingPageType.LOADING);

        log.I(TAG, "_loadData", isNeedLoadCache, this._firstload, this._categoryName);
        if (isNeedLoadCache) {
            iOnlineModel.loadCacheData((error: Object, itemList: VideoInfo[]) => {
                if (error) {
                    log.I(TAG, "loadCacheData", error);
                    return;
                }
                if (!iNetworkState.networkConnected) {
                    log.I(TAG, "loadCacheData, network disconnect");
                    return;
                }
                if (!iTrafficHelper.checkTrafficState(null, false)) {
                    log.I(TAG, "loadCacheData, traffic issue");
                    return;
                }
                if (this._firstload) {
                    itemList = this._checkVideoList(itemList);
                }
                this._trySetSelectionList(itemList);
            });
        }

        if (this._categoryName) {
            iOnlineModel.queryVideoByCategory(this._categoryName, this._pageNo, true, this._loadDataCallback.bind(this));
        } else {
            iOnlineModel.queryVideoToday(this._pageNo, true, this._loadDataCallback.bind(this));
        }
    }

    _loadDataCallback(error: Object, cacheChanged: boolean, itemList: VideoInfo[]) {
        if (error) {
            log.I(TAG, "_loadDataCallback", error);
            if (error === "NETWORK_ERROR") {
                this._showLoadingPage(LoadingPageType.NETWORK);
            } else {
                this._showLoadingPage(LoadingPageType.SERVER);
            }
        } else {
            log.I(TAG, "_loadDataCallback", cacheChanged);
            this._recordPageInfo(this._pageNo, itemList ? itemList.length : 0, false);
            if (cacheChanged) {
                if (this._firstload) {
                    itemList = this._checkVideoList(itemList);
                }
                this._trySetSelectionList(itemList);
            } else {
                if (this._firstload) {
                    itemList = this._checkVideoList(itemList);
                    this._trySetSelectionList(itemList);
                }
            }
            this._firstload = false;
        }
    }

    /**
     * 记录页面信息，页码和视频数量
     */
    _recordPageInfo(pageNo: number, videoCount: number, isPrev: boolean) {
        if (videoCount === 0) {
            log.W(TAG, "_recordPageInfo, videoCount is zero");
            return;
        }

        let pageInfo = {
            pageNo: pageNo,
            videoCount: videoCount
        };

        log.I(TAG, "_recordPageInfo", pageNo, videoCount, isPrev);
        if (isPrev) {
            this._pageInfo.unshift(pageInfo);
        } else {
            this._pageInfo.push(pageInfo);
        }

        log.I(TAG, "_recordPageInfo, ===dump-pageinfo===");
        for (let i = 0; i < this._pageInfo.length; i++) {
            log.I(TAG, this._pageInfo[i].pageNo, "-", this._pageInfo[i].videoCount);
        }
        log.I(TAG, "_recordPageInfo, ===dump-pageinfo===");
    }

    /**
     * 根据记录的页码和显示的第一个视频索引来生成在线视频列表数据
     */
    _checkVideoList(itemList: VideoInfo[]) {
        if (!itemList || itemList.length === 0) {
            log.W(TAG, "_checkVideoList, list is null or empty");
            return itemList;
        }

        log.I(TAG, "_checkVideoList", this._pageFirstVideoPosition, itemList.length);
        if (this._pageFirstVideoPosition <= Config.ONLINE_COLUMNS_NUM) {
            return itemList;
        }

        if (this._pageFirstVideoPosition > itemList.length) {
            this._pageFirstVideoPosition = 1;
            return itemList;
        }

        if (itemList.length <= 2 * Config.ONLINE_COLUMNS_NUM) {
            return itemList;
        }

        let unloadVideoNum = 0;
        if (this._pageFirstVideoPosition % Config.ONLINE_COLUMNS_NUM === 0) {
            unloadVideoNum = this._pageFirstVideoPosition - Config.ONLINE_COLUMNS_NUM;
        } else {
            unloadVideoNum = Math.floor(this._pageFirstVideoPosition / Config.ONLINE_COLUMNS_NUM) * Config.ONLINE_COLUMNS_NUM;
        }
        this._pageUnloadVideosArray = itemList.splice(0, unloadVideoNum);
        log.I(TAG, "_checkVideoList", unloadVideoNum);
        return itemList;
    }

    /**
     * 初始化监听器，监听U盘、高亮、流量的变化消息
     */
    _initListener() {
        this._highlightUrlChanged = (path: string, url: string, index: number) => {
            if (this._destroyed) {
                log.W(TAG, "_highlightUrlChanged, presenter is destroyed");
                return;
            }

            if (path !== Consts.FromType.ONLINE) {
                log.W(TAG, "_highlightUrlChanged, ignore path");
                return;
            }

            if (!url) {
                log.W(TAG, "_highlightUrlChanged, url is null");
                return;
            }

            log.D(TAG, "_highlightUrlChanged", path, url, index);
            if (this._views.onlineList) {
                let adapter = <OnlineAdapter> this._views.onlineList.adapter;
                if (adapter && adapter.data) {
                    for (let i = 0; i < adapter.data.length; i++) {
                        (<VideoInfo> adapter.data[i]).lastPlayed = i === index ? true : false;
                        adapter.update(i, adapter.data[i]);
                    }
                }
            }
        };
        iVideoModel.on(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);

        this._onlineListChanged = (itemList: VideoInfo[], pageNo: number, videoCount: number) => {
            if (this._destroyed) {
                log.W(TAG, "_onlineListChanged, presenter is destroyed");
                return;
            }

            let lastVideoNum = 0;
            if (this._views.onlineList) {
                let adapter = <OnlineAdapter> this._views.onlineList.adapter;
                if (adapter && adapter.data) {
                    lastVideoNum = adapter.data.length;
                }
            }
            this._recordPageInfo(pageNo, videoCount, false);
            this._pageNo = pageNo;
            this._trySetSelectionList(itemList);
            this._views.onlineList.arriveAt(lastVideoNum);
        };
        iOnlineModel.on(Consts.EV_ITEM_LIST_CHANGED, this._onlineListChanged);
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_ONLINE_LIST);
    }

    onBackKey() {
        log.I(TAG, "onBackKey");
        return false;
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        this._closeAllDialog();
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_ONLINE_LIST);
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._viewAttached = true;
        this._updateUsbContainer();
        this._setupTapHandler();
        this._addVoiceCommands();
        this._createCategoryDialog();
        let iLocalModel = require("../model/LocalModel").getInstance();
        iLocalModel.preloadData();
    }

    _setupViews(parentView: View) {
        this._views = {};
        this._views.navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = View.Visibility.None;
        this._views.navigationBar.titleItem.title.align = TextView.Align.Left;
        this._views.navigationBar.preTitleItem.visibility = None;
        this._views.navigationBar.rightItem.visibility = View.Visibility.Visible;
        let rightItem = <CompositeView> LayoutManager.loadSync("online_nav_right_item");
        this._views.dlnaIcon = <ImageView> rightItem.findViewById("id_dlna");
        this._views.searchIcon = <ImageView> rightItem.findViewById("id_search");
        this._views.navigationBar.rightItem = rightItem;

        this._views.loadingPage = <LoadingPage> parentView.findViewById("id_loading_page");
        this._views.loadingPage.addEventListener("RetryButtonReleased", this._retryBtnListener = () => {
            log.I(TAG, "retry button pressed!");
            if (iNetworkState.networkConnected) {
                this._initPageInfo();
                this._loadData(false);
            }
        });
        this._views.empty = <CompositeView> parentView.findViewById("id_empty");
        this._views.onlineList = <GridView> parentView.findViewById("id_list");
        this._views.topMask = <ImageView> parentView.findViewById("id_top_mask");
        this._views.bottomMask = <ImageView> parentView.findViewById("id_bottom_mask");
        this._views.bannerContainer = <CompositeView> parentView.findViewById("id_banner_container");
        this._views.usbFirst = <ICompositeView> this._views.bannerContainer.findViewById("id_usb_first");
        this._views.usbSecond = <ICompositeView> this._views.bannerContainer.findViewById("id_usb_second");

        this._views.cpInfoView = <CompositeView> parentView.findViewById("id_cp_info");
        this._views.categoryIcon = <ImageView> this._views.cpInfoView.findViewById("id_category_icon");
        this._views.categoryTitle = <TextView> this._views.cpInfoView.findViewById("id_category_title");
        this._views.cpLogo = <ImageView> this._views.cpInfoView.findViewById("id_cp_logo");

        let headerViewHeight = 0;
        if (!Features.SUPPORT_USB) {
            headerViewHeight = Config.ONLINE_CP_INFO_HEIGHT;
            this._views.bannerContainer.height = Config.ONLINE_CP_INFO_HEIGHT;
            this._views.usbFirst.visibility = None;
            this._views.usbSecond.visibility = None;
        } else {
            headerViewHeight = Config.ONLINE_BANNER_CONTAINER_HEIGHT;
        }
        if (!Features.SUPPORT_DLNA) {
            rightItem.width = Config.ONLINE_HEADER_RIGHT_ITEM_NO_DLNA_WIDTH;
            this._views.dlnaIcon.visibility = None;
        }

        this._views.onlineList.voiceEnabled = true;
        let headerView = this._getPaddingView(headerViewHeight);
        this._views.onlineList.addHeader(headerView);
        let footerView = this._getPaddingView(Config.ONLINE_LIST_BOTTOM_PADDING);
        this._views.onlineList.addFooter(footerView);

        this._categoryDialogContent = <ICategoryDialogContent> {};
        this._qrCodeDialogContent = <IQrCodeDialogContent> {};
    }

    _getPaddingView(height: number) {
        let view = new View();
        view.background = "transparent";
        view.height = height;
        view.width = PAGE_WIDTH;
        return view;
    }

    /**
     * 更新首页USB入口显示，最对显示两个USB
     */
    _updateUsbContainer() {
        if (!Features.SUPPORT_USB) {
            return;
        }

        let len = 0;
        log.I(TAG, "_updateUsbContainer", len);
        switch (len) {
            case 0:
                this._views.usbFirst.width = Config.ONLINE_USB_LARGE_WIDTH;
                this._views.usbSecond.visibility = None;
                this._createUsbView(this._views.usbFirst, USB_FIRST_INDEX, 1);
                break;
            case 1:
                this._views.usbFirst.width = Config.ONLINE_USB_LARGE_WIDTH;
                this._views.usbSecond.visibility = None;
                this._createUsbView(this._views.usbFirst, USB_FIRST_INDEX, 1);
                break;
            case 2:
                this._views.usbFirst.width = Config.ONLINE_USB_SMALL_WIDTH;
                this._createUsbView(this._views.usbFirst, USB_FIRST_INDEX, 2);

                this._views.usbSecond.width = Config.ONLINE_USB_SMALL_WIDTH;
                this._views.usbSecond.visibility = Visible;
                this._createUsbView(this._views.usbSecond, USB_SECOND_INDEX, 2);
                break;
        }
    }

    /**
     * 根据USB数量，以及title长度，边距等动态计算title和icon的显示位置
     */
    _createUsbView(container: ICompositeView, index: number, total: number) {
        if (!container.title) {
            let icon = new ImageView();
            icon.id = "icon";
            icon.width = Config.ONLINE_USB_ICON_SIZE;
            icon.height = Config.ONLINE_USB_ICON_SIZE;
            icon.propertySetName = Consts.SRC_USB;
            icon.scaleType = ImageView.ScaleType.Fitxy;

            let loading = new Loading();
            loading.id = "loading";
            loading.sizeStyle = Loading.SizeStyle.S;
            loading.visibility = Hidden;

            container.icon = icon;
            container.loading = loading;
            container.addChild(icon);
            container.addChild(loading);
        } else {
            container.removeChild(container.title);
            container.title.destroy();
            container.title = null;
        }

        let title = new TextView();
        title.id = "title";
        title.propertySetName = Consts.ONLINE_USB_TITLE;
        title.align = TextView.Align.Left;
        title.elideMode = TextView.ElideMode.ElideRight;
        container.title = title;
        container.addChild(title);

        let usbTitle = "";
        usbTitle = iRes.getString("ERR_NO_USB");
        container.icon.visibility = Visible;
        setImmediate(() => {
            container.loading.visibility = Hidden;
        });
        container.path = "";

        let maxTitleWidth = 0;
        if (total === Consts.MAX_VOLUME_NUM) {
            maxTitleWidth = Config.ONLINE_USB_SMALL_TEXT_MAX_WIDTH;
        } else {
            maxTitleWidth = Config.ONLINE_USB_LARGE_TEXT_MAX_WIDTH;
        }

        container.title.text = usbTitle;
        if (container.title.width > maxTitleWidth) {
            container.title.width = maxTitleWidth;
        }

        let iconMargin = 0;
        if (total === Consts.MAX_VOLUME_NUM) {
            iconMargin = Config.ONLINE_USB_ICON_MARGIN_LEFT;
        } else {
            iconMargin = (container.width - Config.ONLINE_USB_ICON_SIZE -
                Config.ONLINE_USB_TITLE_SPACE - container.title.width) / 2;
        }
        log.I(TAG, "_createUsbView", container.width, container.title.width, container.title.contentWidth, iconMargin);

        let layout = new RelativeLayout();
        layout.setLayoutParam("icon", "align", {
            left: "parent",
            middle: "parent"
        });
        layout.setLayoutParam("icon", "margin", {
            left: iconMargin
        });

        layout.setLayoutParam("loading", "align", {
            left: "parent",
            middle: "parent"
        });
        layout.setLayoutParam("loading", "margin", {
            left: iconMargin
        });

        layout.setLayoutParam("title", "align", {
            left: {
                target: "icon", side: "right"
            },
            middle: "parent"
        });
        layout.setLayoutParam("title", "margin", {
            left: Config.ONLINE_USB_TITLE_SPACE
        });
        container.layout = layout;
    }

    _trySetSelectionList(itemList: object[]) {
        if (this._destroyed) {
            log.I(TAG, "_trySetSelectionList, destroyed.");
            return;
        }
        if (!this._viewAttached) {
            log.W(TAG, "_trySetSelectionList, waiting for view attached.");
            return;
        }
        if (!iTrafficHelper.checkTrafficState(null, false)) {
            log.I(TAG, "_trySetSelectionList, traffic issue");
            this._showLoadingPage(LoadingPageType.NETWORK);
            return;
        }

        log.I(TAG, "_trySetSelectionList", itemList ? itemList.length : 0);
        if (!itemList || itemList.length === 0) {
            this._showLoadingPage(LoadingPageType.EMPTY);
        } else if (this._views.onlineList && itemList && itemList.length > 0) {
            this._hideLoadingPage();
            this._views.onlineList.arriveAt(0);
            this._views.bannerContainer.translationY = 0;
            this._views.topMask.translationY = 0;
            this._views.bottomMask.visibility = Visible;
            let adapter = <OnlineAdapter> this._views.onlineList.adapter;
            if (!adapter) {
                adapter = new OnlineAdapter(false);
                this._views.onlineList.adapter = adapter;
                this._views.onlineList.on("itemselect", this._selectHandler.bind(this));
                this._views.onlineList.on("touchstart", this._onTouchstart.bind(this));
                this._views.onlineList.on("scroll", this._onScroll.bind(this));
                this._views.onlineList.on("verticalscrollend", this._onVerticalscrollend.bind(this));
                this._views.onlineList.on("panstart", this._onPanstart.bind(this));
                this._views.onlineList.on("panend", this._onPanend.bind(this));
                this._views.onlineList.on("reachend", this._onReachend.bind(this));
            }
            adapter.data = itemList;
        }
    }

    _selectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_selectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        this._playVideo(position);
    }

    _playVideo(position: number) {
        log.I(TAG, "_playVideo", position);
        let data = (<OnlineAdapter> this._views.onlineList.adapter).data;
        let item = <VideoInfo> data[position];
        if (!item) {
            log.I(TAG, "_playVideo, item is null");
            return;
        }
        let paramObj = {
            video_index: position,
            video_id: item.id,
            video_title: item.title,
            video_size: item.videoSize,
            video_category: item.category,
            video_tags: item.tags,
            video_duration: item.duration,
            cpId: iUserTrackHelper.CP_ID
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST,
            iUserTrackHelper.ONLINE_LIST_PICK, paramObj);
        let launchData = {
            from: Consts.FromType.ONLINE,
            index: position,
            list: data,
            pageNo: this._pageNo,
            categoryIndex: this._categoryIndex,
            categoryName: this._categoryName
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _setupTapHandler() {
        this._tapAbleViews = [];
        Utils.setOnTapListener(this._views.usbFirst, () => {
            log.I(TAG, "first usb pressed!");
            this._enterUsbDetail(USB_FIRST_INDEX);
        });
        this._tapAbleViews.push(this._views.usbFirst);

        Utils.setOnTapListener(this._views.usbSecond, () => {
            log.I(TAG, "second usb pressed!");
            this._enterUsbDetail(USB_SECOND_INDEX);
        });
        this._tapAbleViews.push(this._views.usbSecond);

        Utils.setOnTapListener(this._views.dlnaIcon, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_DLNA);
            this._navigate(RoutePath.DLNA);
        });
        this._tapAbleViews.push(this._views.dlnaIcon);

        Utils.setOnTapListener(this._views.searchIcon, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_SEARCH);
            this._navigate(RoutePath.SEARCH);
        });
        this._tapAbleViews.push(this._views.searchIcon);

        Utils.setOnTapListener(this._views.categoryIcon, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_CATEGORY_ICON);
            this._showCategoryDialog();
        });
        this._tapAbleViews.push(this._views.categoryIcon);

        Utils.setOnTapListener(this._views.categoryTitle, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_CATEGORY_ICON);
            this._showCategoryDialog();
        });
        this._tapAbleViews.push(this._views.categoryTitle);

        Utils.setOnTapListener(this._views.cpLogo, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_CP_ICON);
            this._showQRCodeDialog();
        });
        this._tapAbleViews.push(this._views.cpLogo);
    }

    /**
     * 进入USB详情页
     */
    _enterUsbDetail(index: number) {
        let usbContainer = index === USB_FIRST_INDEX ? this._views.usbFirst : this._views.usbSecond;
        let path = usbContainer.path;
        log.I(TAG, "_enterUsbDetail", usbContainer.title.text, usbContainer.path);
        this._navigate(RoutePath.LOCAL, {index: index + 1, path: path}, {launchMode: "single"});
    }

    /**
     * 创建视频分类对话框
     */
    _createCategoryDialog() {
        log.I(TAG, "_createCategoryDialog");
        let container = <ICategoryDialogContent> LayoutManager.loadSync("online_popup_category");
        this._categoryDialogContent.categoryList = <GridView> container.findViewById("id_category_list");
        this._categoryDialogContent.networkError = <CompositeView> container.findViewById("id_network");
        this._categoryDialogContent.loading = <Loading> container.findViewById("id_loading");
        this._categoryDialogContent.retryBtn = <ButtonBM> container.findViewById("id_btn_retry");
        this._categoryDialogContent.container = container;

        Utils.setOnTapListener(this._categoryDialogContent.retryBtn, () => {
            log.I(TAG, "tap category refresh btn.");
            this._showCategoryList();
        });

        const popup = new PopupBM();
        popup.height = Config.ONLINE_CATEGORY_POPUP_HEIGHT;
        popup.title = iRes.getString("CATEGORY_TITLE");
        popup.setContentView(this._categoryDialogContent.container);
        popup.on("close", () => {
            log.I(TAG, "category dialog close");
            iUserTrackHelper.clickButton(
                iUserTrackHelper.PAGE_ONLINE_LIST_CATE,
                iUserTrackHelper.ONLINE_LIST_CATE_CLOSE
            );

            let paramObj = <IParamObj> {};
            if (iNetworkState.networkConnected) {
                if (this._categoryDialogContent.categoryList) {
                    let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
                    if (adapter && adapter.data && adapter.data.length > 0) {
                        let list = "";
                        for (let i = 0; i < adapter.data.length; i++) {
                            list += (<ICategoryData> adapter.data[i]).category;
                            if (i !== adapter.data.length - 1) {
                                list += ",";
                            }
                        }
                        log.I(TAG, "category list:", list);
                        paramObj.result = iUserTrackHelper.SUCCESS;
                        paramObj.category_list = list;
                    } else {
                        paramObj.result = iUserTrackHelper.FAIL;
                        paramObj.reason = iTrafficHelper.failReason; 
                    }
                } else {
                    paramObj.result = iUserTrackHelper.FAIL;
                    paramObj.reason = iTrafficHelper.failReason;
                }
            } else {
                paramObj.result = iUserTrackHelper.FAIL;
                paramObj.reason = iTrafficHelper.failReason;
            }
            iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_ONLINE_LIST_CATE, paramObj);

            Utils.setOnTapListener(this._categoryDialogContent.retryBtn, null);
            this._categoryDialog.destroy(true);
            this._categoryDialog = null;
        });
        this._categoryDialog = popup;
        this._showCategoryList();
    }

    /**
     * 显示视频分类对话框
     */
    _showCategoryDialog() {
        if (this._hidden) {
            log.I(TAG, "_showCategoryDialog, page hidden");
            return;
        }

        if (this._qrCodeDialog && this._qrCodeDialog.isShowing()) {
            log.I(TAG, "_showCategoryDialog, qrCode dialog is showing");
            return;
        }

        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_ONLINE_LIST_CATE);
        if (!this._categoryDialog) {
            this._createCategoryDialog();
        }

        if (this._categoryDialog) {
            log.I(TAG, "_showCategoryDialog, show");
            this._categoryDialog.soundEffectsEnabled = true;
            this._categoryDialog.show();
        }
    }

    /**
     * 显示视频分类列表
     */
    _showCategoryList() {
        if (!iNetworkState.networkConnected || !iTrafficHelper.checkTrafficState()) {
            this._categoryDialogContent.loading.visibility = Hidden;
            this._categoryDialogContent.categoryList.visibility = Hidden;
            this._categoryDialogContent.networkError.visibility = Visible;
        } else {
            if (iOnlineModel.categoryList && iOnlineModel.categoryList.length > 0) {
                this._categoryDialogContent.loading.visibility = Hidden;
                this._categoryDialogContent.networkError.visibility = Hidden;
                this._categoryDialogContent.categoryList.visibility = Visible;
                this._trySetCategoryList();
                this._updateCategoryListSelected(this._categoryIndex);
            } else {
                this._categoryDialogContent.networkError.visibility = Hidden;
                this._categoryDialogContent.loading.visibility = Visible;
                iOnlineModel.queryCategories((error: Object) => {
                    log.I(TAG, "queryCategories", error);
                    this._categoryDialogContent.loading.visibility = Hidden;
                    if (error) {
                        this._categoryDialogContent.categoryList.visibility = Hidden;
                        this._categoryDialogContent.networkError.visibility = Visible;
                    } else {
                        this._categoryDialogContent.categoryList.visibility = Visible;
                        this._categoryDialogContent.networkError.visibility = Hidden;
                        this._trySetCategoryList();
                        this._updateCategoryListSelected(this._categoryIndex);
                    }
                });
            }
        }
    }

    /**
     * 创建视频分类列表
     */
    _trySetCategoryList() {
        log.I(TAG, "_trySetCategoryList");
        if (this._destroyed) {
            log.I(TAG, "_trySetCategoryList, destroyed.");
            return;
        }
        if (!this._viewAttached) {
            log.I(TAG, "_trySetCategoryList, waiting for view attached.");
            return;
        }
        if (!iOnlineModel.categoryList || iOnlineModel.categoryList.length === 0) {
            log.I(TAG, "_trySetCategoryList, category list is null.");
            return;
        }

        if (this._categoryIndex < iOnlineModel.categoryList.length) {
            iOnlineModel.categoryList[this._categoryIndex].selected = true;
        }

        let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
        if (!adapter) {
            adapter = new CategoryAdapter();
            adapter.registerSelectListener(this._categorySelectHandler.bind(this));
            this._categoryDialogContent.categoryList.adapter = adapter;
        }
        adapter.data = iOnlineModel.categoryList;
    }

    /**
     * 视频分类列表选中的回调
     */
    _categorySelectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_categorySelectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        if (this._categoryDialog) {
            this._categoryDialog.soundEffectsEnabled = false;
        }

        if (this._categoryIndex === position) {
            log.I(TAG, "_categorySelectHandler, ignore the same category");
            this._hideCategoryDialog();
            return;
        }

        let categoryName = "";
        if (this._categoryDialogContent) {
            let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
            if (adapter) {
                if (adapter.data[position]) {
                    categoryName = (<ICategoryData> adapter.data[position]).category;
                }
                adapter.onDataChange();
            }
        }
        iUserTrackHelper.clickButton(
            iUserTrackHelper.PAGE_ONLINE_LIST_CATE,
            iUserTrackHelper.ONLINE_LIST_CATE_PICK,
            {category_title: categoryName}
        );

        this._hideCategoryDialog();
        this._pageNo = 1;
        this._pageInfo = [];
        this._pageFirstVideoPosition = 0;
        this._pageUnloadNum = 0;
        this._pageUnloadVideosArray = [];
        this._categoryIndex = position;
        this._categoryName = position === 0 ? "" : categoryName;
        this._savePageInfo();
        this._views.categoryTitle.text = categoryName;
        this._views.onlineList.arriveAt(0);
        if (this._views.onlineList.adapter) {
            (<OnlineAdapter> this._views.onlineList.adapter).clear();
        }
        this._views.bannerContainer.translationY = 0;
        this._views.topMask.translationY = 0;
        this._loadData();
    }

    /**
     * 根据选中的分类更新列表显示
     */
    _updateCategoryListSelected(position: number) {
        log.I(TAG, "_updateCategoryListSelected", position);
        if (!this._categoryDialogContent.categoryList.adapter) {
            this._trySetCategoryList();
        }

        let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter
        if (adapter && adapter.data) {
            for (let i = 0; i < adapter.data.length; i++) {
                (<ICategoryData> adapter.data[i]).selected = position === i;
                adapter.update(i, adapter.data[i]);
            }
        }
    }

    _hideCategoryDialog() {
        if (this._categoryDialog) {
            this._categoryDialog.close();
        }
    }

    /**
     * 显示二维码对话框
     */
    _showQRCodeDialog() {
        if (this._hidden) {
            log.I(TAG, "_showQRCodeDialog, page hidden");
            return;
        }

        if (this._categoryDialog && this._categoryDialog.isShowing()) {
            log.I(TAG, "_showQRCodeDialog, category dialog is showing");
            return;
        }

        if (this._qrCodeDialog) {
            log.I(TAG, "_showQRCodeDialog, created");
            this._qrCodeDialog.show();
            return;
        }

        log.I(TAG, "_showQRCodeDialog, creating");
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_ONLINE_LIST_QR);
        this._qrCodeDialogContent.container = <IQrCodeDialogContent> LayoutManager.loadSync("online_popup_qr_code");
        this._qrCodeDialogContent.loadingContainer = <CompositeView> this._qrCodeDialogContent
            .container.findViewById("id_qr_code_loading_container");
        this._qrCodeDialogContent.loading = <Loading> this._qrCodeDialogContent
            .container.findViewById("id_loading");
        this._qrCodeDialogContent.networkTip = <TextView> this._qrCodeDialogContent
            .container.findViewById("id_network_tip");
        this._qrCodeDialogContent.qrCode = <OnlineImageView><Object> this._qrCodeDialogContent
            .container.findViewById("id_qr_code_icon");

        Utils.setOnTapListener(this._qrCodeDialogContent.networkTip, () => {
            log.I(TAG, "tap qrCode loading error.");
            this._showQrCode();
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST_QR, iUserTrackHelper.ONLINE_LIST_QR_RETRY);
        });

        const popup = new PopupBM();
        popup.height = Config.ONLINE_QR_CODE_POPUP_HEIGHT;
        if (iOnlineModel.cpInfo && iOnlineModel.cpInfo.name) {
            popup.title = iOnlineModel.cpInfo.name;
        } else {
            popup.title = iRes.getString("CP_TITLE");
        }
        popup.setContentView(this._qrCodeDialogContent.container);
        popup.on("close", () => {
            log.I(TAG, "qrCode dialog close");
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST_QR, iUserTrackHelper.ONLINE_LIST_QR_CLOSE);

            let paramObj = <IParamObj>{};
            if (iNetworkState.networkConnected && iOnlineModel.cpInfo && iOnlineModel.cpInfo.qrcode) {
                paramObj.result = iUserTrackHelper.SUCCESS;
                paramObj.reason = "NA";
                paramObj.qrcode_link = iOnlineModel.cpInfo.qrcode;
            } else {
                paramObj.result = iUserTrackHelper.FAIL;
                paramObj.reason = iTrafficHelper.failReason;
                paramObj.qrcode_link = "NA";
            }
            iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_ONLINE_LIST_QR, paramObj);

            Utils.setOnTapListener(this._qrCodeDialogContent.networkTip, null);
            this._qrCodeDialog.destroy(true);
            this._qrCodeDialog = null;
        });
        popup.show();
        this._qrCodeDialog = popup;
        this._showQrCode();
    }

    /**
     * 显示二维码
     */
    _showQrCode() {
        if (!iNetworkState.networkConnected || !iTrafficHelper.checkTrafficState()) {
            this._qrCodeDialogContent.qrCode.visibility = Hidden;
            this._queryCPInfo();
        } else {
            if (iOnlineModel.cpInfo && iOnlineModel.cpInfo.qrcode) {
                this._qrCodeDialogContent.loadingContainer.visibility = Hidden;
                this._qrCodeDialogContent.qrCode.visibility = Visible;
                this._loadQRImage();
            } else {
                this._queryCPInfo();
            }
        }
    }

    /**
     * 查询cp信息，logo和二维码
     */
    _queryCPInfo() {
        this._qrCodeDialogContent.loadingContainer.visibility = Visible;
        this._showQrCodeLoading(true);
        iOnlineModel.queryCPInfo((error: Object, cpInfo: { qrcode: Object }) => {
            if (!error && cpInfo && cpInfo.qrcode) {
                this._qrCodeDialogContent.loadingContainer.visibility = Hidden;
                this._qrCodeDialogContent.qrCode.visibility = Visible;
                this._loadQRImage();
            } else {
                this._qrCodeDialogContent.loadingContainer.visibility = Visible;
                this._qrCodeDialogContent.qrCode.visibility = Hidden;
                this._showQrCodeLoading();
            }
        });
    }

    _loadQRImage() {
        this._qrCodeDialogContent.qrCode.loadUrl(iOnlineModel.cpInfo.qrcode, (error: Object) => {
            log.I(TAG, "_loadQRImage, callback", error);
            if (error && this._qrCodeDialogContent && this._qrCodeDialogContent.qrCode) {
                this._qrCodeDialogContent.qrCode.propertySetName = Consts.SRC_KSQR;
            }
        });
    }

    _hideQRCodeDialog() {
        if (this._qrCodeDialog) {
            this._qrCodeDialog.close();
        }
    }

    _closeAllDialog() {
        log.I(TAG, "_closeAllDialog");
        this._hideCategoryDialog();
        this._hideQRCodeDialog();
    }

    /**
     * 视频列表滑动开始事件
     */
    _onTouchstart() {
        if (!Features.SUPPORT_USB) {
            return;
        }

        log.I(TAG, "_onTouchstart", this._views.onlineList.scrollY);
        this._lastScrollY = this._views.onlineList.scrollY;
        if (this._views.bannerContainer.translationY <= -Config.ONLINE_USB_HIDDEN_HEIGHT) {
            this._views.bannerContainer.translationY = -Config.ONLINE_USB_HIDDEN_HEIGHT;
            this._views.topMask.translationY = -Config.ONLINE_USB_HIDDEN_HEIGHT;
        }
        this._isScrollUp = true;
    }

    /**
     * 视频列表滑动结束事件
     */
    _onVerticalscrollend() {
        log.I(TAG, "_onVerticalscrollend", this._views.onlineList.scrollY);
        if (Features.SUPPORT_USB) {
            this._lastScrollY = -1;
            let scrollY = this._views.onlineList.scrollY;
            if (scrollY === 0) {
                this._views.bannerContainer.translationY = 0;
                this._views.topMask.translationY = 0;
            } else if (scrollY >= (<IGridView><object> this._views.onlineList).getHeadersHight(1)) {
                log.I(TAG, "_onVerticalscrollend", this._isScrollUp);
                if (this._isScrollUp) {
                    this._views.bannerContainer.translationY = -Config.ONLINE_USB_HIDDEN_HEIGHT;
                    this._views.topMask.translationY = -Config.ONLINE_USB_HIDDEN_HEIGHT;
                }
            }
        }
        this._savePageInfo();
    }

    /**
     * 视频列表滑动时触发，需根据滑动距离来移动USB的入口位置信息
     */
    _onScroll() {
        if (!Features.SUPPORT_USB) {
            this._views.topMask.visibility = this._views.onlineList.scrollY === 0 ? None : Visible;
            return;
        }

        let scrollY = this._views.onlineList.scrollY;
        let delta = scrollY - this._lastScrollY;
        let position = this._views.bannerContainer.translationY - delta;
        log.D(TAG, "_onScroll", scrollY, this._lastScrollY, delta, this._views.bannerContainer.translationY, position);

        if (position <= 0) {
            if (scrollY <= Config.ONLINE_USB_HIDDEN_HEIGHT) {
                if (scrollY === 0) {
                    this._views.bannerContainer.translationY = 0;
                    this._views.topMask.translationY = 0;
                } else if (position <= -Config.ONLINE_USB_HIDDEN_HEIGHT) {
                    this._views.bannerContainer.translationY = -Config.ONLINE_USB_HIDDEN_HEIGHT;
                    this._views.topMask.translationY = -Config.ONLINE_USB_HIDDEN_HEIGHT;
                } else {
                    this._views.bannerContainer.translationY = position;
                    this._views.topMask.translationY = position;
                }
                this._views.topMask.visibility = None;
            } else {
                if (position >= -Config.ONLINE_USB_HIDDEN_HEIGHT) {
                    this._views.bannerContainer.translationY = position;
                    this._views.topMask.translationY = position;
                    this._views.topMask.visibility = Visible;
                }
            }
        } else {
            this._views.topMask.visibility = scrollY === 0 ? None : Visible;
        }
        this._lastScrollY = scrollY;

        if (delta < 0) {
            this._isScrollUp = false;
        }
    }

    /**
     * 视频列表顶部时，向下滑动触发
     */
    _onPanstart(e: GestureEvent) {
        if (e.deltaY <= 0 || (<IGridView>(<Object> this._views.onlineList)).canScrollVertical(e.deltaY)) {
            log.W(TAG, "_onPanstart, not reaching the top");
            return;
        }

        if (!this._pageUnloadVideosArray || this._pageUnloadVideosArray.length === 0) {
            if (this._pageUnloadNum <= 0) {
                log.W(TAG, "_onReachstart, all pages loaded");
                return;
            }

            log.I(TAG, "_onPanstart", this._categoryName, this._pageUnloadNum);
            if (this._categoryName) {
                iOnlineModel.queryVideoByCategory(this._categoryName, this._pageUnloadNum, false, this._onPanstartCallback.bind(this));
            } else {
                iOnlineModel.queryVideoToday(this._pageUnloadNum, false, this._onPanstartCallback.bind(this));
            }
        } else {
            this._insertOneRowData();
            this._savePageInfo();
        }
    }

    _onPanend(e: GestureEvent) {
        if (e.deltaX >= 0 || (<IGridView>(<Object> this._views.onlineList)).canScrollVertical(e.deltaX)) {
            log.W(TAG, "_onPanend, not reaching the end");
            return;
        }
        this._onReachend();
    }

    _onPanstartCallback(error: object, changed: boolean, ret: VideoInfo[]) {
        if (error) {
            log.W(TAG, "_onPanstartCallback", error);
            return;
        }

        if (ret && ret.length > 0) {
            log.I(TAG, "_onPanstartCallback", ret.length);
            this._recordPageInfo(this._pageUnloadNum, ret.length, true);
            this._pageUnloadVideosArray = ret;
            this._insertOneRowData();
            this._savePageInfo();
            this._pageUnloadNum--;
        } else {
            log.I(TAG, "_onPanstartCallback, empty");
        }
    }

    /**
     * 加载一行视频数据，如进入视频应用时非第一页，则在向下滑动时每次加载一行数据防止界面有跳跃感
     */
    _insertOneRowData() {
        let len = this._pageUnloadVideosArray.length;
        log.I(TAG, "_insertOneRowData len", len);
        let oneRowData = [];
        if (len <= Config.ONLINE_COLUMNS_NUM) {
            oneRowData = this._pageUnloadVideosArray.splice(0, len);
        } else {
            oneRowData = this._pageUnloadVideosArray.splice(len - Config.ONLINE_COLUMNS_NUM, Config.ONLINE_COLUMNS_NUM);
        }
        (<OnlineAdapter> this._views.onlineList.adapter).insertAll(0, oneRowData);
    }

    _onReachend() {
        log.I(TAG, "_onReachend");
        if (!this._loadingMoreView) {
            this._loadingMoreView = this._getLoadingMoreView();
        }

        if (!this._loadingMore) {
            iUserTrackHelper.sendEvent(iUserTrackHelper.PAGE_ONLINE_LIST,
                iUserTrackHelper.ONLINE_LIST_SLIDING, {page_index: this._pageNo + 1}
            );
            this._loadingMore = true;
            this._views.onlineList.addFooter(this._loadingMoreView);

            let pageNo = this._pageNo + 1;
            if (this._categoryName) {
                iOnlineModel.queryVideoByCategory(this._categoryName, pageNo, false, this._onReachendCallback.bind(this));
            } else {
                iOnlineModel.queryVideoToday(pageNo, false, this._onReachendCallback.bind(this));
            }

            this._removeTimeout(this._loadingMoreTimeout);
            this._loadingMoreTimeout = setTimeout(() => {
                this._removeLoadingMoreView();
            }, Consts.LOADING_MORE_TIMEOUT);
        }
    }

    _onReachendCallback(error: object, changed: boolean, ret: VideoInfo[]) {
        log.I(TAG, "_onReachendCallback", error);
        this._removeLoadingMoreView();
        if (!error) {
            if (ret && ret.length > 0) {
                this._pageNo++;
                (<OnlineAdapter> this._views.onlineList.adapter).addAll(ret);
                this._recordPageInfo(this._pageNo, ret.length, false);
            } else {
                Utils.showToast(iRes.getString("LOAD_MORE_NO_MORE"));
            }
        } else {
            Utils.showToast(iRes.getString("LOAD_MORE_FAILED"));
        }
    }

    _getLoadingMoreView() {
        let view = LayoutManager.loadSync("online_loading_more");
        view.width = PAGE_WIDTH - 2 * Config.PAGE_MARGIN;
        return view;
    }

    _removeLoadingMoreView() {
        this._views.onlineList.removeFooter(this._loadingMoreView);
        this._loadingMore = false;
        this._removeTimeout(this._loadingMoreTimeout);
    }

    _navigate(path: string, launchData?: Object, newConfig = {}) {
        if (path === RoutePath.BACK) {
            this.context.router.back();
        } else {
            if (!this._hidden) {
                if (!launchData) {
                    launchData = {};
                }
                this.context.router.navigate(path, launchData, newConfig);
            } else {
                log.W(TAG, "_navigate negtive", path);
            }
        }
    }

    /**
     * 显示二维码loading信息
     */
    _showQrCodeLoading(showLoading?: boolean) {
        if (!this._views || this._destroyed) {
            return;
        }
        if (showLoading) {
            this._qrCodeDialogContent.networkTip.visibility = Hidden;
            this._qrCodeDialogContent.loading.visibility = Visible;
        } else {
            this._qrCodeDialogContent.networkTip.visibility = Visible;
            this._qrCodeDialogContent.loading.visibility = Hidden;
        }
    }

    /**
     * 关闭二维码loading信息
     */
    _hideQrCodeLoading() {
        if (!this._views || this._destroyed) {
            return;
        }
        this._qrCodeDialogContent.loadingContainer.visibility = Hidden;
    }

    /**
     * 显示loading、无网络、无数据等信息
     */
    _showLoadingPage(type: number) {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_showLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_showLoadingPage", type);
        if (type === LoadingPageType.LOADING) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.LoadingMode;
            this._views.loadingPage.errorTitleVisible = false;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = false;
            this._views.loadingPage.retryButtonVisible = false;
            this._views.loadingPage.loadingText = iRes.getString("LOADING");
            this._resetListView();
        } else if (type === LoadingPageType.SERVER || type === LoadingPageType.NETWORK) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.ErrorMode;
            this._views.loadingPage.errorTitleVisible = true;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = true;
            this._views.loadingPage.retryButtonVisible = true;
            if (type === LoadingPageType.SERVER) {
                this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
                this._views.loadingPage.errorTitle = iRes.getString("SERVER_ERROR");
            } else {
                this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
                this._views.loadingPage.errorTitle = iRes.getString("NETWORK_ERROR");
            }
            this._resetListView();
        } else if (type === LoadingPageType.EMPTY) {
            this._views.empty.visibility = Visible;
            this._views.loadingPage.visibility = None;
            this._resetListView();
        } else {
            log.W(TAG, "_showLoadingPage, type is undefine");
        }
    }

    _hideLoadingPage() {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_hideLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_hideLoadingPage");
        this._views.empty.visibility = None;
        this._views.loadingPage.visibility = None;
    }

    _resetListView() {
        if (this._views.onlineList) {
            this._views.onlineList.arriveAt(0);
            let adapter = <OnlineAdapter> this._views.onlineList.adapter;
            if (adapter) {
                adapter.clear();
            }
        }
        this._views.bannerContainer.translationY = 0;
        this._views.topMask.translationY = 0;
        this._views.topMask.visibility = None;
        this._views.bottomMask.visibility = None;
    }

    _calculatePosition(index: number) {
        let listHeight = this._views.onlineList.height;
        let itemHeight = Config.ONLINE_ITEM_HEIGHT + Config.ITEM_SPACE;
        let gap = itemHeight - listHeight % itemHeight;
        let rowNum = Math.ceil(listHeight / itemHeight);

        let visibleNum = 0;
        let scrollY = this._views.onlineList.scrollY;
        if (scrollY < Config.ONLINE_BANNER_CONTAINER_HEIGHT) {
            visibleNum = rowNum * Config.ONLINE_COLUMNS_NUM;
        } else {
            let delta = (scrollY - Config.ONLINE_BANNER_CONTAINER_HEIGHT) % itemHeight;
            if (delta < gap) {
                visibleNum = rowNum * Config.ONLINE_COLUMNS_NUM;
            } else {
                visibleNum = (rowNum + 1) * Config.ONLINE_COLUMNS_NUM;
            }
        }

        log.I(TAG, "_calculatePosition", visibleNum);
        if (index >= visibleNum) {
            return -1;
        }

        let firstVisiblePos = this._views.onlineList.getFirstVisiblePosition();
        let position = 0;
        if (firstVisiblePos === 0) {
            position = index;
        } else {
            position = firstVisiblePos - 1 + index;
        }
        log.I(TAG, "_calculatePosition", firstVisiblePos, position);
        return position;
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        log.I(TAG, "_addVoiceCommands");
        // jshint unused:false

        let cmdKeys = [];
        if (Features.SUPPORT_USB) {
            cmdKeys = ["VOICECMD_USB_VIDEO", "VOICECMD_USB_VIDEO_ALIAS"];
            Utils.registerVoiceCommand(this._views.usbFirst, cmdKeys, RecognitionMode.Both, (cmdKey: string, index: number) => {
                log.I(TAG, "voice command", cmdKey, index);
                this._enterUsbDetail(USB_FIRST_INDEX);
            }, true);
        }

        cmdKeys = ["CATEGORY_TITLE"];
        Utils.registerVoiceCommand(this._views.categoryIcon, cmdKeys, RecognitionMode.Both, (cmdKey: string, index: number) => {
            log.I(TAG, "voice command", cmdKey, index);
            this._showCategoryDialog();
        }, true);

        if (Consts.SUPPORT_VOICE_CMD) {
            let selectCommand = (<IVoiceCommand> <Object> VoiceCommand).createSelectCommand();
            this._views.onlineList.addVoiceCommand(selectCommand);
            this._views.onlineList.on("voice", (e: IVoiceEvent) => {
                if (e.command === selectCommand) {
                    let index = parseInt(e.result) - 1;
                    log.I(TAG, "onlineList.onVoice, selectCommand", index);
                    if (index < 0 || index >= (<OnlineAdapter> this._views.onlineList.adapter).data.length) {
                        log.W(TAG, "onlineList.onVoice negative, index out of list range");
                        e.endLocalTask();
                        return;
                    }

                    let position = this._calculatePosition(index);
                    if (position < 0 || position >= (<OnlineAdapter> this._views.onlineList.adapter).data.length) {
                        log.W(TAG, "onlineList.onVoice negative, position out of list range");
                        e.endLocalTask();
                        return;
                    }
                    this._playVideo(position);
                }
                e.endLocalTask();
            });

            cmdKeys = ["VOICECMD_ONLINE_LIST_1", "VOICECMD_ONLINE_LIST_2", "VOICECMD_ONLINE_LIST_3",
                "VOICECMD_ONLINE_LIST_4", "VOICECMD_ONLINE_LIST_5", "VOICECMD_ONLINE_LIST_6",
                "VOICECMD_ONLINE_LIST_7", "VOICECMD_ONLINE_LIST_8", "VOICECMD_ONLINE_LIST_9"];
            Utils.registerVoiceCommand(this._views.onlineList, cmdKeys, RecognitionMode.QuickWord, (cmdKey: string, index: number) => {
                log.I(TAG, "onlineList.onVoice, quickWord", cmdKey, index);
                if (index < 0 || index >= cmdKeys.length) {
                    log.W(TAG, "onlineList.onVoice negative, index out of list range");
                    return;
                }

                let position = this._calculatePosition(index);
                if (position < 0 || position >= (<OnlineAdapter> this._views.onlineList.adapter).data.length) {
                    log.W(TAG, "onlineList.onVoice negative, position out of list range");
                    return;
                }
                this._playVideo(position);
            }, true);
        }
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }
        Utils.removeVoiceCommand(this._views.usbFirst);
        Utils.removeVoiceCommand(this._views.categoryIcon);
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];
        this._views.onlineList.removeAllListeners("itemselect");
        this._views.onlineList.removeAllListeners("touchstart");
        this._views.onlineList.removeAllListeners("scroll");
        this._views.onlineList.removeAllListeners("verticalscrollend");
        this._views.onlineList.removeAllListeners("panstart");
        this._views.onlineList.removeAllListeners("panend");
        this._views.onlineList.removeAllListeners("reachend");
        if (this._views.onlineList.adapter) {
            this._views.onlineList.adapter = null;
        }

        if (this._categoryDialogContent.categoryList) {
            this._categoryDialogContent.categoryList.removeAllListeners("itemselect");
            let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter
            if (adapter) {
                adapter.registerSelectListener(null);
                adapter = null;
            }
        }

        if (this._views.loadingPage) {
            this._views.loadingPage.removeEventListener("RetryButtonReleased", this._retryBtnListener);
            this._retryBtnListener = null;
        }

        this._views = null;
        iVideoModel.off(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
        iOnlineModel.off(Consts.EV_ITEM_LIST_CHANGED, this._onlineListChanged);
        log.I(TAG, "_removeAllListeners done");
    }

    /**
     * 移除所有timer
     */
    _removeAllTimeout() {
        this._removeTimeout(this._loadingMoreTimeout);
    }

    _removeTimeout(timeout: NodeJS.Timer) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
    }

    _destroyDialog() {
        if (this._categoryDialog) {
            if (this._categoryDialogContent.retryBtn) {
                Utils.setOnTapListener(this._categoryDialogContent.retryBtn, null);
            }
            this._categoryDialog.destroy(true);
            this._categoryDialog = null;
        }

        if (this._qrCodeDialog) {
            if (this._qrCodeDialogContent.networkTip) {
                Utils.setOnTapListener(this._qrCodeDialogContent.networkTip, null);
            }
            this._qrCodeDialog.destroy(true);
            this._qrCodeDialog = null;
        }
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._removeAllTimeout();
        this._removeVoiceCommands();
        this._removeAllListeners();
        this._destroyDialog();
    }
}

export = OnlinePresenter;
