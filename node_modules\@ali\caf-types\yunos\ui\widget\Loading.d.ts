/// <reference types="node" />
import SpriteView = require("../view/SpriteView");
import Bitmap = require("../../graphics/Bitmap");
interface IStyle {
    spriteEnable: boolean;
    colorStyle: string;
    frameSizeBig: number;
    frameSizeMiddle: number;
    frameSizeLittle: number;
    frameSizeSmall: number;
    frameCount: number;
    frameDuration: number;
    frameTimeFunction: string;
    frameIterationCount: string | number;
    spriteSrc: string | Bitmap | Buffer;
    spriteFrameWidth: number;
    spriteFrameHeight: number;
    fadeEnable: boolean;
    minOpacity: number;
    maxOpacity: number;
    fadeInDuration: number;
    fadeInTimeFunction: string;
    fadeOutDuration: number;
    fadeOutTimeFunction: string;
}
/**
 * <p>Loading widget shows whether is loading or not.</p>
 * @extends yunos.ui.view.SpriteView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class Loading extends SpriteView {
    private _spriteEnable;
    private _lastImageSrc;
    private _frameSizeSmall;
    private _frameSizeLittle;
    private _frameSizeMiddle;
    private _colorStyle;
    private _sizeStyle;
    private _frameSizeBig;
    private _animationParams;
    private _frameDefaultCount;
    private _frameDefaultDuration;
    private _frameDefaultTimeFunction;
    private _frameDefaultIterationCount;
    private _spriteSrc;
    private _spriteFrameWidth;
    private _spriteFrameHeight;
    private _fadeInAnimation;
    private _fadeOutAnimation;
    private _isSkipFade;
    private _fadeEnable;
    private _minOpacity;
    private _maxOpacity;
    private _fadeInDuration;
    private _fadeInTimeFunction;
    private _fadeOutDuration;
    private _fadeOutTimeFunction;
    /**
     * <p>Constructor that create a loading widget.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this Loading widget.</p>
     * @param {bool} recursive
     * @public
     * @since 3
     *
     */
    /**
     * <p>Destructor that destroy this Loading widget.</p>
     * @method destroy
     * @public
     * @since 1
     */
    public destroy(): void;
    /**
     * <p>Set the colorStyle of your Loading widget.</p>
     * @name yunos.ui.widget.Loading#colorStyle
     * @type {yunos.ui.widget.Loading.ColorStyle}
     * @default yunos.ui.widget.Loading.ColorStyle.Green
     * @throws {TypeError} If this value is not in Loading.ColorStyle.
     * @public
     * @since 1
     */
    public colorStyle: string;
    /**
     * <p>Set the sizeStyle of your Loading widget.</p>
     * @name yunos.ui.widget.Loading#sizeStyle
     * @type {yunos.ui.widget.Loading.SizeStyle}
     * @default yunos.ui.widget.Loading.SizeStyle.Normal
     * @throws {TypeError} If this value is not in Loading.SizeStyle.
     * @public
     * @since 1
     */
    public sizeStyle: number;
    /**
     * <p>Set the custom params of animation for Loading.</p>
     * @name yunos.ui.widget.Loading#animationParams
     * @type {object}
     * @throws {TypeError} If the value is not a object.
     * @private
     */
    private animationParams: object;
    /**
     * <p>Start to play this sprite.</p>
     * @param {number} from - the starting frame number of the SpriteView.
     * @param {number} to - the end frame number of the SpriteView.
     * @throws {TypeError} If image is invalid.
     * @throws {RangeError} If frame count is larger than the number of frames that can play.
     * @throws {TypeError} If the from is not undefined and from is not a number or to is not undefined and to is not a number.
     * @throws {RangeError} If the from value is greater than the to value.
     * @public
     * @override
     * @since 6
     *
     */
    public start(from?: number, to?: number): void;
    /**
     * <p>Stop playing this sprite.</p>
     * @public
     * @override
     * @since 6
     */
    public stop(): void;
    private refresh(): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.Loading#defaultStyleName
     * @type {string}
     * @default "Loading"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Apply theme style for loading.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 3
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    private updateOpcaity(isUp: boolean): void;
    private createFadeAnimation(): void;
    private updateFadeAnimation(): void;
    private destroyFadeAnimation(): void;
    private refreshFadeAnimation(): void;
    private startFadeInAnimation(): void;
    private stopFadeInAnimation(): void;
    private startFadeOutAnimation(): void;
    private stopFadeOutAnimation(): void;
    /**
     * <p>Enum for color style of the loading widget.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly ColorStyle: {
        /**
         * The color of loading is green
         * @public
         * @since 1
         */
        Green: string;
        /**
         * The color of loading is white
         * @public
         * @since 1
         */
        White: string;
    };
    /**
     * <p>Enum for size style of the loading widget.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly SizeStyle: {
        /**
         * The sizeStyle is big
         * @public
         * @since 1
         */
        Big: int;
        /**
         * The sizeStyle is normal
         * @public
         * @since 1
         */
        Normal: int;
        /**
         * The sizeStyle is middle
         * @public
         * @since 3
         */
        Middle: int;
        /**
         * The sizeStyle is little
         * @public
         * @since 3
         */
        Little: int;
        /**
         * The sizeStyle is small
         * @public
         * @since 3
         */
        Small: int;
    };
}
export = Loading;
