import { MultiState } from "yunos/ui/util/TypeHelper";
import { StringObjectKV } from "yunos/ui/util/TypeHelper";
import Dialog = require("yunos/ui/widget/Dialog");
interface IStyle extends StringObjectKV {
    textPropertySet: string;
    textColor: string;
    textMarginTopToBg: number;
    textMarginBottom: number;
    maxTextWidth: number;
    shadowColor: string;
    shadowX: number;
    shadowY: number;
    shadowRadius: number;
    closeIconMultiState: MultiState;
    animationMarginTop: number;
    closeIconWidth: number;
    closeIconHeight: number;
    closeIconMarginTop: number;
    closeIconMarginRight: number;
}
declare class LoadingPopBM extends Dialog {
    readonly defaultStyleName: string;
    hasCloseButton: boolean;
    text: string;
    private _loading;
    private _textView;
    private _maxTextWidth;
    private _textMarginTopToBg;
    private _closeBtnView;
    private _animationMarginTop;
    private _textMarginBottom;
    private _textPropertySet;
    private _textColor;
    private _closeIconMultiState;
    private _closeIconWidth;
    private _closeIconHeight;
    private _closeIconMarginTop;
    private _closeIconMarginRight;
    private _hasCloseButton;
    constructor();
    show(x?: number, y?: number): void;
    protected applyStyle(style: IStyle): void;
    updateStyle(style: IStyle, diffStyle: IStyle): void;
    private _initText;
    private _updateHeight;
    private _initLoading;
    private _initCloseBtn;
    private _validCloseBtn;
    private _initLayout;
}
export = LoadingPopBM;
