import YObject = require("yunos/core/YObject");
declare class ShepherdServiceAdapter extends YObject {
    private _iface;
    private bus;
    private service;
    private _permission;
    constructor(permission?: string);
    registergetDeviceToken(cb: () => Promise<string>): void;
    registergetAVNCertValidity(cb: () => Promise<string>): void;
    emitCertValidityChanged(status: string): boolean;
    destroy(): void;
}
export = ShepherdServiceAdapter;
