{"search": {"type": "RelativeLayout", "params": {"id_nav": {"align": {"left": "parent", "top": "parent", "right": "parent"}, "margin": {"left": "{config.HEADER_NAV_MARGIN_LEFT}"}}, "id_search_history": {"align": {"left": "parent", "top": {"target": "id_nav", "side": "bottom"}, "right": "parent", "bottom": "parent"}, "margin": {"left": "{config.PAGE_MARGIN}", "top": "{config.<PERSON>AR<PERSON>_BANNER_MARGIN}", "right": "{config.PAGE_MARGIN}"}}, "id_result_container": {"align": {"left": "parent", "top": {"target": "id_nav", "side": "bottom"}, "right": "parent", "bottom": "parent"}, "margin": {"left": "{config.PAGE_MARGIN}", "right": "{config.PAGE_MARGIN}"}}, "id_loading_page": {"align": {"left": "parent", "top": {"target": "id_nav", "side": "bottom"}, "right": "parent", "bottom": "parent"}, "margin": {"top": "{sdp(0)}"}}, "id_empty": {"align": {"center": "parent", "top": {"target": "id_nav", "side": "bottom"}}, "margin": {"top": "{sdp(140)}"}}}}, "search_header": {"type": "RelativeLayout", "params": {"id_search_container": {"align": {"left": "parent", "top": "parent", "right": "parent", "middle": "parent"}, "margin": {"right": "{config.PAGE_MARGIN}"}}}}, "search_history": {"type": "RelativeLayout", "params": {"id_sh_title": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(8)}"}}, "id_sh_content": {"align": {"left": "parent", "right": "parent", "top": {"target": "id_sh_title", "side": "bottom"}, "bottom": "parent"}}}}, "result_container": {"type": "RelativeLayout", "params": {"id_local_container": {"align": {"left": "parent", "top": "parent", "bottom": "parent"}, "margin": {"bottom": "{sdp(60)}"}}, "id_online_container": {"align": {"top": "parent", "right": "parent", "bottom": "parent"}, "margin": {"bottom": "{sdp(60)}"}}, "id_local_scrollbar": {"align": {"left": "parent", "right": "parent", "bottom": "parent"}, "margin": {"bottom": "{sdp(24)}"}}, "id_online_scrollbar": {"align": {"right": "parent", "bottom": "parent"}, "margin": {"bottom": "{sdp(24)}"}}}}, "search_local_result": {"type": "RelativeLayout", "params": {"id_local_banner": {"align": {"left": "parent", "top": "parent"}}, "id_local_grid": {"align": {"left": "parent", "top": {"target": "id_local_banner", "side": "bottom"}, "right": "parent", "bottom": "parent"}}}}, "search_online_result": {"type": "RelativeLayout", "params": {"id_online_title": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(8)}"}}, "id_online_list": {"align": {"left": "parent", "top": {"target": "id_online_title", "side": "bottom"}, "right": "parent", "bottom": "parent"}}}}, "search_result_banner": {"type": "RelativeLayout", "params": {"0": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(8)}"}}, "1": {"align": {"right": "parent", "middle": "parent"}}}}, "search_local_more": {"type": "RelativeLayout", "params": {"id_local_more_icon": {"align": {"right": "parent", "middle": "parent"}, "margin": {"right": "{sdp(8)}"}}, "id_local_more_tips": {"align": {"right": {"target": "id_local_more_icon", "side": "left"}, "middle": "parent"}}}}, "search_result": {"type": "RelativeLayout", "params": {"id_nav": {"align": {"left": "parent", "top": "parent", "right": "parent"}, "margin": {"left": "{config.HEADER_NAV_MARGIN_LEFT}"}}, "id_search_keyword": {"align": {"left": "parent", "top": {"target": "id_nav", "side": "bottom"}, "right": "parent"}, "margin": {"left": "{config.PAGE_MARGIN}"}}, "id_local_grid": {"align": {"left": "parent", "top": {"target": "id_search_keyword", "side": "bottom"}, "right": "parent", "bottom": "parent"}, "margin": {"left": "{config.PAGE_MARGIN}", "right": "{config.PAGE_MARGIN}", "bottom": "{sdp(60)}"}}, "id_scrollbar": {"align": {"left": "parent", "top": {"target": "id_local_grid", "side": "bottom"}, "right": "parent"}, "margin": {"left": "{config.PAGE_MARGIN}", "top": "{sdp(24)}", "right": "{config.PAGE_MARGIN}"}}, "id_left_mask": {"align": {"left": "parent", "bottom": "parent"}, "margin": {"left": "{config.PAGE_MARGIN}", "bottom": "{sdp(24)}"}}, "id_right_mask": {"align": {"right": "parent", "bottom": "parent"}, "margin": {"right": "{config.PAGE_MARGIN}", "bottom": "{sdp(24)}"}}, "id_empty": {"align": {"center": "parent", "top": {"target": "id_nav", "side": "bottom"}}}, "id_error_dlg": {"align": {"top": {"target": "id_nav", "side": "bottom"}, "center": "parent"}, "margin": {"top": "{sdp(50)}"}}, "id_loading_page": {"align": {"left": "parent", "top": {"target": "id_nav", "side": "bottom"}, "right": "parent", "bottom": "parent"}, "margin": {"top": "{sdp(58)}"}}}}, "search_input": {"type": "FlexLayout", "attrs": {"flexDirection": "{enum.FlexLayout.FlexDirection.Row}", "alignItems": "{enum.FlexLayout.AlignItems.Center}", "paddingLeft": "{sdp(0)}", "paddingRight": "{sdp(32)}"}, "params": {"textField": {"flexGrow": 1, "flexShrink": 1}}}}