<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    layout="{layout.welcome}"
    propertySetName="welcome">

    <NavigationBar
        id="id_nav"
        title="{string.VIDEO_TITLE}"/>

    <ImageView
        id="id_logo"
        height="{sdp(416)}"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"/>

    <ButtonBM
        id="id_btn"
        width="{config.WELCOME_BTN_WIDTH}"
        text="{string.WELCOME_OPEN_VIDEO}"
        enabled="false"/>

    <CompositeView
        id="id_disclaimer"
        width="{config.WELCOME_DISCLAIMER_WIDTH}"
        height="{config.WELCOME_DISCLAIMER_HEIGHT}"
        layout="{layout.welcome_disclaimer}">
        <CheckBox
            id="id_disclaimer_checkbox"
            checked="false"/>
        <TextView
            id="id_disclaimer_tips"
            text="{string.WELCOME_DISCLAIMER_TIPS}"
            propertySetName="extend/hdt/FontBody2"/>
        <TextView
            id="id_disclaimer_link"
            width="{sdp(192)}"
            text="{string.WELCOME_DISCLAIMER_TITLE}"
            propertySetName="extend/hdt/FontBody2"
            multiState="{config.ITEM_MULTISTATE}"/>
    </CompositeView>
</CompositeView>
