import ImageView = require("../view/ImageView");
import View = require("../view/View");
import TouchEvent = require("../event/TouchEvent");
interface IStyle {
    borderWidth: number;
    borderColor: string;
    borderRadius: number | number[];
    sizeNormal: number;
    sizeLarge: number;
    canvasDrawablePath: string;
}
/**
 * <p>ImageButton is a clickable widget which consists of image.</p>
 * <p>There are two appearance of your ImageButton, Block and Border.</p>
 * <p>When the user click this ImageButton, it will emit an "tap" event.</p>
 * @extends yunos.ui.view.ImageView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class ImageButton extends ImageView {
    private _pressMask;
    private _pressed;
    private _styleType;
    private _defaultBorderWidth;
    private _defaultBorderColor;
    private _sizeType;
    private _defaultSizeNormal;
    private _defaultSizeLarge;
    private _defaultBorderRadius;
    private _canvasDrawablePath: string;
    /**
     * <p>Create an ImageButton</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor the destroy this ImageButton</p>
     * @override
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds style of ImageButton, there two types: Block Style and Border Style.</p>
     * @name yunos.ui.widget.ImageButton#styleType
     * @type {yunos.ui.widget.ImageButton.StyleType}
     * @throws {TypeError} If this value is not in ImageButton.StyleType.
     * @public
     * @since 1
     */
    public styleType: number;
    /**
     * <p>This property holds the size of ImageButton, there are 2 types: ImageButton.SizeType.Normal and ImageButton.SizeType.Large.</p>
     * @name yunos.ui.widget.ImageButton#sizeType
     * @type {yunos.ui.widget.ImageButton.SizeType}
     * @throws {TypeError} If this value is not in ImageButton.SizeType.
     * @public
     * @since 1
     */
    public sizeType: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.ImageButton#defaultStyleName
     * @type {string}
     * @default "ImageButton"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * Implement this to apply style.
     * @method applyStyle
     * @protected
     * @override
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    private onTouchStart(e?: TouchEvent): void;
    private onTouchEnd(e?: TouchEvent): void;
    private onPropertyChange(property: string, oldValue: Object, newValue: Object): void;
    /**
     * get the mask view in image button.
     * @name yunos.ui.widget.ImageButton#maskView
     * @type {yunos.ui.View}
     * @public
     * @since 4
     *
     */
    public readonly maskView: View;
    private initMaskView(): void;
    private removeMaskView(): void;
    private refresh(): void;
    private _validCanvasDrawbleFromStyle(): void;
    /**
     * Enum for ImageButton Style Type.
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly StyleType: {
        /**
         * <p>ImageButton.Style.Block is a borderless ImageButton and the image will fill the whole ImageButton background.</p>
         * @public
         * @since 1
         */
        Block: int;
        /**
         * <p>ImageButton.Style.Border is an ImageButton with border and the default border width is 1px and the default border color is "#CCCCCC", and the border color is which you can set.</p>
         * @public
         * @since 1
         */
        Border: int;
    };
    /**
     * Enum for ImageButton sizeType. It will set the default width and height of this ImageButton.
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly SizeType: {
        /**
         * <p>ImageButton.SizeType.Normal sets the width that is 36 dp and the height that is 36 dp of the ImageButton.</p>
         * @public
         * @since 1
         */
        Normal: int;
        /**
         * <p>ImageButton.SizeType.Large sets the width that is 60 dp and the height that is 60 dp of the ImageButton.</p>
         * @public
         * @since 1
         */
        Large: int;
    };
}
export = ImageButton;
