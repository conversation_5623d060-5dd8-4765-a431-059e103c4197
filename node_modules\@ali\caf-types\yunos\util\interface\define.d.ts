/// <reference types="node" />
import { ReadStream } from "fs";
import { PassThrough, Transform } from "stream";
import Calendar = require("../Calendar");
import Range = require("../Range");
import DateComponents = require("../DateComponents");
export interface timerTask {
    readonly id: number;
    readonly status: string;
    readonly periodic?: boolean;
    readonly count?: number;
    cancel: (removeTask?: boolean) => void;
}
export interface asyncDone {
    (...args: Object[]): void;
}
export interface asyncTask {
    (done: asyncDone): void;
}
export interface dateComponentsInput {
    era?: number;
    year?: number;
    quarter?: number;
    month?: number;
    weekday?: number;
    day?: number;
    hour?: number;
    minute?: number;
    second?: number;
    millisecond?: number;
    leapMonth?: number;
}
export declare class ZipFileClass {
    public addFile(filePath: string, pathInZip: string): void;
    public addBuffer(buffer: Buffer, pathInZip: string): void;
    public addReadStream(readStream: ReadStream, pathInZip: string): void;
    public addEmptyDirectory(pathInZip: string): void;
    public outputStream: PassThrough;
    public end(): void;
}
export declare class UnZipFileClass {
    public static open(target: string, options: {
        lazyEntries?: boolean;
    }, callback: (err: NodeJS.ErrnoException) => void): void;
    public openReadStream(entry: Object, callback: (err: NodeJS.ErrnoException, stream?: Transform) => void): void;
    public readEntry(): void;
    public on(event: string, callback: (...args: Object[]) => void): void;
}
export declare class TimeSettingsClass {
    public getTimeZone: () => string;
}
export declare class SolutionClass {
    private source: Calendar;
    private getRange(smallUnit: number, largeUnit: number, date: Date): Range;
    private getMaxRange(unit: number): Range;
    private getMinRange(unit: number): Range;
    private getOrdinality(smallUnit: number, largeUnit: number, date: Date): number;
    private getDate(components: DateComponents): Date;
    private getComponents(date: Date): DateComponents;
    private getDateByAdding(components: DateComponents, toDate: Date, direction: number): Date;
    private getDateBySetting(unit: number, value: number, ofDate: Date): Date;
    private getUnitOfDate(unit: number, date: Date): number | boolean;
    private compareDates(date1: Date, date2: Date, atUnit: number): number;
    private isDateInRange(date: Date, range: Range): boolean;
    private areDatesSameForUnits(unitCollection: number, ...dates: Date[]): boolean;
    private areDatesInSameDay(...dates: Date[]): boolean;
}
