{"_from": "file:D:/jdk1.8.0_202/types/api/hdt-ui/level-6/ivi/EP33L/banma-hdt-ui-types-2.0.85.tgz", "_id": "@banma/hdt-ui-types@2.0.85", "_inBundle": false, "_integrity": "sha512-onbtYePhsHPhs6pmhk17GOY32hFmxL/sftIVbLx0YB5MwmVgXUOZynlZp9rV+1+BdnAo16mydvUI9WjF9AotLA==", "_location": "/@banma/hdt-ui-types", "_phantomChildren": {}, "_requested": {"type": "file", "where": "d:\\codes\\Godspeed\\Date\\2025-07-29\\newww", "raw": "D:\\jdk1.8.0_202\\types\\api\\hdt-ui\\level-6\\ivi\\EP33L\\banma-hdt-ui-types-2.0.85.tgz", "rawSpec": "D:\\jdk1.8.0_202\\types\\api\\hdt-ui\\level-6\\ivi\\EP33L\\banma-hdt-ui-types-2.0.85.tgz", "saveSpec": "file:D:/jdk1.8.0_202/types/api/hdt-ui/level-6/ivi/EP33L/banma-hdt-ui-types-2.0.85.tgz", "fetchSpec": "D:/jdk1.8.0_202/types/api/hdt-ui/level-6/ivi/EP33L/banma-hdt-ui-types-2.0.85.tgz"}, "_requiredBy": ["#USER"], "_resolved": "D:/jdk1.8.0_202/types/api/hdt-ui/level-6/ivi/EP33L/banma-hdt-ui-types-2.0.85.tgz", "_shasum": "87ae3e0312b9c9fc105ebb04fffc85f4182a6fd0", "_spec": "D:\\jdk1.8.0_202\\types\\api\\hdt-ui\\level-6\\ivi\\EP33L\\banma-hdt-ui-types-2.0.85.tgz", "_where": "d:\\codes\\Godspeed\\Date\\2025-07-29\\newww", "author": {"name": "HMI Team"}, "bundleDependencies": false, "deprecated": false, "description": "TypeScript definitions for caf ui extension", "license": "Apache-2.0", "main": "", "name": "@banma/hdt-ui-types", "publishConfig": {"registry": "http://************:8081/repository/npm-banma/"}, "scripts": {}, "version": "2.0.85"}