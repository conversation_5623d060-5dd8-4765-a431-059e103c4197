/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

import AudioManager = require("yunos/device/AudioManager");
import log = require("../utils/log");
import Consts = require("../Consts");
const TAG = "AudioSession";

class AudioSession {
    private static instance: AudioSession;
    private _isMute: boolean;
    private _audioMuteListener: (arg0: boolean) => void;
    private _onAudioSessionChange: (arg0: number, arg1: string) => void;
    private _audioSessionListener: (arg0: string, arg1: boolean) => void;

    constructor() {
        this._isMute = false;
        let audioManager = AudioManager.getInstance();
        if (audioManager) {
            this._isMute = false;
            audioManager.on(AudioManager.VolumeListenerEvent.STREAM_MUTE_UPDATE, this.onAudioMuteUpdate.bind(this));
            audioManager.on(AudioManager.RingerModeChangedEvent.RINGER_MODE_CHANGED, this.onRingerModeChange.bind(this));
        }
    }

    static getInstance() {
        if (!this.instance) {
            this.instance = new AudioSession();
        }
        return this.instance;
    }

    /**
     * 监听通过“静音”或“取消静音”的按键消息
     */
    onRingerModeChange(newMode: string | number, flags: string) {
        log.I(TAG, "onRingerModeChange", newMode, flags);
        let isMute = newMode !== AudioManager.RingerMode.RINGER_MODE_NORMAL;
        if (this._isMute === isMute) {
            log.I(TAG, "onRingerModeChange, ignore");
            return;
        }

        this._isMute = isMute;
        if (this._audioMuteListener) {
            this._audioMuteListener(isMute);
        }
    }

    /**
     * 监听通过“旋钮”调节音量消息
     */
    onAudioMuteUpdate(streamType: string, isMute: boolean, flags: string) {
        log.I(TAG, "onAudioMuteUpdate", streamType, isMute, flags);
        if (this._isMute === isMute) {
            log.I(TAG, "onAudioMuteUpdate, ignore");
            return;
        }

        this._isMute = isMute;
        if (this._audioMuteListener) {
            this._audioMuteListener(isMute);
        }
    }

    /**
     * 设置为正常模式，即非“静音”
     */
    setRingerModeNormal() {
    }

    /**
     * 检查是否为正常模式，即非“静音”
     */
    isRingerModeNormal() {
        return false;
    }

    /**
     * 申请音频焦点
     */
    requestAudioSession() {
        this._initAudioSessionCallback();
        let audioManager = AudioManager.getInstance();
        if (audioManager) {
            let ret = audioManager.requestAudioSession(AudioManager.StreamType.AUDIO_STREAM_MUSIC,
                AudioManager.AudioSessionType.AUDIOSESSION_REQUEST_STOP_OTHERS,
                Consts.VIDEO_PACKAGE_NAME, this._onAudioSessionChange);
            log.I(TAG, "requestAudioSession", ret);
            return ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED;
        }
        return false;
    }

    /**
     * 初始化音频焦点变化的回调函数
     */
    _initAudioSessionCallback() {
        if (this._onAudioSessionChange) {
            return;
        }

        this._onAudioSessionChange = (changeType: number, newClientName: string) => {
            log.I(TAG, "_onAudioSessionChange", changeType, newClientName);
            if (changeType === AudioManager.AudioSessionType.AUDIOSESSION_CHANGE_LOWERED_BY_OTHER) {
                return;
            }

            if (this._audioSessionListener) {
                let gainedSession = changeType === AudioManager.AudioSessionType.AUDIOSESSION_REQUEST_STOP_OTHERS;
                this._audioSessionListener(newClientName, gainedSession);
            }
        };
    }

    /**
     * 查询当前占用音频焦点的应用名称
     */
    getTopSessionClientName() {
        return "";
    }

    /**
     * 检查视频是否获得音频焦点
     */
    gainedAudioSession() {
        let clientName = this.getTopSessionClientName();
        log.I(TAG, "gainedAudioSession", clientName);
        return clientName === Consts.VIDEO_PACKAGE_NAME;
    }

    /**
     * 释放占用的音频焦点
     */
    abandonAudioSession() {
        let audioManager = AudioManager.getInstance();
        if (audioManager) {
            this._initAudioSessionCallback();
            audioManager.abandonAudioSession(Consts.VIDEO_PACKAGE_NAME, this._onAudioSessionChange);
        }
    }

    /**
     * 检查电话是否获得音频焦点
     */
    isPhoneGainedAudioSession(clientName: string) {
        if (!clientName) {
            clientName = this.getTopSessionClientName();
        }
        return clientName === "telecom"
            || clientName === "calldisplayer.yunos.com"
            || clientName === "/com/saicmotor/service/BtAgentService/Phone"
            || clientName === "/com/saicmotor/service/BtAgentService/Ringtone";
    }

    /**
     * 检查小云是否获得音频焦点
     */
    isXiaoYunGainedAudioSession(clientName: string) {
        if (!clientName) {
            clientName = this.getTopSessionClientName();
        }
        return clientName === "xiaoyun" || clientName === "xiaoyun.alios.cn";
    }

    /**
     * 检查其他多媒体是否获得音频焦点
     */
    isMediaGainedAudioSession() {
        let clientName = this.getTopSessionClientName();
        return clientName === "/cn/alios/mafservice/music"
            || clientName === "/cn/alios/mafservice/abook"
            || clientName === "/cn/alios/mafservice/data/onlineradio"
            || clientName === "/cn/alios/tunerd/tuner";
    }

    /**
     * 检查车信是否获得音频焦点
     */
    isChatGainedAudioSession() {
        let clientName = this.getTopSessionClientName();
        return clientName === "vehicle_chat_VOIP";
    }

    registerAudioSessionChangeListener(listener: (arg0: Object) => void) {
        this._audioSessionListener = listener;
    }

    unRegisterAudioSessionChangeListener() {
        this._audioSessionListener = null;
    }

    registerAudioMuteChangeListener(listener: (arg0: boolean) => void) {
        this._audioMuteListener = listener;
    }

    unRegisterAudioMuteChangeListener() {
        this._audioMuteListener = null;
    }
}

export = AudioSession;
