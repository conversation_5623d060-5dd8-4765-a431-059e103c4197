import YObject = require("../core/YObject");
/**
 * A graphics tools class to calculate coordinate change in 3D.
 * @private
 */
declare class Matrix3D extends YObject {
    /**
     * <p>Constructor that create a matrix3D.</p>
     * @private
     */
    /**
     * <p>Constructor that create a matrix3D.</p>
     * @param {yunos.graphics.Matrix3D} matrix - instanceof for yunos.graphics.Matrix3D, assign value to this new instance
     * @private
     */
    private constructor(matrix?: Matrix3D);
    private assign(matrix: Matrix3D): this;
    private vectorMul(vector: number[]): number[];
    private invVectorMul(vector: number[]): number[];
    private mul(matrix: Matrix3D): this;
    private rotateZ(rad: number): this;
    private rotateY(rad: number): this;
    private rotateX(rad: number): this;
    private scale(sx: number, sy: number, sz: number): this;
    private translate(tx: number, ty: number, tz: number): this;
    private identity(): this;
    private at(x: number, y: number, z: number): Object;
    private inverse(): Matrix3D;
    private toArray(): number[];
}
export = Matrix3D;
