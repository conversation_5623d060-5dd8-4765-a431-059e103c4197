/// <reference types="node" />
import YObject = require("../core/YObject");
/**
 * <p>Unzip Util.</p>
 * @example
 * let archive = new UnZip("archive.zip");
 * // extract all file
 * archive.extract("temp", function(err) {
 *     if (!err) {
 *         console.log("done");
 *     }
 * });
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 3
 */
declare class UnZip extends YObject {
    private _target;
    /**
     * <p>Create a zip.</p>
     * <p>If the file already exists, it will be replaced when writing.</p>
     * @param  {string} target - Path for the target zip.
     * @throws {TypeError} If target is not a string.
     * @public
     * @since 3
     */
    public constructor(target: string);
    /**
     * <p>Extract all content from the zip.</p>
     * @param  {string}   targetPath - Path for extracting to.
     * @param  {yunos.util.UnZip~extractRelatedCallback} callback - Callback.
     * @public
     * @since 3
     */
    public extract(targetPath: string, callback?: (err: Object, ...object: Object[]) => void): void;
    private extractEntry;
    /**
     * <p>Get all entries' info from zip.</p>
     * @example
     * let archive = new UnZip("archive.zip");
     * // result example
     * [
     *     {
     *         compressionMethod: 8,
     *         lastModFileTime: 21842,
     *         lastModFileDate: 19284,
     *         compressedSize: 7,
     *         uncompressedSize: 5,
     *         fileName: 'hello.txt'
     *     },
     *     // ...
     * ]
     * archive.getEntries((err, result) => {
     *     if (!err) {
     *         console.log(result);
     *     }
     * });
     * @param  {yunos.util.UnZip~getEntriesCallback} callback - Callback
     * @public
     * @since 3
     */
    public getEntries(callback: (err: NodeJS.ErrnoException, result: Object[]) => void): void;
}
export = UnZip;
