import View = require("./View");
import CompositeView = require("./CompositeView");
import NavigationBar = require("./NavigationBar");
/**
 * <p>Represents a standard navigation menu for application. <br>
 * The menu contains left item, right item, title and sub title.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.view
 * @public
 * @since 2
 */
declare class NavigationView extends CompositeView {
    private _navigationViewContainer;
    private _navigationBar;
    private _leftItemClicked;
    private _needRebackAnimation;
    private _titleItem;
    private _preTitleItem;
    private _windowWidth;
    private _windowHeight;
    private _showBackkeyOnRootView;
    /**
     * <p>Constructor of NavigationView.</p>
     * @public
     * @since 2
     */
    public constructor(...args: Object[]);
    /**
     * <p>Destructor that destroy this NavigationView.</p>
     * @param {boolean} recursive - destroy the children in the NavigationView if the value is true.
     * @public
     * @override
     * @since 2
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>The array of all children in this view.</p>
     * @name yunos.ui.view.NavigationView#children
     * @type {yunos.ui.view.View[]}
     * @readonly
     * @override
     * @public
     * @since 2
     */
    public readonly children: View[];
    /**
     * Title bar of Navigation view.
     * @name yunos.ui.view.NavigationView#NavigationBar
     * @type {yunos.ui.view.NavigationView.NavigationBar}
     * @readonly
     * @private
     */
    private readonly navigationBar: NavigationBar;
    /**
     * Defines whether the navigationView fits system windows such as statusbar window, which means the navigationBar of the navigationView will leave spaces for the status bar.
     * @name yunos.ui.view.NavigationView#fitSystemWindows
     * @type {boolean}
     * @default true
     * @throws {TypeError} If type of parameter is not boolean.
     * @override
     * @public
     * @since 2
     */
    public fitSystemWindows: boolean;
    /**
     * <p>Retrieve the topmost NavigationItem, null is returned when there is no navigationItem</p>
     * @return {yunos.ui.view.NavigationView.NavigationItem} navigationItem - The NavigationItem at the top.
     * @public
     * @since 2
     */
    public peekChild(): View;
    /**
     * <p>Push a navigationItem to this navigationView, the previous navigationItem at top is invisible until this new pushed navigationItem is poped from this navigationView.</p>
     * @param {yunos.ui.view.NavigationView.NavigationItem}
     * @param {boolean} needAnimation - Whether do animation when push a new navigation item, the default value is true.
     * @throws {TypeError} If the first param is not NavigationView.NavigationItem.
     * @throws {TypeError} If the param is not boolean.
     * @public
     * @since 2
     */
    public pushChild(navigationItem: NavigationView.NavigationItem, needAnimation?: boolean): void;
    /**
     * <p>Pop the top navigationItem from this navigationView.</p>
     * @param {boolean} needAnimation - Whether do animation when pop the top navigation item, the default value is true.
     * @fires yunos.ui.view.NavigationView.NavigationItem#pushChild
     * @throws {TypeError} If the param is not boolean.
     * @public
     * @since 2
     */
    public popChild(needAnimation?: boolean): NavigationView.NavigationItem;
    /**
     * <p>Pop navigation items until the specified navigation item is at top.</p>
     * @param {yunos.ui.view.NavigationView.NavigationItem} navigationItem - the navigationItem that you want to be at the top of the navigationView.
     * @return {?yunos.ui.view.NavigationView.NavigationItem[]} An array containing the views that were popped from the stack.
     * @throws {TypeError} If the param is not NavigationView.NavigationItem.
     * @public
     * @since 2
     */
    public popToChild(navigationItem: NavigationView.NavigationItem): View[];
    /**
     * <p>Pop all the navigation items except the root one.</p>
     * @return {?yunos.ui.view.NavigationView.NavigationItem[]} An array of navigation items that were popped from this navigationView.
     * @throws {Error} If current children is empty.
     * @public
     * @since 2
     */
    public popToRootChild(): View[];
    /**
     * @private
     */
    private showBackkeyOnRootView: boolean;
    private onStackPanMove(dx: number): void;
    private onStackPanCancel(): void;
    /**
     * @private
     */
    private needRebackAnimation: boolean;
    private onTapRebackButton(): void;
    private onTapRightButton(): void;
    private onNavigationViewContainerPopChild(popedChild: NavigationView.NavigationItem): void;
    private onNavigationViewContainerPushChild(topChild: NavigationView.NavigationItem): void;
    public static readonly NavigationBar: typeof NavigationBar;
}
declare namespace NavigationView {
    /**
     * <p>NavigationItem is an item which is used for NavigationView.</p>
     * @extends yunos.ui.view.CompositeView
     * @memberof yunos.ui.view.NavigationView
     * @public
     * @since 2
     */
    class NavigationItem extends CompositeView {
        private _title;
        private _subTitle;
        private _titleItem;
        private _leftItem;
        private _rightItem;
        public propertyChangeFunc: (...args: Object[]) => void;
        /**
         * <p>Destructor that destroy this NavigationItem.</p>
         * @param {boolean} recursive - destroy the children in the NavigationItem if the value is true.
         * @public
         * @override
         * @since 2
         */
        public destroy(recursive?: boolean): void;
        /**
         * <p>This property holds text of the title of the navigation bar.</p>
         * @name yunos.ui.view.NavigationView.NavigationItem#title
         * @type {string}
         * @throws {TypeError} If this value is not a string.
         * @public
         * @since 2
         */
        public title: string;
        /**
         * <p>This property holds text of the subTitle of the navigation bar.</p>
         * @name yunos.ui.view.NavigationView.NavigationItem#subTitle
         * @type {string}
         * @throws {TypeError} If this value is not a string.
         * @public
         * @since 2
         */
        public subTitle: string;
        /**
         * <p>This property holds leftItem of the navigation bar, the user can set custom/system widget to replace the default left item.</p>
         * @name yunos.ui.view.NavigationView.NavigationItem#leftItem
         * @type {yunos.ui.view.View}
         * @public
         * @since 2
         */
        public leftItem: View;
        /**
         * <p>This property holds rightItem of the navigation bar, the user can set custom/system widget to replace the default right item.</p>
         * @name yunos.ui.view.NavigationView.NavigationItem#rightItem
         * @type {yunos.ui.view.View}
         * @public
         * @since 2
         */
        public rightItem: View;
        /**
         * <p>This property holds titleItem of the navigation bar, the user can set custom/system widget to replace the default title item.</p>
         * @name yunos.ui.view.NavigationView.NavigationItem#titleItem
         * @type {yunos.ui.view.View}
         * @public
         * @since 2
         */
        public titleItem: View;
    }
}
export = NavigationView;
