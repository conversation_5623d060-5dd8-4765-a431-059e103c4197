import YObject = require("../core/YObject");
import { timerTask as task } from "./interface/define";
/**
 * <p>Timer is a common utility which wraps the setTimeout, setInterval and setImmediate functions and provides a same method to cancel those timers.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 3
 *
 */
declare class Timers extends YObject {
    private _beforeTaskRunning;
    private _tasks;
    /**
     * An array contains all scheduled tasks created by this timer.
     * @name yunos.util.Timers#tasks
     * @type {yunos.util.Timers.TimerTasks}
     * @readonly
     * @public
     * @since 3
     */
    public readonly tasks: task[];
    /**
     * @typedef yunos.util.Timers.TimerTasks
     * @type {Array<yunos.util.Timers.laterReturnObj,yunos.util.Timers.soonReturnObj>}
     */
    /**
     * A optional function which will be run before executing every task created by this timer the task won't be executed if this function returns false.
     * @name yunos.util.Timers#beforeTaskRunning
     * @example
     * let timer = new Timer();
     *
     * // in this timer, we will handle page instance
     * // for the safety, we should check the page's status before execute the task
     * timer.beforeTaskRunning = function() {
     *     return page.stopped !== true;
     * }
     *
     * let t = timer.later(function() {
     *     page.sendLink(...);
     * }, 100);
     *
     * // t will not happen
     * page.stopPage();
     *
     * @type {function}
     * @throws {TypeError} If the value is not a function.
     * @public
     * @since 3
     */
    public beforeTaskRunning: () => boolean;
    /**
     * <p>Execute the 'task' with additional parameters once 'delay' milliseconds expires. Will repeatedly runs the 'task' when periodic is true.</p>
     * @example
     * let t = new Timer();
     *
     * // create a task which will be executed once after 100ms
     * // we will cancel the it
     * let task = t.later(function() {
     *     // ...
     * }, 100);
     * console.log(task.id); // a numeric, non-zero value
     * console.log(task.periodic); // false
     * console.log(task.status); // "pending"
     * task.cancel();
     * console.log(task.status); // "cancelled"
     *
     * // create another task which runs every 100ms
     * let anotherTask = t.later(function() {
     *     // ...
     * }, 100, true);
     * console.log(anotherTask.periodic); // true
     * console.log(anotherTask.count); // 0
     * @param  {Function} task - The function to be executed.
     * @param  {number}   delay - The time in milliseconds format which the timer should wait before or between the executions(s) of 'task'.
     * @param  {any[]}   [parameters] - Additional parameters which will be passed through to the 'task'.
     * @param  {boolean}   [periodic] - Whether execute the 'task' periodically or not.
     * @return {yunos.util.Timers.laterReturnObj} A normal object contains timer id, execute status, periodic setting and a cancel method. If 'periodic' is true, the object will also contain a execution counter.
     * @throws {TypeError} If 'delay' is not a number.
     * @throws {TypeError} If 'task' is not a function.
     * @public
     * @since 3
     */
    public later(task: (...args: Object[]) => void, delay: number, parameters?: Object[], periodic?: boolean): task;
    private removeTask;
    /**
     * @typedef yunos.util.Timers.laterReturnObj
     * @type {object}
     * @property {number} id - Timer id.
     * @property {function} cancel - Call this method to cancel timer.
     * @property {string} status - Timer status.
     * @property {boolean} periodic - Periodic execution timer or not.
     * @property {number} count - Task execution count.
     * @public
     * @since 3
     */
    /**
     * <p>Execute the 'task' with additional parameters asap.</p>
     * @param  {Function} task - The function to be executed.
     * @example
     * let t = new Timer();
     * // create a task which will be executed asap
     * let task = t.soon(function() {
     *     // ...
     * });
     * // cancel the task by calling cancel method
     * task.cancel();
     * @param  {any[]}   parameters - Additional parameters which will be passed through to the 'task'.
     * @return {yunos.util.Timers.soonReturnObj} A normal object contains timer id, execute status and a cancel method
     * @throws {TypeError} If <code>task</code> is not a function.
     * @public
     * @since 3
     */
    public soon(task: (...args: Object[]) => void, parameters?: Object[]): task;
    /**
     * @typedef yunos.util.Timers.soonReturnObj
     * @type {object}
     * @property {number} id - Timer id.
     * @property {function} cancel - Call this method to cancel timer.
     * @property {string} status - Timer status.
     * @public
     * @since 3
     */
    /**
     * <p>Cancel all scheduled tasks' execution.</p>
     * @public
     * @since 3
     *
     */
    public cancelAll(): void;
    /**
     * <p>Destroy this instance and all scheduled tasks would be cancelled.</p>
     * @public
     * @since 3
     */
    public destroy(): void;
}
export = Timers;
