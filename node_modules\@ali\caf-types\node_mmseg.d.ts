declare module "node_mmseg" {
    /**
     * Dictionary helper class to extends mmseg words
     * for now this is an experimental API, and only support leveldb key-value database
     */
    export class Dictionary {
        /**
         *
         * @param path leveldb database file path
         */
        constructor(path: string);

        /**
         * query if this word is in this dictionary
         * @param word
         */
        isWord(word: string): boolean;
    }

    /**
     * MMSEG interface
     */
    export class MMSeg {
        /**
         *
         * @param type implement type of segment alogrithm
         */
        constructor(type: "poi" | "default" | "contact")

        /**
         * run segment alogrithm to split strings
         * @param str string tobe split
         */
        segment(str: string): Array<string>;

        addDictionary(dictionary: Dictionary): void;
    }

    /**
     * helper function to trim MMSEG result, the policy is :
     *  merge consecutively specified length string into one
     * @param strs origin MMSEG split result
     * @param length sub string length tobe meged, default is 1
     */
    export function TrimResult(strs: Array<string>, length?: number) : Array<string>;

}