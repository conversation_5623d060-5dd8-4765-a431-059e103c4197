import Page = require("yunos/page/Page");
import EventEmitter = require("../core/EventEmitter");
import VoiceEvent = require("./event/VoiceEvent");
import View = require("yunos/ui/view/View");
import Window = require("yunos/ui/view/Window");
import ModalLayer = require("yunos/ui/widget/ModalLayer");
import VoiceReply = require("yunos/speech/VoiceReply");
interface ICustomCommand {
    action: string;
    action_param: string;
    display_text: string;
    query_word: string;
    spoken_text: string;
    fireMode: string;
    requestState: number;
}
interface IQuickWordStrcut {
    command: string;
    action: ICustomCommand;
    keepEffective: boolean;
    fullTimeActive: boolean;
    displayId: number;
}
interface IHotWordStruct {
    cmd: string;
    event: number;
    match: string;
    requestState: number;
    category: string;
    sceneId: string;
    displayText: string;
    timestamp?: number;
    displayId: number;
}
interface ISystemCmdStruct {
    cmd: string;
    requestState: number;
    displayId: number;
}
interface IAutoWidgetStruct {
    requestState: number;
    timestamp?: number;
    displayId: number;
    viewType: string;
    title: string[];
    subTitle?: string;
}
interface IWindowCommandCache {
    isDialog: boolean;
    customCommandCache: Map<string, IHotWordStruct>;
    quickWordCommandCache: Map<string, IQuickWordStrcut>;
    systemCommandCache: Map<string, ISystemCmdStruct>;
    autoWidgetCommandCache: Map<View, IAutoWidgetStruct>;
    voiceViewArray: View[];
    needSetCurrentActiveWindow: boolean;
}
interface VoicePoint {
    target: View;
    deepNum: number;
}
declare class VoiceManager extends EventEmitter {
    private _stopCallback;
    private _voiceAgentShowCallback;
    private _voiceAgentHideCallback;
    private _needsUpdateAll;
    private _currentCommendArray;
    private _currentScrollableViews;
    private _dirtyWindowArray;
    private _screenWindowMap;
    private _context;
    private _currentIndexArray;
    private _timer;
    private _pause;
    private _dataAgent;
    private _voiceModality;
    private _interactionManager;
    private _selectIndexAutoHideTimer;
    private _selectIndexAutoHidden;
    private _selectIndexInteractionInterval;
    private _selectIndexShowDuration;
    private _lastRefreshSelectTime;
    private _wakeUpTime;
    private _voiceHitView;
    private _voiceHitAnimationGroup;
    private _animCompletedHandler;
    private _preSpokenText;
    private _dialogLastCommandsMap;
    private _symbols;
    private _disableVgui;
    private static getInstance(context: Page): VoiceManager;
    private static releaseInstance(context: Page): void;
    public constructor(context: Page);
    private _initSelectIndexDeps(): void;
    private setWakeupEnabled(value: boolean): void;
    private setQuickWordsEnabled(value: boolean): void;
    private clearTimer(): void;
    private resetQuickWord(): void;
    private makeDirty(win?: Window | ModalLayer): void;
    private pause(): void;
    private resume(): void;
    private registerWindow(win: Window | ModalLayer): void;
    private updateWindowMap(win: Window | ModalLayer): void;
    private unRegisterWindow(win: Window | ModalLayer): void;
    private updateRegisterAfterMove(win: Window): void;
    private handleVoiceCommandChanged(): void;
    private clearVoiceCommandCache(): void;
    private registerVoiceCommands(updateHotWords: boolean, updateQuickWords: boolean, updateSystemCommands: boolean, updateAutoWidgetCommands: boolean): void;
    private updateDirtyWindows(): {
        updateHotWords: boolean;
        updateQuickWords: boolean;
        updateSystemCommands: boolean;
        updateAutoWidgetCommands: boolean;
    };
    private updateWindowVoiceCommands(win: Window | ModalLayer): {
        updateHotWords: boolean;
        updateQuickWords: boolean;
        updateSystemCommands: boolean;
        updateAutoWidgetCommands: boolean;
    };
    private getValueByCommandState;
    private findViewByCommonID;
    private handlingSpecialCharacters;
    private hideSelectIndex(clear?: boolean): void;
    private canShowSelectIndex(): boolean;
    private updateVoiceViewArray(win: Window | ModalLayer): VoicePoint[];
    private calculatePointArray(win: Window | ModalLayer, checkScroll: boolean): VoicePoint[];
    private setSelectIndex(win: Window | ModalLayer, forceShow?: boolean): void;
    private hideSelectNumber;
    private hasScrollViewNumber;
    private hasPointArray;
    private startHideNumberTimer;
    private _needsUpdateSelectIndex(pointArray: VoicePoint[]): boolean;
    private fillPointArrayToRoot;
    private fillPointArray;
    private hiddenCheck(view: View, rootView: View): int;
    private doEmitVoiceEvent(target: View, voiceEvent: VoiceEvent, voiceReply: VoiceReply): void;
    private selectViewByVoice(options: {
        viewType?: string;
        subTitle?: string;
        item?: string;
        intent?: string;
        actionMode: string;
        querywords?: string;
        result?: number;
        voiceZone?: number;
        direction: string;
        position: string;
    }, guiModality: Object): View;
    private findVoiceTargetInScreen;
    private findVoiceTarget(win: Window | ModalLayer, options: {
        viewType?: string;
        subTitle?: string;
        item?: string;
        intent?: string;
        actionMode: string;
        querywords?: string;
        result?: number;
        voiceZone?: number;
        direction: string;
        position: string;
    }, guiModality: Object): View;
    private initHitVoiceView;
    private doVoiceFeedback;
    private getNlgSpokenText;
    private findHitChildInTabbar;
    private doVoiceAnimation;
    private getTranslation;
    private getInteractiveFocusedWindow(windowMap: Map<(Window | ModalLayer), IWindowCommandCache>): Window | ModalLayer;
    private getDisplayId(win: Window | ModalLayer): number;
    private static instanceMap: Map<Object, VoiceManager>;
}
export = VoiceManager;
