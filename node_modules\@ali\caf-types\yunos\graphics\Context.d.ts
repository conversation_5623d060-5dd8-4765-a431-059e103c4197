import YObject = require("../core/YObject");
import LinearGradient = require("./LinearGradient");
import RadialGradient = require("./RadialGradient");
import ConicalGradient = require("./ConicalGradient");
import ImageData = require("./ImageData");
import Gradient = require("./Gradient");
import Bitmap = require("./Bitmap");
import { CanvasContext2D, ContextPattern } from "./CanvasContext2D";
import ContextNode = require("yunos/ui/rendering/agil2/ContextNode");
/**
 * <p>The 2D render context of canvas.</p>
 *
 * @example
 * context.globalAlpha = (number);
 * context.globalCompositeOperation = ("source-over|source-out|source-in|source-atop|destination-atop|destination-in|destination-out|destination-over|lighter|copy|xor")
 * context.shadowBlur = (number);
 * context.shadowColor = (color);
 * context.shadowOffsetX = (number);
 * context.shadowOffsetY = (number);
 * context.strokeStyle = (color|gradient);
 * context.lineCap = ("butt"|"round"|"square");
 * context.lineDashOffset = (number);
 * context.setLineDash(number[]);
 * context.lineJoin = ("bevel"|"round"|"miter");
 * context.lineWidth = (number);
 * context.miterLimit = (number);
 * context.fillStyle = (color|gradient);
 * context.rotate = (angle);
 * context.textBaseline = ("top"|"middle"|"bottom"|"hanging"|"alphabetic"|"ideographic");
 * context.font = "italic normal bold 12px sans-serif";
 * context.strokeRect(x, y, width, height);
 * var gradient = context.createLinearGradient(x0, y0, x1, y1);
 * // var gradient = context.createRadialGradient(150, 150, 150, 150, 150, 0);
 * // var gradient = context.createConicalGradient(150, 100);
 * gradient.addColorStop(position, color);
 * var pattern = context.createPattern(image, "repeat");
 * context.fillStyle = pattern;
 * context.fillRect(x, y, width, height);
 * context.rect(x, y, width, height);
 * context.clearRect(x, y, width, height);
 * context.fill();
 * context.stroke();
 * context.beginPath();
 * context.moveTo(x, y);
 * context.closePath();
 * context.lineTo(x, y);
 * context.clip();
 * context.quadraticCurveTo(cpx, cpy, x, y);
 * context.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);
 * context.arc(x, y, r, sAngle, eAngle, bCounterclockwise);
 * context.arcTo(x1, y1, x2, y2, r);
 * context.scale(scaleWidth, scaleHeight);
 * context.translate(x, y);
 * context.transform(a, b, c, d, e, f);
 * context.setTransform(a, b, c, d, e, f);
 * context.fillText(string, x, y);
 * context.strokeText(string, x, y);
 * context.measureTextWidth(string);
 * context.drawImage(image, dx, dy, sx, sy);
 * var imageData = context.createImageData(width, height);
 * imageData = context.getImageData(x, y, width, height);
 * context.putImageData(imageData, x, y, dirtyX, dirtyY, dirtyWidth, dirtyHeight);
 * context.roundedRect(x, y, width, height, radius);
 * context.roundedRect(x, y, width, height, radiusX, radiusY);
 * context.save();
 * context.beginPath();
 * context.ellipse(100, 100, 150, 75);
 * context.fill();
 * context.restore();
 * context.isPointInPath(x, y);
 * context.isPointInPath(x, y, "evenodd");
 * context.isPointInStroke(x, y);
 *
 * <p>When drawing the line allows the use of floating point number as input,
 * the system will be carried out interpolation operation for smooth display,
 * such as the user does not need to be interpolated, please use the integer as a coordinate input.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 1
 */
declare class Context extends YObject implements CanvasContext2D {
    private _node: ContextNode;
    private _state;
    private static states;
    /**
     * <p>Create a Canvas 2D context.</p>
     * @private
     */
    private constructor(context: Object);
    /**
     * <p>The stroke color for the context.</p>
     * @name yunos.graphics.Context#strokeStyle
     * @type {string}
     * @throws {TypeError} If value is not a color value;
     * @public
     * @since 1
     */
    /**
     * <p>The stroke style for the context.</p>
     * @name yunos.graphics.Context#strokeStyle
     * @type {string|yunos.graphics.Gradient|yunos.graphics.Context.Pattern}
     * @throws {TypeError} If value is not a color value;
     * @public
     * @since 3
     *
     */
    public strokeStyle: string | number | Gradient | Context.Pattern;
    /**
     * <p>The strock color for the context.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 1
     */
    public strokeRect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>Create a linear gradient.</p>
     * @param {number} x0 - The x axis of the coordinate of the start point.
     * @param {number} y0 - The y axis of the coordinate of the start point.
     * @param {number} x1 - The x axis of the coordinate of the end point.
     * @param {number} y1 - The y axis of the coordinate of the end point.
     * @return {yunos.graphics.LinearGradient} the LinearGradient object
     * @public
     * @since 1
     */
    public createLinearGradient(x0: number, y0: number, x1: number, y1: number): LinearGradient;
    /**
     * <p>Create a radial gradient.</p>
     * @param {number} x0 - The x axis of the coordinate of the start circle.
     * @param {number} y0 - The y axis of the coordinate of the start circle.
     * @param {number} r0 - The radius of the start circle.
     * @param {number} x1 - The x axis of the coordinate of the end circle.
     * @param {number} y1 - The y axis of the coordinate of the end circle.
     * @param {number} r1 - The radius of the end circle.
     * @return {yunos.graphics.RadialGradient} the RadialGradient object
     * @public
     * @since 3
     *
     */
    public createRadialGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number): RadialGradient;
    /**
     * <p>Create a pattern which repeats the source in the directions specified by the repetition argument.</p>
     * @param {string|yunos.graphics.ImageData|yunos.graphics.Bitmap} image - A Bitmap or ImageData to be used as the image to repeat.
     * @param {string} repetition - How to repeat the image.
     * @return {yunos.graphics.Context.Pattern} Return the Pattern object
     * @public
     * @since 3
     *
     */
    public createPattern(image: string | ImageData | Bitmap, repetition?: "repeat" | "repeat-x" | "repeat-y" | "no-repeat" | "" | null): Context.Pattern;
    /**
     * <p>Create a conical gradient.</p>
     * @param {number} x - The x axis of the coordinate of the point.
     * @param {number} y - The y axis of the coordinate of the point.
     * @param {number} [angle] - The angle of gradient, the default value is 0.
     * @return {yunos.graphics.ConicalGradient} the ConicalGradient object
     * @public
     * @since 3
     *
     */
    public createConicalGradient(x: number, y: number, angle?: number): ConicalGradient;
    /**
     * <p>Line end point style. There are three types: butt|round|square</p>
     * @name yunos.graphics.Context#lineCap
     * @type {string}
     * @public
     * @since 1
     */
    public lineCap: string;
    /**
     * <p>Sets the line dash pattern offset.</p>
     * @name yunos.graphics.Context#lineDashOffset
     * @type {number}
     * @public
     * @since 3
     *
     */
    public lineDashOffset: number;
    /**
     * <p>Sets or returns the type of the created edge angle, when the two lines meet. There are three types:
     * bevel|round|miter </p>
     * @name yunos.graphics.Context#lineJoin
     * @type {string}
     * @public
     * @since 1
     */
    public lineJoin: string;
    /**
     * <p> Current line width. </p>
     * @name  yunos.graphics.Context#lineWidth
     * @type {number}
     * @public
     * @since 1
     */
    public lineWidth: number;
    /**
     * <p>The maximum miter length.</p>
     * @name  yunos.graphics.Context#miterLimit
     * @type {number}
     * @public
     * @since 1
     */
    public miterLimit: number;
    /**
     * <p>The fill color for the context.</p>
     * @name yunos.graphics.Context#fillStyle
     * @type {string}
     * @throws {TypeError} If value is not a color value;
     * @public
     * @since 1
     */
    /**
     * <p>The fill style for the context.</p>
     * @name yunos.graphics.Context#fillStyle
     * @type {string|yunos.graphics.Gradient|yunos.graphics.Context.Pattern}
     * @throws {TypeError} If value is not a color value;
     * @public
     * @since 3
     *
     */
    public fillStyle: string | number | Gradient | Context.Pattern;
    /**
     * <p>The fill the Rect with fillStyle color.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 1
     */
    public fillRect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>The define the Rect path.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 1
     */
    public rect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>Clean all the drawing content in the Rect.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 1
     */
    public clearRect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>The fill the path with fillStyle color.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Fill the path with the current fill style using the non-zero or even-odd winding rule.</p>
     * @param {string} [fillRule] - The algorithm by which to determine if a point is inside a path or outside a path.
     * @public
     * @since 3
     *
     */
    public fill(fillRule?: "nonzero" | "evenodd"): void;
    /**
     * <p>The draw the stroke of the path with strokeStyle color.</p>
     * @public
     * @since 1
     */
    public stroke(): void;
    /**
     * <p>Start a new path.</p>
     * @public
     * @since 1
     */
    public beginPath(): void;
    /**
     * <p>Move current painer to the new position.</p>
     * @param {number} x - left position of new point
     * @param {number} y - top position of new point
     * @public
     * @since 1
     */
    public moveTo(x: number, y: number): void;
    /**
     * <p>Creates a path from the current point back to the starting point.</p>
     * @public
     * @since 1
     */
    public closePath(): void;
    /**
     * <p>Creates a path from the current point to the target point.</p>
     * @param {number} x - left position of point
     * @param {number} y - top position of point
     * @public
     * @since 1
     */
    public lineTo(x: number, y: number): void;
    /**
     * <p>Cut shapes and sizes from the original canvas.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Turns the path currently being built into the current clipping path. Using the non-zero or even-odd winding rule.</p>
     * @param {string} [fillRule] - The algorithm by which to determine if a point is inside a path or outside a path.
     * @public
     * @since 3
     *
     */
    public clip(fillRule?: "nonzero" | "evenodd"): void;
    /**
     * <p>Create quadratic Bezier curve.</p>
     * @param {number} cpx - x position of control point
     * @param {number} cpy - y position of control point
     * @param {number} x - x position of end point
     * @param {number} y - y position of end point
     * @public
     * @since 1
     */
    public quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): void;
    /**
     * <p>Create Bezier curve.</p>
     * @param {number} cp1x - x position of first control point
     * @param {number} cp1y - y position of first control point
     * @param {number} cp2x - x position of second control point
     * @param {number} cp2y - y position of second control point
     * @param {number} x - x position of end point
     * @param {number} y - y position of end point
     * @public
     * @since 1
     */
    public bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void;
    /**
     * <p>Create an arc path.</p>
     * @param {number} x - x position of Circle's center
     * @param {number} y - y position of Circle's center
     * @param {number} r - the radius for arc
     * @param {number} sAngle - start angle
     * @param {number} eAngle - end angle
     * @param {boolean} bCounterclockwise - use clockwise or not
     * @public
     * @since 1
     */
    public arc(x: number, y: number, r: number, sAngle: number, eAngle: number, bCounterclockwise: boolean): void;
    /**
     * <p>Create an arc path from one point to another</p>
     * @param {number} x1 - x position of start point
     * @param {number} y1 - y position of start point
     * @param {number} x2 - x position of end point
     * @param {number} y2 - y position of end point
     * @param {number} r - radius of Circle
     * @public
     * @since 1
     */
    public arcTo(x1: number, y1: number, x2: number, y2: number, r: number): void;
    /**
     * <p>Change the scale with and height</p>
     * @param {number} scaleWidth - scale of width
     * @param {number} scaleHeight - scale of height
     * @public
     * @since 1
     */
    public scale(scaleWidth: number, scaleHeight: number): void;
    /**
     * <p>Rotate the canvas.</p>
     * @param {number} angle - rotate's angle
     * @public
     * @since 1
     */
    public rotate(angle: number): void;
    /**
     * <p>Translate the canvas.</p>
     * @param {number} x - x position for translate
     * @param {number} y - y position for translate
     * @public
     * @since 1
     */
    public translate(x: number, y: number): void;
    /**
     * <p>Set the transform. The Mat define as :<br>
     * { a, c, e}<br>
     * { b, d, f}<br>
     * { 0, 0, 1}</p>
     * @param {number} a - a position for Mat
     * @param {number} b - b position for Mat
     * @param {number} c - c position for Mat
     * @param {number} d - d position for Mat
     * @param {number} e - e position for Mat
     * @param {number} f - f position for Mat
     * @public
     * @since 1
     */
    public transform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    /**
     * <p>The font for the canvas.</p>
     * @name yunos.graphics.Context#font
     * @type {string}
     * @default "12px sans-serif"
     * @public
     * @since 1
     */
    public font: string;
    /**
     * <p>The text align for the canvas. Values as center|end|left|right|start.</p>
     * @name yunos.graphics.Context#textAlign
     * @type {string}
     * @public
     * @since 1
     */
    public textAlign: string;
    /**
     * <p>The text's baseline for the canvas. Values as alphabetic|top|hanging|middle|ideographic|bottom.</p>
     * @name yunos.graphics.Context#textBaseline
     * @type {string}
     * @public
     * @since 1
     */
    public textBaseline: string;
    /**
     * <p>To draw the text coloring on the canvas with fillStyle.</p>
     * @param {string} string - drawing text
     * @param {number} x - left start for drawing
     * @param {number} y - top start for drawing
     * @public
     * @since 1
     */
    public fillText(string: string, x: number, y: number): void;
    /**
     * <p>To draw the text coloring on the canvas with strokeStyle.</p>
     * @param {string} string - drawing text
     * @param {number} x - left start for drawing
     * @param {number} y - top start for drawing
     * @public
     * @since 1
     */
    public strokeText(string: string, x: number, y: number): void;
    /**
     * <p>To measure text width using current font.</p>
     * @param  {string} string - drawing text
     * @return {number} pixels of text width
     * @public
     * @since 1
     */
    public measureTextWidth(string: string): number;
    /**
     * <p>To measure text width using current font.</p>
     * @param {string} string - drawing text
     * @return {object} A TextMetrics object. {width: number, height?: number, lineCount?: number, ascent?: number, descent?: number}
     * @public
     * @since 2
     */
    public measureText(string: string): {
        width: number;
        height: number;
        lineCount: number;
        ascent: number;
        descent: number;
    };
    /**
     * <p>To draw the image in canvas.</p>
     * @param {Bitmap} image - Bitmap to be draw
     * @param {number} [sx] - the start x position in bitmap
     * @param {number} [sy] - the start y position in bitmap
     * @param {number} [sw] - the width in source bitmap
     * @param {number} [sh] - the height in source bitmap
     * @param {number} dx - target x position in the canvas
     * @param {number} dy - target y position in the canvas
     * @param {number} [dw] - the width show in canvas
     * @param {number} [dh] - the height show in canvas
     * @public
     * @since 2
     */
    public drawImage(image: Bitmap | string, sx?: int, sy?: int, sw?: int, sh?: int, dx?: int, dy?: int, dw?: int, dh?: int): void;
    /**
     * <p>To creates a new, blank ImageData object with the specified dimensions.</p>
     * @param {number | yunos.graphics.ImageData} width - The width to give the new ImageData object.
     * @param {number} [height] - The height to give the new ImageData object.
     * @return {yunos.graphics.ImageData} A new ImageData object with the specified width and height.
     * @public
     * @since 3
     *
     */
    public createImageData(width: number | ImageData, height?: number): ImageData;
    /**
     * <p>To get an ImageData object representing the underlying pixel data for a specified portion of the canvas.</p>
     * @param {number} x - The x coordinate of the top-left corner of the rectangle.
     * @param {number} y - The y coordinate of the top-left corner of the rectangle.
     * @param {number} width - The width of the rectangle.
     * @param {number} height - The height of the rectangle.
     * @return {yunos.graphics.ImageData} An ImageData object containing the image data for the rectangle of the canvas specified.
     * @public
     * @since 3
     *
     */
    public getImageData(x: number, y: number, width: number, height: number): ImageData;
    /**
     * <p>To paint data from the given ImageData object onto the canvas.</p>
     * @param {yunos.graphics.ImageData} imageData - An ImageData object containing the array of pixel values.
     * @param {number} dx - Horizontal position (x-coordinate) at which to place the image data in the destination canvas.
     * @param {number} dy - Vertical position (y-coordinate) at which to place the image data in the destination canvas.
     * @param {number} sx - Horizontal position (x-coordinate). The x coordinate of the top left hand corner of your Image data. Defaults to 0.
     * @param {number} sy - Vertical position (y-coordinate). The y coordinate of the top left hand corner of your Image data. Defaults to 0.
     * @param {number} sw - Width of the rectangle to be painted. Defaults to the width of the image data.
     * @param {number} sh - Height of the rectangle to be painted. Defaults to the height of the image data.
     * @public
     * @since 3
     *
     */
    public putImageData(image: ImageData, dx: number, dy: number, sx?: number, sy?: number, sw?: number, sh?: number): void;
    /**
     * <p>The global alpha for the canvas between 0 and 1.</p>
     * @name yunos.graphics.Context#globalAlpha
     * @type {number}
     * @public
     * @since 1
     */
    public globalAlpha: number;
    /**
     * <p>A new image to the existing image.</p>
     * @name yunos.graphics.Context#globalCompositeOperation
     * @type {string}
     * @public
     * @since 1
     */
    public globalCompositeOperation: string;
    /**
     * <p>Fuzzy level of shadow.</p>
     * @name yunos.graphics.Context#shadowBlur
     * @type {number}
     * @public
     * @since 1
     */
    public shadowBlur: number;
    /**
     * <p>The color of shadow.</p>
     * @name yunos.graphics.Context#shadowColor
     * @type {string}
     * @public
     * @since 1
     */
    public shadowColor: string | number;
    /**
     * <p>Offset in horizontal direction of shadow.</p>
     * @name yunos.graphics.Context#shadowOffsetX
     * @type {number}
     * @public
     * @since 1
     */
    public shadowOffsetX: number;
    /**
     * <p>Offset in vertical direction of shadow.</p>
     * @name yunos.graphics.Context#shadowOffsetY
     * @type {number}
     * @public
     * @since 1
     */
    public shadowOffsetY: number;
    /**
     * <p>Define the RoundRect path in canvas. since API level 2, use [roundedRect]{@link roundedRect} instead.</p>
     * @param {number} x - left position of round rect
     * @param {number} y - top position of round rect
     * @param {number} width - width of round rect
     * @param {number} height - height of round rect
     * @param {number} radius - radius of round rect
     * @deprecated 2
     * @public
     * @since 1
     */
    public roundRect(x: number, y: number, width: number, height: number, radius: number): void;
    /**
     * <p>Define the RoundedRect path in canvas.</p>
     * @param {number} x - left position of round rect
     * @param {number} y - top position of round rect
     * @param {number} width - width of round rect
     * @param {number} height - height of round rect
     * @param {number} radius - radius of round rect
     * @public
     * @since 2
     */
    /**
     * <p>Define the RoundedRect path in canvas.</p>
     * @param {number} x - left position of round rect
     * @param {number} y - top position of round rect
     * @param {number} width - width of round rect
     * @param {number} height - height of round rect
     * @param {number} radiusX - x radius of round rect
     * @param {number} [radiusY=radiusX] - y radius of round rect
     * @public
     * @since 3
     *
     */
    public roundedRect(x: number, y: number, width: number, height: number, radiusX: number, radiusY?: number): void;
    /**
     * <p>Saves the entire state of the canvas by pushing the current state onto a stack.</p>
     * @public
     * @since 3
     *
     */
    public save(): void;
    /**
     * <p>Restores the most recently saved canvas state by popping the top entry in the drawing state stack.
     * If there is no saved state, this method does nothing.</p>
     * @public
     * @since 3
     *
     */
    public restore(): void;
    /**
     * <p>Adds an ellipse to the path which is centered at (x, y) position with the radii radiusX and radiusY.</p>
     * @param {number} x - The x axis of the coordinate for the ellipse's center.
     * @param {number} y - The y axis of the coordinate for the ellipse's center.
     * @param {number} radiusX - The ellipse's major-axis radius.
     * @param {number} radiusY - The ellipse's minor-axis radius.
     * @public
     * @since 3
     *
     */
    public ellipse(x: number, y: number, radiusX: number, radiusY: number): void;
    /**
     * <p>Gets the current line dash pattern.</p>
     * @return {number[]} A list of numbers that specifies distances to alternately draw a line and a gap.
     * @public
     * @since 3
     *
     */
    public getLineDash(): number[];
    /**
     * <p>Sets the current line dash pattern.</p>
     * @param {number[]} arr - An Array of numbers which specify distances to alternately draw a line and a gap.
     * @public
     * @since 3
     *
     */
    public setLineDash(arr: number[]): void;
    /**
     * <p>Reports whether or not the specified point is inside the area contained by the stroking of a path.</p>
     * @param {number} x - The X coordinate of the point to check.
     * @param {number} y - The Y coordinate of the point to check.
     * @return {boolean} A Boolean, which is true if the point is inside the area contained by the stroking of a path, otherwise false.
     * @public
     * @since 3
     *
     */
    public isPointInStroke(x: number, y: number): boolean;
    /**
     * <p>Reports whether or not the specified point is contained in the current path.</p>
     * @param {number} x - The X coordinate of the point to check.
     * @param {number} y - The Y coordinate of the point to check.
     * @param {string} [fillRule] - The algorithm by which to determine if a point is inside a path or outside a path.
     * @return {boolean} A Boolean, which is true if the specified point is contained in the current or specified path, otherwise false.
     * @public
     * @since 3
     *
     */
    public isPointInPath(x: number, y: number, fillRule?: "nonzero" | "evenodd"): boolean;
    /**
     * <p>Resets the current transformation to the identity matrix and then invokes a transformation described by the arguments of this method. The Mat define as :<br>
     * { a, c, e}<br>
     * { b, d, f}<br>
     * { 0, 0, 1}</p>
     *
     * <p>See also the transform() method, which does not override the current transform matrix and multiplies it with a given one.</p>
     *
     * @param {number} a - Horizontal scaling.
     * @param {number} b - Horizontal skewing.
     * @param {number} c - Vertical skewing.
     * @param {number} d - Vertical scaling.
     * @param {number} e - Horizontal moving.
     * @param {number} f - Vertical moving.
     * @public
     * @since 3
     *
     */
    public setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    /**
     * <p>Resets the current transform to the identity matrix.</p>
     *
     * @public
     * @since 3
     *
     */
    public resetTransform(): void;
}
declare namespace Context {
    /**
     * <p>The Pattern interface represents an opaque object describing a pattern,
     * based on an ImageData or Bitmap created by the [createPattern()]{@link yunos.graphics.Context.createPattern()} method.</p>
     * @memberof yunos.graphics.Context
     * @public
     * @since 3
     *
     */
    class Pattern implements ContextPattern {
        public image: string | ImageData | Bitmap;
        public repetition: number;
        public constructor(image: string | ImageData | Bitmap, repetition: number);
    }
}
export = Context;
