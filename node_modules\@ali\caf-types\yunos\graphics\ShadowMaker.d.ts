import Bitmap = require("./Bitmap");
import YObject = require("../core/YObject");
interface CartonBuffer {
    read(key: string): string | number | ArrayBuffer;
    write(key: string, value: string | number | ArrayBuffer): void;
}
interface ImageCoreParameter {
    get(key: string): number | string;
    set(key: string, value: string | number): void;
}
interface saveCompleteCallback {
    (error: null | Error, result: null | Bitmap | string, width?: number, height?: number): void;
}
/**
 * A helper class to create shadow effect for given image or image file.
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 2
 */
declare class ShadowMaker extends YObject {
    private _imageCore;
    private _offset;
    private _radius;
    private _colorStr;
    private _color;
    private _callback;
    /**
     * create ShadowMaker instance
     * @public
     * @since 2
     */
    public constructor();
    /**
     * Destroy this instance
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * Offset for shadow
     * @name yunos.graphics.ShadowMaker#offset
     * @type {number}
     * @throws {TypeError} If offset is not a number.
     * @public
     * @since 2
     */
    public offset: number;
    /**
     * Radius for shadow.
     * @name yunos.graphics.ShadowMaker#radius
     * @type {number}
     * @throws {TypeError} If radius is not a number.
     * @public
     * @since 2
     */
    public radius: number;
    /**
     * Color for shadow.
     * @name yunos.graphics.ShadowMaker#color
     * @type {string}
     * @throws {TypeError} If parameter is not a valid color String.
     * @public
     * @since 2
     */
    public color: string;
    /**
     * This callback is callback by ShadowMaker when process complete.
     * @callback yunos.graphics.ShadowMaker~saveCompleteCallback
     * @param {Error} error - Error if native catched some exception when process image.
     * @param {yunos.graphics.Bitmap} image - Bitmap process from input image with shadow, null if has error.
     * @public
     * @since 2
     */
    /**
     * Add shadow for given yunos.graphics.Bitmap.
     * @param {yunos.graphics.Bitmap} image - Image to add shadow effect.
     * @param {yunos.graphics.ShadowMaker~saveCompleteCallback} callback - Callback function.
     * @public
     * @since 2
     */
    /**
     * Add shadow for given image file.
     * @param {string} image - Image to add shadow effect.
     * @param {yunos.graphics.ShadowMaker~saveCompleteCallback} callback - Callback function.
     * @public
     * @since 2
     */
    public saveBitmap(image: Bitmap | string, callback: saveCompleteCallback): void;
    /**
     * This callback is called back by ShadowMaker when process and save complete.
     * @callback yunos.graphics.ShadowMaker~saveCompleteCallback
     * @param {Error} error - Error if native catched some exception when process image.
     * @param {string} filePath - New image path, null if has error.
     * @param {number} width - Image width, null if has error.
     * @param {number} height - Image height, null if has error.
     * @public
     * @since 2
     */
    /**
     * Add shadow for given image and save to specify path.
     * @param {string} image - Image to process.
     * @param {string} filePath - Path to save processed image.
     * @param {yunos.graphics.ShadowMaker.ImageType} type - Output image type, currently support png only.
     * @param {yunos.graphics.ShadowMaker~saveCompleteCallback} callback - Callback function.
     * @throws [TypeError] If filePath is not valid value;
     * @public
     * @since 2
     */
    /**
     * Add shadow for given image and save to specify path.
     * @param {yunos.graphics.Bitmap} image - Image to process.
     * @param {string} filePath - Path to save processed image.
     * @param {yunos.graphics.ShadowMaker.ImageType} type - Output image type, currently support png only.
     * @param {yunos.graphics.ShadowMaker~saveCompleteCallback} callback - Callback function.
     * @throws {TypeError} If filePath is not valid value;
     * @public
     * @since 2
     */
    public saveFile(image: Bitmap | string, filePath: string, type: string, callback: saveCompleteCallback): void;
    private callback(buffer: CartonBuffer, param: ImageCoreParameter, callback: saveCompleteCallback): void;
    private processFile(filePath: string, callback: saveCompleteCallback, outPutFilePath?: string): void;
    private processBitmap(bitmap: Bitmap, callback: saveCompleteCallback, filePath?: string): void;
    private createParam(width?: number, height?: number): ImageCoreParameter;
    /**
     * Output image type.
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static ImageType: {
        /**
         * PNG
         * @public
         * @since 2
         */
        PNG: string;
    };
}
export = ShadowMaker;
