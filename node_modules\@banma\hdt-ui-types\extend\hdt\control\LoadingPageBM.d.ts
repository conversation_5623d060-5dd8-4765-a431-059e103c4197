import CompositeView = require("yunos/ui/view/CompositeView");
import { ButtonColorType, ButtonType } from "./Types";
declare enum DisplayMode {
    LoadingMode = 0,
    ErrorMode = 1
}
interface IStyle {
    loadingAnimWidth: number;
    loadingAnimHeight: number;
    loadingAnimOffsetYPct: number;
    loadingAnimPath: string;
    loadingTextPropertySet: string;
    loadingTextColor: string;
    loadingTextMarginTop: number;
    loadingTextLineHeight: number;
    loadingTextContent: string;
    errorImageTop: number;
    errorImageSrc: string;
    errorTitlePropertySet: string;
    errorTitleColor: string;
    errorTitleMarginTop: number;
    errorTitleContent: string;
    errorTextPropertySet: string;
    errorTextColor: string;
    errorTextMarginTop: number;
    errorTextMaxWidth: number;
    errorTextContent: string;
    retryButtonWidth: number;
    retryButtonType: ButtonType;
    retryButtonColorType: ButtonColorType;
    retryButtonText: string;
    retryButtonMarginTop: number;
}
declare class LoadingPageBM extends CompositeView {
    static readonly DisplayMode: typeof DisplayMode;
    private _errorTitleVisible;
    private _errorTextVisible;
    private _errorImageVisible;
    private _retryButtonVisible;
    private _displayMode;
    private loadingAnimView;
    private loadingTextView;
    private errorImageView;
    private errorTitleView;
    private errorTextView;
    private retryButton;
    private _loadingAnimWidth;
    private _loadingAnimHeight;
    private _loadingAnimOffsetYPct;
    private _loadingAnimPath;
    private _loadingTextPropertySet;
    private _loadingTextColor;
    private _loadingTextMarginTop;
    private _loadingTextLineHeight;
    private _loadingTextContent;
    private _errorImageTop;
    private _errorImageSrc;
    private _errorTitlePropertySet;
    private _errorTitleColor;
    private _errorTitleMarginTop;
    private _errorTitleContent;
    private _errorTextPropertySet;
    private _errorTextColor;
    private _errorTextMarginTop;
    private _errorTextMaxWidth;
    private _errorTextContent;
    private _retryButtonWidth;
    private _retryButtonType;
    private _retryButtonColorType;
    private _retryButtonText;
    private _retryButtonMarginTop;
    constructor();
    readonly defaultStyleName: string;
    errorText: string;
    errorImgSrc: string;
    displayMode: DisplayMode;
    loadingText: string;
    errorTitle: string;
    retryButtonText: string;
    retryButtonVisible: boolean;
    errorTitleVisible: boolean;
    errorTextVisible: boolean;
    errorImageVisible: boolean;
    protected applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    private _initLoadingViews;
    private _initErrorViews;
    private _validDisplayMode;
    private _initLayout;
    private _getLoadingAnimViewMarginTop;
    private getLoadingAnimView;
    private _initLottieView;
    private _initSpriteView;
    private getLoadingTextView;
    private getErrorImageView;
    private getErrorTitleView;
    private getErrorTextView;
    private getRetryButton;
}
export = LoadingPageBM;
