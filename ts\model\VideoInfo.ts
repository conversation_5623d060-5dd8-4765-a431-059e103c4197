/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers
import Consts = require("../Consts");
import {IOnlineVideo} from "../Types";

class VideoInfo {
    id: string;
    title: string;
    videoId: number;
    videoWidth: number;
    videoHeight: number;
    videoSize: number;
    tags: string;
    category: string;
    url: string;
    thumbnail: string;
    duration: number;
    viewCount: number;
    likeCount: number;
    displayName: string;
    dateModified: string;
    orientation: number;
    mimeType: string;
    lastPlayed: boolean;
    [key: string]: Object;

    constructor(rawObject?: IOnlineVideo) {
        if (rawObject && typeof rawObject === "object") {
            this.id = rawObject.id;
            this.title = rawObject.title;
            this.videoId = rawObject.videoId;
            this.videoWidth = rawObject.videoWidth;
            this.videoHeight = rawObject.videoHeight;
            this.videoSize = rawObject.videoSize;
            this.tags = rawObject.tags;
            this.category = rawObject.category;
            this.url = rawObject.mp4Url;
            this.thumbnail = rawObject.thumbnail;
            this.duration = rawObject.duration;
            this.viewCount = rawObject.viewCount;
            this.likeCount = rawObject.likeCount;
            this.displayName = "";
            this.dateModified = "";
            this.orientation = 0;
            this.mimeType = "";
            this.lastPlayed = false;
        } else {
            this.id = "";
            this.title = "";
            this.videoId = -1;
            this.videoWidth = 0;
            this.videoHeight = 0;
            this.tags = "";
            this.category = "";
            this.videoSize = 0;
            this.url = "";
            this.thumbnail = "";
            this.duration = 0;
            this.viewCount = 0;
            this.likeCount = 0;
            this.displayName = "";
            this.dateModified = "";
            this.orientation = 0;
            this.mimeType = "";
            this.lastPlayed = false;
        }
    }

    static createFrom(obj: VideoInfo): VideoInfo {
        let newObj: VideoInfo = new VideoInfo();
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                newObj[key] = obj[key];
            }
        }
        return newObj;
    }

    equal(obj: VideoInfo): boolean {
        let equals = false;
        if (typeof obj === "object") {
            if (this.id === obj.id && this.videoId === obj.videoId && this.url === obj.url) {
                equals = true;
            }
        }
        return equals;
    }

    checkValid() {
        if (typeof this.videoId === Consts.TYPE_NUMBER && this.videoId > 0) {
            return true;
        }
        return false;
    }

    dumpInfo() {
        return "{ title:" + this.title + ", tags:" + this.tags + ", category:" + this.category +
            ", url:" + this.url + ", duration:" + this.duration + ", thumbnail:" + this.thumbnail + "}";
    }
}

export = VideoInfo;