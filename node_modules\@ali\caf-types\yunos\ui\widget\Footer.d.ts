import CompositeView = require("../view/CompositeView");
import TextView = require("../view/TextView");
import View = require("../view/View");
import EventEmitter = require("../../core/EventEmitter");
import { Options } from "../util/TypeHelper";
interface IStyle {
    normalUri: string;
    pressedUri: string;
    disableUri: string;
    fontSize: number;
    iconSize: number;
    titlePadding: number;
    itemMargin: number;
    fontColor: string;
    defaultItemWidth: number;
    defaultItemHeight: number;
    defaultBorderColor: string;
    defaultBackground: string;
}
/**
 * <p>Footer widget.</p>
 * @memberof yunos.ui.widget
 * @extends yunos.ui.view.CompositeView
 * @public
 * @since 2
 */
declare class Footer extends CompositeView {
    private _itemList;
    private _upMenu;
    private _max;
    private _itemWidth;
    private _itemHeight;
    private _defaultMoreItem;
    private _moreItem;
    private _normalUri;
    private _pressedUri;
    private _disableUri;
    private _itemOptions;
    private _defaultNormalColor;
    private _defaultItemWidth;
    private _defaultItemHeight;
    private _defaultBorderColor;
    private _defaultBackground;
    private _onWindowResizeFunc;
    /**
     * <p>This property holds the max of the Footer. Indicates how many items will be displayed.</p>
     * @name yunos.ui.widget.Footer#max
     * @type {number}
     * @public
     * @since 2
     */
    public max: number;
    /**
     * <p>Defines itemWidth of this footer item, in pixels.</p>
     * @name yunos.ui.widget.Footer#itemWidth
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is a negative number.
     * @public
     * @since 2
     */
    public itemWidth: number;
    /**
     * <p>Defines itemHeight of this footer item, in pixels.</p>
     * @name yunos.ui.widget.Footer#itemHeight
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is a negative number.
     * @public
     * @since 2
     */
    public itemHeight: number;
    /**
     * <p>Add a item (yunos.ui.widget.Footer.FooterItem) into Footer.</p>
     * @param {yunos.ui.widget.Footer.FooterItem} item - The item will be displayed.
     * @throws {TypeError} If item is not a FooterItem.
     * @public
     * @since 2
     */
    /**
     * <p>Add a item (yunos.ui.widget.Footer.FooterItem) into Footer.</p>
     * @param {yunos.ui.widget.Footer.FooterItem} item - The item will be displayed.
     * @param {number} index - The index at which to insert the item to.
     * @throws {TypeError} If item is not a FooterItem.
     * @throws {RangeError} If index is a negative number.
     * @public
     * @since 2
     */
    public addFooterItem(item: Footer.FooterItem, index: number): void;
    /**
     * <p>Remove a last item (yunos.ui.widget.Footer.FooterItem) from Footer.</p>
     * @public
     * @since 2
     */
    /**
     * <p>Remove a item (yunos.ui.widget.Footer.FooterItem) from Footer.</p>
     * @param {number} index - The index at which to remove the item.
     * @throws {TypeError} If item is not a FooterItem.
     * @throws {RangeError} If index is a negative number.
     * @public
     * @since 2
     */
    public removeFooterItem(index: number): void;
    /**
     * <p>Set the MoreItem (yunos.ui.widget.Footer.FooterItem) in Footer.</p>
     * <p>There are more items that can be displayed.</p>
     * @param {yunos.ui.widget.Footer.FooterItem} item - The item will be displayed.
     * @throws {TypeError} If item is not a FooterItem.
     * @public
     * @since 2
     */
    public setMoreItem(item: Footer.FooterItem): void;
    /**
     * <p>Set enabled state of the item by index, true if this item is enabled, false otherwise.</p>
     * @param {number} index - The index at which to enabled.
     * @param {boolean} enabled - The item will be enable/disabled.
     * @throws {TypeError} If index of parameter is not number.
     * @throws {RangeError} If index is a negative number.
     * @throws {TypeError} If type of parameter is not boolean.
     * @public
     * @since 2
     */
    public setFooterItemEnabled(index: number, enabled: boolean): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.view.TextView#defaultStyleName
     * @type {string}
     * @default "Footer"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    private refreshUI(): void;
    private handleMore(): void;
    private initDefaultMoreItem(): void;
    private packageMultiState(item: Options): {
        normal: {};
        pressed: {};
        disabled: {};
    };
    private packageSingleState(state: Object, values: Options, uriKey: string, opacityKey: string, colorKey: string): void;
    private getItemView(options: Object): ItemView;
    private onWindowResize(): void;
}
declare namespace Footer {
    /**
     * <p>FooterItem class.</p>
     * @extends yunos.core.EventEmitter
     * @memberof yunos.ui.widget.Footer
     * @public
     * @since 2
     */
    class FooterItem extends EventEmitter {
        private _title;
        private _normalUri;
        private _pressedUri;
        private _disabledUri;
        private _normalOpacity;
        private _pressedOpacity;
        private _disabledOpacity;
        private _normalColor;
        private _pressedColor;
        private _disabledColor;
        /**
         * <p>The plain-text content that this title is to display.</p>
         * @name yunos.ui.widget.Footer.FooterItem#title
         * @type {string}
         * @public
         * @since 2
         */
        public title: string;
        /**
         * <p>Set the icon uri by state.</p>
         * @param {yunos.ui.widget.Footer.FooterItem.State} state - the state to set.
         * @param {string} uri - The icon uri.
         * @throws {TypeError} If state is not a FooterItem.State.
         * @throws {TypeError} If uri is not a string or null.
         * @public
         * @since 2
         */
        public setIconUri(state: number, uri: string): void;
        /**
         * <p>Set the item's opacity by state.</p>
         * @param {yunos.ui.widget.Footer.FooterItem.State} state - the state to set.
         * @param {string} opacity - the opacity of the item. between 0 and 1.
         * @throws {TypeError} If state is not a FooterItem.State.
         * @throws {TypeError} If opacity is not a number.
         * @public
         * @since 2
         */
        public setOpacity(state: number, opacity: number): void;
        /**
         * <p>Set the color by state.</p>
         * @param {yunos.ui.widget.Footer.FooterItem.State} state - the state to set.
         * @param {string} color - text color.
         * @throws {TypeError} If state is not a FooterItem.State.
         * @throws {TypeError} If color is not a string and valid format.
         * @public
         * @since 2
         */
        public setColor(state: number, color: string): void;
        /**
         * <p>Enum for State.</p>
         * @enum {number}
         * @readonly
         * @public
         * @since 2
         */
        public static readonly State: {
            /**
             * <p>The normal state.</p>
             * @public
             * @since 2
             */
            Normal: int;
            /**
             * <p>The pressed state.</p>
             * @public
             * @since 2
             */
            Pressed: int;
            /**
             * <p>The disabled state.</p>
             * @public
             * @since 2
             */
            Disabled: int;
        };
    }
}
/**
 * @private
 */
declare class ItemView extends CompositeView {
    private _titleView;
    private _iconView;
    private _iconSize;
    private _iconHS;
    private _itemMargin;
    private _buttonTapRecognizer;
    private title: string;
    private setIconMultiState(iconView: View, state: Object): void;
    private setTitleMultiState(titleView: TextView, state: Object): void;
    private setMultiState(state: Options, key: string, value: Object): void;
    private refreshUI(): void;
}
export = Footer;
