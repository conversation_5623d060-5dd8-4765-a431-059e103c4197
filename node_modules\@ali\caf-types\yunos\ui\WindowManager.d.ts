import EventEmitter = require("../core/EventEmitter");
import Window = require("./view/Window");
import View = require("./view/View");
import Point = require("../graphics/Point");
import TouchEvent = require("./event/TouchEvent");
import KeyEvent = require("./event/KeyEvent");
import MouseEvent = require("./event/MouseEvent");
import Page = require("../page/Page");
import { TouchPoint, IKeyboardModalityEvent } from "./util/TypeHelper";
import CompositeView = require("./view/CompositeView");
import TextView = require("./view/TextView");
import { WheelObject } from "./util/TypeHelper";
import ClipboardMenu = require("./widget/ClipboardMenu");
import ModalLayer = require("./widget/ModalLayer");
declare class WindowManager extends EventEmitter {
    private _keyEventPropagationChain;
    private _mouseTargetView;
    private _clickEventSaveArray;
    private _mouseTargetViewEventChain;
    private _mouseEventPropagationChain;
    private renderingViewArray: Set<View>;
    private _dirtyWindowArray;
    private _onEnviromentChangeFunc;
    private _page: Page;
    private _orientation;
    private _subWindows;
    private _metadown;
    private _shiftdown;
    private _ctrldown;
    private _altdown;
    private _toasts;
    private _mainWindow;
    private _currentWindow;
    private _orphanDialogs;
    private _touchedWindowMap;
    private _focusedView: View;
    private _touchMode;
    private _buttons;
    private _clipboard;
    private _timer;
    private _eventPropagationChainMapArray: Map<View, number>[];
    private _dataAgent;
    private static __maybeImeOwner: View;
    private static __imeOwner: View;
    private static __lastImeOwner: View;
    private static _managerMap: Map<Page, WindowManager>;
    private static _currentWindowManager: WindowManager;
    public constructor(context?: Page);
    private addClipBoard(clipboard: ClipboardMenu): void;
    private releaseClipBoard(): void;
    private initWindowKeyEvent(win: Window): void;
    private addNeedRenderView(view: View): void;
    private addSubWindow(win: Window): void;
    private removeSubWindow(win: Window): void;
    private attachOrphanDialog(win: ModalLayer): void;
    private detachOrphanDialog(win: ModalLayer): void;
    private addToast(win: Window | ModalLayer): void;
    private removeToast(win: Window | ModalLayer): void;
    private readonly context: Page;
    private registCurrentPage(page: Page): void;
    private readonly window: Window;
    private focusedView: View;
    private readonly touchMode: boolean;
    private addMainWindow(win: Window): void;
    private processKeyEvent(type: string, keyObj: IKeyboardModalityEvent, win: Window): boolean;
    private chainedDispatchKeyEvent(target: View, event: KeyEvent, eventPropagationChain: View[]): void;
    private processMouseEvent(x: number, y: number, mode: number, button: number, time: number, win: Window | ModalLayer): void;
    private translateButton(button: number): 0 | 1 | 2;
    private processWheelEvent(wheelObject: WheelObject): void;
    private chainedDispatchMouseEvent(target: View, event: MouseEvent, eventPropagationChain: View[]): void;
    private getDialogs(): ModalLayer[];
    private getWindowList(): {
        mainWindow: Window;
        dialogs: ModalLayer[];
        subWindows: Window[];
        clipBoard: ClipboardMenu;
        toasts: CompositeView[];
    };
    private offsetMouseEventPositive(event: MouseEvent, view: View): void;
    private offsetMouseEventNegative(event: MouseEvent, view: View): void;
    private setOffsetForMouseEventPositive(event: MouseEvent, view: View): void;
    private setOffsetForMouseEventNegative(event: MouseEvent, view: View): void;
    private checkExcludeTouch(parent: View, target: View, touchId: number, touchObj: {
        _lastTouchPoints: {
            x: number;
            y: number;
            type: number;
        }[];
        _eventPropagationChain: View[][];
        _eventPropagationChainMapArray: Map<CompositeView, number>[];
        _targetViews: View[];
    }): boolean;
    private processTouchEvent(touchPoints: Array<TouchPoint>, win: Window | ModalLayer): {
        preventDefault: boolean;
        changedTouches: {
            identifier: number;
            screenX: number;
            screenY: number;
            clientX: number;
            clientY: number;
            pageX: number;
            pageY: number;
            target: View | CompositeView;
            changed: boolean;
        }[];
        type: string;
    };
    private transIntoTouchCancelEvent(event: TouchEvent): TouchEvent;
    private dispatchTouchCancelEvent(view: View, touchCancelEvent: TouchEvent): void;
    private emitTouchEvent(target: View, event: TouchEvent): void;
    private chainedDispatchEvent(chainId: number, target: View, event: TouchEvent, touchObj: {
        _lastTouchPoints: {
            x: number;
            y: number;
            type: number;
        }[];
        _eventPropagationChain: View[][];
        _eventPropagationChainMapArray: Map<CompositeView, number>[];
        _targetViews: View[];
    }): void;
    private offsetTouchEventTouchesPositive(event: TouchEvent, view: View): void;
    private offsetTouchEventTouchesNegative(event: TouchEvent, view: View): void;
    private setOffsetForTouchEventPositive(event: TouchEvent, view: View): void;
    private setOffsetForTouchEventNegative(event: TouchEvent, view: View): void;
    private processInputModeChange(inputmode: string): void;
    private loseFocusInTouchMode(): void;
    private changeOrientation(orientationValue: number, autoOrientation: boolean): void;
    private getNativeOrientation(orientationValue: number): int;
    private getCurrentOrientation(): number;
    private postOrientationChange(orientation: number): void;
    private sendEventToClipBoard(point: Point): boolean;
    private onHide(ignored?: boolean): void;
    private onShow(ignored?: boolean): void;
    private scanTextView(fontScale: number): void;
    private onWindowOrModalLayerHide(): void;
    private getTextViewByView(view: View): TextView[];
    private scanListView(view: View): void;
    private addDirtyWindow(win: Window | ModalLayer, immediately: boolean): void;
    private renderingWindows(immediately?: boolean): void;
    private _renderingWindows(): void;
    private rendering(): void;
    private setSpecifiedMode(enabled: boolean, mode: number, win: Window | ModalLayer): void;
    private releaseTouchTarget(win: Window | ModalLayer): void;
    private static getInstance(context?: Page): WindowManager;
    private static releaseInstance(context?: Page): void;
    private static getImeOwner(): View;
    private static getLastImeOwner(): View;
    private static resetLastImeOwner(): void;
    private static setImeOwner(view: View): void;
    private static getMaybeImeOwner(): View;
    private static setMaybeImeOwner(view: View): void;
}
export = WindowManager;
