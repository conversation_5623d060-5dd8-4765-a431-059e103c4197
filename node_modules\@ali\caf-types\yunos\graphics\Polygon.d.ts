import Point = require("./Point");
import Shape = require("./Shape");
import Context = require("./Context");
import Gradient = require("./Gradient");
/**
 * <p>A shape defined by multiable vertex Points<br>
 * Note that a valid Polygon needs at least 3 points.</p>
 * @extends yunos.graphics.Shape
 * @memberof yunos.graphics
 * @public
 * @since 6
 */
declare class Polygon extends Shape {
    private _vertexes;
    /**
     * <p>Constructor that create a polygon.</p>
     * @param {yunos.graphics.Point[]} vertexes - the vertex points of the polygon.
     * @public
     * @since 6
     */
    public constructor(vertexes: Point[]);
    /**
     * <p>Destructor that destroy this polygon.</p>
     * @public
     * @since 6
     */
    public destroy(): void;
    /**
     * <p>Set the polygon's vertexes to the specified values.</p>
     * <p>Note that no range checking is performed, so it is up to the caller to ensure that left <= right and top <= bottom.</p>
     * @method yunos.graphics.Polygon#assign
     * @param {yunos.graphics.Point[]} vertexes - the vertex points of the polygon.
     * @public
     * @since 6
     */
    public assign(vertexes: Point[]): void;
    /**
     * <p>The vertexes of the polygon.</p>
     * @name yunos.graphics.Polygon#vertexes
     * @type {yunos.graphics.Point[]} vertexes - the vertex points of the polygon.
     * @public
     * @readonly
     * @since 6
     */
    public readonly vertexes: Point[];
    /**
     * <p>Indicates whether the specified point is inside this polygon.</p>
     * <p>An invalid polygon never contains any point.</p>
     * @param {number} x - the X coordinate of the point being tested for containment.
     * @param {number} y - the Y coordinate of the point being tested for containment.
     * @return {boolean} true if (x,y) is inside the polygon, otherwise false.
     * @public
     * @since 6
     */
    public containsXY(x: number, y: number): boolean;
    /**
     * <p>Indicates whether the specified point is inside this polygon.</p>
     * <p>An empty polygon never contains any point.</p>
     * @param {yunos.graphics.Point} point - the point being tested for containment.
     * @return {boolean} true if the point is inside the polygon, otherwise false.
     * @public
     * @since 6
     */
    public containsPoint(point: Point): boolean;
    /**
     * <p>Clone a new polygon from this polygon.</p>
     * @return {yunos.graphics.Polygon} a new polygon that has the same vertexes of this polygon.
     * @override
     * @public
     * @since 6
     */
    public clone(): Polygon;
    /**
     * <p>Returns a human-readable polygon string.</p>
     * @return {string} the polygon string.
     * @override
     * @public
     * @since 6
     */
    public toString(): string;
    /**
     * <p>Draw this Polygon in canvas</p>
     * @param {yunos.graphics.Context} ctx - the canvas context
     * @param {(string | number | yunos.graphics.Gradient)} fillStyle - the style of shape filling
     * @friend
     */
    drawCanvas(ctx: Context, fillStyle: string | number | Gradient): void;
}
export = Polygon;
