import CompositeView = require("../view/CompositeView");
import TextView = require("../view/TextView");
import RelativeLayout = require("../layout/RelativeLayout");
import Checkmark = require("./Checkmark");
import Dialog = require("./Dialog");
import { StringObjectKV } from "yunos/ui/util/TypeHelper";
interface IStyle {
    height: number;
    textMargin: number;
    textFontSize: string;
    textColor: string;
    background: string;
    backgroundPressed: string;
    disabledOpacity: string;
    checkmarkMarginRight: number;
    dividerColor: string;
    dividerWidth: number;
    dividerMargin: number;
    itemFocusedWidth: number;
    itemFocused: boolean;
    fontSize: string;
    fontFamily: string;
    itemHeight: number;
    margin: number;
    titleLineColor: string;
}
/**
 * <p>ActionMenu widget displays a list of items in a vertical list that is at the top/bottom of the screen.</p>
 * <p>A menu is consisted of some menuitems(only text messages) that appears in on top of the window.</p>
 * <p>The underlying Activity loses focus and the menu accepts all user interaction.</p>
 * @extends yunos.ui.widget.Dialog
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class ActionMenu extends Dialog {
    private _group;
    private _titleView;
    private _title;
    private _showAtTop;
    private _mode;
    private _handleCheckedChange;
    private _checkedItem;
    private _itemHeight;
    private _defaultBackground;
    private _defaultBackgroundPressed;
    private _defaultFontSize;
    private _defaultFontColor;
    private _defaultTextColor;
    private _font;
    private _margin;
    private _titleLineColor;
    private _titleLineHeight;
    private _paddingLeft;
    private _paddingRight;
    private _paddingTop;
    private _paddingBottom;
    private _result;
    private _currentIndex;
    private _needClose;
    /**
     * <p>Create ActionMenu.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this ActionMenu.</p>
     * @param {boolean} recursive - destroy the children in the ActionMenu if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * <p>Destructor that destroy this ActionMenu.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds title of the menu.</p>
     * @name yunos.ui.widget.ActionMenu#title
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 1
     */
    public title: string;
    /**
     * <p>This property holds the menu shown at the top/bottom of the screen.</p>
     * <p>When showAtTop is true the menu will appear at top, otherwise the menu will appear at bottom of the screen.</p>
     * <p>The default value is false.</p>
     * @name yunos.ui.widget.ActionMenu#showAtTop
     * @type {boolean}
     * @throws {TypeError} if this value is not a boolean
     * @public
     * @since 1
     */
    public showAtTop: boolean;
    /**
     * <p>Mode of this ActionMenu, defines whether the action menu item has checkmark at right side.</p>
     * @name yunos.ui.widget.ActionMenu#mode
     * @type {yunos.ui.widget.ActionMenu.Mode}
     * @throws {TypeError} If this value is not in ActionMenu.Mode.
     * @public
     * @since 1
     */
    public mode: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.ActionMenu#defaultStyleName
     * @type {string}
     * @default "ActionMenu"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Add a menuItem to this action menu.</p>
     * @param {yunos.ui.widget.ActionMenu.ActionMenuItem} menuItem - the menu item that added to the end of parent children.
     * @throws {TypeError} If this value is not an ActionMenuItem.
     * @public
     * @since 1
     */
    public addChild(menuItem: ActionMenu.ActionMenuItem): void;
    /**
     * <p>Insert a menuItem to this action menu.</p>
     * @param {yunos.ui.widget.ActionMenu.ActionMenuItem} menuItem - the menu item that inserted to the specified position of parent children.
     * @param {number} index - the specified position of the action menu to insert.
     * @throws {TypeError} If this value is not an ActionMenuItem.
     * @public
     * @since 1
     */
    public insertChild(menuItem: ActionMenu.ActionMenuItem, index: number): void;
    private initMenuItem(menuItem: ActionMenu.ActionMenuItem, index: number): void;
    /**
     * <p>Implement this function before doing animation to show dialog.</p>
     * @override
     * @protected
     * @since 2
     */
    protected prepareForShowing(): void;
    /**
     * <p>Apply theme style for action menu.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: StringObjectKV): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: StringObjectKV, diffStyle: StringObjectKV): void;
    private refresh(): void;
    private onTap(menuItem: ActionMenu.ActionMenuItem, event: Object): void;
    private updateFocus(preIndex: number): void;
    private updateCheckedStatus(index: number): void;
    private updateCheckedStatusByDefault(index: number): void;
    /**
     * Get the surfaceId of this ActionMenu.
     * @private
     */
    private readonly surfaceId: number;
    /**
     * <p>Enum for ActionMenu mode type. Defines whether there is a checkmark at right side.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly Mode: {
        [index: string]: number;
    };
}
declare namespace ActionMenu {
    /**
     * <p>ActionMenuItem is an item which is used for ActionMenuItem.</p>
     * @extends yunos.ui.view.CompositeView
     * @memberof yunos.ui.widget.ActionMenu
     * @public
     * @since 1
     */
    class ActionMenuItem extends CompositeView {
        private _checkmark;
        private _contentView;
        private _textFontSize;
        private _textMargin;
        private _disableOpacity;
        private _defaultTextColor;
        private _defaultHeight;
        private _defaultBackground;
        private _backgroundPressed;
        private _checkmarkMarginRight;
        private _itemFocused;
        private _itemFocusedWidth;
        private _checkedLayout: RelativeLayout;
        private _normalLayout: RelativeLayout;
        /**
         * <p>Create an ActionMenuItem.</p>
         * @param {string} [text=""] - the title of action menu item.
         * @public
         * @since 1
         */
        public constructor(context: Object, text?: string);
        /**
         * <p>Destructor that destroy this ActionMenuItem.</p>
         * @param {boolean} recursive - destroy the children in the ActionMenuItem if the value is true.
         * @public
         * @override
         * @since 2
         */
        /**
         * <p>Destructor that destroy this ActionMenuItem.</p>
         * @public
         * @since 1
         */
        public destroy(recursive?: boolean): void;
        /**
         * <p>This property holds text of the ActionMenuItem.</p>
         * @name yunos.ui.widget.ActionMenu.ActionMenuItem#text
         * @type {string}
         * @throws {TypeError} If this value is not a string.
         * @public
         * @since 1
         */
        public text: string;
        /**
         * <p>Defines the enabled state for this ActionMenuItem.</p>
         * @name yunos.ui.widget.ActionMenu.ActionMenuItem#enabled
         * @type {boolean}
         * @throws {TypeError} If the type of value is not boolean.
         * @override
         * @public
         * @since 2
         */
        public enabled: boolean;
        /**
         * <p>This property holds the checkmark object of the ActionMenu.</p>
         * @name yunos.ui.widget.ActionMenu.ActionMenuItem#checkmark
         * @type {yunos.ui.widget.Checkmark}
         * @readonly
         * @public
         * @since 1
         */
        public readonly checkmark: Checkmark;
        /**
         * <p>Get the default style name of this view.</p>
         * @name yunos.ui.widget.ActionMenu.ActionMenuItem#defaultStyleName
         * @type {string}
         * @default "ActionMenuItem"
         * @readonly
         * @public
         * @override
         * @since 4
         *
         */
        public readonly defaultStyleName: string;
        /**
         * <p>Get the content view. When designing an ActionMenuItem with custom view, override this method.</p>
         * @param {string} text - the title of action item.
         * @returns {yunos.ui.view.View} a content view that created.
         * @protected
         * @since 1
         */
        protected getContentView(text: string): TextView;
        /**
         * <p>Apply theme style for action menu.</p>
         * @override
         * @protected
         * @since 1
         */
        protected applyStyle(style?: StringObjectKV): void;
        /**
         * <p>Implement this method to update style when style properties changed.</p>
         * @param {Object} style - New style config from theme.
         * @param {Object} diffStyle - Object contains difference values compare with the preview style.
         * @override
         * @protected
         * @since 4
         *
         */
        protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    }
}
export = ActionMenu;
