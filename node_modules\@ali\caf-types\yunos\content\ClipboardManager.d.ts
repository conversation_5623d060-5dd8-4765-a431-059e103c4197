import ClipContent = require("yunos/content/ClipContent");
import EventEmitter = require("yunos/core/EventEmitter");
/**
 * <p>Use this class to describe the manager of clipboard.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.content
 * @public
 * @since 1
 * @hiddenOnPlatform auto
 */
declare class ClipboardManager extends EventEmitter {
    private _listener;
    /**
     * <p>Constructor that create a clipboard manager.</p>
     * @public
     * @since 1
     */
    public constructor();
    /**
     * <p>Get singleton ClipboardManager instance.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * @return {Object} the instance of ClipboardManager.
     * @public
     * @since 1
     */
    public static getInstance(ctx?: Object): ClipboardManager;
    /**
     * <p>This callback is to get result and content when getting clipboard content.</p>
     * @callback yunos.content.ClipboardManager~getContentCallback
     * @param {boolean} r - true means success, othewise false.
     * @param {Object} res - the content if r is true, otherwise undefined.
     * @public
     * @since 1
     */
    /**
     * <p>Get the clipboard content.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasText()) {
     *                 console.log(item.getText());
     *             }
     *         }
     *     }
     * });
     * @param {yunos.content.ClipboardManager~getContentCallback} callback - the callback that return the result.
     * @public
     * @since 1
     */
    public getContent(callback: (result: boolean, content?: ClipContent) => void): void;
    /**
     * <p>This callback is to get result when setting clipboard content.</p>
     * @callback yunos.content.ClipboardManager~setContentCallback
     * @param {boolean} r - true means success, othewise false.
     * @param {Object} message - the err message if r is false, otherwise undefined.
     * @public
     * @since 1
     */
    /**
     * <p>Set the clipboard content.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * let clipboardManager = ClipboardManager.getInstance();
     * let contentText = ClipContent.newText("test text");
     * clipboardManager.setContent(contentText, function(r, err) {
     *     console.log(r + ":" + err);
     * });
     * @param {Object} content - instance of ClipContent want to set.
     * @param {yunos.content.ClipboardManager~setContentCallback} callback the callback that return the result.
     * @public
     * @since 1
     */
    public setContent(content: ClipContent, callback: (result: boolean, err?: string) => void): void;
    /**
     * <p>The event is fired when the clipboard is changed.</p>
     * @event yunos.content.ClipboardManager#clipboardchanged
     * @public
     * @since 2
     */
    /**
     * <p>Add a listener to subscribe clipboard changed event.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * let listener = function() {
     *     console.log("clipboardchanged");
     * };
     * clipboardManager.on("clipboardchanged", listener);
     * @param {string} event - The event name.
     * @param {function} listener - The function which will be called when the event is fired.
     * @public
     * @override
     * @since 2
     */
    public on(event: string, listener: () => void): this;
    /**
     * <p>Remove a clipboard changed listener that added by on.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * let listener = function() {
     *     console.log("clipboardchanged");
     * };
     * clipboardManager.on("clipboardchanged", listener);
     * // ...
     * clipboardManager.removeListener("clipboardchanged", listener);
     * @param {string} event - The event name.
     * @param {function} listener - The function which is removed from the event listeners.
     * @public
     * @override
     * @since 2
     */
    public removeListener(event: string, listener: () => void): this;
}
export = ClipboardManager;
