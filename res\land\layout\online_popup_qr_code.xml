<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    width="{sdp(624)}"
    height="{sdp(464)}"
    layout="{layout.online_qr_code_container}"
    propertySetName="online">

    <CompositeView
        id="id_qr_code_loading_container"
        width="{sdp(240)}"
        height="{sdp(240)}"
        layout="{layout.online_qr_code_loading}">
        <LoadingBM
            id="id_loading"
            sizeStyle="{enum.LoadingBM.SizeStyle.M}"/>
        <TextView
            id="id_network_tip"
            width="{sdp(200)}"
            text="{string.CP_QRCODE_LOAD_ERROR}"
            multiLine="true"
            maxLineCount="2"
            propertySetName="extend/hdt/FontBody4"
            align="{enum.TextView.Align.Center}"/>
    </CompositeView>

    <OnlineImageViewBM
        id="id_qr_code_icon"
        width="{config.ONLINE_QR_CODE_WIDTH}"
        height="{config.ONLINE_QR_CODE_HEIGHT}"
        asynchronous="false"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"/>

    <CompositeView
        id="id_qr_code_tips_container"
        width="{sdp(400)}"
        height="{sdp(120)}"
        layout="{layout.online_qr_code_tips}">
        <TextView
            id="id_qr_code_tips"
            text="{string.CP_QRCODE_TIPS}"
            align="{enum.TextView.Align.Center}"
            propertySetName="extend/hdt/FontBody2"/>
    </CompositeView>
</CompositeView>
