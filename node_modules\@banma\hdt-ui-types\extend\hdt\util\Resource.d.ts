import Theme = require("yunos/ui/theme/Theme");
declare class Resource {
    static getInstance(theme: Theme): Resource;
    private static _instance;
    private _cafResource;
    private _theme;
    private constructor();
    getValue(valueKey: string, fileName: string): Object;
    getValueByTag(valueKey: string, fileName: string): Object;
    getConfig(configKey: string): Object;
    getColor(colorKey: string): string;
    getString(stringKey: string, data?: (string | number)[]): string;
    getImageSrc(filePath: string): string;
}
export = Resource;
