<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    layout="{layout.dlna}"
    propertySetName="dlna">

    <NavigationBar id="id_nav" title="{string.DLNA}"/>

    <ScrollableView
        id="id_container"
        orientation="{enum.ScrollableView.Orientation.Horizontal}"
        overScroll="false"
        scrollBarCustomized="true"
        horizontalFadingEdgeEnabled="true"
        layout="{layout.dlna_container}">
        <TextView
            id="id_title"
            text="{string.DLNA_TIPS_TITLE}"
            width="{config.DLNA_TITLE_WIDTH}"
            height="{config.DLNA_TITLE_HEIGHT}"
            propertySetName="extend/hdt/FontHeadline"/>

        <TextView
            id="id_lan"
            text="{string.DLNA_TIPS_LAN}"
            width="{config.DLNA_LAN_WIDTH}"
            height="{config.DLNA_LAN_HEIGHT}"
            multiLine="true"
            propertySetName="extend/hdt/FontBody2"/>

        <TextView
            id="id_lan_secondary"
            text="{string.DLNA_TIPS_LAN_SECONDARY}"
            width="{config.DLNA_LAN_SEC_WIDTH}"
            height="{config.DLNA_LAN_SEC_HEIGHT}"
            multiLine="true"
            propertySetName="extend/hdt/FontBody4"/>

        <ImageView
            id="id_lan_image"
            width="{config.DLNA_LAN_IMG_WIDTH}"
            height="{config.DLNA_LAN_IMG_HEIGHT}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"/>

        <TextView
            id="id_way"
            text="{string.DLNA_TIPS_WAY}"
            width="{config.DLNA_WAY_WIDTH}"
            height="{config.DLNA_WAY_HEIGHT}"
            multiLine="true"
            propertySetName="extend/hdt/FontBody2"/>

        <TextView
            id="id_way_secondary"
            text="{string.DLNA_TIPS_WAY_SECONDARY}"
            width="{config.DLNA_WAY_SEC_WIDTH}"
            height="{config.DLNA_WAY_SEC_HEIGHT}"
            multiLine="true"
            propertySetName="extend/hdt/FontBody4"/>

        <ImageView
            id="id_way_image"
            width="{config.DLNA_WAY_IMG_WIDTH}"
            height="{config.DLNA_WAY_IMG_HEIGHT}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"/>

        <TextView
            id="id_traffic"
            text="{string.DLNA_TIPS_TRAFFIC}"
            width="{config.DLNA_TRA_WIDTH}"
            height="{config.DLNA_TRA_HEIGHT}"
            multiLine="true"
            propertySetName="extend/hdt/FontBody2"/>

        <TextView
            id="id_traffic_secondary"
            text="{string.DLNA_TIPS_TRAFFIC_SECONDARY}"
            width="{config.DLNA_TRA_SEC_WIDTH}"
            height="{config.DLNA_TRA_SEC_HEIGHT}"
            multiLine="true"
            propertySetName="extend/hdt/FontBody4"/>
    </ScrollableView>

    <ScrollBar
        id="id_scrollbar"
        height="{config.SCROLL_BAR_SIZE}"
        autoHidden="true"/>
</CompositeView>
