import YObject = require("../core/YObject");
/**
 * <p>Contains common settings for multiple WebView.</p>
 * <p>Contains settings and history shared by all WebEngineView.</p>
 * <p>A default profile is built-in that all web pages not specifically created
 * with another profile belongs to. If you change the WebViewProfile in one WebView,
 * the WebViewProfile will be avalible to others WebViews.</p>
 * <p>A typical use case is a dedicated profile for a 'private browsing' mode. For exmaple:</p>
 *     <pre>
 *         // Obtain WebViewProfile object, change the offTheRecord from default false to true to
 *         enter the 'private browsing' mode.
 *         let WebViewProfile = webView.profile;  // webView is a WebView object;
 *         console.log("default value of profile offTheRecord is :", WebViewProfile.offTheRecord);
 *         WebViewProfile.offTheRecord = true;
 *         // Create another WebView and the offTheRecord of WebViewProfile is true.
 *         let anotherWebView = new WebView();
 *         console.log("All WebViews share the same profile, offTheRecord is:", anotherWebView.profile.offTheRecord);
 *     </pre>
 *
 * @extends yunos.core.YObject
 * @memberof yunos.web
 * @public
 * @since 2
 */
declare class WebViewProfile extends YObject {
    private _impl;
    private httpCacheTypeArray;
    private persistentCookiesPolicyArray;
    /**
     * <p>Create a WebViewProfile.</p>
     * @private
     */
    private constructor(nativeProfile: Object);
    /**
     * <p>Defines storageName of WebViewProfile. The storage name is used to give each profile that uses the disk separate
     * subdirectories for persistent data and cache.</p>
     * @name yunos.web.WebViewProfile#storageName
     * @type {string}
     * @public
     * @since 2
     */
    public storageName: string;
    /**
     * <p>Defines OffTheRecord of WebViewProfile. This will force cookies and HTTP cache to be in memory, but also force all
     * other normally persistent data to be stored in memory. An off-the-record profile leaves no record on the local machine,
     * and has no persistent data or cache. Thus, the HTTP cache can only be in memory and the cookies can only be non-persistent. </p>
     * @name yunos.web.WebViewProfile#offTheRecord
     * @type {boolean}
     * @public
     * @since 2
     */
    public offTheRecord: boolean;
    /**
     * <p>Defines path used to store  persistent data for the browser and web content. Persistent data includes persistent cookies,
     * HTML5 local storage, and visited links. If path is set to the null string, the default path is restored.</p>
     * @name yunos.web.WebViewProfile#persistentStoragePath
     * @type {string}
     * @public
     * @since 2
     */
    public persistentStoragePath: string;
    /**
     * <p>Defines the path used for caches.</p>
     * @name yunos.web.WebViewProfile#cachePath
     * @type {string}
     * @public
     * @since 2
     */
    public cachePath: string;
    /**
     * <p>Defines user-agent sent with HTTP to identify the WebView.</p>
     * @name yunos.web.WebViewProfile#httpUserAgent
     * @type {string}
     * @private
     */
    private httpUserAgent: string;
    /**
     * <p>Defines the HTTP cache type.This enum describes the HTTP cache type: memoryHttpCache, diskHttpCache.
     * "memoryHttpCache" means use an in-memory cache. This is the only setting possible if off-the-record is
     * set or no cache path is available. "diskHttpCache" means use a disk cache which is the default.</p>
     * @name yunos.web.WebViewProfile#httpCacheType
     * @type {string}
     * @public
     * @since 2
     */
    public httpCacheType: string;
    /**
     * <p>Defines whether session and persistent cookies are saved to and restored from memory or disk.
     * This enum describes the kinds of policy: noPersistentCookies, allowPersistentCookies, forcePersistentCookies.
     * "noPersistentCookies" means both session and persistent cookies are stored in memory. This is the only
     * setting possible if off-the-record is set or no persistent data path is available. "allowPersistentCookies"
     * means cookies marked persistent are saved to and restored from disk, whereas session cookies are only stored
     * to disk for crash recovery. This is the default setting. "forcePersistentCookies" means both session and persistent
     * cookies are saved to and restored from disk.</p>
     * @name yunos.web.WebViewProfile#persistentCookiesPolicy
     * @type {string}
     * @public
     * @since 2
     */
    public persistentCookiesPolicy: string;
    /**
     * <p>Defines the maximum size of the HTTP cache to maxSize bytes. Setting it to 0 means the
     * size will be controlled automatically by WebView. </p>
     * @name yunos.web.WebViewProfile#httpCacheMaximumSize
     * @type {number}
     * @public
     * @since 2
     */
    public httpCacheMaximumSize: number;
    /**
     * <p>Defines delete all localstorage data of all WebViews.</p>
     * @public
     * @since 2
     */
    public deleteAllLocalStorageData(): void;
}
export = WebViewProfile;
