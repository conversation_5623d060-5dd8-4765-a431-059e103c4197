export declare class WebGLContext {
    constructor(v1: string, v2: number, v3: Object);
    destroy(): void;
    scissor(x: number, y: number, width: number, height: number): void;
    viewport(x: number, y: number, width: number, height: number): void;
    activeTexture(texture : number): void;
    blendColor(red: number, green: number, blue: number, alpha: number): void;
    blendEquation(mode: number): void;
    blendEquationSeparate(modeRGB: number, modeAlpha: number): void;
    blendFunc(sfactor: number, dfactor: number): void;
    blendFuncSeparate(srcRGB: number, dstRGB: number, srcAlpha: number, dstAlpha: number): void;
    clearColor(red: number, green: number, blue: number, alpha: number): void;
    clearDepth(depth: number): void;
    clearStencil(s: number): void;
    colorMask(red: number, green: number, blue: number, alpha: number): void;
    cullFace(mode: number): void;
    depthFunc(func: number): void;
    depthMask(flag: boolean): void;
    depthRange(zNear: number, zFar: number): void;
    disable(cap: number): void;
    enable(cap: number): void;
    frontFace(mode: number): void;
    getParameter(pname: number): Object;
    getError(): number;
    hint(target: number, mode: number): void;
    isEnabled(cap: number): boolean;
    lineWidth(width: number): void;
    pixelStorei(pname: number, param: number): void;
    polygonOffset(factor: number, units: number): void;
    sampleCoverage(value: number, invert: boolean): void;
    stencilFunc(func: number, ref: number, mask: number): void;
    stencilFuncSeparate(face: number, func: number, ref: number, mask: number): void;
    stencilMask(mask: number): void;
    stencilMaskSeparate(face: number, mask: number): void;
    stencilOp(fail: number, zfail: number, zpass: number): void;
    stencilOpSeparate(face: number, fail: number, zfail: number, zpass: number): void;
    attachShader(program: object, shader: object): void;
    bindAttribLocation(program: object, index: number, name: string): void;
    compileShader(shader: object): void;
    createProgram(): object;
    createShader(type: number): Object;
    deleteProgram(program: object): void;
    deleteShader(shader: object): void;
    detachShader(program: object, shader: object): void;
    getAttachShaders(program: object): object[];
    getProgramParameter(program: object, pname: number): number;
    getProgramInfoLog(program: object): string;
    getShaderParameter(shader: object, pname: number): number;
    getShaderPrecisionFormat(shaderType: number, precisionType: number): object;
    getShaderInfoLog(shader: object): string;
    getShaderSource(shader: object): string;
    isProgram(program: object): boolean;
    isShader(shader: object): boolean;
    linkProgram(program: object): void;
    shaderSource(shader: object, source: string): void;
    useProgram(program: object): void;
    validateProgram(program: object): void;
    disableVertexAttribArray(index: number): void;
    enableVertexAttribArray(index: number): void;
    getActiveAttrib(program: object, index: number): {name: string, size:number, type: number};
    getActiveUniform(program: object, index: number): {name: string, size:number, type: number};
    getAttribLocation(program: object, name: string): number;
    getUniform(program: object, location: object): Object;
    getUniformLocation(program: object, name: string): object;
    getVertexAttrib(index: number, pname: number): Object;
    getVertexAttribOffset(index: number, pname: number): number;
    uniform1f(location: object, x: number): void;
    uniform2f(location: object, x: number, y: number): void;
    uniform3f(location: object, x: number, y: number, z: number): void;
    uniform4f(location: object, x: number, y: number, z: number, w: number): void;
    uniform1fv(location: object, v: Object): void;
    uniform2fv(location: object, v: Object): void;
    uniform3fv(location: object, v: Object): void;
    uniform4fv(location: object, v: Object): void;
    uniform1i(location: object, x: Object): void;
    uniform2i(location: object, x: number, y: number): void;
    uniform3i(location: object, x: number, y: number, z: number): void;
    uniform4i(location: object, x: number, y: number, z: number, w: number): void;
    uniform1iv(location: object, v: Object): void;
    uniform2iv(location: object, v: Object): void;
    uniform3iv(location: object, v: Object): void;
    uniform4iv(location: object, v: Object): void;
    uniformMatrix2fv(location: object, transpose: Object, v: Object): void;
    uniformMatrix3fv(location: object, transpose: Object, v: Object): void;
    uniformMatrix4fv(location: object, transpose: Object, v: Object): void;
    vertexAttrib1f(index: number, x: number): void;
    vertexAttrib2f(index: number, x: number, y: number): void;
    vertexAttrib3f(index: number, x: number, y: number, z: number): void;
    vertexAttrib4f(index: number, x: number, y: number, z: number, w: number): void;
    vertexAttrib1fv(index: number, v: Object): void;
    vertexAttrib2fv(index: number, v: Object): void;
    vertexAttrib3fv(index: number, v: Object): void;
    vertexAttrib4fv(index: number, v: Object): void;
    vertexAttribPointer(index: number, size: number, type: number, normalized: number, stride: number, offset: number): void;
    bindBuffer(target: number, buffer: object): void;
    bufferData(target: number, data: Object, usage: number): void;
    bufferSubData(target: number, offset: number, srcData: Object): void;
    createBuffer(): object;
    deleteBuffer(buffer: object): void;
    getBufferParameter(target: number, pname: number): number;
    isBuffer(buffer: object): boolean;
    bindFramebuffer(target: number, framebuffer: object): void;
    checkFramebufferStatus(framebuffer: number): number;
    createFramebuffer(): object;
    deleteFramebuffer(framebuffer: object): void;
    framebufferRenderbuffer(target: number, attachment: number, renderbuffertarget: number, renderbuffer: object): void;
    framebufferTexture2D(target: number, attachment: number, textarget: number, texture: object, level: number): void;
    getFramebufferAttachmentParameter(target: number, attachment: number, pname: number): Object;
    isFramebuffer(framebuffer: object): boolean;
    readPixels(x: number, y: number, width: number, height: number, format: number, type: number, pixels: Object): void;
    bindRenderbuffer(target: number, renderbuffer: object): void;
    createRenderbuffer(): object;
    deleteRenderbuffer(renderbuffer: object): void;
    getRenderbufferParameter(target: number, pname: number): number;
    isRenderbuffer(renderbuffer: object): boolean;
    renderbufferStorage(target: number, internalformat: number, width: number, height: number): void;
    bindTexture(target: number, texture: object): void;
    compressedTexImage2D(target: number, level: number, internalformat: number, width: number, height: number, border: number, pixels: Object): void;
    compressedTexSubImage2D(target: number, level: number, xoffset: number, yoffset: number, width: number, height: number, format: number, pixels: Object): void;
    copyTexImage2D(target: number, level: number, internalformat: number, x: number, y: number, width: number, height: number, border: number): void;
    copyTexSubImage2D(target: number, level: number, xoffset: number, yoffset: number, x: number, y: number, width: number, height: number): void;
    createTexture(): object;
    deleteTexture(texture: object): void;
    generateMipmap(target: number): void;
    getTexParameter(target: number, pname: number): number | boolean;
    isTexture(texture: object): boolean;
    texImage2D(target: number, level: number, internalformat: number, width: number, height: number, border: Object, format?: number, type?: number, pixels?: Object): void;
    texSubImage2D(target: number, level: number, xoffset: number, yoffset: number, width: number, height: number, format: number, type: number, pixels: Object): void;
    texParameterf(target: number, pname: number, param: number): void;
    texParameteri(target: number, pname: number, param: number): void;
    clear(mask: number): void;
    drawArrays(mode: number, first: number, count: number): void;
    drawElements(mode: number, count: number, type: number, offset: number): void;
    finish(): void;
    flush(): void;
    swapBuffer(): void;
    querySurface(attribute: number): number;
    getExtension(name: string): object;
    getSupportedExtensions(): Array<object>;
    makeCurrent(): void;
    registrationEvent(e: (eventType: number) => void): void;
}
