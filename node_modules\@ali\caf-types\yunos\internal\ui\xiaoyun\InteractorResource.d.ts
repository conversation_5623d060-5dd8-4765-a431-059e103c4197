import EventEmitter = require("yunos/core/EventEmitter");
import Page = require("yunos/page/Page");
interface IServiceProxy {
    registerApp: (domain: string, callback: Object) => void;
    unsubscribeEvent: (handle: Object) => void;
    subscribeEvent: (event: string, callback: Object) => Object;
    closeConnection: () => void;
    destroy: () => void;
}
/**
 * <p>InteractorResource is used for voice interaction, to get the nlg result</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.speech
 * @friend
 */
declare class InteractorResource extends EventEmitter {
    _context: Page;
    _ttsMap: Map<string, Object>;
    _mService: IServiceProxy;
    _handle: Object;
    static sInstanceMap: Map<Page, InteractorResource>;
    constructor(context: Page);
    /**
     * <p>Destroy the InteractorResource object.</p>
     * @override
     * @friend
     */
    destroy(): void;
    /**
     * <p>Get singleton InteractorResource instance.</p>
     * @param {yunos.page.Page} context - the page instance.
     * @return {yunos.speech.InteractorResource} the instance of InteractorResource.
     * @friend
     */
    static getInstance(context: Page): InteractorResource;
    /**
     * <p>Release singleton instance of InteractorResource.</p>
     * @param {yunos.page.Page} context - the page instance.
     * @friend
     */
    static releaseInstance(context: Page): void;
    _clearHashMap(): void;
    /**
     * <p>Get the nlg result for scene.</p>
     * @param {string} [domain] - the domain for the scene.
     * @param {string} [intent] - the intent for the scene.
     * @param {string} [sceneId] - the sceneId for the scene.
     * @return {Object} [data] - nlg data.
     * @return {array} [data.spoken] - the spoken text array.
     * @return {array} [data.written] - the written text array.
     * @return {array} [data.tip] - the tip text array.
     * @friend
     */
    getNlg(domain: string, intent: string, sceneId?: string): Object;
    _bindInteratorService(): void;
}
export = InteractorResource;
