import Drawable = require("./Drawable");
import Context = require("../Context");
/**
 * CanvasDrawable is a abstract class which can customize view's canvas.
 *
 * @extends yunos.graphics.drawable.Drawable
 * @memberof yunos.graphics.drawable
 * @public
 * @abstract
 * @since 6
 */
declare abstract class CanvasDrawable extends Drawable {
    /**
     * start drawing process, [onDraw] will be called
     *
     * @public
     * @since 6
     * @throws {Error} If is not bind to a view
     * @override
     */
    public startDraw(): void;
    /**
     * stop drawing process
     *
     * @public
     * @since 6
     * @override
     */
    public stopDraw(): void;
    /**
     * implement this function to customize canvas drawing.
     *
     * @public
     * @abstract
     * @param {yunos.graphics.Context} ctx Canvas Context
     * @return {boolean}  true do draw next frame, false to stop.
     * @since 6
     */
    public abstract onDraw(ctx: Context): boolean;
}
export = CanvasDrawable;
