import YObject = require("../core/YObject");
/**
 * <p>Point that represents a point with x and y-axis value.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 1
 */
declare class Point extends YObject {
    /**
     * <p>The x-axis value.</p>
     * @name yunos.graphics.Point#x
     * @type {number}
     * @public
     * @since 1
     */
    public x: number;
    /**
     * <p>The y-axis value.</p>
     * @name yunos.graphics.Point#y
     * @type {number}
     * @public
     * @since 1
     */
    public y: number;
    /**
     * <p>Constructor that create a point.</p>
     * @param {number} x - the x-axis value.
     * @param {number} y - the y-axis value.
     * @public
     * @since 1
     */
    public constructor(x: number, y: number);
    /**
     * <p>Destructor that destroy this point.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Assign the x and y value of this point.</p>
     * @param {number} x - the point's x-axis.
     * @param {number} y - the point's y-axis.
     * @public
     * @since 1
     */
    public assign(x: number, y: number): this;
    /**
     * <p>Offset the point's coordinates by the specified x and y value.</p>
     * @param {number} x - the x value.
     * @param {number} y - the y value.
     * @public
     * @since 1
     */
    public offset(dx: number, dy: number): void;
    /**
     * <p>Clone a new point from this point.</p>
     * @return {yunos.graphics.Point} a new point that has the same x and y value with this point.
     * @override
     * @public
     * @since 1
     */
    public clone(): Point;
    /**
     * <p>Check whether this point equals a specified point.</p>
     * @param {yunos.graphics.Point} point - the specified point.
     * @return {boolean} true means equal, otherwise false.
     * @override
     * @public
     * @since 1
     */
    public equals(point: YObject): boolean;
    /**
     * <p>Returns a human-readable point string.</p>
     * @return {string} the point string.
     * @override
     * @public
     * @since 1
     */
    public toString(): string;
}
export = Point;
