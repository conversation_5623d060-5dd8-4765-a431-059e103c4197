import YObject = require("../core/YObject");
/**
 * <p>Location based fence signal</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @see [FenceClient]{@link yunos.fenceclient.FenceClient}
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class FenceSignal extends YObject {
    private _signalId;
    private _businessId;
    private _groupId;
    private _fenceId;
    private _action;
    private _name;
    private _address;
    private _city;
    private _province;
    private _poiId;
    private _coordinates;
    private _data;
    private _dir;
    private _init;
    private _lat;
    private _lng;
    private _eventLat;
    private _eventLng;
    private _radius;
    private _timestamp;
    private _type;
    public constructor(datas?: Object);
    /**
     * <p>Identity of signal, Please contact business administrator to get signal id for a business</p>
     * @name yunos.fenceclient.FenceSignal#signalId
     * @type {string}
     * @public
     * @since 5
     */
    public signalId: string;
    /**
     * <p>businessId of signal.</p>
     * @name yunos.fenceclient.FenceSignal#businessId
     * @type {string}
     * @public
     * @since 5
     */
    public businessId: string;
    /**
     * <p>groupId of signal.</p>
     * @name yunos.fenceclient.FenceSignal#groupId
     * @type {string}
     * @public
     * @since 5
     */
    public groupId: string;
    /**
     * <p>Identify a fence in a fence group.</p>
     * @name yunos.fenceclient.FenceSignal#fenceId
     * @type {string}
     * @public
     * @since 5
     */
    public fenceId: string;
    /**
     * <p>The action actually triggered by the fence,
     * it may be one of the following values.
     * enter
     * exit
     * stay
     * </p>
     * @name yunos.fenceclient.FenceSignal#action
     * @type {string}
     * @public
     * @since 5
     */
    public action: string;
    /**
     * <p>Type of fence.</p>
     * @example
     * 0-circle,1-polygon,2-line
     * @name yunos.fenceclient.FenceSignal#type
     * @type {number}
     * @public
     * @since 5
     */
    public type: number;
    /**
     * <p>When fence is of circle type, radius is circle radius in meters</p>
     * @name yunos.fenceclient.FenceSignal#radius
     * @type {number}
     * @public
     * @since 5
     */
    public radius: number;
    /**
     * <p>timestamp when fence event triggered.</p>
     * @name yunos.fenceclient.FenceSignal#timestamp
     * @type {number}
     * @public
     * @since 5
     */
    public timestamp: number;
    /**
     * <p>PoiId of fence, if the fence is created from a known POI</p>
     * @name yunos.fenceclient.FenceSignal#poiId
     * @type {string}
     * @public
     * @since 5
     */
    public poiId: string;
    /**
     * <p>Address of fence.</p>
     * @name yunos.fenceclient.FenceSignal#address
     * @type {string}
     * @public
     * @since 5
     */
    public address: string;
    /**
     * <p>Name of fence.</p>
     * @name yunos.fenceclient.FenceSignal#name
     * @type {string}
     * @public
     * @since 5
     */
    public name: string;
    /**
     * <p>Province of fence.</p>
     * @name yunos.fenceclient.FenceSignal#province
     * @type {string}
     * @public
     * @since 5
     */
    public province: string;
    /**
     * <p>Coordinates, when type=0, it is a single coordinate,
     * indicating the center of the circle;
     * when fenceType=1, it is a polygon boundary,
     * which needs to meet the polygon limit,
     * and the start point and end point are closed;
     * when fenceType=2, it is a line type and needs to meet the number of coordinates > 1</p>
     * @example
     * circle: "116.483972 40.012197", "longitude latitude"
     * polygon: "116.483972 40.012752,116.485030 40.012197,116.485033 40.012150,116.484227 40.011310,116.483346 40.011859,116.483348 40.011916,116.483854 40.012733,116.483972 40.012752"
     * line: "116.483972 40.012752,116.485030 40.012197,116.485033 40.012150"
     * @name yunos.fenceclient.FenceSignal#coordinates
     * @type {string}
     * @public
     * @since 5
     */
    public coordinates: string;
    /**
     * <p>City of fence.</p>
     * @name yunos.fenceclient.FenceSignal#city
     * @type {string}
     * @public
     * @since 5
     */
    public city: string;
    /**
     * <p>Business data, json format string,
     * will be transparently transmitted to the subscriber when the fence is notified</p>
     * @name yunos.fenceclient.FenceSignal#data
     * @type {string}
     * @public
     * @since 5
     */
    public data: string;
    /**
     * <p>dir of signal.</p>
     * @name yunos.fenceclient.FenceSignal#dir
     * @type {number}
     * @public
     * @since 5
     */
    public dir: number;
    /**
     * <p>Indicate whether this signal is an immediatelly triggered event, or a stored event.</p>
     * @name yunos.fenceclient.FenceSignal#init
     * @type {boolean}
     * @public
     * @since 5
     */
    public init: boolean;
    /**
     * <p>latitude of signal, when fence is circle, this value is latitude of center,
     * when fence is polygon or line, this value is the latitude first coordinates in series.</p>
     * @name yunos.fenceclient.FenceSignal#lat
     * @type {number}
     * @public
     * @since 5
     */
    public lat: number;
    /**
     * <p>longitude of signal, when fence is circle, this value is longitude of center,
     * when fence is polygon or line, this value is the longitude first coordinates in series.</p>
     * @name yunos.fenceclient.FenceSignal#lng
     * @type {number}
     * @public
     * @since 5
     */
    public lng: number;
    /**
     * <p>latitude of coordinates where the signal event occurs.</p>
     * @name yunos.fenceclient.FenceSignal#eventLat
     * @type {number}
     * @public
     * @since 5
     */
    public eventLat: number;
    /**
     * <p>longitude of coordinates where the signal event occurs.</p>
     * @name yunos.fenceclient.FenceSignal#eventLng
     * @type {number}
     * @public
     * @since 5
     */
    public eventLng: number;
}
export = FenceSignal;
