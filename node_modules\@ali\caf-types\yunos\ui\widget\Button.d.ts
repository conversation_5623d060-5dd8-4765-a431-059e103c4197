import TextView = require("../view/TextView");
import TouchEvent = require("../event/TouchEvent");
import { MultiState } from "../util/TypeHelper";
interface IStyle {
    noneStyleBackgroundDisabledSecondary: string;
    noneStyleBackgroundDisabled: string;
    noneStyleBackgroundSecondary: string;
    noneStyleTextColorDisabledSecondary: string;
    noneStyleTextColorFocusedSecondary: string;
    noneStyleTextColorPressedSecondary: string;
    noneStyleTextColorSecondary: string;
    noneStyleTextColorDisabled: string;
    noneStyleBackgroundFocusedSecondary: string;
    noneStyleBackgroundPressedSecondary: string;
    noneStyleTextColorFocused: string;
    noneStyleTextColorPressed: string;
    defaultOriginY: number | string;
    defaultOriginX: number | string;
    defaultScalePressed: number;
    defaultMultistateTransitionEnable: boolean;
    defaultFontWeight: number;
    blockStyleBackground: string;
    blockStyleBackgroundPressed: string;
    blockStyleBackgroundFocused: string;
    blockStyleTextColor: string;
    borderStyleBackground: string;
    borderStyleBackgroundPressed: string;
    borderStyleBackgroundFocused: string;
    borderStyleTextColor: string;
    borderStyleBorderColor: string;
    borderStyleBorderColorPressed: string;
    borderStyleBorderColorFocused: string;
    noneStyleBackground: string;
    noneStyleTextColor: string;
    borderRadiusSmall: number;
    borderRadiusNormal: number;
    borderRadiusLarge: number;
    buttonHeightSmall: number;
    buttonHeightNormal: number;
    buttonHeightLarge: number;
    fontSizeSmall: string | number;
    fontSizeNormal: string;
    fontSizeLarge: string | number;
    paddingLeft: number;
    paddingRight: number;
    disabledOpacity: number;
    pressedColorOverlay: string;
    noneStyleBackgroundPressed: string;
    noneStyleBackgroundFocused: string;
    sizeType: number;
    voiceEnabled: boolean;
    canvasDrawablePath: string;
}
/**
 * <p>Button is a clickable widget which consists of text.</p>
 * <p>There are two appearance of your button, Button.Style.Block is a borderless button and the background color will fill the whole button block.
 * Button.Style.Border is a button with border and the middle region is always white, the border color is the background color you set.</p>
 * <p>When the user clicks a button, the button widget will emit an "tap" event.</p>
 * @example
 * const largeBorderDisabled = Button.create({
     text: "大号有边框",
     sizeType: Button.SizeType.Large,
     styleType: Button.StyleType.Border,
     enabled: false
   });
 *
 * largeBorderDisabled.on("tap", () => {});
 * const smallBlock = Button.create({
     text: "小号无边框",
     sizeType: Button.SizeType.Small
    });
 * smallBlock.on("tap", () => {});
 *
 * @extends yunos.ui.view.TextView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class Button extends TextView {
    private _backgroundFocused;
    private _backgroundPressed;
    private _backgroundDisabled;
    private _fontSizeLarge;
    private _borderRadiusLarge;
    private _buttonHeightLarge;
    private _borderRadiusNormal;
    private _buttonHeightNormal;
    private _fontSizeSmall;
    private _noneStyleTextColor;
    private _defaultBorderWidth;
    private _buttonHeightSmall;
    private _mBorderRadius;
    private _borderStyleTextColor;
    private _mButtonHeight;
    private _defaultFontSize;
    private _myMulitState;
    private _borderStyleBackground;
    private _borderStyleBackgroundPressed;
    private _borderStyleBackgroundFocused;
    private _pressed;
    private _onTouchEndFunc;
    private _sizeType;
    private _styleType;
    private _blockStyleBackground;
    private _blockStyleTextColor;
    private _borderStyleBorderColor;
    private _pressedColorOverlay;
    private _disabledOpacity;
    private _noneStyleBackground;
    private _noneStyleBackgroundSecondary;
    private _borderRadiusSmall;
    private _fontSizeNormal;
    private _blockStyleBackgroundPressed;
    private _blockStyleBackgroundFocused;
    private _borderStyleBorderColorPressed;
    private _borderStyleBorderColorFocused;
    private _defaultFontWeight;
    private _defaultMultistateTransitionEnable;
    private _defaultScalePressed;
    private _defaultOriginX;
    private _defaultOriginY;
    private _colorType;
    private _noneStyleTextColorPressed;
    private _noneStyleTextColorFocused;
    private _backgroundPressedSecondary;
    private _backgroundFocusedSecondary;
    private _backgroundDisabledSecondary;
    private _noneStyleTextColorDisabled;
    private _noneStyleTextColorSecondary;
    private _noneStyleTextColorPressedSecondary;
    private _noneStyleTextColorFocusedSecondary;
    private _noneStyleTextColorDisabledSecondary;
    private _canvasDrawablePath: string;
    /**
     * <p>This property holds color of button.</p>
     * <p>If styleType is Button.StyleType.Block the buttonColor is color of the whole button and textColor is white.</p>
     * <p>If styleType is Button.StyleType.Border the buttonColor is color of button border and text.</p>
     * <p>If styleType is Button.StyleType.None the buttonColor is color of text.</p>
     * @name yunos.ui.widget.Button#buttonColor
     * @type {string}
     * @public
     * @since 1
     */
    public buttonColor: string;
    /**
     * <p>Indicates the properties of the button in the multiple state.</p>
     * <p>Note that it is only available when the styleType is Button.StyleType.None.</p>
     * @name yunos.ui.widget.Button#multiState
     * @type {Object}
     * @override
     * @public
     * @since 1
     */
    public multiState: MultiState;
    /**
     * <p>This property holds style of button, there are 2 types: Block Style and Border Style. </p>
     * <p>Button.Style.Block is a borderless button and the buttonColor will fill the whole button block. </p>
     * <p>Button.Style.Border is a button with border and the middle region is always white, the border color is the buttonColor you set. </p>
     * @name yunos.ui.widget.Button#styleType
     * @type {yunos.ui.widget.Button.StyleType}
     * @public
     * @since 1
     */
    public styleType: number;
    /**
     * <p>This property holds size of button, there 3 types: Button.SizeType.Small, Button.SizeType.Normal and Button.SizeType.Large. </p>
     * @name yunos.ui.widget.Button#sizeType
     * @type {yunos.ui.widget.Button.SizeType}
     * @public
     * @since 1
     */
    public sizeType: number;
    /**
     * <p>This property holds color type of button.</p>
     * <p>Only for None style button in this version</p>
     * @name yunos.ui.widget.Button#colorType
     * @type {yunos.ui.widget.Button.ColorType}
     * @public
     * @since 5
     */
    public colorType: number;
    /**
     * the background color when you press the button.
     * @name Button#backgroundPressed
     * @type {string}
     * @private
     */
    private backgroundPressed: string;
    /**
     * Defines the width of the Button, in pixels. And set the textView._defaultWidth as false.
     * If set width by user, the paddingLeft and paddingRight will be 0.
     * @name yunos.ui.view.TextView#width
     * @type {number}
     * @override
     * @public
     * @since 3
     *
     */
    public width: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.Button#defaultStyleName
     * @type {string}
     * @default "Button"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    /**
     * Defined current button handle voice event or not, if true, auto register text.
     * @name yunos.ui.widget.Button#voiceEnabled
     * @type {boolean}
     * @public
     * @override
     * @since 5
     *
     */
    public voiceEnabled: boolean;
    /**
     * <p>voiceViewType.</p>
     * @override
     * @protected
     * @since 6
     */
    protected readonly voiceViewType: string;
    /**
     * The plain-text content that this text view is to display.
     * @name yunos.ui.view.TextView#text
     * @type {string}
     * @fires yunos.ui.view.TextView#textchange
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @override
     * @since 6
     */
    public text: string;
    private setMultiState(): void;
    private getPressedColor(value: string, colorverlay?: string): string;
    private onTouchStart(e?: TouchEvent): void;
    private onTouchEnd(e?: TouchEvent): void;
    private onButtonPropertyChange(property: string, oldValue: string, newValue: string): void;
    private _validCanvasDrawbleFromStyle(): void;
    /**
     * <p>Enum for Button sizeType. It will only limit the height of the button.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly SizeType: {
        /**
         * The small size of button, which height is 25dp
         * @public
         * @since 1
         */
        Small: int;
        /**
         * The noraml size of button, which height is 32dp
         * @public
         * @since 1
         */
        Normal: int;
        /**
         * The large size of button, which height is 40dp
         * @public
         * @since 1
         */
        Large: int;
    };
    /**
     * <p>Enum for Button style type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly StyleType: {
        /**
         * Borderless button and the background color will fill the whole button block
         * @public
         * @since 1
         */
        Block: int;
        /**
         * A button with border and the middle region is always white, the border color is the background color you set.
         * @public
         * @since 1
         */
        Border: int;
        /**
         * No style is used for button
         * @public
         * @since 1
         */
        None: int;
    };
    /**
     * <p>Enum for Button color type.</p>
     * <p>Only for None style button in this version</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly ColorType: {
        /**
         * Primary Color.
         * @public
         * @since 5
         */
        Primary: int;
        /**
         * Secondary Color.
         * @public
         * @since 5
         */
        Secondary: int;
    };
}
export = Button;
