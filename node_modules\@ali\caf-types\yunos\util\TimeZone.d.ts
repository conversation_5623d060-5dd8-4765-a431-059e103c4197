import YObject = require("../core/YObject");
import Locale = require("./Locale");
/**
 * TimeZone class for obtaining specific timezone
 * @memberof yunos.util
 * @extends yunos.core.YObject
 * @public
 * @since 2
 * @relyon YUNOS_SYSCAP_SYSTIME
 */
declare class TimeZone extends YObject {
    private _standardOffset;
    private _localizedStandardNames;
    private _standardAbbr;
    private _name;
    private _transitions;
    private static _cachedMap;
    /**
     * Do not construct this class directly. Use static methods to obtain instances instead.
     * @public
     * @since 2
     */
    public constructor();
    /**
     * Get a full list of timezone available names.
     * @return {string[]} an array of string of all available names of timezones
     * @public
     * @since 2
     */
    public static getAvailableNames(): string[];
    /**
     * Get a full list of timezone available abbreviations.
     * @return {string[]} an array of strings of all the standard abbreviations of all available timezones
     * @public
     * @since 2
     */
    public static getAvailableStandardAbbrs(): string[];
    /**
     * Get specific timezone with name.
     * You can get a full list of available timezones by calling TimeZone.getAvailableNames().
     * Returns null if the timezone with the given name is not in the database.
     * @param {string} name - the name of the timezone
     * @return {yunos.util.TimeZone|null} the TimeZone instance with the given name
     * @throws {TypeError} If the param is not a string.
     * @public
     * @since 2
     */
    public static getWithName(name: string): TimeZone;
    /**
     * Get specific time zone with standard abbreviation.
     * Returns null if the given standard abbreviation is not found in the database.
     * You can get a full list of abbreviations by calling TimeZone.getAvailableAbbrs().
     * @param {string} standardAbbr - the standard abbreviation for the timezone.
     * @return {yunos.util.TimeZone|null} the TimeZone object with the given standard abbreviation
     * @throws {TypeError} If the param is not a string.
     * @public
     * @since 2
     */
    public static getWithStandardAbbr(standardAbbr: string): TimeZone;
    /**
     * Create a custom TimeZone object with given offset from GMT in milliseconds.
     * Positive numbers for timezone behind GMT, negative numbers for timezone ahead of GMT.
     * @param {number} msOffset - offset in milliseconds from GMT
     * @return {yunos.util.TimeZone} a TimeZone object generated with the given offset from GMT
     * @throws {TypeError} If the param is not a number.
     * @public
     * @since 2
     */
    public static createWithOffset(msOffset: number): TimeZone;
    /**
     * Get the default timezone according to system settings.
     * Note this object does not update automatically when the user changes the system preference of timezone
     * @return {yunos.util.TimeZone|null} the default locale associated with user settings
     * @public
     * @since 2
     */
    public static getDefault(): TimeZone;
    private initWithOptions(opt: Object): void;
    private static getCachedTimeZoneInstance(name: string): TimeZone;
    private static setCachedTimeZoneInstance(name: string, instance: TimeZone): void;
    /**
     * Get offset from GMT in milliseconds for a specific date.
     * If you pass in empty arguments, this function will be invoked with the current date.
     * If you want to get the standard offset, use standardOffset property instead.
     * @param {Date} [date] - the date object that is used for the calculation
     * @return {number} the offset in milliseconds for the offset from GMT
     * @throws {TypeError} If the param is not a valid Date object.
     * @public
     * @since 2
     */
    public getOffset(date: Date): number;
    /**
     * Get abbreviation for a specific date.
     * If you pass in empty arguments, this function will be invoked with the current date.
     * If you want to get the standard abbreviation, use standardAbbr property instead.
     * @param {Date} [date] - the date object that is used for obtaining the abbreviation
     * @return {string} the abbreviation string for the timezone in the certain date
     * @throws {TypeError} If the param is not a valid Date object.
     * @public
     * @since 2
     */
    public getAbbr(date?: Date): string;
    /**
     * Get the description for the timezone of a specific date in locale.
     * If you do not specify locale, this function will be invoked with the default locale.
     * @param {Date} date - the date object that is used for obtaining the localized description
     * @param {yunos.util.Locale} [locale] - the locale that is used for the localization of the string
     * @return {string} the localized description for the timezone
     * @throws {TypeError} If the param date is not a valid Date object, or the param locale is not an instance of Locale.
     * @public
     * @since 2
     */
    public getLocalizedDescription(date: Date, locale?: Locale): string;
    /**
     * Get the standard offset for the timezone in milliseconds, regardless of the current date.
     * @name yunos.util.TimeZone#standardOffset
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly standardOffset: number;
    /**
     * Get the standard abbreviation for timezone.
     * @name yunos.util.TimeZone#standardAbbr
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly standardAbbr: string;
    /**
     * Check if the timezone is currently in a daylight saving time period in the given date.
     * If you pass empty arguments, this function will be invoked with the current date.
     * @param {Date} [date] - the date object that is used for the calcuation
     * @return {boolean} boolean result indicates whether the given date is within a daylight saving time under the certain timezone
     * @throws {TypeError} If the param is not a valid Date object.
     * @public
     * @since 2
     */
    public isDST(date?: Date): boolean;
    /**
     * Get the name of the timezone.
     * @name yunos.util.TimeZone#name
     * @type {string}
     * @readonly
     * @public
     * @since 2
     */
    public readonly name: string;
    /**
     * Get localized name of the timezone.
     * If you do not specify the locale, this function will be invoked with the default locale.
     * @param {Locale} [locale] - the locale that is used for the localization of the string
     * @return {string} localized name for the timezone
     * @throws {TypeError} If the param is not an instance of Locale.
     * @public
     * @since 2
     */
    public getLocalizedName(locale: Locale): string;
}
export = TimeZone;
