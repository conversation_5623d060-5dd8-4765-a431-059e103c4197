/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable disallowQuotedKeysInObjects
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

const log = require("./log");
import fs = require("fs");

const VIDEO_CACHE_PATH = "/private/video/cache";
const VIDEO_CACHE_LIMIT_SIZE = 500 * 1024 * 1024;
const VIDEO_CACHE_CHECK_SIZE = 450 * 1024 * 1024;
const ONE_MB_SIZE = 1024 * 1024;
const TAG = "CacheVideo";

interface IVideoInfo {
    id: string;
    size: number;
}

class CacheVideo {
    private static _instance: CacheVideo;
    private _cachedVideoList: IVideoInfo[];
    private _cachedVideoSize: number;

    constructor() {
        log.I(TAG, "constructor");
        this._init();
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new CacheVideo();
        }
        return this._instance;
    }

    _init() {
        this._cachedVideoList = [];
        this._cachedVideoSize = 0;

        let isDirExsit = fs.existsSync(VIDEO_CACHE_PATH);
        if (!isDirExsit) {
            fs.mkdir(VIDEO_CACHE_PATH, (err) => {
                if (err) {
                    log.E(TAG, "_init, create cache directory", err);
                } else {
                    log.I(TAG, "_init, create cache directory succ");
                }
            });
        } else {
            // 计算已有缓存的视频文件总大小
            let filesArr = fs.readdirSync(VIDEO_CACHE_PATH);
            log.I(TAG, "_init, totle num", filesArr.length);
            for (let i = 0; i < filesArr.length; i++) {
                if (filesArr[i].startsWith(".")) {
                    log.I(TAG, "_init, ignore", filesArr[i]);
                } else {
                    let path = VIDEO_CACHE_PATH + "/" + filesArr[i];
                    let stat = fs.statSync(path);
                    if (stat.isFile()) {
                        let videoInfo = {
                            id: filesArr[i],
                            size: stat.size
                        };
                        this._cachedVideoList.push(videoInfo);
                        this._cachedVideoSize += stat.size;
                        log.I(TAG, "_init", path, stat.size);
                    }
                }
            }
            log.I(TAG, "_init, totle size", this._convertToMB(this._cachedVideoSize));
        }
    }

    /**
     * 记录缓存的视频文件信息
     */
    recordVideo(id: string, videoSize: number) {
        log.I(TAG, "recordVideo", id, videoSize);
        if (!id) {
            return;
        }

        for (let i = 0; i < this._cachedVideoList.length; i++) {
            if (id === this._cachedVideoList[i].id) {
                log.I(TAG, "recordVideo", id, "is in cache list");
                return;
            }
        }

        if (!this.isVideoExist(id)) {
            log.I(TAG, "recordVideo", id, "is not exist");
            return;
        }

        let videoInfo = {
            id: id,
            size: videoSize
        };
        this._cachedVideoList.push(videoInfo);
        this._cachedVideoSize += videoSize;

        if (this._cachedVideoSize > VIDEO_CACHE_CHECK_SIZE) {
            this._checkCachedSize();
        }

        log.I(TAG, "recordVideo, before check", this._convertToMB(this._cachedVideoSize));
        if (this._cachedVideoSize > VIDEO_CACHE_LIMIT_SIZE) {
            let tmpCachedVideoSize = this._cachedVideoSize;
            let toDelete = [];
            for (let i = 0; i < this._cachedVideoList.length; i++) {
                let item = this._cachedVideoList[i];
                tmpCachedVideoSize -= item.size;
                toDelete.push(item);
                if (tmpCachedVideoSize <= VIDEO_CACHE_LIMIT_SIZE) {
                    break;
                }
            }

            for (let i = 0; i < toDelete.length; i++) {
                this._delCachedVideo(toDelete[i]);
            }
        }
        log.I(TAG, "recordVideo, after check", this._convertToMB(this._cachedVideoSize));
    }

    /**
     * 当缓存文件到达阈值后，开始检查缓存文件和实际文件总大小
     */
    _checkCachedSize() {
        let tempCachedList = [];
        let tempCachedSize = 0;
        let filesArr = fs.readdirSync(VIDEO_CACHE_PATH);
        // log.I(TAG, "_checkCachedSize totle num", filesArr.length);
        for (let i = 0; i < filesArr.length; i++) {
            if (filesArr[i].startsWith(".")) {
                log.I(TAG, "_checkCachedSize, ignore", filesArr[i]);
            } else {
                let path = VIDEO_CACHE_PATH + "/" + filesArr[i];
                let stat = fs.statSync(path);
                if (stat.isFile()) {
                    let videoInfo = {
                        id: filesArr[i],
                        size: stat.size
                    };
                    tempCachedList.push(videoInfo);
                    tempCachedSize += stat.size;
                    // log.I(TAG, "_checkCachedSize", filesArr[i], stat.size, tempCachedSize);
                }
            }
        }

        log.I(TAG, "_checkCachedSize", tempCachedSize, this._cachedVideoSize);
        if (Math.abs(tempCachedSize - this._cachedVideoSize) > ONE_MB_SIZE) {
            this._cachedVideoList = tempCachedList;
            this._cachedVideoSize = tempCachedSize;
        }
    }

    /**
     * 删除缓存的视频文件
     */
    _delCachedVideo(videoInfo: IVideoInfo) {
        log.I(TAG, "delCachedVideo", videoInfo.id);
        for (let i = 0; i < this._cachedVideoList.length; i++) {
            let item = this._cachedVideoList[i];
            if (videoInfo.id === item.id) {
                this._cachedVideoList.splice(i, 1);
                break;
            }
        }
        if (this.isVideoExist(videoInfo.id)) {
            fs.unlinkSync(VIDEO_CACHE_PATH + "/" + videoInfo.id);
        }
        this._cachedVideoSize -= videoInfo.size;
    }

    _convertToMB(size: number) {
        return size + "=>" + (size / ONE_MB_SIZE).toFixed(1) + "MB";
    }

    isVideoExist(id: string) {
        return fs.existsSync(VIDEO_CACHE_PATH + "/" + id);
    }

    getCachePath(id: string) {
        return VIDEO_CACHE_PATH + "/" + id;
    }

    /**
     * 删除全部缓存的视频文件
     */
    delAllCachedVideo() {
        let isDirExsit = fs.existsSync(VIDEO_CACHE_PATH);
        if (!isDirExsit) {
            log.E(TAG, "delAllCachedVideo, cache dir is not exist");
            return;
        }

        let filesArr = fs.readdirSync(VIDEO_CACHE_PATH);
        log.I(TAG, "delAllCachedVideo, file num is", filesArr.length);
        for (let i = 0; i < filesArr.length; i++) {
            let path = VIDEO_CACHE_PATH + "/" + filesArr[i];
            fs.unlink(path, (err) => {
                if (err) {
                    log.E(TAG, "delAllCachedVideo", err);
                } else {
                    log.I(TAG, "delAllCachedVideo, succ", path);
                }
            });
        }

        this._cachedVideoList = [];
        this._cachedVideoSize = 0;
    }
}

export = CacheVideo;
