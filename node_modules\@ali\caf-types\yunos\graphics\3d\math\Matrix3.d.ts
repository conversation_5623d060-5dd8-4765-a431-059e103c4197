import Matrix4 = require("./Matrix4");
/**
 * <p>This class represents a 3 * 3 matrix.</p>
 * @memberof yunos.graphics.3d.math
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class Matrix3 {
    private _out;
    /**
     * Constructor that create a 3 * 3 matrix.
     * @param {number} n11 - the first number of the 3 * 3 matrix.
     * @param {number} n12 - the fourth number of the 3 * 3 matrix.
     * @param {number} n13 - the seventh number of the 3 * 3 matrix.
     * @param {number} n21 - the second number of the 3 * 3 matrix.
     * @param {number} n22 - the fifth number of the 3 * 3 matrix.
     * @param {number} n23 - the eighth number of the 3 * 3 matrix.
     * @param {number} n31 - the third number of the 3 * 3 matrix.
     * @param {number} n32 - the sixth number of the 3 * 3 matrix.
     * @param {number} n33 - the ninth number of the 3 * 3 matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(n11?: number, n12?: number, n13?: number, n21?: number, n22?: number, n23?: number, n31?: number, n32?: number, n33?: number);
    /**
     * Destructor that destroy this 3 * 3 matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public destroy(): void;
    /**
      * Return the Float32Array of this 3 * 3 matrix.
      * @public
      * @since 5
      * @hiddenOnPlatform auto
      */
    public data(): Float32Array;
    /**
     * Create a new Matrix3 and with identical elements to this one.
     * @return {yunos.graphics.3d.math.Matrix3} the new Matrix3
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clone(): Matrix3;
    /**
     * Copies the elements of matrix m into this matrix.
     * @param {yunos.graphics.3d.math.Matrix3} m - the matrix3 to copy
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copy(m: Matrix3): void;
    /**
     * Computes and returns the determinant of this matrix.
     * @return {number} the determinant of this matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public determinant(): number;
    private scale(sx: number, sy: number): void;
    private rotate(theta: number): void;
    private translate(tx: number, ty: number): void;
    /**
     * Return true if this matrix and passed matrix are equal
     * @param {yunos.graphics.3d.math.Matrix3} m - the Matrix to equal
     * @return {boolean} check if this matrix and passed matrix are equal.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public equals(m: Matrix3): boolean;
    /**
     * Sets the elements of this matrix based on an array in column-major format.
     * @param {number[]} array - the array to read the elements from
     * @param {number} offset - index of first element in the array
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public fromArray(array: Array<number> | Float32Array, offset?: number): void;
    /**
     * Set this matrix to the inverse of the passed matrix m.
     * @param {yunos.graphics.3d.math.Matrix3} m - the matrix to getInverse
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getInverse(m: Matrix3): void;
    /**
     * Sets this matrix as the upper left 3x3 of the normal matrix of the passed matrix4.
     * @param {yunos.graphics.3d.math.Matrix4} m - the passed matrix4
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getNormalMatrix(m: Matrix4): void;
    /**
     * Resets this matrix to the 3x3 identity matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public identity(): void;
    /**
     * Post-multiplies this matrix by m.
     * @param {yunos.graphics.3d.math.Matrix3} m - the passed Matrix3 to multiply.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiply(m: Matrix3): void;
    /**
     * Sets this matrix to Matrix3 a x Matrix3 b.
     * @param {yunos.graphics.3d.math.Matrix3} a - the first Matrix3 to multiply
     * @param {yunos.graphics.3d.math.Matrix3} b - the second Matrix3 to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyMatrices(a: Matrix3, b: Matrix3): void;
    /**
     * Multiplies every component of the matrix by the scalar value s.
     * @param {number} s - the scalar value to multiply.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyScalar(s: number): void;
    /**
     * Sets the 3x3 matrix values to the given row-major sequence of values.
     * @param {number} n11 - the first number of the 3 * 3 matrix.
     * @param {number} n12 - the fourth number of the 3 * 3 matrix.
     * @param {number} n13 - the seventh number of the 3 * 3 matrix.
     * @param {number} n21 - the second number of the 3 * 3 matrix.
     * @param {number} n22 - the fifth number of the 3 * 3 matrix.
     * @param {number} n23 - the eighth number of the 3 * 3 matrix.
     * @param {number} n31 - the third number of the 3 * 3 matrix.
     * @param {number} n32 - the sixth number of the 3 * 3 matrix.
     * @param {number} n33 - the ninth number of the 3 * 3 matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public set(n11: number, n12: number, n13: number, n21: number, n22: number, n23: number, n31: number, n32: number, n33: number): void;
    /**
     * Pre-multiplies this matrix by the passed matrix.
     * @param {yunos.graphics.3d.math.Matrix3} m - the passed matrix to premultiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public premultiply(m: Matrix3): void;
    /**
     * Set this matrx to the upper 3x3 matrix of the Matrix4 m.
     * @param {yunos.graphics.3d.math.Matrix4} m - the passed matrix4 to set from
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromMatrix4(m: Matrix4): void;
    /**
     * Sets the UV transform matrix from offset, repeat, rotation, and center.
     * @param {number} tx - offset x
     * @param {number} ty - offset y
     * @param {number} sx - repeat x
     * @param {number} sy - repear y
     * @param {number} rotation - rotation in radians
     * @param {number} cx - center x of rotation
     * @param {number} cy - center y of rotation
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setUvTransform(tx: number, ty: number, sx: number, sy: number, rotation: number, cx: number, cy: number): void;
    /**
     * Writes the elements of this matrix to an array in column-major format.
     * @param {number[]} array - array to store the resulting vector in.
     * @param {number} offset - offset in the array at which to put the result.
     * @return {number[]} return the array to store the resulting vector in
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public toArray(array: Array<number>, offset: number): Array<number>;
    /**
     * Transposes this matrix in place.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public transpose(): void;
    /**
     * Transposes this matrix into the supplied array, and returns itself unchanged.
     * @param {number[]} array - array to store the resulting in.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public transposeIntoArray(array: Array<number>): void;
}
export = Matrix3;
