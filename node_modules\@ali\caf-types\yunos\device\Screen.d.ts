import YObject = require("../core/YObject");
import BitmapClass = require("../graphics/Bitmap");
import PageWindow = require("node_pagewindow.node");
/**
 * <p>You can use this class to get device screen width/height.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.device
 * @relyon YUNOS_SYSCAP_SCREEN
 * @public
 * @since 1
 */
declare class Screen extends YObject {
    private _connection;
    private _animations;
    private _animationsFree;
    private _animationIdCount;
    private _animationHasCallback;
    private _isLand;
    private _displayId;
    private _defaultDeviceResolution;
    private _scale;
    private _sdpDisabled;
    private static instance;
    /** @private */
    private constructor();
    /**
     * @hiddenOnPlatform auto
     */
    private readonly pageWindow: typeof PageWindow;
    /**
     * <p>Get singleton instance of Screen.</p>
     * @example
     * var Screen = require("yunos/device/Screen");
     * var screen = Screen.getInstance();
     * @public
     * @since 1
     */
    public static getInstance(): Screen;
    /**
     * <p>Release singleton instance of Screen.</p>
     * @example
     * Screen.releaseInstance();
     * @public
     * @since 1
     */
    public static releaseInstance(): void;
    /**
     * <p>Tell the system that you wish to perform an animation and call a specified function to update an animation before the next repaint.</p>
     * @example
     * Screen.requestAnimationFrame(callback);
     * @param {callback} [callback] - the callback which is invoked before the repaint.
     * @return {number} the Id of the request.
     * @public
     * @since 3
     */
    public static requestAnimationFrame(callback: (...args: Object[]) => void): number;
    /**
     * <p>Cancels an animation frame request previously scheduled.</p>
     * @example
     * Screen.cancelAnimationFrame(id);
     * @param {Number} [requestId] - The ID value returned by the call to Screen.requestAnimationFrame().
     * @public
     * @since 3
     */
    public static cancelAnimationFrame(requestId: number): void;
    /**
     * <p>Capture the Screen Image Sync by screen id.</p>
     * @param {number} [id] - the screen id for capture.
     * @return {yunos.graphics.Bitmap} the bitmap of the select screen
     * @throws {TypeError} If id is not a number >= 0
     * @public
     * @since 6
     */
    public captureScreenByID(id: number): 0 | BitmapClass;
    /**
     * <p>Capture the Screen Image ASync by screen id.</p>
     * @param {number} [id] - the bitmap width after scaled, default is current Screen width.
     * @param {yunos.device.Screen~captureScreenCallback} callback - the function when finished captureScreen
     * @throws {TypeError} If id is not a number >= 0, callback is undefined or not a function
     * @public
     * @since 6
     */
    public captureScreenByIDAsync(id: number, callback: (bitmap: BitmapClass) => void): int;
    private captureScreen(width?: number, height?: number): 0 | BitmapClass;
    private captureScreenAsync(width?: number | ((bitmap: BitmapClass) => void), height?: number, callback?: (bitmap: BitmapClass) => void): int;
    /**
     * <p>Get the resolution object which contains several parameters of the screen.
     * The format is
     * {
     *      resolution: [string],
     *      modeName: [string],
     *      width: [number],
     *      height: [number],
     *      fbWidth: [number],
     *      fbHeight: [number],
     *      refreshRate: [number],
     *      imode: [boolean],
     *      bitsPerPixel: [number],
     *      deviceId: [number],
     *      xdpi: [number],
     *      ydpi: [number]
     *  }
     * Note that this API has been deprecated. Use the corresponding property of this class for convenience.
     * For example, you can use yunos.device.Screen#xdpi instead of yunos.device.Screen#resolution.xdpi.
     * </p>
     * @name yunos.device.Screen#resolution
     * @type {Object}
     * @readonly
     * @public
     * @since 1
     * @deprecated 2
     */
    public readonly resolution: {
        [key: string]: number;
    };
    /**
     * <p>Get the density factor for the DIP unit.
     * It represents a scaling number which compared to the MDPI density (generally 160dpi).</p>
     * @name yunos.device.Screen#densityFactor
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly densityFactor: number;
    /**
     * <p>Get the screen density in DPI.</p>
     * @name yunos.device.Screen#density
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly density: number;
    /**
     * <p>Get the screen absolute width of the available display size in pixels.</p>
     * @name yunos.device.Screen#widthPixels
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly widthPixels: number;
    /**
     * <p>Get the screen absolute height of the available display size in pixels.</p>
     * @name yunos.device.Screen#heightPixels
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly heightPixels: number;
    /**
     * <p>Get the screen exact physical pixels per inch in the X dimension.</p>
     * @name yunos.device.Screen#xdpi
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly xdpi: number;
    /**
     * <p>Get the screen exact physical pixels per inch in the Y dimension.</p>
     * @name yunos.device.Screen#ydpi
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly ydpi: number;
    /**
     * <p>Get the screen physical refresh rate in frames per second. Typically, it is 60 (Hz).</p>
     * @name yunos.device.Screen#refreshRate
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly refreshRate: number;
    /**
     * <p>Get the number of bits per pixels. Typically, a pixel needs 4 bytes to represent RGBA color, thus the value is 32 (bits).</p>
     * @name yunos.device.Screen#bitsPerPixel
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly bitsPerPixel: number;
    /**
     * <p>Get pixel value of dp on device.</p>
     * @example
     * var Screen = require("yunos/device/Screen");
     * var screen = Screen.getInstance();
     * var pixelValue = screen.getPixelByDp(100);
     * @param {number} dpValue - value of dp.
     * @return {number} pixel value of dp on this device.
     * @public
     * @since 1
     */
    public getPixelByDp(dpValue: number): number;
    private getDpByPixel(pixelValue: number): number;
    /**
     * <p>Get pixel value of sdp on device.</p>
     * @example
     * var Screen = require("yunos/device/Screen");
     * var screen = Screen.getInstance();
     * var pixelValue = screen.getPixelBySDp(100);
     * @param {number} sdpValue - value of sdp.
     * @return {number} pixel value of sdp on this device.
     * @public
     * @since 3
     *
     */
    public getPixelBySDp(dpValue: number): number;
    private _getPixelBySDp(dpValue: number): number;
    /**
     * <p>Get sdp value of pixel on device.</p>
     * @example
     * var Screen = require("yunos/device/Screen");
     * var screen = Screen.getInstance();
     * var pixelValue = screen.getSDpByPixel(100);
     * @param {number} pixelValue - value of pixel.
     * @return {number} sdp value of pixel on this device.
     * @public
     * @since 3
     *
     */
    public getSDpByPixel(pixelValue: number): number;
    /**
     * <p>Get valid display ids.</p>
     * @example
     * var Screen = require("yunos/device/Screen");
     * var screen = Screen.getInstance();
     * var dislayIds = screen.getDisplayIds();
     * @return {number[]} valid display id list.
     * @public
     * @since 6
     *
     */
    public getDisplayIds(): number[];
    /**
     * @friend
     */
    getResolution(displayId?: number): PageWindow.Resolution;
    private scale: number;
    public setCustomDensityFactor(value: number): void;
    private readonly pixelPerMM: number;
    private requestFrame(callback: (...args: Object[]) => void): number;
    private cancelFrame(id: number): void;
    private isLand(): boolean;
}
export = Screen;
