import CompositeView = require("../view/CompositeView");
import ModalLayer = require("./ModalLayer");
import Window = require("yunos/ui/view/Window");
import PropertyAnimation = require("../animation/PropertyAnimation");
interface LocateRect {
    left: number;
    top: number;
    width: number;
    height: number;
}
/**
 * <p>Dialog widget.<br>
 * The Dialog class is the basic class for creating dialogs.<br>
 * A dialog is usually a small view that appears in on top of the window. The underlying Activity loses focus and the dialog accepts all user interaction.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class Dialog extends CompositeView {
    private __stage;
    protected _showAnimation: PropertyAnimation;
    protected _modalLayer: ModalLayer;
    private _closeAnimationGroup;
    private _windowInsets;
    protected _closeAnimation: PropertyAnimation;
    protected _defaultRadius: number;
    protected _defaultHeight: number;
    protected _defaultWidth: number;
    private _closeAnimationFinished;
    protected _closeOnTouchOutside: boolean;
    protected _closeIsCalled: boolean;
    protected _windowWidth: number;
    protected _windowHeight: number;
    private _showAnimationFinished;
    private _closeOnBackkey;
    private _showingMask;
    private _dialogLevel;
    protected _dialogShowAnimationDuration: number;
    protected _dialogCloseAnimationDuration: number;
    protected _padding: number;
    protected _margins: number[];
    private _dialogType;
    protected _isAutoStyle: boolean;
    protected _defaultLocateType: number;
    protected _defaultDialogBackground: string;
    protected _isHideWithWindow: boolean;
    protected _uniqueOffDimnessMode: boolean;
    protected _modalLayerBackground: string | number;
    protected _modalLayerBorderRadius: number;
    protected _modalLayerMargins: number[];
    private _showAnimationGroup;
    private _locateType;
    private _locateRect;
    private _fullScreenPadding;
    private _showPosition;
    private _specifiedMode;
    private _dialogProxy;
    private _brightnessManager;
    private _openReason;
    private _lastSurfaceId;
    protected _topMargin: number;
    protected _leftMargin: number;
    protected _bottomMargin: number;
    protected _rightMargin: number;
    private _dialogProxyListener;
    protected _parentWindow: Window;
    /**
     * <p>Create a Dialog.</p>
     * @public
     * @since 1
     */
    public constructor(...args: Object[]);
    /**
     * <p>Destructor that destroy this Dialog.</p>
     * @param {boolean} recursive - destroy the children in the Dialog if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * <p>Destructor that destroy this Dialog.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds whether the dialog should be closed when backkey is pressed, the default value is true.</p>
     * @name yunos.ui.widget.Dialog#closeOnBackkey
     * @type {boolean}
     * @throws {TypeError} If this value is not a boolean.
     * @public
     * @since 1
     */
    public closeOnBackkey: boolean;
    /**
     * <p>This property holds whether the dialog should be closed when tap the region outside of the dialog.</p>
     * @name yunos.ui.widget.Dialog#closeOnTouchOutside
     * @type {boolean}
     * @throws {TypeError} If this value is not a boolean.
     * @public
     * @since 2
     */
    public closeOnTouchOutside: boolean;
    /**
     * This property holds whether to show the mask background. The default value is true.
     * @name yunos.ui.widget.Dialog#showingMask
     * @type {boolean}
     * @throws {TypeError} If the value is not type boolean.
     * @public
     * @since 2
     */
    public showingMask: boolean;
    /**
     * This property holds the level of the dialog, such as application dialog, system dialog or dialog that can show on the lock screen.
     * @name yunos.ui.widget.Dialog#dialogLevel
     * @type {yunos.ui.widget.Dialog.DialogLevel}
     * @default yunos.ui.widget.Dialog.DialogLevel.Application
     * @throws {TypeError} If the value is not type of Dialog.DialogLevel.
     * @public
     * @since 2
     */
    public dialogLevel: number;
    /**
     * This property holds the type of the dialog, the way to show and close the dialog.
     * @name yunos.ui.widget.Dialog#dialogType
     * @type {yunos.ui.widget.Dialog.DialogType}
     * @throws {TypeError} If the value is not type of Dialog.DialogType.
     * @private
     */
    private dialogType: number;
    /**
     * <p> This property holds the locateType that control where ModalLayer of dialog locate on.</p>
     * <p> Setting locateType only works before dialog shown.</p>
     * @name yunos.ui.widget.Dialog#locateType
     * @type {yunos.ui.widget.Dialog.LocateType}
     * @default yunos.ui.widget.Dialog.LocateType.Auto
     * @throws {TypeError} If this value is not a number.
     * @public
     * @since 6
     */
    public locateType: number;
    /**
     * This property holds the animation to show the dialog.
     * @name yunos.ui.widget.Dialog#showingAnimation
     * @type {yunos.ui.animation.Animation}
     * @throws {TypeError} If the value is not an instance of Animation.
     * @public
     * @since 2
     */
    public showingAnimation: PropertyAnimation;
    /**
     * This property holds the animation to close the dialog.
     * @name yunos.ui.widget.Dialog#closingAnimation
     * @type {yunos.ui.animation.Animation}
     * @throws {TypeError} If the value is not an instance of Animation.
     * @public
     * @since 2
     */
    public closingAnimation: PropertyAnimation;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.Dialog#defaultStyleName
     * @type {string}
     * @default "Dialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>attach self to the specific window. So it will be shown or hide automatically<br>
     * with the window.
     * Note: Wallpaper Window won't hide/show with page period. This functionality works only<br>
     * before dialog shown. if not invoked, the dialog will attach the main window if exist.<br>
     * @param {Window} [w] - the specific Window.
     * @public
     * @since 5
     */
    public attachedTo(w: Window): void;
    /**
     * <p>Set the LocateRect that dialog show in screen.</p>
     * <p>it only works when locateType is SpecifyInScreen and before dialog shown.</p>
     * @param {number} left - the left of rect
     * @param {number} top - the top of rect
     * @param {number} width - the width of rect
     * @param {number} height - the height of rect
     * @public
     * @since 6
     */
    public setLocateRect(left: number, top: number, width: number, height: number): void;
    /**
     * <p>Refresh locate rect of ModalLayer.</p>
     * @friend
     */
    refreshLocateRect(callback?: (rect: LocateRect) => void): void;
    /**
     * <p>Set the color of the mask after custom style if exists. it won't take effect after showing<br></p>
     * @param {string| number} [value] - color string or number
     * @public
     * @since 5
     */
    public setMaskColor(value: string | number): void;
    /**
     * <p>Start to show the dialog and display it on screen.</p>
     * <p>The dialog is placed in the application layer and opaque.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Start to show the dialog and display it on screen.<br>
     * The dialog is placed in the application layer and opaque.<br>
     * Note that the x and y will not take effect when Current Dialog is AlertDialog, ActionMenu or ModalView.</p>
     * @param {number} x - the left position of the dialog.
     * @param {number} y - the top position of the dialog.
     * @public
     * @since 2
     */
    /**
     * <p>Start to show the dialog and display it on screen.<br>
     * The dialog is placed in the application layer and opaque.<br>
     * Note that the x and y will not take effect when Current Dialog is ActionMenu or ModalView.<br>
     * Furthermore: x, y take effect when both value present.<br>
     * Otherwise: top, left will be determined by current theme setting</p>
     * @param {number} [x] - the left position of the dialog.
     * @param {number} [y] - the top position of the dialog.
     * @public
     * @since 5
     */
    public show(x?: number, y?: number): void;
    /**
     * <p>Close the dialog, removing it from the screen.</p>
     * @public
     * @since 1
     */
    public close(): void;
    private detachDialog;
    /**
     * <p>Close the dialog, removing it from the screen. It's a alias of close.</p>
     * @public
     * @since 1
     */
    public hide(): void;
    /**
     * <p>Return true if the dialog is showing at the screen otherwise return false.</p>
     * @public
     * @since 1
     */
    public isShowing(): boolean;
    /**
     * <p>Apply theme style for dialog.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Fetch the effective view which will be used for reshowing.</p>
     * <p>Typically, we will resize the dialog when the main window reshowing if the effective view contained by the main window.</p>
     * <p>Otherwise, we keep the dialog size to be full screen.</p>
     * @friend
     */
    fetchEffectiveView(): CompositeView;
    /**
     * <p>Override this function to do your operations when backkey is clicked.</p>
     * <p>The default operation is to close the view and the window.</p>
     * @protected
     * @since 1
     */
    protected onBackkey(): void;
    /**
     * Implement this function before doing animation to show dialog.
     * @param {number} left - the left position of the dialog.
     * @param {number} top - the top position of the dialog.
     * @protected
     * @since 2
     */
    protected prepareForShowing(x: number, y: number): void;
    /**
     * Implement this function before doing animation to close dialog.
     * @protected
     * @since 2
     */
    protected prepareForClosing(): void;
    /**
     * Implement this function after the dialog has shown on the screen.
     * @param {number} left - the left position of the dialog.
     * @param {number} top - the top position of the dialog.
     * @protected
     * @since 2
     */
    protected onDialogShown(): void;
    /**
     * Implement this function after the dialog is closed.
     * @protected
     * @since 2
     */
    protected onDialogClosed(): void;
    private onWindowResize(reason: number, windowWidth: number, windowHeight: number): void;
    private onTouchOutSide(): void;
    private readonly showAnimation: PropertyAnimation;
    private readonly closeAnimation: PropertyAnimation;
    private _stage: Object;
    private specifiedMode: number;
    private updateLeftForAlertDialogType(): void;
    private createDialogProxy(): void;
    private createModalLayer(): void;
    private bindModalLayer(): void;
    private removeModalLayer(): void;
    private offDimnessMode(): void;
    private initializeAnimations(): void;
    private destroyAnimations(): void;
    private doingShowAnimation(x: number, y: number): void;
    private doingCloseAnimation(): void;
    private onShowComplete(): void;
    private onCloseComplete(): void;
    private sendResult(): void;
    private onPropertyChange(property: string, oldValue: Object, value: number): void;
    /**
     * <p>Enum for dialog level.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly DialogLevel: {
        [index: string]: number;
    };
    private static readonly DialogType: {
        [index: string]: number;
    };
    /**
     * <p>Enum for the locate type of Toast.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 6
     *
     */
    public static readonly LocateType: {
        /**
         * <p>When Auto, ModalLayer will set size and position depend on Type automatically.</p>
         * @public
         * @since 6
         */
        Auto: int;
        /**
         * <p>When MatchWindow, ModalLayer will set size and position depend on parent window.</p>
         * @public
         * @since 6
         */
        MatchWindow: int;
        /**
         * <p>When FullScreen, ModalLayer will fill full screen.</p>
         * @public
         * @since 6
         */
        FullScreen: int;
        /**
         * <p>When FullScreenFit, ModalLayer will fill full screen with padding.</p>
         * @public
         * @since 6
         */
        FullScreenFit: int;
        /**
         * <p>When SpecifyInScreen, ModalLayer will show in rect provided specifically.</p>
         * @public
         * @since 6
         */
        SpecifyInScreen: int;
    };
    private static readonly Stage;
}
export = Dialog;
