/// <reference types="node" />
import YObject = require("../core/YObject");
import fs = require("fs");
/**
 * <p>Zip Util.</p>
 * @example
 * let archive = new Zip("archive.zip");
 * // start writing
 * archive.write(function(err) {
 *     if (!err) {
 *         // write finished
 *     }
 * });
 * // add a local file named foo.txt and put it under folder txt in the archive
 * archive.addFile("foo.txt", "txt/foo.txt");
 * // add other files, folder or buffer then close it
 * // ...
 * archive.close();
 * // archbive closed and won't accept any modification
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 3
 */
declare class Zip extends YObject {
    private _target;
    private out;
    private _isClosed;
    private _isWriting;
    /**
     * <p>Create a zip.</p>
     * <p>If the file already exists, it will be replaced when writing.</p>
     * @param  {string} target - Target path for the generated zip file.
     * @throws {TypeError} If target is not a string.
     * @public
     * @since 3
     */
    public constructor(_target: string);
    private _checkClosed;
    /**
     * <p>Add a local file to the zip.</p>
     * @param {string} filePath - The path for the file to be added.
     * @param {string} pathInZip - The store path in the zip.
     * @throws {TypeError} If filePath is not a string.
     * @throws {TypeError} If pathInZip is not a string.
     * @throws {Error} If the content read from the filePath is not a file.
     * @throws {Error} When zip closed.
     * @public
     * @since 3
     */
    public addFile(filePath: string, pathInZip: string): void;
    /**
     * <p>Add a file to the zip from a buffer.</p>
     * @param {Buffer} buffer - The input buffer.
     * @param {string} pathInZip - The store path in the zip.
     * @throws {TypeError} If the input is not a buffer.
     * @throws {Error} If the buffer length exceeds 0x3fffffff.
     * @throws {TypeError} If pathInZip is not a string.
     * @throws {Error} When zip closed.
     * @public
     * @since 3
     */
    public addBuffer(buffer: Buffer, pathInZip: string): void;
    /**
     * <p>Add a file to the zip from a read stream.</p>
     * @param {Stream} readStream - The input read stream.
     * @param {string} pathInZip - The store path in the zip.
     * @throws {TypeError} If the input is not a read stream.
     * @throws {TypeError} If pathInZip is not a string.
     * @throws {Error} When zip closed.
     * @public
     * @since 3
     */
    public addReadStream(readStream: fs.ReadStream, pathInZip: string): void;
    /**
     * <p>Add a local folder to the zip.</p>
     * <p>All contents in the folder will be added to the zip according to it's directory structure.</p>
     * @param {string} folderPath - The path for the folder to be added.
     * @param {string} pathInZip - The store path in the zip.
     * @param {yunos.zip.Zip~filter} [filter] - The filter function.
     * @throws {Error} If the content read from the folderPath is not a folder.
     * @throws {Error} When zip closed.
     * @public
     * @since 3
     */
    public addFolder(folderPath: string, pathInZip: string, filter: (filePath: string) => boolean): void;
    /**
     * <p>Filter input when adding content form a folder.</p>
     * @example
     * // only add javascript file under target folder
     * const path = require("path");
     * function(filePath) {
     *      return path.extname(filePath) === '.js';
     * }
     * @callback yunos.zip.Zip~filter
     * @param {string} filePath - File path.
     * @return {boolean} Add to zip or not.
     * @public
     * @since 3
     */
    /**
     * <p>Add a empty folder to the zip.</p>
     * @param {string} pathInZip - The store path in the zip.
     * @throws {TypeError} If pathInZip is not a string.
     * @throws {Error} When zip closed.
     * @public
     * @since 3
     */
    public addEmptyFolder(pathInZip: string): void;
    /**
     * <p>Starting writing.</p>
     * <p>You can add contents to this zip before or after calling write method.</p>
     * @throws {Error} When zip closed.
     * @public
     * @since 3
     */
    public write(callback?: (err: Object, ...object: Object[]) => void): void;
    /**
     * <p>Close the zip so it won't accept any modification then the final zip file will be generated.</p>
     * @public
     * @since 3
     */
    public close(): void;
}
export = Zip;
