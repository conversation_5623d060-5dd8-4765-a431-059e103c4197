import CompositeView = require("yunos/ui/view/CompositeView");
interface IStyle {
    defaultLineColor: string;
    defaultLineHeight: number;
    defaultBorderRadius: number;
}
declare class GroupListContainerBM extends CompositeView {
    readonly defaultStyleName: string;
    private _defaultLineColor;
    private _defaultLineHeight;
    private _defaultBorderRadius;
    constructor();
    applyStyle(style: IStyle): void;
    private _initView;
}
export = GroupListContainerBM;
