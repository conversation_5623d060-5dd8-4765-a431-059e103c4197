/// <reference types="node" />
import YObject = require("../core/YObject");
import Page = require("../page/Page");
import View = require("./view/View");
import CompositeView = require("./view/CompositeView");
import WindowClass = require("./view/Window");
import ModalLayerClass = require("./widget/ModalLayer");
import WindowManager = require("./WindowManager");
import Timer = NodeJS.Timer;
declare class FocusManager extends YObject {
    private focusableChildren: Array<View>;
    private focusableDescendants: Array<View>;
    private host: View;
    private windowDescendantsChanged: boolean;
    public constructor(context?: Page, host?: View);
    private updateChildren(children: View[]): void;
    private updateWindowFocusableDescendants(): void;
    private toRight(currentFocusView: View): boolean;
    private toLeft(currentFocusView: View): boolean;
    private toUp(currentFocusView: View): boolean;
    private toDown(currentFocusView: View): boolean;
    private toNext(currentFocusView: View): boolean;
    private toWindowNext(currentFocusView: View): void;
    private toPrevious(currentFocusView: View): boolean;
    private toWindowPrevious(currentFocusView: View): void;
    private toChild(parent: CompositeView): boolean;
    private toTarget(currentFocusView: View, direction: string): boolean;
    private chainedDispatchDescendantsFocusChangeEvent(focusedView: View, isFocusChanged: Boolean, direction?: String): boolean;
    private getFocusableDescendants(exceptedChild?: View): View[];
    private findNearestViewByTabIndex(origin: View, targets: View[], direction: string): View;
    private findNearestViewByPosition(origin: View, targets: View[], direction: string): View;
    private findNearestView(origin: View, targets: View[], direction: string): View;
    private getCenterPos(view: View): {
        x: number;
        y: number;
    };
    private getAbsolutePos(view: View): {
        x: int;
        y: int;
    };
    private isViewFocusable(cv: View): boolean;
    private isViewInsideAncestor(v: View, ancestor: View): boolean;
    private usingInputMode(value: string): boolean;
    private static _needRestoreFocus: boolean;
    private static _blurTimer: Timer;
    private static processInputModeChange(wm: WindowManager, inputmode: string): void;
    private static setBlurTimer(win: WindowClass | ModalLayerClass, focused: View): void;
    private static handleKeyEvent(type: string, keyObj: {
        key: string;
    }, win: WindowClass): boolean;
    private static setWindowFocus(win: WindowClass): void;
    private static mayChangeFocus(code: string): boolean;
}
export = FocusManager;
