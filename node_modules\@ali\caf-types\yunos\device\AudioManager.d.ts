import EventEmitter = require("../core/EventEmitter");
/**
 * <p>AudioManager class is a client of AudioStreamer, provides interfaces to manage
 * audio volume, ringer mode, audio session, audio route and audio devices.</p>
 * <p>
 * You can obtain an instance of this class by calling
 * <pre><code>
 * var AudioManager = require("yunos/device/AudioManager");
 * var audiomanager = AudioManager.getInstance();
 * </code></pre>
 * </p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.device
 * @relyon YUNOS_SYSCAP_AUDIO
 * @public
 * @since 2
 */
declare class AudioManager extends EventEmitter {
    private _nodeAudioManager;
    private _ref;
    private onConnected;
    /** @private */
    private constructor();
    /**
     * <p>Get singleton AudioManager instance.</p>
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * @return {yunos.device.AudioManager} the instance of AudioManager.
     * @public
     * @since 2
     */
    public static getInstance(): AudioManager;
    /**
     * <p>Get singleton AudioManager instance async.</p>
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstanceAsync(function(event: boolean){
     *     if(event)
     *     {
     *          audiomanager.registerAudioVolumeListener();
     *          ...
     *     }
     * });
     * @return {yunos.device.AudioManager} the instance of AudioManager.
     * @public
     * @since 5
     */
    public static getInstanceAsync(callback: (event: boolean) => void): AudioManager;
    /**
     * <p>Release singleton instance of AudioManager.</p>
     * @example
     * AudioManager.releaseInstance();
     * @public
     * @since 2
     */
    public static releaseInstance(): void;
    /**
     * <p>Get the maximum valid volume level of a specific audio stream type.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var max = audiomanager.getMaxAudioVolumeLevel(AudioManager.StreamType.AUDIO_STREAM_MUSIC);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the audio stream type
     * of which the maximum volume level you want to obtain.
     * The streamType argument must be one of AudioManager.StreamType.
     *
     * @return {number} The maximum volume level of the specified audio stream type.
     *
     * @throws {RangeError} If param is invalid.
     * @throws {TypeError} If param is invalid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getMaxAudioVolumeLevel(streamType: number, zoneId?: number): number;
    /**
     * <p>Get the minimum volume level of a specific audio stream type.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var max = audiomanager.getMinAudioVolumeLevel(AudioManager.StreamType.AUDIO_STREAM_MUSIC);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the audio stream type
     * of which the maximum volume level you want to obtain.
     * The streamType argument must be one of AudioManager.StreamType.
     *
     * @return {number} The minimum volume level of the specified audio stream type.
     *
     * @throws {RangeError} If param is invalid.
     * @throws {TypeError} If param is invalid.
     * @hiddenOnPlatform auto
     * @public
     * @since 3
     */
    public getMinAudioVolumeLevel(streamType: number, zoneId?: number): number;
    private getStreamMaxVolume(streamType: number): number;
    /**
     * <p>Checks whether the audio volume of the device can be changed.
     * Some devices adopt a fixed audio volume strategy, which means the audio
     * volume can't be adjusted on these devices.</p>
     *
     * <p>The audio volume management interfaces don't work if volume is fixed,
     * including:
     * adjustOptionalAudioVolumeByStep(int, int, int)
     * adjustAudioVolumeByStep(int, int, int)
     * setAudioVolumeLevel(int, int, int)
     * setAudioRingerMode(int)
     * setAudioMute(int, boolean)</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var isFixed = audiomanager.isAudioVolumeFixed();
     *
     * @return {boolean} Return true if audio volume is fixed; otherwise return false.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isAudioVolumeFixed(): boolean;
    private isVolumeFixed(): boolean;
    /**
     * <p>Adjusts the volume of a specific audio stream type to increase or decrease by one step.<br>
     * This method is usually used to handle audio volume hard key event.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.adjustAudioVolumeByStep(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          AudioManager.AdjustDirection.ADJUST_RAISE,
     *          AudioManager.AdjustFlag.FLAG_NONE);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * type of audio stream to be adjusted.
     * One of AUDIO_STREAM_VOICE_CALL, AUDIO_STREAM_RING, AUDIO_STREAM_MUSIC or AUDIO_STREAM_ALARM.
     *
     * @param {yunos.device.AudioManager.AdjustDirection} direction - The direction of adjustment.
     * One of ADJUST_LOWER, ADJUST_RAISE, or ADJUST_SAME.
     *
     * @param {yunos.device.AudioManager.AdjustFlag} flags - Usually passed to
     * SystemUI to indicate some UI behaviors. For example, show the volume panel.
     * One or more of FLAG_SHOW_VOLUME_PANEL | FLAG_CHANGE_RINGER_MODE | FLAG_PLAY_PROMPT_TONE
     * | FLAG_DISMISS_ALL | FLAG_VIBRATE | FLAG_NONE
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public adjustAudioVolumeByStep(streamType: number, direction: number, flags: number, zoneId?: number): void;
    private adjustStreamVolume(streamType: number, direction: number, flags: number): void;
    /**
     * <p>Adjusts the volume of a specific audio stream type to increase or decrease by one step.<br>
     * But if the specified audio stream type is not playing, the audio system
     * will select the most revelant one from current playing audio streams to
     * adjust. And if there is no audio stream playing currently, the passed in
     * audio stream type will be adopted.
     * This method is usually used to handle audio volume hard key event.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.adjustOptionalAudioVolumeByStep(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          AudioManager.AdjustDirection.ADJUST_RAISE,
     *          AudioManager.AdjustFlag.FLAG_NONE);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * optional audio stream type that will be adopted by audio system if there
     * is no audio stream playing.
     * One of AUDIO_STREAM_VOICE_CALL, AUDIO_STREAM_RING, AUDIO_STREAM_MUSIC or AUDIO_STREAM_ALARM.
     *
     * @param {yunos.device.AudioManager.AdjustDirection} direction - The direction of adjustment.
     * One of ADJUST_LOWER, ADJUST_RAISE, or ADJUST_SAME.
     *
     * @param {yunos.device.AudioManager.AdjustFlag} flags - Usually passed to
     * SystemUI to indicate some UI behaviors. For example, show the volume panel.
     * One or more of FLAG_SHOW_VOLUME_PANEL | FLAG_CHANGE_RINGER_MODE | FLAG_PLAY_PROMPT_TONE
     * | FLAG_DISMISS_ALL | FLAG_VIBRATE | FLAG_NONE
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public adjustOptionalAudioVolumeByStep(streamType: number, direction: number, flags: number, zoneId?: number): void;
    private adjustSuggestedStreamVolume(streamType: number, direction: number, flags: number): void;
    private adjustVolume(direction: number, flags: number): void;
    /**
     * <p>Gets the current volume level of a specific audio stream type.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var index = audiomanager.getAudioVolumeLevel(
     *      AudioManager.StreamType.AUDIO_STREAM_MUSIC);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * audio stream type of which the current volume level you want to obtain.
     * The streamType argument must be one of AudioManager.StreamType.
     *
     * @return {number} The current volume level of the specified audio stream type.
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getAudioVolumeLevel(streamType: number, zoneId?: number): number;
    private getStreamVolume(streamType: number): number;
    /**
     * <p>Sets the volume level of a specific audio stream type to a fixed value.</p>
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioVolumeLevel(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          6,
     *          AudioManager.AdjustFlag.FLAG_NONE);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * audio stream type of which the current volume level you want to set.
     * The streamType argument must be one of AudioManager.StreamType.
     * @param {number} index - The volume level value to be set.
     * Use getMaxAudioVolumeLevel(int) to get for the maximum valid value.
     * @param {yunos.device.AudioManager.AdjustFlag} flags - Usually passed to
     * SystemUI to indicate some UI behaviors. For example, show the volume panel.
     * One or more of FLAG_SHOW_VOLUME_PANEL | FLAG_CHANGE_RINGER_MODE | FLAG_PLAY_PROMPT_TONE
     * | FLAG_DISMISS_ALL | FLAG_VIBRATE | FLAG_NONE
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setAudioVolumeLevel(streamType: number, index: number, flags: number, zoneId?: number): void;
    private setStreamVolume(streamType: number, index: number, flags: number): void;
    /**
     * <p>Sets the mute status of a specific audio stream type.<br>
     * This method should only be used by system audio setting or telephony.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioMute(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          false);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * type of audio stream you want to mute or unmute.
     * The streamType argument must be one of AudioManager.StreamType.
     * @param {boolean} isMute - Specify mute status, true to mute and false to unmute.
     * @param {yunos.device.AudioManager.AdjustFlag} flags - Specify flags passed to volume listener,
     * such as whether to show volume panel UI. The default value is FLAG_SHOW_VOLUME_PANEL.
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setAudioMute(streamType: number, isMute: boolean, flags?: number, zoneId?: number): void;
    private setStreamMute(streamType: number, isMute: boolean): void;
    /**
     * <p>Sets the audio ringer mode.<br>
     * The ringer mode determines the phone's notification behavior
     * when there's a call comming in.
     * Slient mode no ringing and no vibrate; Vibrate mode will vibrate but no
     * ringing; Normal mode may ring and vibrate according to settings.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioRingerMode(
     *          AudioManager.RingerMode.RINGER_MODE_NORMAL);
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {yunos.device.AudioManager.RingerMode} ringerMode - The ringer
     * mode to be set. One of RINGER_MODE_NORMAL, RINGER_MODE_SILENT, or RINGER_MODE_VIBRATE.
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setAudioRingerMode(ringerMode: number): void;
    private setRingerMode(ringerMode: number): void;
    /**
     * <p>Gets the current audio ringer mode.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var mode = audiomanager.getAudioRingerMode();
     *
     * @return {number} The current audio ringer mode.
     * One of RINGER_MODE_NORMAL, RINGER_MODE_SILENT, or RINGER_MODE_VIBRATE.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getAudioRingerMode(): number;
    private getRingerMode(): number;
    /**
     * <p>Sets the audio system mode.Informs the HAL about the current audio state so
     * that it can route the audio appropriately.</p>
     * <p>This method should only be used by system setting or telephony to
     * turn the audio into appropriate mode according current phone state. For
     * example, when making a phone call, the audio mode will be changed into
     * MODE_IN_CALL.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioMode(AudioManager.Mode.MODE_NORMAL);
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {yunos.device.AudioManager.Mode} mode - The specified audio mode.
     * One of MODE_NORMAL, MODE_RINGTONE, MODE_IN_CALL, MODE_IN_COMMUNICATION, MODE_IN_TBOX_CALL.
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setAudioMode(mode: number): void;
    private setMode(mode: number): void;
    /**
     * <p>Gets the current audio mode.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var mode = audiomanager.getAudioMode();
     *
     * @return {number} The current audio mode.
     * One of MODE_NORMAL, MODE_RINGTONE, MODE_IN_CALL, MODE_IN_COMMUNICATION, MODE_IN_TBOX_CALL.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getAudioMode(): number;
    private getMode(): number;
    /**
     * <p>Checks if there is music stream currently playing.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var state = audiomanager.isMusicPlaying();
     *
     * @return {boolean} true if any music streams are playing, otherwise false.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isMusicPlaying(zoneId?: number): boolean;
    private isMusicActive(): boolean;
    /**
     * <p>Checks if there is specific stream type currently playing.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var isActive = audiomanager.isAudioPlaying(AudioManager.StreamType.AUDIO_STREAM_MUSIC);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the audio stream type to be checked.
     * @return {boolean} true if any specified stream type are playing,
     * otherwise false.
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isAudioPlaying(streamType: number, zoneId?: number): boolean;
    private isStreamActive(streamType: number): boolean;
    /**
     * <p>Checks whether the specific audio stream type is mute.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var isMute = audiomanager.isAudioMute(AudioManager.StreamType.AUDIO_STREAM_MUSIC);
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the audio stream type to be checked.
     * @return {boolean} true if the specified stream type is mute;
     * false if the specified stream type is not mute.
     *
     * @throws {RangeError} If param is not valid.
     * @throws {TypeError} If param is not valid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isAudioMute(streamType: number, zoneId?: number): boolean;
    private isStreamMute(streamType: number): boolean;
    /**
     * <p>Returns the name of client which owns current audio session.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var clientName = audiomanager.getTopSessionClientName();
     *
     * @return {string} If exits, return the current audio session owner's
     * name. If not exits, return null.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getTopSessionClientName(zoneId?: number): string;
    /**
     * <p>Register a listener to AudioStreamer to receive notification when
     * the volume of any audio stream type is changed. After registerAudioVolumeListener,
     * AudioManager will emit a corresponding event when volume is changed.
     * Developer must provide a callback function to handle this event.</p>
     *
     * <p>The event type is one of [AudioManager.VolumeListenerEvent]
     * {@link yunos.device.AudioManager.VolumeListenerEvent}
     * You can only deal with the events you are insterested in.</p>
     *
     * <p>The callback function, please see following callback definition.</p>
     *
     * <p>Note:Currently, each client can register only one listener to
     * AudioStreamer, multi-listener is NOT supported now.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when stream volume status is changed
     * audiomanager.registerAudioVolumeListener();
     *
     * //your callback function to handle volume update event
     * //you can just handle the event which you are insterested in
     * function onSafeVolumeAlert(flags) {
     *    //your logic here...
     * }
     *
     * function onStreamVolumeUpdate(streamType, newIndex, oldIndex, flags) {
     *     console.log("Stream volume is updated: streamType=" + streamType +
     *     " oldIndex=" + oldIndex + " newIndex=" + newIndex + " flags=" +
     *     flags);
     * }
     *
     * function onStreamMuteUpdate(streamType, isMute, flags) {
     *     //your logic here...
     * }
     *
     * //no need to handle all evnets, just handle the one important to you.
     * audiomanager.on(AudioManager.VolumeListenerEvent.SHOW_SAFE_ALERT,
     * onSafeVolumeAlert);
     * audiomanager.on(AudioManager.VolumeListenerEvent.STREAM_VOLUME_UPDATE,
     * onStreamVolumeUpdate);
     * audiomanager.on(AudioManager.VolumeListenerEvent.STREAM_MUTE_UPDATE,
     * onStreamMuteUpdate);
     *
     * //when you no longer need to receive volume update
     * audiomanager.unregisterAudioVolumeListener();
     *
     * @return {number} Return 0 if register listener successfully.
     * Return -1 if error occurred; usually caused by attempting to register
     * the listener repeatedly.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public registerAudioVolumeListener(): number;
    private registerVolumeListener(): number;
    /**
     * <p>Unregister the audio volume listener if you don't want to receive the
     * volume update event anymore. You can only unregister a listener that has
     * been registered before by registerAudioVolumeListener().</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when stream volume is changed
     * audiomanager.registerAudioVolumeListener();
     *
     * //your function to handle volume update event
     * function onStreamVolumeUpdate(streamType, newIndex, oldIndex, flags) {
     *     console.log("Stream volume is updated: streamType=" + streamType +
     *     " oldIndex=" + oldIndex + " newIndex=" + newIndex + " flags=" +
     *     flags);
     * }
     * audiomanager.on(AudioManager.VolumeListenerEvent.STREAM_VOLUME_UPDATE,
     * onStreamVolumeUpdate);
     *
     * //when you no longer need to receive volume update
     * audiomanager.unregisterAudioVolumeListener();
     *
     * @return {number} Return 0 if unregister successfully.
     * Return -1 if error occurred; usually caused by attempting to unregister a
     * listener that has not been registered before.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public unregisterAudioVolumeListener(): number;
    private unregisterVolumeListener(): number;
    /**
     * <p>Register a listener to AudioStreamer to receive notification when
     * the audio misc setting is changed. After registerAudioMiscEventListener,
     * AudioManager will emit a corresponding event when misc setting is changed.
     * Developer must provide a callback function to handle this event.</p>
     *
     * <p>The event type is [AudioManager.AudioMiscEventListenerNotify]
     * {@link yunos.device.AudioManager.AudioMiscEventListenerNotify}
     * You can only deal with the sub events you are insterested in.</p>
     *
     * <p>The callback function, please see following callback definition.</p>
     *
     * <p>Note:Currently, each client can register only one listener to
     * AudioStreamer, multi-listener is NOT supported now.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when audio misc setting is changed
     * audiomanager.registerAudioMiscEventListener();
     *
     * //your callback function to handle audio misc event
     * //you can just handle the sub event which you are insterested in
     *
     * function onAudioMiscEventUpdate(subEventType, bmtType, newValue, subBandIndex, flags) {
     *     console.log("Sub Event:" + subEventType + " is updated: bmtType =" + bmtType +
     *     " newValue=" + newValue + " subBandIndex=" + subBandIndex + " flags=" + flags);
     * }
     *
     * audiomanager.on(AudioManager.AudioMiscEventListenerNotify.AUDIO_MISC_EVENT_UPDATE,
     * onAudioMiscEventUpdate);
     *
     * //when you no longer need to receive volume update
     * audiomanager.unregisterAudioMiscEventListener();
     *
     * @return {number} Return 0 if register listener successfully.
     * Return -1 if error occurred; usually caused by attempting to register
     * the listener repeatedly.
     * @hiddenOnPlatform auto
     * @public
     * @since 4
     */
    public registerAudioMiscEventListener(): number;
    /**
     * <p>Unregister the audio misc event listener if you don't want to receive the
     * audio misc event update event anymore. You can only unregister a listener that has
     * been registered before by registerAudioMiscEventListener().</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when audio misc setting is changed
     * audiomanager.registerAudioMiscEventListener();
     *
     * //your function to handle audio misc setting update event
     * function onAudioMiscEventUpdate(subEventType, bmtType, newValue, subBandIndex, flags) {
     *     console.log("Sub Event:" + subEventType + " is updated: bmtType =" + bmtType +
     *     " newValue=" + newValue + " subBandIndex=" + subBandIndex + " flags=" + flags);
     * }
     *
     * audiomanager.on(AudioManager.AudioMiscEventListenerNotify.AUDIO_MISC_EVENT_UPDATE,
     * onAudioMiscEventUpdate);
     *
     * //when you no longer need to receive audio misc setting update
     * audiomanager.unregisterAudioMiscEventListener();
     *
     * @return {number} Return 0 if unregister successfully.
     * Return -1 if error occurred; usually caused by attempting to unregister a
     * listener that has not been registered before.
     * @hiddenOnPlatform auto
     * @public
     * @since 4
     */
    public unregisterAudioMiscEventListener(): number;
    private setEnableSafeVolumeAlert(isEnable: boolean): void;
    private setSafeVolumeAlertEnable(isEnable: boolean): void;
    /**
     * <p>Enable or disable the speakerphone output during a phone call.
     * This method should only be used by telephony or system audio settings.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setEnableSpeakerphone(true); //turn it on
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {boolean} isEnable - Set true to enable speakerphone;
     * false to disable  speakerphone.
     *
     * @throws {TypeError} If param is invalid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setEnableSpeakerphone(isEnable: boolean): void;
    private enableSpeakerphone(isEnable: boolean): void;
    /**
     * <p>Checks whether the speakerphone is enabled or disabled.</p>
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var state = audiomanager.isSpeakerphoneEnabled();
     *
     * @return {boolean} Return true if speakerphone is enabled;
     * return false if it is disabled.
     *
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isSpeakerphoneEnabled(): boolean;
    private isSpeakerphoneEnable(): boolean;
    /**
     * <p>Mute or unmute the microphone during a phone call.
     * This method should only be used by the telephony or system audio
     * settings.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setMuteMicrophone(true); //mute
     * audiomanager.setMuteMicrophone(false); //unmute
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {boolean} isMute - Set true to mute the microphone;
     * false to unmute the microphone.
     *
     * @throws {TypeError} If param is invalid.
     *
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setMuteMicrophone(isMute: boolean): number;
    private muteMicrophone(isMute: boolean): number;
    /**
     * <p>Checks whether the microphone is mute or unmute.</p>
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var state = audiomanager.isMicrophoneMute();
     *
     * @return {boolean} Return true if microphone is mute;
     * return false if it is unmute.
     *
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isMicrophoneMute(): boolean;
    /**
     * <p>Enable or disable the Bluetooth SCO headset during a phone call.
     * If enabled, the voice call will be routed Bluetooth to Bluetooth headset
     * (if connected). This method should only be used by the telephony.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setEnableBTSco(true); //turn it on
     * audiomanager.setEnableBTSco(false); //turn it off
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {boolean} isEnable - Set true to enable Bluetooth SCO;
     * false to disable Bluetooth SCO.
     *
     * @throws {TypeError} If param is invalid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setEnableBTSco(isEnable: boolean): void;
    private enableBTSco(isEnable: boolean): void;
    /**
     * <p>Checks whether the Bluetooth SCO headset is enabled during a phone
     * call.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var state = audiomanager.isBTScoEnabled();
     *
     * @return {boolean} Return true if Bluetooth SCO is enabled;
     * false if otherwise.
     *
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isBTScoEnabled(): boolean;
    private isBTScoEnable(): boolean;
    /**
     * <p>Set config or control parameters to audio system.
     * The parameters is a list of Key-Value pairs string.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioStringKvs("key1=value1;key2=value2");
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {string} keyValuePairs - Specify the parameters key value string.
     * String must be in this format: "key1=value1;key2=value2;..."
     *
     * @return {number} Return 0 for no errors, negative error number if otherwise.
     * @throws {TypeError} If param is invalid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setAudioStringKvs(keyValuePairs: string): number;
    private setParameters(keyValuePairs: string): number;
    /**
     * <p>Gets a list of parameter string from audio system.
     * The returned parameters Key-Value pairs string in this form:
     * "key1=value1;key2=value2;..."</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var keyvalues = audiomanager.getAudioStringKvs("key1;key2");
     *
     * @param {string} keyString - Specify the key string for the parameters to
     * be fetched, must be in this form: "key1;key2;..."
     * @return {string} Return the key-value strings for the required key,
     * in this form: "key1=value1;key2=value2;..."
     * @throws {TypeError} If param is invalid.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getAudioStringKvs(keyString: string): string;
    private getParameters(keyString: string): string;
    /**
     * <p>After the AudioSession is obtained, this callback function will be
     * invoked when the AudioSession changes. The developer should handle the
     * changes of AudioSession within this callback and make appropriate actions,
     * such as pause or resume playback.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * // a simple callback of a MusicPlayer
     * var onAudioSessionChange = function(changeType, newClientName) {
     *     console.log('Audio session changed, changeType: ' + changeType +
     *     ' new owner name:' + newClientName);
     *    if (changeType === AudioManager.AudioSessionType.AUDIOSESSION_CHANGE_RESUMED_BY_SYSTEM) {
     *      // obtain audio session
     *      // resume playback or output volume
     *      // according to previous state(for example, if stopped or paused by
     *      // user, should not resume.)
     *    } else if (changeType === AudioManager.AudioSessionType.AUDIOSESSION_CHANGE_STOPPED_BY_OTHER) {
     *      // loss audio session, stop playback
     *      // abandonAudioSession and release resource
     *    } else if (changeType === AudioManager.AudioSessionType.AUDIOSESSION_CHANGE_PAUSED_BY_OTHER) {
     *      // loss audio session temporary, pause playback
     *    } else if (changeType === AudioManager.AudioSessionType.AUDIOSESSION_CHANGE_LOWERED_BY_OTHER) {
     *      // loss audio session temporary with duck
     *      // keep playing and lower output volume
     *    }
     * };
     *
     * //befor start playback
     * var ret = audiomanager.requestAudioSession(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          AudioManager.AudioSessionType.AUDIOSESSION_REQUEST_STOP_OTHERS,
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     * if (ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED) {
     *     // Start playback
     * } else {
     *    // failed to obtain audio session
     *    // error handle or try again later.
     * }
     *
     * // when playback finish, stop playback
     *
     * // abandon audio session and releae resource
     * var ret = audiomanager.abandonAudioSession(
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     *
     * AudioManager.releaseInstance();
     *
     * @see {@link yunos.device.AudioManager.requestAudioSession}
     * @see {@link yunos.device.AudioManager.AudioSessionType}
     *
     * @callback yunos.device.AudioManager~onAudioSessionChangeCallback
     *
     * @param {yunos.device.AudioManager.AudioSessionType} changeType -
     * Represents the type of AudioSession change event.
     * One of AUDIOSESSION_CHANGE_RESUMED_BY_SYSTEM, AUDIOSESSION_CHANGE_STOPPED_BY_OTHER,
     * AUDIOSESSION_CHANGE_PAUSED_BY_OTHER,AUDIOSESSION_CHANGE_LOWERED_BY_OTHER.
     * @param {string} newClientName - The new audio session owner's client name.
     *
     * @public
     * @since 2
     */
    /**
     * <p>Request audio session with specific session type and audio stream
     * type. In most cases, an Audio APP should obtain the audio session before
     * start playing. Some short prompting sounds, such as a touch screen
     * prompt could play directly without obtaining audio session.
     * After obtaining audio session, APP should listen to the audio session
     * changes and take appropriate actions(pause/stop/resume/lower volume).</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //callback
     * var onAudioSessionChange = function(changeType, newClientName) {
     *     console.log('Audio session changed, changeType: ' + changeType +
     *     ' new owner name:' + newClientName);
     *    // pause or stop or resume playback according to changeType
     *    // see onAudioSessionChangeCallback example
     * };
     *
     * //befor start playback
     * var ret = audiomanager.requestAudioSession(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          AudioManager.AudioSessionType.AUDIOSESSION_REQUEST_STOP_OTHERS,
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     * if (ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED) {
     *     // obtained audio session, begin to play
     *     // strat playback here...
     * } else {
     *    // failed to obtain audio session
     *    // error handle or try again later.
     * }
     *
     * // waiting playback finish
     * // stop playback here...
     *
     * // abandon audio session and releae resource
     * var ret = audiomanager.abandonAudioSession(
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     *
     * AudioManager.releaseInstance();
     *
     * @see {@link yunos.device.AudioManager.StreamType}
     * @see {@link yunos.device.AudioManager.AudioSessionType}
     * @see {@link yunos.device.AudioManager.AudioSessionResult}
     * @see {@link yunos.device.AudioManager~onAudioSessionChangeCallback}
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * type of audio stream the APP will paly.
     * @param {yunos.device.AudioManager.AudioSessionType} sessionType - The
     * session type represents how long the APP will play and how the other
     * audio APPs currently playing should respond. One of
     * AUDIOSESSION_REQUEST_STOP_OTHERS,
     * AUDIOSESSION_REQUEST_PAUSE_OTHERS, AUDIOSESSION_REQUEST_LOWER_OTHERS, or
     * AUDIOSESSION_REQUEST_EXCLUDE_OTHERS.
     * @param {string} clientName - The name of current requester, used to
     * distinguish between different requests.
     * NOTE:If you have 2(or more) different audio session clients in one same
     * page, make sure use different name for each client.
     * @param {yunos.device.AudioManager~onAudioSessionChangeCallback} callback -
     * The callback will be called when audio session changes.
     * APP should take appropriate actions in this callback.
     * @param {yunos.device.AudioManager.AudioSessionFlag} flags - Specify the
     * session flags used to indicate some extras process. Such as whether to
     * allow a delayed session request.
     *
     * @return {yunos.device.AudioManager.AudioSessionResult} If request audio session successfully,
     * return AUDIOSESSION_RESULT_GRANTED; return AUDIOSESSION_RESULT_FAILED if falied.
     * Or return AUDIOSESSION_RESULT_DELAYED if FLAG_ALLOW_DELAY is
     * passed under some specific condition.
     *
     * @throws {RangeError} If param is invalid.
     * @throws {TypeError} If param is invalid.
     *
     * @public
     * @since 2
     */
    /**
     * <p>Request an audio session with specific audio stream type, session type,
     * and use case type. In most cases, an Audio APP should obtain the audio session
     * before start playing. Some short prompting sounds, such as a touch screen
     * prompt could play directly without obtaining audio session.
     * After obtaining audio session, APP should listen to the audio session
     * changes and take appropriate actions(pause/stop/resume).</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //callback
     * var onAudioSessionChange = function(changeType, newClientName) {
     *     console.log('Audio session changed, changeType: ' + changeType +
     *     ' new owner name:' + newClientName);
     *    // pause or stop or resume playback according to changeType
     *    // see onAudioSessionChangeCallback example
     * };
     *
     * //befor start playback
     * var ret = audiomanager.requestAudioSession(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          AudioManager.AudioSessionType.AUDIOSESSION_NONE,
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange,
     *          AudioManager.AudioSessionFlag.AUDIOSESSION_NONE,
     *          AudioManager.AudioSessionUsecaseType.AUDIO_SESSION_USECASE_MEDIA);
     * if (ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED) {
     *     // obtained audio session, begin to play
     *     // strat playback here...
     * } else {
     *    // failed to obtain audio session
     *    // error handle or try again later.
     * }
     *
     * // waiting playback finish
     * // stop playback here...
     *
     * // abandon audio session and releae resource
     * var ret = audiomanager.abandonAudioSession(
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     *
     * AudioManager.releaseInstance();
     *
     * @see {@link yunos.device.AudioManager.StreamType}
     * @see {@link yunos.device.AudioManager.AudioSessionType}
     * @see {@link yunos.device.AudioManager~onAudioSessionChangeCallback}
     * @see {@link yunos.device.AudioManager.AudioSessionFlag}
     * @see {@link yunos.device.AudioManager.AudioSessionUsecaseType}
     * @see {@link yunos.device.AudioManager.AudioSessionResult}
     *
     * @param {yunos.device.AudioManager.StreamType} streamType - Specify the
     * type of audio stream the APP will paly, and if you also define the usecaseType,
     * it must match with the usecaseType.
     *
     * about the stream type choose, there are two rules you should follow:
     * 1. choose the right stream type to match with usecaseType if has:
     * @example
     * for AUDIO_SESSION_USECASE_CHIME, you need choose AUDIO_STREAM_CHIME.
     * for AUDIO_SESSION_USECASE_REVERSE, you need choose AUDIO_STREAM_CHIME.
     * for AUDIO_SESSION_USECASE_ALERT, you need choose AUDIO_STREAM_SAFETY_ALERT.
     * for AUDIO_SESSION_USECASE_SAFETY, you need choose AUDIO_STREAM_SAFETY_ALERT.
     * for AUDIO_SESSION_USECASE_TBOX, AUDIO_SESSION_USECASE_VOICE, AUDIO_SESSION_USECASE_RING and AUDIO_SESSION_USECASE_COMMUNICATION,
     * you need choose AUDIO_STREAM_VOICE_CALL, AUDIO_STREAM_BLUETOOTH_SCO, AUDIO_STREAM_RING, AUDIO_STREAM_DTMF or AUDIO_STREAM_TTS.
     * for AUDIO_SESSION_USECASE_VR, you need choose AUDIO_STREAM_TTS.
     * for AUDIO_SESSION_USECASE_NAVIGATION, you need choose AUDIO_STREAM_NAVIGATION.
     * for AUDIO_SESSION_USECASE_NOTIFICATION, you need choose AUDIO_STREAM_NOTIFICATION, AUDIO_STREAM_ALARM or AUDIO_STREAM_TTS.
     * for AUDIO_SESSION_USECASE_MEDIA, you need choose AUDIO_STREAM_MUSIC or AUDIO_STREAM_FM.
     * for AUDIO_SESSION_USECASE_SYSTEM, you need choose AUDIO_STREAM_SYSTEM, AUDIO_STREAM_ENFORCED_AUDIBLE.
     * for AUDIO_SESSION_USECASE_EXTERNAL and AUDIO_SESSION_USECASE_RECORDING, there are no actual playback data to IVI audio system,
     * system doesn't limit the streamType.
     *
     * 2. use the requested stream type for your subsequent data playback;
     *
     * @param {yunos.device.AudioManager.AudioSessionType} sessionType - The
     * session type represents how long the APP will play and how the other
     * audio APPs currently playing should respond. One of
     * AUDIOSESSION_NONE,
     * AUDIOSESSION_REQUEST_STOP_OTHERS,
     * AUDIOSESSION_REQUEST_PAUSE_OTHERS, AUDIOSESSION_REQUEST_LOWER_OTHERS, or
     * AUDIOSESSION_REQUEST_EXCLUDE_OTHERS.
     * @param {string} clientName - The name of current requester, used to
     * distinguish between different requests.
     * NOTE:If you have 2(or more) different audio session clients in one same
     * page, make sure use different name for each client.
     * @param {yunos.device.AudioManager~onAudioSessionChangeCallback} callback -
     * The callback will be called when audio session changes.
     * APP should take appropriate actions in this callback.
     * @param {yunos.device.AudioManager.AudioSessionFlag} flags - Specify the
     * session flags used to indicate some extras process. Such as whether to
     * allow a delayed session request.
     * @param {yunos.device.AudioManager.AudioSessionUsecaseType} usecaseType - Choose the
     * type of the audio use case it will happen.
     *
     * @return {yunos.device.AudioManager.AudioSessionResult} If request audio session successfully,
     * return AUDIOSESSION_RESULT_GRANTED; return AUDIOSESSION_RESULT_FAILED if falied.
     * Or return AUDIOSESSION_RESULT_DELAYED if FLAG_ALLOW_DELAY is
     * passed under some specific condition.
     *
     * @throws {RangeError} If param is invalid.
     * @throws {TypeError} If param is invalid.
     *
     * @public
     * @since 5
     */
    public requestAudioSession(streamType: number, sessionType: number, clientName: string, callback: (changeType: number, newClientName: string, zoneId: number) => void, flags?: number, usecaseType?: number, sessionName?: {
        sessionName: string;
    }, zoneId?: number): number;
    /**
     * <p>Abandon audio session to releae ownership so that the preempted app
     * can be regranted again. When the playback is finished, APP should
     * abandon audio session and request it before the next playback.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //callback
     * var onAudioSessionChange = function(changeType, newClientName) {
     *     console.log('Audio session changed, changeType: ' + changeType +
     *     ' new owner name:' + newClientName);
     *    // pause or stop or resume playback according to changeType
     *    // see onAudioSessionChangeCallback example
     * };
     *
     * //befor start playback
     * var ret = audiomanager.requestAudioSession(
     *          AudioManager.StreamType.AUDIO_STREAM_MUSIC,
     *          AudioManager.AudioSessionType.AUDIOSESSION_REQUEST_STOP_OTHERS,
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     * if (ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED) {
     *     // obtained audio session, begin to play
     *     // strat playback here...
     * } else {
     *    // failed to obtain audio session
     *    // error handle or try again later.
     * }
     *
     * // waiting playback finish
     * // stop playback here...
     * // stopPlay();
     *
     * // abandon audio session and releae resource
     * var ret = audiomanager.abandonAudioSession(
     *          'yunos.example.audiomanager',
     *          onAudioSessionChange);
     *
     * AudioManager.releaseInstance();
     *
     * @see {@link yunos.device.AudioManager.AudioSessionResult}
     * @see {@link yunos.device.AudioManager~onAudioSessionChangeCallback}
     * @see {@link yunos.device.AudioManager.requestAudioSession}
     *
     * @param {string} clientName - The name of current client. This name must
     * be the same as the name used for request.
     * @param {yunos.device.AudioManager~onAudioSessionChangeCallback} callback -
     * This is the callback used to request audio session.
     *
     * @return {yunos.device.AudioManager.AudioSessionResult} If abandon ausio session successfully,
     * return AUDIOSESSION_RESULT_GRANTED; otherwise return AUDIOSESSION_RESULT_FAILED.
     *
     * @throws {TypeError} If param is invalid.
     *
     * @public
     * @since 2
     */
    public abandonAudioSession(clientName: string, callback: (changeType: number, newClientName: string, zoneId: number) => void, zoneId?: number): number;
    /**
     * <p>Register a listener to AudioStreamer to receive notification when
     * the audio ringer mode is changed. After registerAudioVolumeListener,
     * AudioManager will emit a RingerModeChangedEvent event and APP should
     * handle this event appropriately.</p>
     *
     * <p>The event emitted is [AudioManager.RingerModeChangedEvent]
     * {@link yunos.device.AudioManager.RingerModeChangedEvent}.</p>
     *
     * <p>The callback function, please see following event definition.</p>
     *
     * <p>Note:Only one ringer mode listener can be registered for each client.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when ringer mode is changed
     * audiomanager.registerAudioRingerModeListener();
     *
     * //your callback function to handle ringer mode change event
     * //newMode: yunos.device.AudioManager.RingerMode
     * //flags: reserved
     * function onRingerModeChange(newMode, flags) {
     *    //your logic here...
     * }
     *
     * audiomanager.on(AudioManager.RingerModeChangedEvent.RINGER_MODE_CHANGED,
     * onRingerModeChange);
     *
     * //when you no longer need to receive ringer mode change
     * audiomanager.unregisterAudioRingerModeListener();
     *
     * @see {@link yunos.device.AudioManager.RingerMode}
     * @see {@link yunos.device.AudioManager.RingerModeChangedEvent}
     * @see {@link yunos.device.AudioManager#ringermodechanged}
     *
     * @return {number} Return 0 if register listener successfully.
     * Return negative if error occurred; usually caused by attempting to
     * register the listener repeatedly.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public registerAudioRingerModeListener(): number;
    private registerRingerModeListener(): number;
    /**
     * <p>Unregister the audio ringer mode listener, when you don't want to
     * recevie the ringer mode notification anymore.</p>
     * <p>You can only unregister the listener that has been registered by calling
     * registerAudioRingerModeListener().</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when ringer mode is changed
     * audiomanager.registerAudioRingerModeListener();
     *
     * //your callback function to handle ringer mode change event
     * function onRingerModeChange(newMode, flags) {
     *    //your logic here...
     * }
     *
     * audiomanager.on(AudioManager.RingerModeChangedEvent.RINGER_MODE_CHANGED,
     * onRingerModeChange);
     *
     * //when you no longer need to receive ringer mode change
     * audiomanager.unregisterAudioRingerModeListener();
     *
     * @return {number} Return 0 if unregister successfully.
     * Return negative if error occurred; usually caused by trying to unregister a
     * listener that has not been registered before.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public unregisterAudioRingerModeListener(): number;
    private unregisterRingerModeListener(): number;
    /**
     * <p>Register a listener to receive notification when audio device is
     * connected or disconnected. After register, AudioManager will emit one of
     * DeviceStateChangedEvent when the connection state of a certain audio
     * device changes(connected or disconnected). Client should provide a
     * callback function to handle this event.</p>
     *
     * <p>Note:This method can be invoked many times with different device type
     * to listen on multiple devices state change.</p>
     *
     * <p>The event emitted is one of [AudioManager.DeviceStateChangedEvent]
     * {@link yunos.device.AudioManager.DeviceStateChangedEvent}.</p>
     *
     * <p>The callback function, please see following event definition.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when specified device state changed
     * audiomanager.registerAudioDeviceStateListener(AudioManager.AudioDeviceType.OUT_WIRED_HEADSET);
     *
     * //your callback function to handle device state change event
     * function onDevicePlugIn(deviceType) {
     *    //your logic here...
     * }
     * function onDevicePlugOut(deviceType) {
     *    //your logic here...
     * }
     *
     * audiomanager.on(AudioManager.DeviceStateChangedEvent.DEVICE_PLUGGED_IN,
     * onDevicePlugIn);
     * audiomanager.on(AudioManager.DeviceStateChangedEvent.DEVICE_PLUGGED_OUT,
     * onDevicePlugOut);
     *
     * //when you no longer need to receive device state change
     * audiomanager.unregisterAudioDeviceStateListener(AudioManager.AudioDeviceType.OUT_WIRED_HEADSET);
     *
     * @see {@link yunos.device.AudioManager.AudioDeviceType}
     * @see {@link yunos.device.AudioManager.DeviceStateChangedEvent}
     * @see {@link yunos.device.AudioManager#devicepluggedin}
     * @see {@link yunos.device.AudioManager#devicepluggedout}
     *
     * @param {yunos.device.AudioManager.AudioDeviceType} deviceType - Specify
     * the type of audio device to be listened.
     * @return {number} Return 0 if register listener successfully.
     * Return negative if error occurred.
     * @throws {TypeError} If param is invalid.
     *
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public registerAudioDeviceStateListener(deviceType: number): number;
    /**
     * <p>Unregister the audio device state listener for the specific device
     * type if you don't want to receive notification about such audio device
     * anymore.
     * NOTE: Invoke this method with AUDIO_DEVICE_NONE type passed in
     * to unregister listener for all audio devices.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     *
     * //want to got notified when specified device state changed
     * audiomanager.registerAudioDeviceStateListener(AudioManager.AudioDeviceType.OUT_WIRED_HEADSET);
     *
     * //your callback function to handle device state change event
     * function onDevicePlugIn(deviceType) {
     *    //your logic here...
     * }
     * function onDevicePlugOut(deviceType) {
     *    //your logic here...
     * }
     *
     * audiomanager.on(AudioManager.DeviceStateChangedEvent.DEVICE_PLUGGED_IN,
     * onDevicePlugIn);
     * audiomanager.on(AudioManager.DeviceStateChangedEvent.DEVICE_PLUGGED_OUT,
     * onDevicePlugOut);
     *
     * //when you no longer need to receive device state change
     * audiomanager.unregisterAudioDeviceStateListener(AudioManager.AudioDeviceType.OUT_WIRED_HEADSET);
     * //or you can unregister listener for all devices
     * audiomanager.unregisterAudioDeviceStateListener(AudioManager.AudioDeviceType.AUDIO_DEVICE_NONE);
     *
     * @see {@link yunos.device.AudioManager.AudioDeviceType}
     *
     * @param {yunos.device.AudioManager.AudioDeviceType} deviceType - Specify
     * the type of audio device that does not need to be listened.
     * AUDIO_DEVICE_NONE to remove listener for all audio devices.
     * @return {number} Return 0 if register listener successfully.
     * Return negative if error occurred.
     * @throws {TypeError} If param is invalid.
     *
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public unregisterAudioDeviceStateListener(deviceType: number): number;
    private getAudioDeviceForStream(streamType: number, zoneId?: number): number;
    private isAudioRingerModeRelevant(streamType: number): boolean;
    private isStreamAffectedByRingerMode(streamType: number): boolean;
    private setAudioRouteConfig(type: number, config: number): number;
    private setForceUse(type: number, config: number): number;
    /**
     * <p>Sets the audio effect fade level.</p>
     * <p>This method should only be used by system setting to adjust system
     * fade effect level.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioEffectFade(fade);
     *
     * @param {number} fade - The integer value range, and make sue to call getAudioEffectFade
     * first to get right level range which device supported.
     *
     * @return {number} Return 0 for non-error; othres for error.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public setAudioEffectFade(fade: number): number;
    /**
     * <p>Sets the audio effect balance level.</p>
     * <p>This method should only be used by system setting to adjust system
     * balance effect level.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioEffectBalance(balance);
     *
     * @param {number} balance - The integer value range, and make sue to call getAudioEffectBalance
     * first to get right level range which device supported.
     *
     * @return {number} Return 0 for non-error; othres for error.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public setAudioEffectBalance(balance: number): number;
    /**
     * <p>Sets the audio effect filterType level.</p>
     * <p>This method should only be used by system setting to adjust system
     * filter effect level.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioEffectBMT(filterType, enableFlag, value);
     *
     * @param {yunos.device.AudioManager.AudioEffectFilterType} filterType - The specified type
     * One of AUDIO_EFFECT_FILTER_TYPE_BASS, AUDIO_EFFECT_FILTER_TYPE_MID, AUDIO_EFFECT_FILTER_TYPE_TREBLE.
     * @param {boolean} enableFlag - The boolean value to enable or disable filter.
     * @param {number} value - The integer value range, and make sue to call getAudioEffectBMT
     * first to get right level range which device supported.
     *
     * @throws {RangeError} If param is not valid.
     * @return {number} Return 0 for non-error; others for error.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public setAudioEffectBMT(filterType: number, enableFlag: boolean, value: number): number;
    /**
     * <p>Sets the audio preset eq level.</p>
     * <p>This method should only be used by system setting to adjust system
     * preset eq effect level.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioEffectPresetEQ(presetId, enableFlag);
     *
     * @param {number} presetId - the supported preset eq id
     * make sure to call getAudioEffectEQ first to get id list.
     * @param {boolean} enableFlag - The boolean value to enable or disable filter.
     *
     * @return {number} Return 0 for non-error; othres for error.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public setAudioEffectPresetEQ(presetId: number, enableFlag: boolean): number;
    /**
     * <p>Sets the audio customized eq level.</p>
     * <p>This method should only be used by system setting to adjust system
     * customized eq effect level.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setAudioEffectCustomizedEQ(bandIndex, centerFreq, bandLevel);
     *
     * @param {number} bandIndex - the supported customized eq id list
     * make sure to call getAudioEffectEQ first to get id list.
     * @param  {number} centerFreq - The integer value of center frequency in millihertz.
     * @param  {number} bandLevel - the supported bandLevel in millibels, which get from getAudioEffectEQ.
     *
     * @return {number} Return 0 for non-error; othres for error.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public setAudioEffectCustomizedEQ(bandIndex: number, centerFreq: number, bandLevel: number): number;
    /**
     * <p>Gets the audio effect fade level.</p>
     * <p>This method should only be used by system setting to get system
     * fade effect level, including current value, min, and max value.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var fade = audiomanager.getAudioEffectFade();
     * console.log(fade.status);
     * console.log(fade.value);
     * console.log(fade.min);
     * console.log(fade.max);
     *
     * @return {Object} Return Object, including error status, 0 for non-error,
     * current fade level, supported min and max level setting.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public getAudioEffectFade(): object;
    /**
     * <p>Gets the audio effect balance level.</p>
     * <p>This method should only be used by system setting to get system
     * balance effect level, including current value, min, and max value.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var balance = audiomanager.getAudioEffectBalance();
     * console.log(balance.status);
     * console.log(balance.value);
     * console.log(balance.min);
     * console.log(balance.max);
     *
     * @return {Object} Return Object, including error status, 0 for non-error,
     * current balance level, supported min and max level setting.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public getAudioEffectBalance(): object;
    /**
     * <p>Gets the audio effect filter level based on filter type.</p>
     * <p>This method should only be used by system setting to get system
     * filter level, including current value, min, and max value.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var bass = audiomanager.getAudioEffectBMT(AudioManager.AudioEffectFilterType.AUDIO_EFFECT_FILTER_TYPE_BASS);
     * console.log(bass.status);
     * console.log(bass.value);
     * console.log(bass.min);
     * console.log(bass.max);
     *
     * @param {yunos.device.AudioManager.AudioEffectFilterType} filterType - The specified type
     * One of AUDIO_EFFECT_FILTER_TYPE_BASS, AUDIO_EFFECT_FILTER_TYPE_MID, AUDIO_EFFECT_FILTER_TYPE_TREBLE.
     *
     * @throws {RangeError} If param is not valid.
     * @return {Object} Return Object, including error status, 0 for non-error,
     * current filter level, supported min and max level setting.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public getAudioEffectBMT(filterType: number): object;
    /**
     * <p>Gets the device supported audio preset eq list and customized eq number .</p>
     * <p>This method should only be used by system setting to get system
     * supported customized eq number and detail information of preset eq.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var eq = audiomanager.getAudioEffectEQ();
     * console.log(eq.status);
     * console.log(eq.num_customized_bands);
     * console.log(eq.preset_value);
     * console.log(eq.preset_min);
     * console.log(eq.preset_max);
     *
     * @return {Object} Return Object, including error status, 0 for non-error,
     * number of customized eq and preset eq level, min and max level setting.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public getAudioEffectEQ(): object;
    /**
     * <p>Gets the specified customized eq detail information .</p>
     * <p>This method should only be used by system setting to get system
     * specified band index customized eq information.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var eq = audiomanager.getAudioEffectCustomizedEQ(bandIndex);
     * console.log(eq.status);
     * console.log(eq.freq_value);
     * console.log(eq.freq_min);
     * console.log(eq.freq_max);
     * console.log(eq.level_value);
     * console.log(eq.level_min);
     * console.log(eq.level_max);
     *
     * @param {number} bandIndex - the supported customized eq id.
     *
     * @return {Object} Return Object, including error status, 0 for non-error,
     * specified band frequency range in millihertz., and supported min and max level in millibels.
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     * @public
     * @since 3
     *
     */
    public getAudioEffectCustomizedEQ(bandIndex: number): object;
    /**
     * <p>Enable or disable the music fadein.
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setEnableMusicFadein(true); //turn it on
     *
     * @permission MODIFY_AUDIO_SETTINGS.permission.yunos.com
     *
     * @param {boolean} isEnable - Set true to enable music fadein;
     * false to disable music fadein.
     *
     * @throws {TypeError} If param is invalid.
     * @public
     * @since 3
     */
    public setEnableMusicFadein(isEnable: boolean): number;
    /**
     * <p>Checks whether music fadein is enabled
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var state = audiomanager.isMusicFadeinEnabled();
     *
     * @return {boolean} Return true if music fadein is enabled;
     * false if otherwise.
     *
     * @public
     * @since3
     */
    public isMusicFadeinEnabled(): boolean;
    private setAudioVolumeRange(streamType: number, minLevel: number, maxLevel: number, zoneId?: number): number;
    private getAudioVolumeRange(streamType: number, zoneId?: number): {
        minLevel: number;
        maxLevel: number;
    };
    private restoreAudioSetting(settingType: number, zoneId?: number): number;
    private setSpectrumBand(band: number, frequency: number): number;
    private setSpectrumQ(Q: number): number;
    private setSpectrumBeat(streamType: number, duration: number, zoneId?: number): number;
    /**
     * <p>Gets the current spectrum band capacity.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var mode = audiomanager.getSpectrumCapacity();
     *
     * @return {number} The current band capacity.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getSpectrumCapacity(): number;
    /**
     * <p>Gets the current spectrum beat duration.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var mode = audiomanager.getSpectrumDuation(streamType);
     *
     * @param {number} streamType - stream type
     *
     * @return {number} The current beat duration.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getSpectrumDuation(streamType: number, zoneId?: number): number;
    /**
     * <p>Register a listener to AudioStreamer to receive spectrum band inforamtion
     * After registerSpectrumInfoListener, AudioManager will emit a SpectrumInfoNotifyEvent event
     * and APP should handle this event appropriately.</p>
     *
     * <p>The event emitted is [AudioManager.SpectrumInfoNotifyEvent]
     * {@link yunos.device.AudioManager.SpectrumInfoNotifyEvent}.</p>
     *
     * <p>The callback function, please see following event definition.</p>
     *
     * <p>Note:Only one spectrum info listener can be registered for each client.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.registerSpectrumInfoListener(streamType);
     *
     * audiomanager.on(AudioManager.SpectrumInfoNotifyEvent.SPECTRUM_INFO_NOTIFY,
     * onSpectrumInfoNotify);
     *
     * //when you no longer need to receive spectrum band information
     * audiomanager.unregisterSpectrumInfoListener();
     *
     * @param {number} streamType - stream type
     *
     * @return {number} Return 0 if register listener successfully.
     * Return negative if error occurred; usually caused by attempting to
     * register the listener repeatedly.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public registerSpectrumInfoListener(streamType: number, zoneId?: number): number;
    /**
     * <p>Unregister the spectrum band information listener, when you don't want to
     * recevie the spectrum band information notification anymore.</p>
     * <p>You can only unregister the listener that has been registered by calling
     * registerSpectrumInfoListener().</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.unregisterSpectrumInfoListener(streamType);
     *
     * @param {number} streamType - stream type
     *
     * @return {number} Return 0 if unregister successfully.
     * Return negative if error occurred; usually caused by trying to unregister a
     * listener that has not been registered before.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public unregisterSpectrumInfoListener(streamType: number, zoneId?: number): number;
    /**
     * <p>set master mute state.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setMasterMute(true);
     *
     * @param {boolean} isMute - is mute
     *
     * @return {number} Return 0 if successfully.
     * Return negative if error occurred;
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setMasterMute(isMute: boolean): number;
    /**
     * <p>get master mute state.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var isMute = audiomanager.getMasterMute();
     *
     * @return {number} Return true if master is muted, false if master is not muted.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public isMasterMute(): boolean;
    /**
     * <p>set zone active state.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.setZoneActiveByType(zoneType, streamType, true);
     *
     * @param {number} zoneType - zone type
     * @param {number} streamType - stream type
     * @param {boolean} active - active state
     *
     * @return {number} Return 0 if successfully.
     * Return negative if error occurred;
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public setZoneActiveByType(zoneType: number, streamType: number, active: boolean): number;
    /**
     * <p>get zone active state.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.getZoneActiveByType(zoneType, streamType);
     *
     * @param {number} zoneType - zone type
     * @param {number} streamType - stream type
     *
     * @return {number} Return 0 or 1 if successfully. 0 is deactive , 1 is active
     * Return negative if error occurred;
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getZoneActiveByType(zoneType: number, streamType: number): number;
    /**
     * <p>get all zone type state.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.getZoneTypeState();
     *
     * @return {number} Return all zonetype state
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getZoneTypeState(): {
        zoneTypeInfos: number[];
        zoneTypeStreamState: number[][];
    };
    /**
     * <p>get all zone state.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * var zoneStates = audiomanager.getAllZoneState();
     * var driverZoneState = (zoneStates >> AudioManager.ZoneId.ADEV_ZONE_DRIVER) & 1
     * var rearLeftZoneState = (zoneStates >> AudioManager.ZoneId.ADEV_ZONE_REAR_LEFT) & 1
     * var outSideZoneState = (zoneStates >> AudioManager.ZoneId.ADEV_ZONE_OUTSIDE) & 1
     *
     * @return {number} Return all zonet state in bitmap
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public getAllZoneState(): number;
    /**
 * <p>get zone state.</p>
 *
 * @example
 * var AudioManager = require("yunos/device/AudioManager");
 * var audiomanager = AudioManager.getInstance();
 * var rearLeftZoneState = audiomanager.getZoneState(AudioManager.ZoneId.ADEV_ZONE_REAR_LEFT);
 *
 * @return {number} Return zone state 1/0
 * @hiddenOnPlatform auto
 * @public
 * @since 2
 */
    public getZoneState(zoneId: number): number;
    /**
     * <p>Register a listener to AudioStreamer to receive zone type event.</p>
     *
     * <p>The event emitted is [AudioManager.ZoneTypeEvent]
     * {@link yunos.device.AudioManager.ZoneTypeEvent}.</p>
     *
     * <p>The callback function, please see following event definition.</p>
     *
     * <p>Note:Only one zonetype listener can be registered for each client.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.registerZoneEventListener();
     *
     * audiomanager.on(AudioManager.ZoneTypeEvent.ZONE_EVENT_NOTIFY,
     * onzoneEvent);
     *
     * //when you no longer need to receive zone type event
     * audiomanager.unregisterZoneEventListener();
     *
     *
     * @return {number} Return 0 if register listener successfully.
     * Return negative if error occurred; usually caused by attempting to
     * register the listener repeatedly.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public registerZoneEventListener(): number;
    /**
     * <p>Unregister the zone type event listener, when you don't want to
     * recevie the zone type event notification anymore.</p>
     *
     * @example
     * var AudioManager = require("yunos/device/AudioManager");
     * var audiomanager = AudioManager.getInstance();
     * audiomanager.unregisterZoneEventListener();
     *
     * @return {number} Return 0 if unregister successfully.
     * Return negative if error occurred; usually caused by trying to unregister a
     * listener that has not been registered before.
     * @hiddenOnPlatform auto
     * @public
     * @since 2
     */
    public unregisterZoneEventListener(): number;
    private static readonly ZoneType: {
        ADEV_ZONE_TYPE_DEFAULT: int;
        ADEV_ZONE_TYPE_MAIN: int;
        ADEV_ZONE_TYPE_SEAT_HEAD_REST: int;
        ADEV_ZONE_TYPE_BT_HEAD_PHONE: int;
        ADEV_ZONE_TYPE_REAR_ZONE_ONE: int;
        ADEV_ZONE_TYPE_REAR_ZONE_TWO: int;
        ADEV_ZONE_TYPE_CNT: int;
    };
    /**
     * <p>Enum for Audio Zone id</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ZoneId: {
        /**
         * All passengers in the car..
         * @public
         */
        ADEV_ZONE_INVALID: int;
        /**
         * All passengers in the car..
         * @public
         */
        ADEV_ZONE_ALL: int;
        /**
         * Driver
         * @public
         */
        ADEV_ZONE_DRIVER: int;
        /**
         * Front Right.
         * @public
         */
        ADEV_ZONE_FRONT_RIGHT: int;
        /**
         * Middle left passenger.
         * @public
         */
        ADEV_ZONE_MID_LEFT: int;
        /**
         * Middle center passenger.
         * @public
         */
        ADEV_ZONE_MID_CENTER: int;
        /**
         * Middle right passenger.
         * @public
         */
        ADEV_ZONE_MID_RIGHT: int;
        /**
         * Rear left passenger.
         * @public
         */
        ADEV_ZONE_REAR_LEFT: int;
        /**
         * Rear center passenger.
         * @public
         */
        ADEV_ZONE_REAR_CENTER: int;
        /**
         * Rear right passenger.
         * @public
         */
        ADEV_ZONE_REAR_RIGHT: int;
        /**
         * BT headphone
         * @public
         */
        ADEV_ZONE_BT_HEADPHONE: int;
        /**
         * Outside of the car.
         * @public
         */
        ADEV_ZONE_OUTSIDE: int;
        /**
         * Inside of the car.
         * @public
         */
        ADEV_ZONE_INSIDE: int;
        ADEV_ZONE_MAX: int;
    };
    /**
     * <p>Enum for Audio stream type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly StreamType: {
        /**
         * Used internal only.
         * @private
         */
        AUDIO_STREAM_MIN: int;
        /**
         * Used when no idea for stream type.
         * @private
         */
        AUDIO_STREAM_DEFAULT: int;
        /**
         * The stream type for telephony calls.
         * @public
         * @since 2
         */
        AUDIO_STREAM_VOICE_CALL: int;
        /**
         * The stream type for system sound.
         * @public
         * @since 2
         */
        AUDIO_STREAM_SYSTEM: int;
        /**
         * The stream type for phone ring.
         * @public
         * @since 2
         */
        AUDIO_STREAM_RING: int;
        /**
         * The stream type for music/video playback.
         * @public
         * @since 2
         */
        AUDIO_STREAM_MUSIC: int;
        /**
         * The stream type for alarms.
         * @public
         * @since 2
         */
        AUDIO_STREAM_ALARM: int;
        /**
         * The stream type for notifications and prompt sound.
         * @public
         * @since 2
         */
        AUDIO_STREAM_NOTIFICATION: int;
        /**
         * The stream type for bluetooth voice call.
         * @public
         * @since 5
         */
        AUDIO_STREAM_BLUETOOTH_SCO: int;
        /**
         * The stream type for sound can't be muted, like shutter sound.
         * @public
         * @since 5
         */
        AUDIO_STREAM_ENFORCED_AUDIBLE: int;
        /**
         * The stream type for dailing sound during the call.
         * @public
         * @since 5
         */
        AUDIO_STREAM_DTMF: int;
        /**
         * The stream type for text to speech sound.
         * @public
         * @since 5
         */
        AUDIO_STREAM_TTS: int;
        /**
         * The stream type for fm sound.
         * @public
         * @since 5
         */
        AUDIO_STREAM_FM: int;
        /**
         * The stream type for navigation sound.
         * @public
         * @since 5
         */
        AUDIO_STREAM_NAVIGATION: int;
        /**
         * The stream type for alert sound.
         * @public
         * @since 5
         */
        AUDIO_STREAM_SAFETY_ALERT: int;
        /**
         * The stream type for car information sound.
         * @public
         * @since 5
         */
        AUDIO_STREAM_CHIME: int;
        /**
         * The stream type for bt music.
         * @public
         * @since 5
         */
        AUDIO_STREAM_MAX: int;
    };
    /**
     * <p>Enum for Audio Session Use Case Type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly AudioSessionUsecaseType: {
        /**
         * audio use case for invalid case.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_INVALID: int;
        /**
         * audio use case for car chime sound.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_CHIME: int;
        /**
         * audio use case for back a car.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_REVERSE: int;
        /**
         * audio use case for car alert tone.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_ALERT: int;
        /**
         * audio use case for safety playback.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_SAFETY: int;
        /**
         * audio use case for car tbox e-call, b-call and i-call.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_TBOX: int;
        /**
         * audio use case for bluetooth voice call.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_VOICE: int;
        /**
         * audio use case for bluetooth ringtone.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_RING: int;
        /**
         * audio use case for VoIP call and instant message.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_COMMUNICATION: int;
        /**
         * audio use case for voice recognition tts playback.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_VR: int;
        /**
         * audio use case for navigation playback.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_NAVIGATION: int;
        /**
         * audio use case for notification playback.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_NOTIFICATION: int;
        /**
         * audio use case for media.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_MEDIA: int;
        /**
         * audio use case for system sound.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_SYSTEM: int;
        /**
         * audio use case for sound outside the car.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_EXTERNAL: int;
        /**
         * audio use case for sound recording.
         * @public
         * @since 5
         */
        AUDIO_SESSION_USECASE_RECORDING: int;
    };
    /**
     * <p>Enum for Audio adjust direction.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly AdjustDirection: {
        /**
         * Decrease the stream volume.
         * @public
         * @since 2
         */
        ADJUST_LOWER: int;
        /**
         * Keep current stream volume and not change.
         * @public
         * @since 2
         */
        ADJUST_SAME: int;
        /**
         * Increase the stream volume.
         * @public
         * @since 2
         */
        ADJUST_RAISE: int;
    };
    /**
     * <p>Enum for Audio adjust flags.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly AdjustFlag: {
        /**
         * No flags when adjust volume.
         * @public
         * @since 2
         */
        FLAG_NONE: int;
        /**
         * Show the volume panel UI.
         * @public
         * @since 2
         */
        FLAG_SHOW_VOLUME_PANEL: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        FLAG_SHOW_UI: int;
        /**
         * Whether to change ringer mode when adjusting volume.
         * @public
         * @since 2
         */
        FLAG_CHANGE_RINGER_MODE: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        FLAG_ALLOW_RINGER_MODES: int;
        /**
         * Whether to play a sound on volume changed.
         * @public
         * @since 2
         */
        FLAG_PLAY_PROMPT_TONE: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        FLAG_PLAY_SOUND: int;
        /**
         * Remove any vibrate/sounds that are in queue, or are playing.
         * @public
         * @since 2
         */
        FLAG_DISMISS_ALL: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        FLAG_REMOVE_SOUND_AND_VIBRATE: int;
        /**
         * Whether to vibrate if going to change to vibrate ringer mode.
         * @public
         * @since 2
         */
        FLAG_VIBRATE: int;
        /**
         * Whether to adjust volume according to session policy.
         * @public
         * @since 2
         */
        FLAG_POLICY_VOLUME: int;
        /**
         * Used to indicate that the adjust direction is lower.
         * @private
         */
        FLAG_DIRECTION_LOWER: int;
        /**
         * Used to indicate that the adjust direction is lower.
         * @private
         */
        FLAG_DIRECTION_RAISE: int;
    };
    /**
     * <p>Enum for Audio Ringer Mode.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly RingerMode: {
        /**
         * Ringer mode that ringer/system/notification sound
         * will be slient and will not vibrate.
         * @public
         * @since 2
         */
        RINGER_MODE_SILENT: int;
        /**
         * Ringer mode that ringer/system/notification sound
         * will be slient and will vibrate.
         * @public
         * @since 2
         */
        RINGER_MODE_VIBRATE: int;
        /**
         * Ringer mode that ringer/system/notification sound
         * are audible and may vibrate.
         * @public
         * @since 2
         */
        RINGER_MODE_NORMAL: int;
        /**
        * Deep slient mode which more stream will be muted
        * than RINGER_MODE_SILENT.
        * @public
        * @since 4
        */
        RINGER_MODE_DEEP_SILENT: int;
    };
    /**
     * <p>Enum for Audio Mode.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Mode: {
        /**
         * Current audio mode.
         * Used to change audio routing to current mode.
         * @public
         * @since 2
         */
        MODE_CURRENT: int;
        /**
         * Normal audio mode.
         * No phone ringing and no telephony call.
         * @public
         * @since 2
         */
        MODE_NORMAL: int;
        /**
         * Ringing audio mode.
         * @public
         * @since 2
         */
        MODE_RINGTONE: int;
        /**
         * In telephony call audio mode.
         * @public
         * @since 2
         */
        MODE_IN_CALL: int;
        /**
         * In communication audio mode.
         * A VoIP call or video/audio chat is established.
         * @public
         * @since 2
         */
        MODE_IN_COMMUNICATION: int;
        /**
         * In TBOX call audio mode .
         * @public
         * @since 5
         */
        MODE_IN_TBOX_CALL: int;
        /**
         * Audio mode count .
         * @private
         * @since 5
         */
        AUDIO_MODE_MAX: int;
    };
    /**
     * <p>Enum for volume status update event.</p>
     * <p>After registerAudioVolumeListener, AudioManager will emit one of the following
     * event to notify client that volume status was changed.</p>
     * <p>Then client can handle one or all of the following event type.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly VolumeListenerEvent: {
        SHOW_SAFE_ALERT: string;
        STREAM_VOLUME_UPDATE: string;
        STREAM_MUTE_UPDATE: string;
        MASTER_VOLUME_UPDATE: string;
        MASTER_MUTE_UPDATE: string;
    };
    /**
     * <p>Enum for audio misc setting status update event.</p>
     * <p>After registerAudioMiscEventListener, AudioManager will emit one of the following
     * event to notify client that audio misc setting status was changed.</p>
     * <p>Then client can handle one or all of the following event type.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly AudioMiscEventListenerNotify: {
        AUDIO_MISC_EVENT_UPDATE: string;
    };
    /**
     * <p>Enum for Audio Session request/change type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly AudioSessionType: {
        /**
         * No AudioSession request or change.
         * @public
         * @since 2
         */
        AUDIOSESSION_NONE: int;
        /**
         * Used to request an AudioSession which lasts for a long time or unkonwn
         * duration.Other running audio APPs should stop playing and release resource.
         * For example, use AUDIOSESSION_REQUEST_STOP_OTHERS for the playback of a music or video.
         *
         * Or used to notify APP that AudioSession changed and APP gained AudioSession again.
         * @public
         * @since 2
         */
        AUDIOSESSION_REQUEST_STOP_OTHERS: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_GAIN: int;
        /**
         * Used to request a temporary AudioSession which lasts a short amount of time,
         * and audio session will be abandonned shortly. Other running audio APPs
         * should pause playback during this period.
         * For example, use AUDIOSESSION_REQUEST_PAUSE_OTHERS for the playback of driving
         * direction or notification sounds.
         *
         * Or used to notify APP that it gained AudioSession temporary.
         * (NOTE: Unsupported currently).
         * @public
         * @since 2
         */
        AUDIOSESSION_REQUEST_PAUSE_OTHERS: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_GAIN_TRANSIENT: int;
        /**
         * Used to request a temporary AudioSession which lasts a short amount of time,
         * and it's OK for other running audio APPs to keep playing in background
         * with lowered output volume.
         * For example, use AUDIOSESSION_REQUEST_LOWER_OTHERS for the playback of
         * a short prompt tone while music plays in the background but lowers its output volume.
         * @public
         * @since 2
         */
        AUDIOSESSION_REQUEST_LOWER_OTHERS: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_GAIN_TRANSIENT_MAY_DUCK: int;
        /**
         * Used to request a temporary AudioSession which lasts a short amount of time,
         * and during this period all other audio APPs and system components
         * should NOT play anything.
         * For example, use AUDIOSESSION_REQUEST_EXCLUDE_OTHERS for a voice memo
         * recording, or speech recognition; during this, the music should have pasued
         * and system should not play any notification.
         * @public
         * @since 2
         */
        AUDIOSESSION_REQUEST_EXCLUDE_OTHERS: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_GAIN_TRANSIENT_EXCLUSIVE: int;
        /**
         * Used to indicate that audio APP have lost AudioSession for unknown duration.
         *
         * Audio APP which received AUDIOSESSION_CHANGE_STOPPED_BY_OTHER should stop playback
         * and release resource, because this AudioSession loss may last for a very
         * long time(for example MusicPlayer owns AudioSession).
         * @public
         * @since 2
         */
        AUDIOSESSION_CHANGE_STOPPED_BY_OTHER: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_LOSS: int;
        /**
         * Used to indicate that audio APP have temporary lost AudioSession which
         * lasts for a short time only.
         *
         * Audio APP which received AUDIOSESSION_CHANGE_PAUSED_BY_OTHER should pause playback
         * and set a flag to indicate this temporary loss, so that when receive
         * AUDIOSESSION_CHANGE_RESUMED_BY_SYSTEM again you can resume playback.
         * No need to release resource.
         * @public
         * @since 2
         */
        AUDIOSESSION_CHANGE_PAUSED_BY_OTHER: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_LOSS_TRANSIENT: int;
        /**
         * Used to indicate that audio APP have temporary lost AudioSession which
         * lasts for a short time only and the new AudioSession owner doesn't
         * require othres to be slient.
         *
         * Audio APP which received AUDIOSESSION_CHANGE_LOWERED_BY_OTHER
         * can keep playback with lowered output volume or
         * pause playback with a flag to indicate this temporary loss.
         * No need to release resource.
         * @public
         * @since 2
         */
        AUDIOSESSION_CHANGE_LOWERED_BY_OTHER: int;
        /**
         * Deprecated, Don't use.
         * @private
         */
        AUDIOSESSION_LOSS_TRANSIENT_CAN_DUCK: int;
        /**
         * Used to indicate that audio APP regained AudioSession again after
         * interrupted by other APP.
         *
         * Audio APP which received AUDIOSESSION_CHANGE_RESUMED_BY_SYSTEM
         * can resume playback or resume audio volume back to previous level.
         * @public
         * @since 2
         */
        AUDIOSESSION_CHANGE_RESUMED_BY_SYSTEM: int;
        /**
         * Used to indicate the AudioSession gained state, just used by some
         * system app internally as the state flag.
         *
         * @private
         * @since 2
         */
        AUDIOSESSION_GAINED: int;
    };
    /**
     * <p>Enum for Audio Session request/abandon result.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly AudioSessionResult: {
        /**
         * Used to indicate a failed AudioSession request or abandon.
         * @public
         * @since 2
         */
        AUDIOSESSION_RESULT_FAILED: int;
        /**
         * Used to indicate a successful AudioSession request or abandon.
         * @public
         * @since 2
         */
        AUDIOSESSION_RESULT_GRANTED: int;
        /**
         * Used to indicate a delayed AudioSession request.
         * When current owner has higher priority, requester can't gian session immediately.
         * Requester will receive gain notificaiton after current owner abandon session.
         * @private
         * @since 2
         */
        AUDIOSESSION_RESULT_DELAYED: int;
    };
    private static readonly AudioSessionFlag: {
        FLAG_NONE: int;
        FLAG_ALLOW_DELAY: int;
    };
    /**
     * <p>Enum for Audio endpoint device type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly AudioDeviceType: {
        /**
         * Empty audio device type, don't represent any device.
         * @public
         * @since 2
         */
        AUDIO_DEVICE_NONE: int;
        /**
         * Bit mask used to represent input audio device type.
         * @public
         * @since 2
         */
        AUDIO_DEVICE_IN_MASK: int;
        /**
         * This type represents the earpiece on the top of phone.
         * @public
         * @since 2
         */
        OUT_PHONE_EARPIECE: int;
        /**
         * This type represents the built-in speaker on phone.
         * @public
         * @since 2
         */
        OUT_PHONE_SPEAKER: int;
        /**
         * This type represents wired headset audio output device.
         * @public
         * @since 2
         */
        OUT_WIRED_HEADSET: int;
        /**
         * This type represents wired headphone audio output device.
         * @public
         * @since 2
         */
        OUT_WIRED_HEADPHONE: int;
        /**
         * This type represents Bluetooth SCO audio output device.
         * @public
         * @since 2
         */
        OUT_BLUETOOTH_SCO: int;
        /**
         * This type represents Bluetooth SCO headset audio output device.
         * @public
         * @since 2
         */
        OUT_BLUETOOTH_SCO_HEADSET: int;
        /**
         * This type represents Bluetooth SCO carkit audio output device.
         * @public
         * @since 2
         */
        OUT_BLUETOOTH_SCO_CARKIT: int;
        /**
         * This type represents Bluetooth A2DP audio output device.
         * @public
         * @since 2
         */
        OUT_BLUETOOTH_A2DP: int;
        /**
         * This type represents Bluetooth A2DP headphone audio output device.
         * @public
         * @since 2
         */
        OUT_BLUETOOTH_A2DP_HEADPHONE: int;
        /**
         * This type represents Bluetooth A2DP speaker audio output device.
         * @public
         * @since 2
         */
        OUT_BLUETOOTH_A2DP_SPEAKER: int;
        /**
         * This type represents using YunOS as USB output device
         * and connected to other USB host.
         * @public
         * @since 2
         */
        OUT_USB_ACCESSORY: int;
        /**
         * This type represents USB device which is connected to YunOS
         * and as output device.
         * @public
         * @since 2
         */
        OUT_USB_DEVICE: int;
        /**
         * This type represents Bluetooth SCO headset audio input device.
         * @public
         * @since 2
         */
        IN_BLUETOOTH_SCO_HEADSET: number;
        /**
         * This type represents wired headset audio input device.
         * @public
         * @since 2
         */
        IN_WIRED_HEADSET: number;
        /**
         * This type represents using YunOS as USB input device
         * and connected to other USB host.
         * @public
         * @since 2
         */
        IN_USB_ACCESSORY: number;
        /**
         * This type represents USB device which is connected to YunOS
         * and as input device.
         * @public
         * @since 2
         */
        IN_USB_DEVICE: number;
    };
    /**
     * <p>Enum for Audio effect filter type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public static readonly AudioEffectFilterType: {
        /**
         * Invalid audio effect filter type, don't represent any type.
         * @public
         * @since 3
         *
         */
        AUDIO_EFFECT_FILTER_TYPE_INVALID: int;
        /**
         * base audio effect filter type.
         * @public
         * @since 3
         *
         */
        AUDIO_EFFECT_FILTER_TYPE_BASS: int;
        /**
         * middle audio effect filter type.
         * @public
         * @since 3
         *
         */
        AUDIO_EFFECT_FILTER_TYPE_MID: int;
        /**
         * treble audio effect filter type.
         * @public
         * @since 3
         *
         */
        AUDIO_EFFECT_FILTER_TYPE_TREBLE: int;
        /**
         * max number of audio effect filter type.
         * @public
         * @since 3
         *
         */
        AUDIO_EFFECT_FILTER_TYPE_MAX: int;
    };
    /**
     * <p>Enum for Audio effect preset EQ type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly AudioEffectPresetEQType: {
        /**
         * it is default subbands setting.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_NORMAL: int;
        /**
         * Rock EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_ROCK: int;
        /**
         * Pop EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_POP: int;
        /**
         * Jazz EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_JAZZ: int;
        /**
         * Classic EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_CLASSIC: int;
        /**
         * Voice EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_VOCAL: int;
        /**
         * Customized EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_CUSTOM: int;
        /**
         * Numbers of EQ type.
         * @public
         * @since 4
         *
         */
        AUDIO_EFFECT_PRESET_EQ_NUM: int;
    };
    /**
     * <p>Enum for ringer mode changed event.</p>
     * <p>After registerAudioRingerModeListener, AudioManager will emit the below
     * event to notify client that ringer mode was changed.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly RingerModeChangedEvent: {
        RINGER_MODE_CHANGED: string;
    };
    /**
     * <p>Enum for audio device state changed event.</p>
     * <p>After registerAudioDeviceStateListener, AudioManager will emit the below
     * event to notify client that audio device state changed.
     * Such as device was pluged in or pluged out.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly DeviceStateChangedEvent: {
        DEVICE_PLUGGED_IN: string;
        /**
         * Deprecated, Don't use.
         * @private
         */
        DEVICE_PLUGED_IN: string;
        DEVICE_PLUGGED_OUT: string;
        /**
         * Deprecated, Don't use.
         * @private
         */
        DEVICE_PLUGED_OUT: string;
    };
    private static readonly CallbackID: {
        CB_ID_STREAM_VOLUME_UPDATE: int;
        CB_ID_STREAM_MUTE_UPDATE: int;
        CB_ID_MASTER_VOLUME_UPDATE: int;
        CB_ID_MASTER_MUTE_UPDATE: int;
        CB_ID_AUDIO_SESSION_CHANGED: int;
        CB_ID_RINGER_MODE_CHANGED: int;
        CB_ID_DEVICE_PLUGGED_IN: int;
        CB_ID_DEVICE_PLUGGED_OUT: int;
        CB_ID_SHOW_SAFE_ALERT: int;
    };
    /**
     * <p>Enum for AudioManager misc sub event message ID.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly AudioMiscSubEventID: {
        AUDIO_MISC_EVENT_DUCK_UPDATE: int;
        AUDIO_MISC_EVENT_SPEED_VOLUME_UPDATE: int;
        AUDIO_MISC_EVENT_LOUDNESS_UPDATE: int;
        AUDIO_MISC_EVENT_3D_UPDATE: int;
        AUDIO_MISC_EVENT_FADE_UPDATE: int;
        AUDIO_MISC_EVENT_BALANCE_UPDATE: int;
        AUDIO_MISC_EVENT_BMT_UPDATE: int;
        AUDIO_MISC_EVENT_PRESET_EQ_UPDATE: int;
        AUDIO_MISC_EVENT_CUSTOMIZED_EQ_UPDATE: int;
        AUDIO_MISC_EVENT_SUBWOOFER_UPDATE: int;
        AUDIO_MISC_EVENT_VOLUME_AGC_UPDATE: int;
        AUDIO_MISC_EVENT_VOLUME_CHIME_LEVEL_UPDATE: int;
        AUDIO_MISC_EVENT_VOLUME_MIC_VOL_UPDATE: int;
        AUDIO_MISC_EVENT_AMBIENCE_UPDATE: int;
        AUDIO_MISC_EVENT_EQUALIZERBY_BAND_UPDATE: int;
        AUDIO_MISC_EVENT_VOLUME_UPDATE: int;
        AUDIO_MISC_EVENT_AVAS_SOUND_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_AVAS_SOUND_TYPE_UPDATE: int;
        AUDIO_MISC_EVENT_IN_SIDE_SOUND_WAVE_SWITCH_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_IN_SIDE_SOUND_WAVE_TYPE_UPDATE: int;
        AUDIO_MISC_EVENT_INSIDE_SPEAKER_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_OUT_SIDE_SOUND_WAVES_TYPE_UPDATE: int;
        AUDIO_MISC_EVENT_OUT_SIDE_SOUND_WAVE_SWITCH_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_OUTSIDE_SPEAKER_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_SOUND_FIELD_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_CALIBRATED_SET: int;
        AUDIO_MISC_EVENT_IN_SIDE_SOUND_WAVE_VOLUME_UPDATE: int;
        AUDIO_MISC_EVENT_HEADREST_SWITCH_UPDATE: int;
        AUDIO_MISC_EVENT_NEARFIELD_VOICE_UPDATE: int;
        AUDIO_MISC_EVENT_ICC_ACTIVE_UPDATE: int;
        AUDIO_MISC_EVENT_ICC_STATE_UPDATE: int;
        AUDIO_MISC_EVENT_BEST_POSITION_UPDATE: int;
        AUDIO_MISC_EVENT_NEARFIELD_PHONE_UPDATE: int;
        AUDIO_MISC_EVENT_ICC_SWITCH_UPDATE: int;
        AUDIO_MISC_EVENT_ICC_VOLUME_UPDATE: int;
        AUDIO_MISC_EVENT_HIGH_ORDER_SURROUND_SWITCH_UPDATE: int;
        AUDIO_MISC_EVENT_OUTSIDE_WELCOME_SND_MODE_UPDATE: int;
        AUDIO_MISC_EVENT_OUTSIDE_GOODBYE_SND_MODE_UPDATE: int;
        AUDIO_MISC_EVENT_PHONE_ROUTE_UPDATE: int;
        AUDIO_MISC_EVENT_BTHEADPHONE_A2DP_UPDATE: int;
        AUDIO_MISC_EVENT_SOUND_EFFECT_MUTEX_UPDATE: int;
    };
    private static readonly RouteType: {
        ROUTE_TYPE_MIN: int;
        ROUTE_TYPE_FM: int;
        ROUTE_TYPE_MAX: int;
    };
    private static readonly ForceUseType: {
        FORCE_USE_MIN: int;
        FORCE_FOR_COMMUNICATION: int;
        FORCE_FOR_MEDIA: int;
        FORCE_FOR_RECORD: int;
        FORCE_FOR_DOCK: int;
        FORCE_FOR_SYSTEM: int;
        FORCE_FOR_HDMI_SYSTEM_AUDIO: int;
        FORCE_FOR_FM: int;
        FORCE_USE_MAX: int;
    };
    private static readonly RouteConfig: {
        ROUTE_CONFIG_MIN: int;
        ROUTE_CONFIG_NONE: int;
        ROUTE_CONFIG_SPEAKER: int;
        ROUTE_CONFIG_HEADPHONES: int;
        ROUTE_CONFIG_MAX: int;
    };
    private static readonly ForceUseConfig: {
        FORCE_CONFIG_MIN: int;
        FORCE_CONFIG_NONE: int;
        FORCE_CONFIG_SPEAKER: int;
        FORCE_CONFIG_HEADPHONES: int;
        FORCE_CONFIG_BT_SCO: int;
        FORCE_CONFIG_BT_A2DP: int;
        FORCE_CONFIG_WIRED_ACCESSORY: int;
        FORCE_CONFIG_BT_CAR_DOCK: int;
        FORCE_CONFIG_BT_DESK_DOCK: int;
        FORCE_CONFIG_ANALOG_DOCK: int;
        FORCE_CONFIG_DIGITAL_DOCK: int;
        FORCE_CONFIG_NO_BT_A2DP: int;
        FORCE_CONFIG_SYSTEM_ENFORCED: int;
        FORCE_CONFIG_HDMI_SYSTEM_AUDIO_ENFORCED: int;
        FORCE_CONFIG_ENCODED_SURROUND_NEVER: int;
        FORCE_CONFIG_ENCODED_SURROUND_ALWAYS: int;
        FORCE_CONFIG_MAX: int;
    };
    private static readonly AudioSettingType: {
        AUDIO_SETTING_TYPE_VOLUME: int;
        AUDIO_SETTING_TYPE_EFFECT: int;
        AUDIO_SETTING_TYPE_ALL: int;
    };
    /**
     * <p>Enum for spectrum info event.</p>
     * <p>After registerSpectrumInfoListener, AudioManager will emit the below
     * event to notify client about spectrum band information.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly SpectrumInfoNotifyEvent: {
        SPECTRUM_INFO_NOTIFY: string;
    };
    private static readonly ZoneTypeEvent: {
        AUDIO_ZONE_EVENT_ZONE_TYPE: string;
        AUDIO_ZONE_EVENT_ZONE_TYPE_STREAM: string;
    };
}
export = AudioManager;
