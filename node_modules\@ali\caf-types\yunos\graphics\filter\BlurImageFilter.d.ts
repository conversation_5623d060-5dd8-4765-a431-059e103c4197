import ImageFilter = require("./ImageFilter");
/**
 * <p>The BlurImageFilter class applies a Gaussian blur to the input image.</p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class BlurImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The value of radius defines the standard deviation to the Gaussian function,
     * or how many pixels on the screen blend into each other, so a larger value will create more blur.</p>
     * @name yunos.graphics.filter.BlurImageFilter#radius
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public radius: number;
}
export = BlurImageFilter;
