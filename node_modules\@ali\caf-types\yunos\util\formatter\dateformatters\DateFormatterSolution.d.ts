import DateFormatter = require("../DateFormatter");
import YObject = require("../../../core/YObject");
/**
 * @memberof yunos.util.formatter
 * @relyon YUNOS_SYSCAP_SYSTIME
 * @private
 */
declare class DateFormatterSolution extends YObject {
    private _source;
    private readonly type: string;
    private source: DateFormatter;
    private getStringOfDate(date: Date, template: string): string;
}
export = DateFormatterSolution;
