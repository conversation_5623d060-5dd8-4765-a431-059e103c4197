import YObject = require("yunos/core/YObject");
declare class InputMethodManager extends YObject {
    private _busPath: string;
    private _busName: string;
    private _busInterface: string;
    private _ubus;
    private _iface;
    private static getInstance(): InputMethodManager;
    public constructor();
    private initInputMethodManager(): void;
    private release(): void;
    private getInputMethodListInfo(type: string, callback: (data: string) => void): void;
    private getInputMethodListInfoSync(type: string): string;
}
export = InputMethodManager;
