import YObject = require("../core/YObject");
/**
 * <p>Group definition of fence, fences are organized in a group, and a business can contains multile fence group</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @see [FenceClient]{@link yunos.fenceclient.FenceClient}
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class FenceGroup extends YObject {
    private _businessId;
    private _groupId;
    private _validTime;
    private _repeatWeekday;
    private _fixedDate;
    private _fixedTime;
    private _toCloud;
    private _version;
    public constructor();
    /**
     * <p>businessId of group.</p>
     * @name yunos.fenceclient.FenceInfo#businessId
     * @type {string}
     * @public
     * @since 5
     */
    public businessId: string;
    /**
     * <p>groupId of group.</p>
     * @name yunos.fenceclient.FenceInfo#groupId
     * @type {string}
     * @public
     * @since 5
     */
    public groupId: string;
    /**
     * <p>Indicate when fence will expire, blank means never expire.</p>
     * @example
     * "2019-10-01"
     * @name yunos.fenceclient.FenceInfo#validTime
     * @type {string}
     * @public
     * @since 5
     */
    public validTime: string;
    /**
     * <p>Weekday repeat pattern.</p>
     * @example
     * "1,2,3,4,5,6,7", Separated by commas
     * @name yunos.fenceclient.FenceInfo#repeatWeekday
     * @type {string}
     * @public
     * @since 5
     */
    public repeatWeekday: string;
    /**
     * <p>Fixed date, effective for all fences in the same group,
     * as long as fixedDate and repeatWeekday meet one of them,
     * blank means the default is effective.</p>
     * @example
     * "2019-08-01;2019-10-01"
     * @name yunos.fenceclient.FenceInfo#fixedDate
     * @type {string}
     * @public
     * @since 5
     */
    public fixedDate: string;
    /**
     * <p>Fixed effective time period of fence, blank means always effective.</p>
     * @example
     * "08:00-10:00;15:00-16:00"
     * @name yunos.fenceclient.FenceInfo#fixedTime
     * @type {string}
     * @public
     * @since 5
     */
    public fixedTime: string;
    /**
     * <p>version of fence, for client to do version control,
     * a number value in string, length cannot exceed 10</p>
     * @name yunos.fenceclient.FenceInfo#version
     * @type {string}
     * @public
     * @since 5
     */
    public version: string;
    /**
     * <p>Indication whether to send fence detect result to cloud platform.</p>
     * @name yunos.fenceclient.FenceInfo#toCloud
     * @type {boolean}
     * @public
     * @since 5
     */
    public toCloud: boolean;
}
export = FenceGroup;
