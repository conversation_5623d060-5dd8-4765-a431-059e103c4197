import CompositeView = require("yunos/ui/view/CompositeView");
import View = require("yunos/ui/view/View");
interface IStyle {
    defaultBgColor: string;
    defaultBorderRadius: number;
    defaultBorderWidth: number;
    defaultBorderColor: string;
    defaultViewOpacity: number;
}
declare class FocusBM extends CompositeView {
    readonly defaultStyleName: string;
    private _scaleAnima;
    private _target;
    private _defaultBorderRadius;
    private _defaultBorderWidth;
    private _defaultBorderColor;
    private _defaultBgColor;
    private _defaultViewOpacity;
    constructor();
    applyStyle(style: IStyle): void;
    updateStyle(style: IStyle, diffStyle: IStyle): void;
    hilightView(view: View, animated?: boolean): void;
    private initView;
    private _playAnimation;
    private _destroyHandler;
    private _sizeHandler;
    destroy(recusive?: boolean): void;
}
export = FocusBM;
