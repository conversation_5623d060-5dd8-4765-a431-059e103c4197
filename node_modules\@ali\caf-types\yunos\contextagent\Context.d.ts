/// <reference types="yunos" />
import YObject = require("yunos/core/YObject");
import UBus = require("ubus");
/**
 * <p>Context provides the ability to get the state from specific scene.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.contextagent
 * @friend
 */
declare class Context extends YObject {
    readonly iface: UBus.Interface;
    /**
     * This method will be called when finished getting context status.
     * @callback yunos.contextagent.Context~getStatusCallback
     * @param {?string} err - Errors of getting context status
     * @param {Object} result - state
     * @example
     * let callback = function(err, result) {
     *     if (!err) {
     *         // do something with result
     *     }
     * };
     * @friend
     */
    /**
     * <p>Get the latest state from the context.</p>
     * @param {string} contextName - status name.
     * @param {yunos.contextagent.Context~getStatusCallback} callback -
     * Callback of getting context state
     * @friend
     */
    public getContextStatus(contextName: string, callback: (err: string, data: {
        [key: string]: Object;
    }) => void): void;
    /**
     * This method will be called when finished checking context happening.
     * @callback yunos.contextagent.Context~isContextHappeningCallback
     * @param {?string} err - Errors of getting context status
     * @param {Object} result - check result
     * @example
     * let callback = function(err, result) {
     *     if (!err) {
     *         if (result.isHappening === true) {
     *             result.traceId; // the trace id for this context
     *             result.extraData; // the reference data for this context
     *         } else {
     *             // ...
     *         }
     *     }
     * };
     * @friend
     */
    /**
     * <p>Check a conetxt is happening or not.</p>
     * @param {string} contextName - status name.
     * @param {yunos.contextagent.Context~isContextHappeningCallback} callback -
     * Callback of checking context happening
     * @friend
     */
    public isContextHappening(contextName: string, callback: (err: string, data: {
        [key: string]: Object;
    }) => void): void;
    _fetch(method: string, param: Object, callback: (err: string, data: {
        [key: string]: Object;
    }) => void, reshaper: (data: ObjectReflectI) => ObjectReflectI): void;
}
export = Context;
