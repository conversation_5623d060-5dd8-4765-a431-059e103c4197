import Projection = require("./Projection");
/**
 * This class represents an perspective camera.
 * @extends yunos.graphics.3d.projection.Projection
 * @memberof yunos.graphics.3d.projection
 * @public
 * @since 5
 */
declare class Perspective extends Projection {
    private _fov: number;
    private _aspect: number;
    /**
     * Constructor that creates an perspective projection
     * @param {number} fov - projection frustum vertical field of view
     * @param {number} aspect - projection frustum aspect ratio
     * @param {number} near - projection frustum near plane
     * @param {number} far - projection frustum far plane
     * @public
     * @since 5
     */
    public constructor(fov?: number, aspect?: number, near?: number, far?: number);
    /**
     * projection frustum vertical field of view
     * @name yunos.graphics.3d.projection.Perspective#fov
     * @type {number}
     * @public
     * @since 5
     */
    public fov: number;
    /**
     * projection frustum aspect ratio
     * @name yunos.graphics.3d.projection.Perspective#aspect
     * @type {number}
     * @public
     * @since 5
     */
    public aspect: number;
}
export = Perspective;
