declare namespace NodeAixc {
    class AixcClient {
        call(name: string, data?: object, callback?:(err?: Object, result?: Object) => void,
            timeout?: number) : void;
        close(callback?: () => void) : void;
        onError(handler: (err?: Object) => void) : void;
        onEvent(eventName: string, handler: (data?: Object) => void) : void;
    }

    class AixcServer {
        close(callback?: () => void) : void;
        onCall(name: string, handler: (data?: Object, reply?: () => void) => void,
            perm?: string): void;
        onConnect(handler: (handle?: Object, info?: Object) => void) : void;
        onDisconnect(handler: (handle?: Object) => void) : void;
        onError(handler: (err?: Object) => void) : void;
        sendEvent(eventName: string, data?: object) : void;
    }
}

/* eslint-disable no-redeclare */
declare class NodeAixc {
    static createClient(name: string) : NodeAixc.AixcClient;
    static createServer(name: string, scope?: number) : NodeAixc.AixcServer;
    static createPlatformHandle(fd: number) : Object;
}

export = NodeAixc;
