export interface FenceGroupObj {
    getBusinessId(): string;
    setBusinessId(businessId: string): void;
    getGroupId(): string;
    setGroupId(groupId: string): void;
    getValidTime(): string;
    setValidTime(validTime: string): void;
    getRepeatWeekday(): string;
    setRepeatWeekday(repeatWeekday: string): void;
    getFixedDate(): string;
    setFixedDate(fixedDate: string): void;
    getFixedTime(): string;
    setFixedTime(fixedTime: string): void;
    getToCloud(): number;
    setToCloud(toCloud: number): void;
    getVersion(): string;
    setVersion(version: string): void;
}
export interface FenceInfoObj {
    getFenceId(): string;
    setFenceId(fenceId: string): void;
    getAction(): string;
    setAction(action: string): void;
    getType(): number;
    setType(type: number): void;
    getRadius(): number;
    setRadius(radius: number): void;
    getStayMins(): number;
    setStayMins(stayMins: number): void;
    getCategory(): string;
    setCategory(category: string): void;
    getAddress(): string;
    setAddress(address: string): void;
    getName(): string;
    setName(name: string): void;
    getPoiId(): string;
    setPoiId(poiId: string): void;
    getProvince(): string;
    setProvince(province: string): void;
    getCoordinates(): string;
    setCoordinates(coordinates: string): void;
    getCity(): string;
    setCity(city: string): void;
    getData(): string;
    setData(data: string): void;
}
export interface FenceDetailInfoObj {
    getBusinessId(): string;
    setBusinessId(businessId: string): void;
    getGroupId(): string;
    setGroupId(groupId: string): void;
    getValidTime(): string;
    setValidTime(validTime: string): void;
    getRepeatWeekday(): string;
    setRepeatWeekday(repeatWeekday: string): void;
    getFixedDate(): string;
    setFixedDate(fixedDate: string): void;
    getFixedTime(): string;
    setFixedTime(fixedTime: string): void;
    getToCloud(): number;
    setToCloud(toCloud: number): void;
    getVersion(): string;
    setVersion(version: string): void;
    getFenceId(): string;
    setFenceId(fenceId: string): void;
    getAction(): string;
    setAction(action: string): void;
    getType(): number;
    setType(type: number): void;
    getRadius(): number;
    setRadius(radius: number): void;
    getStayMins(): number;
    setStayMins(stayMins: number): void;
    getCategory(): string;
    setCategory(category: string): void;
    getPoiId(): string;
    setPoiId(poiId: string): void;
    getAddress(): string;
    setAddress(address: string): void;
    getName(): string;
    setName(name: string): void;
    getProvince(): string;
    setProvince(province: string): void;
    getCoordinates(): string;
    setCoordinates(coordinates: string): void;
    getCity(): string;
    setCity(city: string): void;
    getData(): string;
    setData(data: string): void;
}
export interface FenceSignalObj {
    getSignalId(): string;
    setSignalId(signalId: string): void;
    getBusinessId(): string;
    setBusinessId(businessId: string): void;
    getGroupId(): string;
    setGroupId(groupId: string): void;
    getFenceId(): string;
    setFenceId(fenceId: string): void;
    getAction(): string;
    setAction(action: string): void;
    getName(): string;
    setName(name: string): void;
    getAddress(): string;
    setAddress(address: string): void;
    getCity(): string;
    setCity(city: string): void;
    getProvince(): string;
    setProvince(province: string): void;
    getPoiId(): string;
    setPoiId(poiId: string): void;
    getCoordinates(): string;
    setCoordinates(coordinates: string): void;
    getData(): string;
    setData(data: string): void;
    getDir(): number;
    setDir(dir: number): void;
    getInit(): boolean;
    setInit(init: boolean): void;
    getLat(): number;
    setLat(lat: number): void;
    getLng(): number;
    setLng(lng: number): void;
    getEventLat(): number;
    setEventLat(eventLat: number): void;
    getEventLng(): number;
    setEventLng(eventLng: number): void;
    getRadius(): number;
    setRadius(radius: number): void;
    getTimestamp(): number;
    setTimestamp(timestamp: number): void;
    getType(): number;
    setType(type: number): void;
}
export interface AddFenceRequestObj {
    getCloudFence(): boolean;
    setCloudFence(cloudFence: boolean): void;
    getFenceGroup(): FenceGroupObj;
    setFenceGroup(fenceGroup: FenceGroupObj): void;
    getFenceList(): FenceInfoObj[];
    setFenceList(fenceList: FenceInfoObj[]): void;
}
export interface QueryFenceRequestObj {
    getCloudFence(): boolean;
    setCloudFence(cloudFence: boolean): void;
    getBusinessId(): string;
    setBusinessId(businessId: string): void;
    getGroupId(): string;
    setGroupId(groupId: string): void;
    getFenceId(): string;
    setFenceId(fenceId: string): void;
    getPageNum(): number;
    setPageNum(pageNum: number): void;
    getPageSize(): number;
    setPageSize(pageSize: number): void;
}
export interface DeleteFenceRequestObj {
    getCloudFence(): boolean;
    setCloudFence(cloudFence: boolean): void;
    getBusinessId(): string;
    setBusinessId(businessId: string): void;
    getGroupId(): string;
    setGroupId(groupId: string): void;
    getFenceId(): string;
    setFenceId(fenceId: string): void;
}
export interface RegisterSignalRequestObj {
    getSignalIds(): string[];
    setSignalIds(signalIds: string[]): void;
    getTraceId(): string;
    setTraceId(traceId: string): void;
}
export interface FenceAddon {
    fenceGroup(): FenceGroupObj;
    fenceInfo(): FenceInfoObj;
    addFenceRequest(): AddFenceRequestObj;
    queryFenceRequest(): QueryFenceRequestObj;
    deleteFenceRequest(): DeleteFenceRequestObj;
    registerSignalRequest(): RegisterSignalRequestObj;
    addFence(request: AddFenceRequestObj, callback: (success: boolean, code: number, err: Error, traceId: string) => void): void;
    queryFence(request: QueryFenceRequestObj, callback: (success: boolean, code: number, err: Error, traceId: string, fenceList: object[]) => void): void;
    deleteFence(request: DeleteFenceRequestObj, callback: (success: boolean, code: number, err: Error, traceId: string) => void): void;
    registerSignalListener(request: RegisterSignalRequestObj, callback: (fenceSignal: Object) => void): boolean;
    unregisterSignalListener(callback: (fenceSignal: Object) => void): void;
}
