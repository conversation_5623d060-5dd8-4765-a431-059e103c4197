/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets

import View = require("yunos/ui/view/View");
import CursorAdapter = require("yunos/ui/adapter/CursorAdapter");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import Cursor = require("yunos/provider/Cursor");
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
import ListItemBM = require("extend/hdt/control/ListItemBM");
const {Visible, None} = require("yunos/ui/view/View").Visibility;
const iVideoModel = require("../model/VideoModel").getInstance();
const log = require("../utils/log");
const Utils = require("../utils/Utils");
const Consts = require("../Consts");
import {IVoiceEvent} from "../Types";
const TAG = "LocalAdapter";

const VIDEO_PATH_INDEX = 1;
const VIDEO_DATE_MODIFIED_INDEX = 2;
const VIDEO_DISPLAY_NAME_INDEX = 4;
const VIDEO_DURATION_INDEX = 8;

interface ILocalView extends ListItemBM {
    title: TextView;
    time: TextView;
    index: number;
    lastPlayed: ImageView;
}

class LocalAdapter extends CursorAdapter {
    private _voiceSelectListener: (itemView: View, position: number, point: object, voice: boolean) => void;
    private _isSearchType: boolean;
    private _isOrderByDate: boolean;

    constructor(isSearchType = false) {
        super();
        this._isSearchType = isSearchType;
        this._isOrderByDate = false;
    }

    destroy() {
        log.I(TAG, "destroy");
        super.destroy();
    }

    newItem(cursor: Cursor, position: number, convertView: ILocalView) {
        if (!convertView) {
            if (this._isSearchType) {
                convertView = <ILocalView> LayoutManager.loadSync("search_local_item");
            } else {
                convertView = <ILocalView> LayoutManager.loadSync("local_item");
            }
            convertView.title = <TextView>convertView.findViewById("id_title");
            convertView.time = <TextView>convertView.findViewById("id_time");
            convertView.lastPlayed = <ImageView>convertView.findViewById("id_last_played");

            if (Consts.SUPPORT_VOICE_CMD) {
                convertView.title.voiceEnabled = true;
                convertView.title.voiceSelectMode = View.VoiceSelectMode.Custom;
                const VoiceCommand = require("yunos/ui/voice/VoiceCommand");
                convertView.title.defaultVoiceCommand.recognitionQuality = VoiceCommand.RecognitionQuality.LOW;
                convertView.title.defaultVoiceCommand.keepAwake = false;
                convertView.title.on("voice", (e: IVoiceEvent) => {
                    log.I(TAG, "voice", position, convertView.index);
                    if (this._voiceSelectListener) {
                        this._voiceSelectListener(convertView, convertView.index, null, true);
                    }
                    e.endLocalTask();
                });
            }
        }
        return convertView;
    }

    updateItem(cursor: Cursor, position: number, convertView: ILocalView) {
        if (!cursor || !convertView) {
            log.E(TAG, "updateItem, wrong parameters");
            return convertView;
        }

        let title = <string>cursor.getValue(VIDEO_DISPLAY_NAME_INDEX);
        let url = <string>cursor.getValue(VIDEO_PATH_INDEX);
        let duration = cursor.getValue(VIDEO_DURATION_INDEX);
        let date = <number>cursor.getValue(VIDEO_DATE_MODIFIED_INDEX) * 1000;
        log.D(TAG, "updateItem", title, url, duration, position);
        convertView.title.text = String(title);
        convertView.time.text = this._isOrderByDate ? Utils.getTsFormatDate(date) : Utils.secondToTime(duration);
        convertView.index = position;

        if (this._isHighlightUrl(url)) {
            convertView.lastPlayed.visibility = Visible;
        } else {
            convertView.lastPlayed.visibility = None;
        }
    }

    _isHighlightUrl(url: string) {
        return false;
    }

    setOrderByDate(isOrderByDate: boolean) {
        this._isOrderByDate = isOrderByDate;
    }

    registerVoiceSelectListener(callback: (itemView: View, position: number, point: object, voice: boolean) => void) {
        this._voiceSelectListener = callback;
    }
}

export = LocalAdapter;
