import Segment = require("./Segment");
/**
 * @private
 */
declare class CubicBezierCurveSegment extends Segment {
    private _x1: number;
    private _y1: number;
    private _x2: number;
    private _y2: number;
    private _cp1x: number;
    private _cp1y: number;
    private _cp2x: number;
    private _cp2y: number;
    private _length: number;
    private static getCurveLength(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, limit: number): number;
    private static integrate(f: (t: number) => number, a: number, b: number, n: number): number;
    private static getLength(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, ds: (t: number) => number): number;
    private static getLengthIntegrand(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number): (t: number) => number;
    private static getIterations(a: number, b: number): number;
    private static subdivide(x0: number, y0: number, x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, t: number): number[][];
    private static findRoot(f: (v: number) => number, df: (v: number) => number, x: number, a: number, b: number, n: number, tolerance: number): number;
    private static clamp(value: number, min: number, max: number): number;
}
export = CubicBezierCurveSegment;
