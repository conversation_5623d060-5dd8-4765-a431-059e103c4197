import { MultiStateTransitionType, MultiState } from "yunos/ui/util/TypeHelper";
import CompoundButton = require("yunos/ui/widget/CompoundButton");
interface IStyle {
    defaultHeight: number;
    defaultBorderRadius: number;
    defaultLeftPadding: number;
    defaultRightPadding: number;
    defaultIconSize: number;
    defaultFontSize: number;
    defaultFontWeight: number;
    defaultHorizontalSpacing: number;
    defaultPressedScale: number;
    defaultMultiStateTransition: MultiStateTransitionType[];
    voiceEnable: boolean;
    multiState: MultiState;
}
declare class BaseButtonBM extends CompoundButton {
    private _iconView;
    private _iconSrc;
    private _iconColor;
    private _defaultHeight;
    private _defaultBorderRadius;
    private _defaultLeftPadding;
    private _defaultRightPadding;
    private _defaultIconSize;
    private _defaultFontSize;
    private _defaultFontWeight;
    private _defaultHorizontalSpacing;
    private _defaultPressedScale;
    private _defaultMultiStateTransition;
    private _voiceEnable;
    readonly defaultStyleName: string;
    iconSize: number;
    iconSrc: string | MultiState;
    iconColor: string | null;
    multiState: MultiState;
    height: number;
    width: number;
    constructor(...args: Object[]);
    destroy(recursive?: boolean): void;
    protected applyStyle(style: IStyle): void;
    updateStyle(): void;
}
export = BaseButtonBM;
