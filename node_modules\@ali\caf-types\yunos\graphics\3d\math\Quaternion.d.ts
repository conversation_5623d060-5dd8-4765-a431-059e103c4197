import Vector3 = require("./Vector3");
import Matrix4 = require("./Matrix4");
/**
 * <p>This class represents a Quaternion</p>
 * @memberof yunos.graphics.3d.math
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class Quaternion {
    private _out;
    /**
     * Constructor that create a 4 * 4 matrix
     * @param {number} x - x coordinate
     * @param {number} y - y coordinate
     * @param {number} z - z coordinate
     * @param {number} w - w coordinate
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(x?: number, y?: number, z?: number, w?: number);
    /**
     * Destructor that destroy this Quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public destroy(): void;
    /**
     * Defines the first value of this Quaternion.
     * @name yunos.graphics.3d.math.Quaternion#x
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public x: number;
    /**
     * Defines the second value of this Quaternion.
     * @name yunos.graphics.3d.math.Quaternion#y
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public y: number;
    /**
     * Defines the third value of this Quaternion.
     * @name yunos.graphics.3d.math.Quaternion#z
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public z: number;
    /**
     * Defines the fourth value of this Quaternion.
     * @name yunos.graphics.3d.math.Quaternion#w
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public w: number;
    /**
     * Return the angle between this quaternion and quaternion q in radians.
     * @param {yunos.graphics.3d.math.Quaternion} q - the passed quaternion
     * @return {number} return the angle
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public angleTo(q: Quaternion): number;
    /**
     * Create a new Quaternion with identical x, y, z, and w properties to this one
     * @return {yunos.graphics.3d.math.Quaternion} return the new quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clone(): Quaternion;
    /**
     * Set the rotational conjugate of this quaternion.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public conjugate(): void;
    /**
     * Copy the x, y, z and w properties of q into this quaternion.
     * @param {yunos.graphics.3d.math.Quaternion} q - the passed Quaternion to copy.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copy(q: Quaternion): void;
    /**
     * Compares the x, y,	z and w properties of v to the equivalent properties of this quaternion to determine if they represent the same rotation.
     * @param {yunos.graphics.3d.math.Quaternion} q - the passed Quaternion to equal
     * @return {boolean} the result of equal
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public equals(q: Quaternion): boolean;
    /**
     * Calculates the dot product of quaternions v and this one
     * @param {yunos.graphics.3d.math.Quaternion} q - the passed quaternion to dot
     * @return {boolean} the result of dot
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public dot(q: Quaternion): number;
    /**
     * Set the quaternion's x, y, z and w properties from an array.
     * @param {number[]} array - array of format used to construct the quaternion
     * @param {number} offset - an offset into the array
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public fromArray(array: Array<number>, offset: number): void;
    /**
     * Inverse this quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public inverse(): void;
    /**
     * Computes the Euclidean length of this quaternion, considered as a 4 dimensional vector.
     * @return {number} return the length of this Quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public length(): number;
    /**
     * Computes the Euclidean length of this quaternion, considered as a 4 dimensional vector.
     * @return {number} return the euclidean length of this quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lengthSq(): number;
    /**
     * normalize this quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public normalize(): void;
    /**
     * Multiply this quaternion by the passed quaternion
     * @param {yunos.graphics.3d.math.Quaternion} q - the passed quaternion to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiply(q: Quaternion): void;
    /**
     * Set this quaternion to the passed Quaternion a * the passed Quaternion b.
     * @param {yunos.graphics.3d.Quaternion} a - the first passed Quaternion
     * @param {yunos.graphics.3d.Quaternion} b - the second passed Quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyQuaternions(a: Quaternion, b: Quaternion): void;
    /**
     * Pre-multiply this quaternion by passed quaternion
     * @param {yunos.graphics.3d.math.Quaternion} q - the passed Quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public premultiply(q: Quaternion): void;
    /**
     * Rotates this quaternion by a given angular step to the defined quaternion q.
     * @param {yunos.graphics.3d.math.Quaternion} q - the target quaternion
     * @param {number} step - the angular step in radians.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public rotateTowards(q: Quaternion, step: number): void;
    /**
     * Handles the spherical linear interpolation between quaternions.
     * @param {yunos.graphics.3d.math.Quaternion} q - The other quaternion rotation
     * @param {number} t - interpolation factor in the closed interval[0, 1]
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public slerp(q: Quaternion, t: number): void;
    /**
     * Sets x, y, z, w properties of this quaternion.
     * @param {number} x - x coordinate
     * @param {number} y - y coordinate
     * @param {number} z - z coordinate
     * @param {number} w - w coordinate
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public set(x: number, y: number, z: number, w: number): void;
    /**
     * Sets this quaternion from rotation specified by axis and angle.
     * @param {yunos.graphics.3d.math.Vector3} axis - Axis is assumed to be normalized.
     * @param {number} angle - angle is in radians
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromAxisAngle(axis: Vector3, angle: number): void;
    /**
     * Sets this quaternion from rotation specified by an euler which order 'XYZ'.
     * @param {yunos.graphics.3d.math.Vector3} rotation - Rotation is an euler which specified by a Vector3 and order 'XYZ'.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromEulerXYZ(rotation: Vector3): void;
    /**
     * Sets this quaternion from rotation specified by an euler which order 'YXZ'.
     * @param {yunos.graphics.3d.math.Vector3} rotation - Rotation is an euler which specified by a Vector3 and order 'YXZ'.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromEulerYXZ(rotation: Vector3): void;
    /**
     * Sets this quaternion from rotation specified by an euler which order 'ZXY'.
     * @param {yunos.graphics.3d.math.Vector3} rotation - Rotation is an euler which specified by a Vector3 and order 'ZXY'.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromEulerZXY(rotation: Vector3): void;
    /**
     * Sets this quaternion from rotation specified by an euler which order 'ZYX'.
     * @param {yunos.graphics.3d.math.Vector3} rotation - Rotation is an euler which specified by a Vector3 and order 'ZYX'.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromEulerZYX(rotation: Vector3): void;
    /**
     * Sets this quaternion from rotation specified by an euler which order 'YZX'.
     * @param {yunos.graphics.3d.math.Vector3} rotation - Rotation is an euler which specified by a Vector3 and order 'YZX'.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromEulerYZX(rotation: Vector3): void;
    /**
     * Sets this quaternion from rotation specified by an euler which order 'XZY'.
     * @param {yunos.graphics.3d.math.Vector3} rotation - Rotation is an euler which specified by a Vector3 and order 'XZY'.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromEulerXZY(rotation: Vector3): void;
    /**
     * Sets this quaternion from rotation component of m.
     * @param {yunos.graphics.3d.math.Matrix4} m - the Matrix to rotate
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromRotationMatrix(m: Matrix4): void;
    /**
     * Sets this quaternion to the rotation required to rotate direction vector vFrom to direction vector vTo.
     * @param {yunos.graphics.3d.math.Vector3} vFrom - rotate direction vector form
     * @param {yunos.graphics.3d.math.Vector3} vTo - rotate direction vector to
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setFromUnitVectors(vFrom: Vector3, vTo: Vector3): void;
    /**
     * Returns the numerical elements of this quaternion in an array of format [x, y, z, w].
     * @param {number[]} array - An optional array to store the quaternion.
     * @param {number} offset - offset in the array at which to put the result.
     * @return {number[]} array to store the resulting vector in
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public toArray(array: Array<number>, offset: number): Array<number>;
    /**
     * Unlike the normal method, the static version of slerp sets a target quaternion to the result of the slerp operation.
     * @param {yunos.graphics.3d.math.Quaternion} qStart - The starting quaternion
     * @param {yunos.graphics.3d.math.Quaternion} qEnd - The ending quaternion
     * @param {yunos.graphics.3d.math.Quaternion} qTarget - The target quaternion that gets set with the result
     * @param {number} t - interpolation factor in the closed interval[0, 1]
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public static slerp(qStart: Quaternion, qEnd: Quaternion, qTarget: Quaternion, t: number): Quaternion;
    /**
     * Like the static slerp method above, but operates directly on flat arrays of numbers.
     * @param {number[]} dst - The output array.
     * @param {number} dstOffset - An offset into the output array.
     * @param {number} src0 - The source array of the starting position.
     * @param {number}  srcOffset0 - An offset into the array src0.
     * @param {number[]} src1 - The source array of the target quaternion.
     * @param {number} srcOffset1 - An offset into the array src1.
     * @param {number} t - Normalized interpolation factor.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public static slerpFlat(dst: Array<number>, dstOffset: number, src0: Array<number>, srcOffset0: number, src1: Array<number>, srcOffset1: number, t: number): void;
}
export = Quaternion;
