import YObject = require("../core/YObject");
declare namespace Databoard {
    interface Result {
        err: number;
        msg: string;
    }
    type GetDataCallback = (err: Result, data: Object) => void;
    type SignalListener = (err: Result, signalName: string, data: Object) => void;
}
/**
 * @example
 * <p>The Databoard provides basic data interface to query or subscribe signals.</p>
 * <p>There are following data types which are supported by Databoard:
 * (1). The vehicle information like license plate number. (2). The user information.</p>
 * <p>The class also provides signal subscription, that allows client to register a
 * function to listen signal changing.</p>
 * <p>User permission must be granted to access data by the Databoard interface. The authorization
 * process could be done with the permission API.</p>
 * @example
 * const logger = log("DataboardTest");
 * const databoard = require("yunos/datadriver/Databoard");
 * const permission = require("permission/permission");

 * const INQUIRE_PERMISSION_KEY = 1;
 * let permList = ["VIN", "PlateLicense", "CarColor", "CarModel", "CarType"];
 * // check if permissions are granted already
 * let res = permission.checkSelfPrivacyPermissionSync(permList);
 * let inquirePermArray = [];
 * let permMap = JSON.parse(res);
 * // exclude granted permissions
 * Object.keys(permMap).forEach((item) => {
 *     if (permMap[item] == permission.PermOption.PERMISSION_ALLOW) {
 *         inquirePermArray.push(item);
 *     }
 * });
 * // request permissions
 * permission.inquirePrivacyPermissions(
 *     permList, INQUIRE_PERMISSION_KEY,
 *     function (inquireKey, err, auth) {
 *         if (inquireKey != INQUIRE_PERMISSION_KEY) {
 *             logger.E("PERM_TEST error INQUIRE_PERMISSION_KEY");
 *             return;
 *         }
 *         if (auth) {
 *             logger.E("PERM_TEST inquire permission pass");
 *         } else {
 *             logger.E("PERM_TEST inquire permission fail");
 *         }
 *     });
 * // synchronous method
 * let result = databoard.getVehicleInfoSync(inquirePermArray);
 * if (!result.err) {
 *     logger.D("Get vehicle information synchronously: ", result.data);
 * }
 * // asynchronous method
 * databoard.getVehicleInfo(inquirePermArray,
 *     function(err, data) {
 *         if (!err) {
 *             logger.D("Get vehicle information asynchronously: ", data);
 *         }
 *     });
 * // subscription method
 * databoard.subscribeSignal(["OnVehicle", "OffVehicle"],
 *     function(err, sigName, data) {
 *         if (!err) {
 *             logger.D("Get signal dispatched from Databoard: ", sigName, data);
 *         }
 *     });
 * // unsubscription method
 * result = databoard.unsubscribeSignal(["OnVehicle"]);
 * logger.D("Result of unsubscription:", result.err);
 *
 * @extends yunos.core.YObject
 * @memberof yunos.datadriver
 * @relyon YUNOS_SYSCAP_DATADRIVER
 * @public
 * @since 3
 */
declare class Databoard extends YObject {
    public constructor();
    /**
     * Get the vehicle information in asynchronous way.
     * @param {string[]} filter - the vector of attribute names. Only the attributes listed in it will
     * be returned. If it is empty, no attributes returned. The supported attributes are: "VIN",
     * "PlateLicense", "CarType", "CarModel" and "CarColor".
     * @param {Function} callback - the function which will be invoked if the vechicle information
     * is returned.
     * @return {Object} Return the error code and error message with JSON format.
     * @public
     * @since 3
     */
    public getVehicleInfo(filter: [string], callback: Databoard.GetDataCallback): Databoard.Result;
    /**
     * Get the vehicle information in synchronous way.
     * @param {string[]} filter - the vector of attribute names. Only the attributes listed in it will
     * be returned. If it is empty, no attributes returned. The supported attributes are: "VIN",
     * "PlateLicense", "CarType", "CarModel" and "CarColor".
     * @return {Object} The detailed value of vehicle information in key-value pairs.
     * @public
     * @since 3
     */
    public getVehicleInfoSync(filter: [string]): Object;
    /**
     * Get the user information in asynchronous way.
     * @param {Function} callback - the function which will be invoked when the user information
     * is received.
     * @return {Object} Return the error code and error message with JSON format.
     * @public
     * @since 3
     */
    public getUserInfo(callback: Databoard.GetDataCallback): Databoard.Result;
    /**
     * Get the user information in synchronous way.
     * @return {Object} The detailed value of user information in key-value pairs.
     * @public
     * @since 3
     */
    public getUserInfoSync(): Object;
    private getSceneInfo(filter: [string], callback: Databoard.GetDataCallback): Databoard.Result;
    private getSceneInfoSync(filter: [string]): Object;
    private getLocationInfo(callback: Databoard.GetDataCallback): Databoard.Result;
    private getLocationInfoSync(): Object;
    /**
     * Subscribe specific signal data. The registered function will be invoked when the signal
     * data changed.
     * @param {string[]} signalNames - the list of signal name which the client want to listen.  The
     * supported signals are: "OnVehicle", "OffVehicle", "ServiceRequest" and "SensorFault".
     * @param {Function} callback - the function which will be invoked when subscribed signal value
     * is changed.
     * @return {Object} Return the error code and error message with JSON format.
     * @public
     * @since 3
     */
    public subscribeSignal(signalNames: [string], callback: Databoard.SignalListener): Databoard.Result;
    /**
     * Unsubscribe specific signal data. The pre-registered function will NOT be invoked event when
     * the signal value is changed.
     * @param {string[]} signalNames - the list of signal name which the client want to unsubscribed.
     * The supported signals are: "OnVehicle", "OffVehicle", "ServiceRequest" and "SensorFault".
     * @return {Object} Return the error code and error message with JSON format.
     * @public
     * @since 3
     */
    public unsubscribeSignal(signalNames: [string]): Databoard.Result;
    /**
     * Release all allocated resource and registered listeners.
     * @return {Object} Return the error code and error message with JSON format.
     * @public
     * @since 3
     */
    public release(): Databoard.Result;
}
declare const _default: Databoard;
export = _default;
