import DataError = require("yunos/database/sqlite/DataError");
import SQLiteDatabase = require("yunos/database/sqlite/SQLiteDatabase");
import SQLiteTransaction = require("yunos/database/sqlite/SQLiteTransaction");
import YObject = require("yunos/core/YObject");
/**
 * <p>The function which indicates the result of the function.</p>
 * @callback yunos.database.sqlite.SQLiteOpenHelper~onCompleteCallback
 * @param {yunos.database.sqlite.DataError} error The error object that indicates
 * the result of the function.
 * @public
 * @since 2
 */
/**
 * <p>Helper class for creating/opening SQLite database.</p>
 * <p>This is an abstract class, subclass must implement below functions:</p>
 * <ul>
 *     <li>onCreate(db, transaction, onComplete): funciton be called by framework when creating database</li>
 *     <li>onOpen(db, onComplete): funciton be called  by framework after database is opened</li>
 *     <li>onUpgrade(db, oldVersion, newVersion, transaction, onComplete): funciton be called by framework
 *     when database need to be upgraded</li>
 *     <li>onDowngrade(db, oldVersion, newVersion, transaction, onComplete): funciton be called by framework
 *     when database need to be downgraded</li>
 * </ul>
 *
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class SQLiteOpenHelper extends YObject {
    private _path;
    private _version;
    private _database;
    private _isInInit;
    private _databaseMap;
    private _readOnlyRequest;
    private _readWriteRequest;
    /**
     * Create a SQLiteOpenHelper instance with given db file path and db version.
     *
     * @param {string} path - A string of database file path
     * @param {number} version - The version of the database, used by framework to
     * compare and to upgrade the database
     * @throws {yunos.database.sqlite.DataError} If given path and version is invalid.
     * @public
     * @since 2
     */
    public constructor(path: string, version: number);
    /**
     * <p>Abstract function that is called when creating the database.</p>
     * <p>Developer CAN override it and it will be called by SQLiteOpenHelper</p>
     * <p>when creating the database.</p>
     * <p>onComplete function MUST be called when onCreate is finished.</p>
     *
     * @param {yunos.database.sqlite.SQLiteDatabase} db - The SQLiteDatabase object
     * that is created by this open helper
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates if the the function shall be part of the transaction.
     * In case this parameter is not null, all the database operations inside onCreate
     * must be executed inside the specified transaction. The transaction, if it is not
     * null, must not be committed or rollback in side the function.
     * @param {yunos.database.sqlite.SQLiteOpenHelper~onCompleteCallback} onComplete - The
     * callback function that handles the result
     * @protected
     * @since 2
     */
    protected onCreate(db: SQLiteDatabase, transaction: SQLiteTransaction, onComplete: (error: DataError) => void): void;
    /**
     * <p>Abstract function that is called when openning the database.</p>
     * <p>Developer CAN override it and it will be called by SQLiteOpenHelper when</p>
     * <p>openning the database.</p>
     * <p>onComplete function MUST be called when onOpen is finished.</p>
     *
     * @param {yunos.database.sqlite.SQLiteDatabase} db - The SQLiteDatabase object
     * that is opened by this open helper
     * @param {yunos.database.sqlite.SQLiteOpenHelper~onCompleteCallback} onComplete - The
     * callback function that handles the result
     * @protected
     * @since 2
     */
    protected onOpen(db: SQLiteDatabase, onComplete: (error: DataError) => void): void;
    /**
     * <p>Abstract function that is called when downgrading the database</p>
     * <p>Developer MUST override it and it will be called by SQLiteOpenHelper</p>
     * <p>in case the expected database version is less than the version of the</p>
     * <p>the underlying database.</p>
     * <p>onComplete function MUST be called when onDowngrade is finished.</p>
     *
     * @param {yunos.database.sqlite.SQLiteDatabase} db - The SQLiteDatabase object
     * that is opened by this open helper
     * @param {number} oldVersion - The version of the existing database
     * @param {number} newVersion - The target database version of the downgrade
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates if the function shall be part of the transaction. In
     * case this parameter is not null, all the database operations inside onDowngrade
     * shall be executed inside the specified transaction. The transaction, if it is
     * not null, must not be committed or rollback in side the function.
     * @param {yunos.database.sqlite.SQLiteOpenHelper~onCompleteCallback} onComplete - The
     * callback function that handles the result
     * @protected
     * @since 2
     */
    protected onDowngrade(db: SQLiteDatabase, oldVersion: number, newVersion: number, transaction: SQLiteTransaction, onComplete: (error: DataError) => void): void;
    /**
     * <p>Abstract function that is called when upgrading the database</p>
     * <p>Developer MUST override it and it will be called by SQLiteOpenHelper</p>
     * <p>in case the expected database version is greater than the version of the</p>
     * <p>the underlying database.</p>
     * <p>onComplete function MUST be called when onUpgrade is finished.</p>
     *
     * @param {yunos.database.sqlite.SQLiteDatabase} db - The SQLiteDatabase object
     * that is opened by this open helper
     * @param {number} oldVersion - The version of the existing database
     * @param {number} newVersion - The target database version of the upgrade
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates if the function shall be part of the transaction. In
     * case this parameter is not null, all the database operations inside onUpgrade
     * shall be executed inside the specified transaction. The transaction, if it is
     * not null, must not be committed or rollback in side the function.
     * @param {yunos.database.sqlite.SQLiteOpenHelper~onCompleteCallback} onComplete - The
     * callback function that handles the result
     * @protected
     * @since 2
     */
    protected onUpgrade(db: SQLiteDatabase, oldVersion: number, newVersion: number, transaction: SQLiteTransaction, onComplete: (error: DataError) => void): void;
    /**
     * <p>Get the database instance from Helper.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteDatabase.OpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let currentVersion = 2;
     *    let helper = new SQLiteOpenHelper(dbPath, currentVersion);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteDatabase.OpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteDatabase.OpenMode} mode - The mode of open
     * database which is defined in OpenMode such as OpenReadOnly, OpenReadWrite
     * @param {yunos.database.sqlite.SQLiteOpenHelper~onCompleteCallback} onComplete -
     * The callback function that handles the result
     * @return {yunos.database.sqlite.SQLiteDatabase} Return the SQLiteDatabase instance
     * opened in expected mode
     * @throws {yunos.database.sqlite.DataError} If given mode or callback is invalid.
     * @public
     * @since 2
     */
    public getDatabase(mode: number, onComplete: (error: DataError, db: SQLiteDatabase) => void): void;
}
export = SQLiteOpenHelper;
