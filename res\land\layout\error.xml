<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    width="{sdp(320)}"
    height="{sdp(328)}"
    layout="{layout.err_dlg}"
    propertySetName="error">
    <ImageView
        id="id_error_icon"
        width="{sdp(160)}"
        height="{sdp(160)}"
        src="{img(images/ic_no_wifi.png)}"
        scaleType="{enum.ImageView.ScaleType.Center}"/>

    <TextView
        id="id_error_text"
        text="{string.NETWORK_ERROR}"
        propertySetName="extend/hdt/FontTitle2"
        color="{theme.color.White_2}"/>

    <ButtonBM
        id="id_error_btn"
        text="{string.RELOAD}"
        height="{sdp(64)}"
        colorType="{enum.ButtonBM.ColorType.Secondary}"/>
</CompositeView>
