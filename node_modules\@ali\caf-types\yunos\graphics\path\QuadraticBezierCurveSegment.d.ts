import Segment = require("./Segment");
declare class QuadraticBezierCurveSegment extends Segment {
    private _x1: number;
    private _y1: number;
    private _x2: number;
    private _y2: number;
    private _cpx: number;
    private _cpy: number;
    private _length: number;
    public constructor(x1: number, y1: number, x2: number, y2: number, cpx: number, cpy: number);
    private static vecADotvecB(aX: number, aY: number, bX: number, bY: number): number;
    private static vecLength(x: number, y: number): number;
    private static getCurveLength(x1: number, y1: number, x2: number, y2: number, cpx: number, cpy: number): number;
}
export = QuadraticBezierCurveSegment;
