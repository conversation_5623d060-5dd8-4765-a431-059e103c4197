import YObject = require("../../core/YObject");
import View = require("../../ui/view/View");
import ShaderGroup = require("./ShaderGroup");
import ShaderImpl = require("yunos/ui/rendering/agil2/shader/Shader");
/**
 * <p>The Shader class applies a custom vertex and fragment shader to a View.</p>
 * <p><em>Note: Need to define the vertex and fragment shader in the SubClass.</em></p>
 * <p>There are two types of input to the vertexShader: uniform variables and attributes. Some are predefined: <br>
 * uniform mat4 mvp - combined transformation matrix, an orthogonal projection. <br>
 * uniform float opacity - combined opacity, the product of the opacities from the view to this Shader. <br>
 * attribute vec4 pos - vertex position, the top-left vertex has position (0, 0), the bottom-right (width, height). <br>
 * varying vec2 uv - texture coordinate, the top-left coordinate is (0, 0), the bottom-right (1, 1). <br>
 * A sampler is predefined in fragmentShader: <br>
 * uniform sampler2D mysource - sampler, the sampler of the attached view. </p>
 *
 * @example
 * class MyShader extends Shader {
 *     getVertexShader() {
 *         return `
 *             void main() {
 *                 gl_Position = mvp  pos;
 *             }`;
 *     }
 *     getFragmentShader() {
 *         return `
 *             uniform highp vec4 mycolor;
 *             uniform highp float mysize;
 *             void main(void) {
 *                 gl_FragColor = texture2D(mysource, uv);
 *             }`;
 *     }
 *
 *     set color(value) {
 *         this.setColorProperty("mycolor", value);
 *     }
 * }
 * let co = new MyShader();
 * co.color = "red";
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics.shader
 * @public
 * @since 5
 */
declare class Shader extends YObject {
    private _impl: ShaderImpl;
    private _source: View | Shader;
    private _fragmentShader: string;
    private _vertexShader: string;
    private _width: number;
    private _height: number;
    private _top: number;
    private _layerType: string;
    private _left: number;
    private _attachedView: View;
    /**
     * Create a shader.
     * @public
     * @since 5
     */
    public constructor();
    private initializeImpl(): void;
    /**
     * Set property to shader.
     * @param {string} name - property name.
     * @param {number | number[]} value - uniform value, can be number or number array.
     * @protected
     * @since 5
     */
    protected setProperty(name: string, value: Object): void;
    /**
     * Set color property to shader.
     * @param {string} name - property name.
     * @param {string | number} color - color string or number.
     * @protected
     * @since 5
     */
    protected setColorProperty(name: string, color: string): void;
    private setVectorProperty(name: string, v0: number, v1: number, v2: number): void;
    private setPointFProperty(name: string, v0: number, v1: number): void;
    /**
     * Set source for shader
     * @param {yunos.ui.view.View | yunos.graphics.shader.Shader} source
     * @private
     */
    private source: View | Shader;
    private setSource(name: string, source: View | Shader | ShaderGroup, visible?: boolean): void;
    private getImpl(): ShaderImpl;
    private blending: boolean;
    private mesh: number[];
    private cullMode: number;
    private cached: boolean;
    private attachedView: View;
    /**
     * Destroy this shader.
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * Defines the fragment shader's GLSL source code.
     * @name yunos.graphics.shader.Shader#fragmentShader
     * @type {string}
     * @private
     */
    private fragmentShader: string;
    /**
     * Defines the vertex shader's GLSL source code.
     * @name yunos.graphics.shader.Shader#vertexShader
     * @type {string}
     * @private
     */
    private vertexShader: string;
    /**
     * <p>Defines the vertex shader's GLSL source code.</p>
     * @return {string} The source code
     *
     * @protected
     * @since 5
     */
    protected getVertexShader(): string;
    /**
     * <p>Defines the fragment shader's GLSL source code.</p>
     * @return {string} The source code
     *
     * @protected
     * @since 5
     */
    protected getFragmentShader(): string;
    /**
     * LayerType for shader
     * @name yunos.graphics.shader.Shader#layerType
     * @type {yunos.graphics.shader.Shader.LayerType}
     * @default yunos.graphics.shader.Shader.LayerType.NONE.
     * @public
     * @since 2
     * @deprecated 5
     */
    public layerType: string;
    private width: number;
    private height: number;
    private left: number;
    private top: number;
    /**
     * Enum for Shader LayerType.
     * The value set shader layerType.
     * Current only support hardware layer.
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     * @deprecated 5
     */
    public static readonly LayerType: {
        /**
         * NONE
         * render shader in normal way.
         * @public
         * @since 2
         */
        NONE: string;
        /**
         * HARDWARE
         * render shader using hardware acceleration, using FBO
         * @public
         * @since 2
         */
        HARDWARE: string;
    };
    private static readonly CullMode: {
        NoCulling: int;
        BackFaceCulling: int;
        FrontFaceCulling: int;
    };
}
export = Shader;
