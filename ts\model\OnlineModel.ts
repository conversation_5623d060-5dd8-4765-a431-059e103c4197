/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Model = require("yunos/appmodel/Model");
import ConfigStore = require("yunos/content/ConfigStore");
import VideoInfo = require("./VideoInfo");
import log = require("../utils/log");
import Consts = require("../Consts");
import {IOnlineVideo} from "ts/Types";
const TAG = "OnlineModel";
const VIDEO_CACHE_KEY = "video_data";

class OnlineModel extends Model {
    private static _instance: OnlineModel;
    private _configStore: ConfigStore;
    private _speedLimit: number;
    private _cpInfo: Object;
    private _categoryList: Object[];
    private _videoList: VideoInfo[];

    constructor() {
        super();
        this._configStore = ConfigStore.getInstance("VIDEO_CONFIGSTORE");
        this._speedLimit = Consts.DEFAULT_SPEED_LIMIT;
        this._categoryList = [];
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new OnlineModel();
        }
        return this._instance;
    }

    /**
     * 查询今日在线视频
     */
    queryVideoToday(pageNo: number, saveCache: boolean, callback: (arg0: Object, arg1: boolean, arg2: VideoInfo[]) => void) {
        log.I(TAG, "queryVideoToday");
        if (callback) {
            callback(null, false, []);
        }
    }

    _convertOnlineData(data: IOnlineVideo[]) {
        let list: VideoInfo[] = [];
        if (!data || data.length === 0) {
            return list;
        }
        for (let i = 0; i < data.length; i++) {
            list.push(new VideoInfo(data[i]));
        }
        return list;
    }

    /**
     * 查询缓存的在线视频列表数据
     */
    loadCacheData(callback: (arg0: Object, arg1?: Object[]) => void) {
        log.I(TAG, "loadCacheData");
        let rawData = "";
        if (this._configStore) {
            rawData = <string> this._configStore.get(VIDEO_CACHE_KEY, "");
        }
        let parsedData = [];
        try {
            if (rawData && rawData !== "") {
                parsedData = JSON.parse(rawData);
            }
        } catch (e) {
            log.E(TAG, "loadCacheData", e, rawData);
            if (callback) {
                callback("EMPTY_LIST");
                return;
            }
        }
        let itemList = [];
        for (let i = 0; i < parsedData.length; i++) {
            let item = VideoInfo.createFrom(parsedData[i]);
            if (item.checkValid()) {
                itemList.push(item);
            }
        }

        log.I(TAG, "loadCacheData Done! len: ", itemList.length);
        if (itemList.length <= 0) {
            if (callback) {
                callback("EMPTY_LIST");
            }
        } else {
            this._videoList = itemList;
            if (callback) {
                callback(null, itemList);
            }
        }
    }

    /**
     * 缓存在线视频列表数据
     */
    _saveCacheData(data: VideoInfo[]) {
        if (!this._checkDataChanged(this._videoList, data)) {
            log.I(TAG, "_saveCacheData, data hasn't change");
            return false;
        }

        let jsonData = JSON.stringify(data);
        this._configStore.put(VIDEO_CACHE_KEY, jsonData);
        this._configStore.apply((err) => {
            if (err !== null) {
                log.D(TAG, "_saveCacheData failed");
            } else {
                log.D(TAG, "_saveCacheData success");
            }
        });
        return true;
    }

    /**
     * 检查在线视频和缓存数据信息
     */
    _checkDataChanged(curData: VideoInfo[], newData: VideoInfo[]) {
        if (!curData || !newData) {
            log.W(TAG, "_checkDataChanged failed. curData:", Boolean(curData), "newData:", Boolean(newData));
            return true;
        }

        let changed = false;
        if (curData.length !== newData.length) {
            changed = true;
        } else {
            for (let i = 0; i < curData.length; i++) {
                if (!(curData[i] instanceof VideoInfo) || !curData[i].equal(newData[i])) {
                    log.D(TAG, "_checkDataChanged curData:", curData[i].id, curData[i].title, "newData:", newData[i].id, newData[i].title);
                    changed = true;
                    break;
                }
            }
        }
        return changed;
    }

    /**
     * 根据分类信息查询视频数据
     */
    queryVideoByCategory(category: string, pageNo: number, saveCache: boolean, callback: (arg0: Object, arg1: boolean, arg2: VideoInfo[]) => void) {
        log.I(TAG, "queryVideoByCategory");
        if (callback) {
            callback(null, false, []);
        }
    }

    /**
     * 搜索在线视频
     */
    searchVideo(title: string, pageNo: number, callback: (arg0: Object, arg1?: Object[]) => void) {
        log.I(TAG, "searchVideo");
        if (callback) {
            callback(null, []);
        }
    }

    /**
     * 查询视频分类
     */
    queryCategories(callback: (arg0: Object) => void) {
        log.I(TAG, "queryCategories");
        if (callback) {
            callback(null);
        }
    }

    /**
     * 查询cp信息
     */
    queryCPInfo(callback?: (arg0: Object, arg1?: Object) => void) {
        log.I(TAG, "queryCPInfo");
    }

    /**
     * 查询限速信息
     */
    querySpeedLimit(callback?: (arg0: Object, arg1?: Object) => void) {
        log.I(TAG, "querySpeedLimit");
    }

    /**
     * 查询视频是否下线
     */
    checkVideos(ids: Object, callback: (arg0: Object, arg1?: Object) => void) {
        log.I(TAG, "checkVideos");
    }

    notifyItemListChanged(itemList: Object, pageNo: number, videoCount: number) {
        this.emit(Consts.EV_ITEM_LIST_CHANGED, itemList, pageNo, videoCount);
    }

    get cpInfo() {
        return this._cpInfo;
    }

    get categoryList() {
        return this._categoryList;
    }

    get speedLimit() {
        return this._speedLimit;
    }
}

export = OnlineModel;
