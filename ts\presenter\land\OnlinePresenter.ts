/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
const {Visible, Hidden, None} = require("yunos/ui/view/View").Visibility;
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import ScrollBar = require("yunos/ui/widget/ScrollBar");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import View = require("yunos/ui/view/View");
import GridView = require("yunos/ui/view/GridView");
import ListView = require("yunos/ui/view/ListView");
import CompositeView = require("yunos/ui/view/CompositeView");
const RecognitionMode = require("yunos/ui/voice/VoiceCommand").RecognitionMode;
import GestureEvent = require("yunos/ui/event/GestureEvent");
import OnlineImageView = require("extend/hdt/control/OnlineImageViewBM");
import ButtonBM = require("extend/hdt/control/ButtonBM");
import PopupBM = require("extend/hdt/control/PopupBM");
import LoadingPage = require("extend/hdt/control/LoadingPageBM");
import Loading = require("extend/hdt/control/LoadingBM");
import CategoryAdapter = require("./adapter/CategoryAdapter");
import OnlineAdapter = require("../../adapter/OnlineAdapter");
import Consts = require("../../Consts");
const RoutePath = Consts.RoutePath;
import Features = require("../../Features");
import VideoInfo = require("../../model/VideoInfo");
const iOnlineModel = require("../../model/OnlineModel").getInstance();
const iVideoModel = require("../../model/VideoModel").getInstance();
const iNetworkState = require("../../monitor/NetworkState").getInstance();
const iTrafficHelper = require("../../utils/TrafficHelper").getInstance();
const iUserTrackHelper = require("../../utils/UserTrackHelper").getInstance();
const iCacheVideo = require("../../utils/CacheVideo").getInstance();
import log = require("../../utils/log");
import Utils = require("../../utils/Utils");
import {IVoiceCommand, IVoiceEvent} from "../../Types";
const TAG = "OnlinePresenter";

const USB_FIRST_INDEX = 0;
const USB_SECOND_INDEX = 1;
const LoadingPageType = {
    LOADING: 0,
    NETWORK: 1,
    SERVER: 2,
    EMPTY: 3
};

interface IHeaderItem extends ButtonBM {
    path: string;
}

interface IHeaderItemNarrow extends ImageView {
    path: string;
}

interface IViews {
    navigationBar?: NavigationBar;
    usbFirst?: IHeaderItem;
    usbSecond?: IHeaderItem;
    usbFirstNarrow?: IHeaderItemNarrow;
    usbSecondNarrow?: IHeaderItemNarrow;
    dlna?: View;
    search?: View;
    cpInfoView?: View;
    loadingPage?: LoadingPage;
    empty?: CompositeView;
    onlineList?: ListView;
    scrollbar?: ScrollBar;
    categoryIcon?: ImageView;
    categoryTitle?: TextView;
    cpLogo?: ImageView;
}

interface ICategoryDialogContent extends CompositeView {
    container?: CompositeView;
    categoryList?: GridView;
    scrollbar?: ScrollBar;
    networkError?: CompositeView;
    loading?: Loading;
    retryBtn?: ButtonBM;
}

interface ICategoryData {
    category: string;
}

interface IQrCodeDialogContent extends CompositeView {
    container?: CompositeView;
    loadingContainer?: CompositeView;
    loading?: Loading;
    networkTip?: TextView;
    qrCode?: OnlineImageView;
}

interface IListView {
    canScrollHorizontal(delta: number): boolean;
}

interface IParamObj {
    result?: Object;
    category_list?: string;
    reason?: Object;
    qrcode_link?: Object;
    category?: Object;
    usb_count?: Object;
    video_count?: number;
    video_title_list?: string;
    video_id_list?: string;
    cpId?: string;
}

interface IPageInfo {
    pageNo: number;
    videoCount: number;
}

class OnlinePresenter extends Presenter {
    private _highlightUrlChanged: (path: string, url: string, index: number) => void;
    private _onlineListChanged: (arg0: object[], arg1: number, arg2: number) => void;
    private _retryBtnListener: () => void;

    private _destroyed: boolean;
    private _hidden: boolean;
    private _firstload: boolean;
    private _categoryIndex: number;
    private _categoryName: string;
    private _isNarrowScreen: boolean;
    private _views: IViews;
    private _viewAttached: boolean;
    private _tapAbleViews: View[];
    private _loadingMoreView: View;
    private _loadingMore: boolean;
    private _loadingMoreTimeout: NodeJS.Timer
    private _categoryDialog: PopupBM;
    private _categoryDialogContent: ICategoryDialogContent;
    private _qrCodeDialog: PopupBM;
    private _qrCodeDialogContent: IQrCodeDialogContent;

    /**
     * 在线视频记忆页面的逻辑
     * 1.当天进入在线视频需要记忆页码，在下次当天下次打开视频时停留在上次页码
     * 2.每天刷新，即每天第一次页码从头开始
     * 3.页码需要支持分类场景
     * 4.用户可上拉查看之前的内容，下拉加载新内容
     *
     * 实现
     * 1.每次滑动列表，记录本次停留的页码和当前可见的第一个视频位置（本页）
     * 2.再进入视频后先向服务端查询记录页码的视频列表，并根据上次视频的位置来显示
     * 3.未显示的视频，每次下拉加载3个视频
     */
    private _pageInfo: IPageInfo[]; // 页面信息：页码和本页视频数量
    private _pageNo: number; // 本页页码，从1开始
    private _pageFirstVideoPosition: number; // 本页当前可见的第一个视频位置，从0开始
    private _pageUnloadVideosArray: VideoInfo[]; // 本页未加载的视频列表
    private _pageUnloadNum: number; // 未加载的视频页数

    onCreate() {
        log.I(TAG, "onCreate");
        this._destroyed = false;
        this._isNarrowScreen = Utils.isNarrowScreen();
        this.attachView("online");
        this._init();
    }

    _init() {
        if (iTrafficHelper.checkTrafficState(null, false)) {
            iOnlineModel.querySpeedLimit();
            iOnlineModel.queryCPInfo();
            iOnlineModel.queryCategories();
        }
        this._initPageInfo();
        this._loadData();
        this._initListener();
    }

    /**
     * 初始化页面信息
     * 1.当前页码和第一个视频索引
     * 2.视频分类名称和索引
     */
    _initPageInfo() {
        let nowDateString = new Date().toDateString();
        let lastDateString = iVideoModel.getPageRefreshDate();
        log.I(TAG, "_initPageInfo", nowDateString, lastDateString);
        if (nowDateString === lastDateString) {
            let pageInfo = iVideoModel.getPageInfo();
            this._pageNo = pageInfo.pageNo;
            this._pageFirstVideoPosition = pageInfo.videoIndex;
            this._pageUnloadNum = this._pageNo - 1;
            this._categoryIndex = pageInfo.categoryIndex;
            this._categoryName = pageInfo.categoryName;
            if (pageInfo.categoryName) {
                this._views.categoryTitle.text = pageInfo.categoryName;
            }
        } else {
            this._pageNo = 1;
            this._pageFirstVideoPosition = 0;
            this._pageUnloadNum = 0;
            this._categoryIndex = 0;
            this._categoryName = "";
            iVideoModel.savePageRefreshDate(nowDateString);
            iCacheVideo.delAllCachedVideo();
        }
        this._firstload = true;
        this._pageInfo = [];
        this._pageUnloadVideosArray = [];
        log.I(TAG, "_initPageInfo", this._pageNo, this._pageFirstVideoPosition, this._categoryName);
    }

    /**
     * 记录页面信息
     * 1.当前页码和第一个视频索引
     * 2.视频分类名称和索引
     */
    _savePageInfo() {
        let firstPosition = this._views.onlineList.getFirstVisiblePosition();
        let pageNo = 1;
        let videoIndex = 0;
        let lastPageVideosSum = 0;
        let pageVideosSum = 0;
        let unloadVideosNum = this._pageUnloadVideosArray.length;
        log.I(TAG, "_savePageInfo", firstPosition, unloadVideosNum);
        for (let i = 0; i < this._pageInfo.length; i++) {
            // log.I(TAG, "_savePageInfo", this._pageInfo[i].pageNo, "---", this._pageInfo[i].videoCount);
            if (i === 0) {
                pageVideosSum = this._pageInfo[i].videoCount - unloadVideosNum;
            } else {
                pageVideosSum += this._pageInfo[i].videoCount;
            }

            if (pageVideosSum > firstPosition) {
                pageNo = this._pageInfo[i].pageNo;
                if (i === 0) {
                    videoIndex = firstPosition + unloadVideosNum;
                } else {
                    videoIndex = firstPosition - lastPageVideosSum;
                }
                break;
            } else {
                lastPageVideosSum = pageVideosSum;
            }
        }

        log.I(TAG, "_savePageInfo", pageNo, videoIndex, this._categoryIndex, this._categoryName);
        iVideoModel.savePageInfo(pageNo, videoIndex, this._categoryIndex, this._categoryName);
    }

    /**
     * 加载在线视频数据
     * 1.查询缓存的在线视频信息
     * 2.请求cosmo服务端，查询在线视频数据
     */
    _loadData(isNeedLoadCache = true) {
        this._showLoadingPage(LoadingPageType.LOADING);

        log.I(TAG, "_loadData", isNeedLoadCache, this._firstload, this._categoryName);
        if (isNeedLoadCache) {
            iOnlineModel.loadCacheData((error: Object, itemList: VideoInfo[]) => {
                if (error) {
                    log.I(TAG, "loadCacheData", error);
                    return;
                }
                if (!iNetworkState.networkConnected) {
                    log.I(TAG, "loadCacheData, network disconnect");
                    return;
                }
                if (!iTrafficHelper.checkTrafficState(null, false)) {
                    log.I(TAG, "loadCacheData, traffic issue");
                    return;
                }
                if (this._firstload) {
                    itemList = this._checkVideoList(itemList);
                }
                this._trySetSelectionList(itemList);
            });
        }

        if (this._categoryName) {
            iOnlineModel.queryVideoByCategory(this._categoryName, this._pageNo, true, this._loadDataCallback.bind(this));
        } else {
            iOnlineModel.queryVideoToday(this._pageNo, true, this._loadDataCallback.bind(this));
        }
    }

    _loadDataCallback(error: Object, cacheChanged: boolean, itemList: VideoInfo[]) {
        if (error) {
            log.I(TAG, "_loadDataCallback", error);
            if (error === "NETWORK_ERROR") {
                this._showLoadingPage(LoadingPageType.NETWORK);
            } else {
                this._showLoadingPage(LoadingPageType.SERVER);
            }
        } else {
            log.I(TAG, "_loadDataCallback", cacheChanged);
            this._recordPageInfo(this._pageNo, itemList ? itemList.length : 0, false);
            if (cacheChanged) {
                if (this._firstload) {
                    itemList = this._checkVideoList(itemList);
                }
                this._trySetSelectionList(itemList);
            } else {
                if (this._firstload) {
                    itemList = this._checkVideoList(itemList);
                    this._trySetSelectionList(itemList);
                }
            }

            if (this._firstload) {
                // 如果当前显示的数量小于阈值，则加载下一页
                if (itemList && itemList.length < Consts.DEFAULT_THRESHOLD_NUM) {
                    this._onReachend();
                }
            }
            this._firstload = false;
        }
    }

    /**
     * 记录页面信息，页码和视频数量
     */
    _recordPageInfo(pageNo: number, videoCount: number, isPrev: boolean) {
        if (videoCount === 0) {
            log.W(TAG, "_recordPageInfo, videoCount is zero");
            return;
        }

        let pageInfo = {
            pageNo: pageNo,
            videoCount: videoCount
        };

        log.I(TAG, "_recordPageInfo", pageNo, videoCount, isPrev);
        if (isPrev) {
            this._pageInfo.unshift(pageInfo);
        } else {
            this._pageInfo.push(pageInfo);
        }

        log.I(TAG, "_recordPageInfo, ===dump-pageinfo===");
        for (let i = 0; i < this._pageInfo.length; i++) {
            log.I(TAG, this._pageInfo[i].pageNo, "-", this._pageInfo[i].videoCount);
        }
        log.I(TAG, "_recordPageInfo, ===dump-pageinfo===");
    }

    /**
     * 根据记录的页码和显示的第一个视频索引来生成在线视频列表数据
     */
    _checkVideoList(itemList: VideoInfo[]) {
        if (!itemList || itemList.length === 0) {
            log.W(TAG, "_checkVideoList, list is null or empty");
            return itemList;
        }

        log.I(TAG, "_checkVideoList", this._pageFirstVideoPosition, itemList.length);
        if (this._pageFirstVideoPosition >= itemList.length) {
            this._pageFirstVideoPosition = 1;
            return itemList;
        }

        this._pageUnloadVideosArray = itemList.splice(0, this._pageFirstVideoPosition);
        log.I(TAG, "_checkVideoList", this._pageUnloadVideosArray.length);
        return itemList;
    }

    /**
     * 初始化监听器，监听U盘、高亮、流量的变化消息
     */
    _initListener() {
        this._highlightUrlChanged = (path: string, url: string, index: number) => {
            if (this._destroyed) {
                log.W(TAG, "_highlightUrlChanged, presenter is destroyed");
                return;
            }

            if (path !== Consts.FromType.ONLINE) {
                log.W(TAG, "_highlightUrlChanged, ignore path");
                return;
            }

            if (!url) {
                log.W(TAG, "_highlightUrlChanged, url is null");
                return;
            }

            log.D(TAG, "_highlightUrlChanged", path, url, index);
            if (this._views.onlineList) {
                let adapter = <OnlineAdapter> this._views.onlineList.adapter;
                if (adapter && adapter.data) {
                    for (let i = 0; i < adapter.data.length; i++) {
                        (<VideoInfo> adapter.data[i]).lastPlayed = i === index ? true : false;
                        adapter.update(i, adapter.data[i]);
                    }
                }
            }
        };
        iVideoModel.on(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);

        this._onlineListChanged = (itemList: VideoInfo[], pageNo: number, videoCount: number) => {
            if (this._destroyed) {
                log.W(TAG, "_onlineListChanged, presenter is destroyed");
                return;
            }

            let lastVideoNum = 0;
            if (this._views.onlineList) {
                let adapter = <OnlineAdapter> this._views.onlineList.adapter;
                if (adapter) {
                    lastVideoNum = adapter.data.length;
                }
            }
            this._recordPageInfo(pageNo, videoCount, false);
            this._pageNo = pageNo;
            this._trySetSelectionList(itemList);
            this._views.onlineList.arriveAt(lastVideoNum);
        };
        iOnlineModel.on(Consts.EV_ITEM_LIST_CHANGED, this._onlineListChanged);
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_ONLINE_LIST);
    }

    onBackKey() {
        log.I(TAG, "onBackKey");
        return false;
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        this._closeAllDialog();
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_ONLINE_LIST);
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._viewAttached = true;
        this._updateUsbContainer();
        this._setupTapHandler();
        this._addVoiceCommands();
        this._createCategoryDialog();
        let iLocalModel = require("../../model/LocalModel").getInstance();
        iLocalModel.preloadData();
    }

    _setupViews(parentView: View) {
        this._views = {};
        this._views.navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = None;
        this._views.navigationBar.titleItem.title.align = TextView.Align.Left;
        this._views.navigationBar.preTitleItem.visibility = None;
        this._views.navigationBar.rightItem.visibility = Visible;
        let rightItem = null;
        if (this._isNarrowScreen) {
            rightItem = <CompositeView> LayoutManager.loadSync("online_nav_right_item_narrow");
            this._views.usbFirstNarrow = <IHeaderItemNarrow> rightItem.findViewById("id_usb_first");
            this._views.usbSecondNarrow = <IHeaderItemNarrow> rightItem.findViewById("id_usb_second");
        } else {
            rightItem = <CompositeView> LayoutManager.loadSync("online_nav_right_item_normal");
            this._views.usbFirst = <IHeaderItem> rightItem.findViewById("id_usb_first");
            this._views.usbSecond = <IHeaderItem> rightItem.findViewById("id_usb_second");
        }
        this._views.navigationBar.rightItem = rightItem;
        this._views.dlna = <View> rightItem.findViewById("id_dlna");
        this._views.search = <View> rightItem.findViewById("id_search");

        this._views.loadingPage = <LoadingPage> parentView.findViewById("id_loading_page");
        this._views.loadingPage.addEventListener("RetryButtonReleased", this._retryBtnListener = () => {
            log.I(TAG, "retry button pressed!");
            if (iNetworkState.networkConnected) {
                this._initPageInfo();
                this._loadData(false);
            }
        });
        this._views.empty = <CompositeView> parentView.findViewById("id_empty");
        this._views.onlineList = <ListView> parentView.findViewById("id_list_grid");
        this._views.scrollbar = <ScrollBar> parentView.findViewById("id_scrollbar");
        this._views.onlineList.horizontalScrollBar = this._views.scrollbar;

        this._views.cpInfoView = <CompositeView> parentView.findViewById("id_cp_info");
        this._views.categoryIcon = <ImageView> this._views.cpInfoView.findViewById("id_category_icon");
        this._views.categoryTitle = <TextView> this._views.cpInfoView.findViewById("id_category_title");
        this._views.cpLogo = <ImageView> this._views.cpInfoView.findViewById("id_cp_logo");

        this._categoryDialogContent = <ICategoryDialogContent> {};
        this._qrCodeDialogContent = <IQrCodeDialogContent> {};

        if (!Features.SUPPORT_USB) {
            if (this._isNarrowScreen) {
                this._views.usbFirstNarrow.visibility = None;
                this._views.usbSecondNarrow.visibility = None;
            } else {
                this._views.usbFirst.visibility = None;
                this._views.usbSecond.visibility = None;
            }
        }
    }

    /**
     * 更新首页USB入口显示，默认显示一个USB，最多显示两个USB
     */
    _updateUsbContainer() {
        if (!Features.SUPPORT_USB) {
            return;
        }

        let len = 0;
        log.I(TAG, "_updateUsbContainer", len);
        switch (len) {
            case 0:
                if (this._isNarrowScreen) {
                    this._views.usbFirstNarrow.visibility = None;
                    this._views.usbSecondNarrow.visibility = Visible;
                    this._views.usbSecondNarrow.propertySetName = Consts.SRC_USB;
                } else {
                    this._views.usbFirst.visibility = None;
                    this._views.usbSecond.visibility = Visible;
                    this._views.usbSecond.propertySetName = Consts.ICON_SRC_USB;
                }
                break;
        }
    }

    _trySetSelectionList(itemList: object[]) {
        if (this._destroyed) {
            log.I(TAG, "_trySetSelectionList, destroyed.");
            return;
        }
        if (!this._viewAttached) {
            log.W(TAG, "_trySetSelectionList, waiting for view attached.");
            return;
        }
        if (!iTrafficHelper.checkTrafficState(null, false)) {
            log.I(TAG, "_trySetSelectionList, traffic issue");
            this._showLoadingPage(LoadingPageType.NETWORK);
            return;
        }

        log.I(TAG, "_trySetSelectionList", itemList ? itemList.length : 0);
        if (!itemList || itemList.length === 0) {
            this._showLoadingPage(LoadingPageType.EMPTY);
        } else if (this._views.onlineList && itemList && itemList.length > 0) {
            this._hideLoadingPage();
            this._views.onlineList.arriveAt(0);

            let adapter = this._views.onlineList.adapter;
            if (!adapter) {
                adapter = new OnlineAdapter();
                this._views.onlineList.adapter = adapter;
                this._views.onlineList.on("itemselect", this._selectHandler.bind(this));
                this._views.onlineList.on("scrollstatechange", this._onScrollStateChange.bind(this));
                this._views.onlineList.on("panstart", this._onPanstart.bind(this));
                this._views.onlineList.on("panend", this._onPanend.bind(this));
                this._views.onlineList.on("reachstart", this._onReachstart.bind(this));
                this._views.onlineList.on("reachend", this._onReachend.bind(this));
            }
            (<OnlineAdapter>adapter).data = itemList;
        }
    }

    _selectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_selectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }
        this._playVideo(position);
    }

    _playVideo(position: number) {
        log.I(TAG, "_playVideo", position);
        let data = (<OnlineAdapter> this._views.onlineList.adapter).data;
        let item = <VideoInfo> data[position];
        if (!item) {
            log.I(TAG, "_playVideo, item is null");
            return;
        }
        let paramObj = {
            video_index: position,
            video_id: item.id,
            video_title: item.title,
            video_size: item.videoSize,
            video_category: item.category,
            video_tags: item.tags,
            video_duration: item.duration,
            cpId: iUserTrackHelper.CP_ID
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST,
            iUserTrackHelper.ONLINE_LIST_PICK, paramObj);
        let launchData = {
            from: Consts.FromType.ONLINE,
            index: position,
            list: data,
            pageNo: this._pageNo,
            categoryIndex: this._categoryIndex,
            categoryName: this._categoryName
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _setupTapHandler() {
        this._tapAbleViews = [];
        if (this._isNarrowScreen) {
            Utils.setOnTapListener(this._views.usbFirstNarrow, () => {
                log.I(TAG, "first usb pressed!");
                this._enterUsbDetail(USB_FIRST_INDEX);
            });
            this._tapAbleViews.push(this._views.usbFirstNarrow);

            Utils.setOnTapListener(this._views.usbSecondNarrow, () => {
                log.I(TAG, "first usb pressed!");
                this._enterUsbDetail(USB_SECOND_INDEX);
            });
            this._tapAbleViews.push(this._views.usbSecondNarrow);
        } else {
            Utils.setOnTapListener(this._views.usbFirst, () => {
                log.I(TAG, "first usb pressed!");
                this._enterUsbDetail(USB_FIRST_INDEX);
            });
            this._tapAbleViews.push(this._views.usbFirst);

            Utils.setOnTapListener(this._views.usbSecond, () => {
                log.I(TAG, "second usb pressed!");
                this._enterUsbDetail(USB_SECOND_INDEX);
            });
            this._tapAbleViews.push(this._views.usbSecond);
        }

        Utils.setOnTapListener(this._views.dlna, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_DLNA);
            this._navigate(RoutePath.DLNA);
        });
        this._tapAbleViews.push(this._views.dlna);

        Utils.setOnTapListener(this._views.search, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_SEARCH);
            this._navigate(RoutePath.SEARCH);
        });
        this._tapAbleViews.push(this._views.search);

        Utils.setOnTapListener(this._views.categoryIcon, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_CATEGORY_ICON);
            this._showCategoryDialog();
        });
        this._tapAbleViews.push(this._views.categoryIcon);

        Utils.setOnTapListener(this._views.categoryTitle, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_CATEGORY_ICON);
            this._showCategoryDialog();
        });
        this._tapAbleViews.push(this._views.categoryTitle);

        Utils.setOnTapListener(this._views.cpLogo, () => {
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST, iUserTrackHelper.ONLINE_LIST_CP_ICON);
            this._showQRCodeDialog();
        });
        this._tapAbleViews.push(this._views.cpLogo);
    }

    /**
     * 进入USB详情页
     */
    _enterUsbDetail(index: number) {
        if (!Features.SUPPORT_USB) {
            return;
        }

        let usbContainer = undefined;
        if (this._isNarrowScreen) {
            usbContainer = index === USB_FIRST_INDEX ? this._views.usbFirstNarrow : this._views.usbSecondNarrow;
        } else {
            usbContainer = index === USB_FIRST_INDEX ? this._views.usbFirst : this._views.usbSecond;
        }
        let path = usbContainer.path;
        log.I(TAG, "_enterUsbDetail", usbContainer.path);
        this._navigate(RoutePath.LOCAL, {index: 1, path: path}, {launchMode: "single"});
    }

    /**
     * 创建视频分类对话框
     */
    _createCategoryDialog() {
        log.I(TAG, "_createCategoryDialog");
        let container = <ICategoryDialogContent> LayoutManager.loadSync("online_popup_category");
        this._categoryDialogContent.categoryList = <GridView> container.findViewById("id_category_list");
        this._categoryDialogContent.scrollbar = <ScrollBar> container.findViewById("id_scrollbar");
        this._categoryDialogContent.categoryList.verticalScrollBar = this._categoryDialogContent.scrollbar;
        this._categoryDialogContent.networkError = <CompositeView> container.findViewById("id_network");
        this._categoryDialogContent.loading = <Loading> container.findViewById("id_loading");
        this._categoryDialogContent.retryBtn = <ButtonBM> container.findViewById("id_btn_retry");
        this._categoryDialogContent.container = container;

        Utils.setOnTapListener(this._categoryDialogContent.retryBtn, () => {
            log.I(TAG, "tap category refresh btn.");
            this._showCategoryList();
        });

        const popup = new PopupBM();
        popup.title = iRes.getString("CATEGORY_TITLE");
        popup.setContentView(this._categoryDialogContent.container);
        popup.on("close", () => {
            log.I(TAG, "category dialog close");
            iUserTrackHelper.clickButton(
                iUserTrackHelper.PAGE_ONLINE_LIST_CATE,
                iUserTrackHelper.ONLINE_LIST_CATE_CLOSE
            );

            let paramObj = <IParamObj> {};
            if (iNetworkState.networkConnected) {
                if (this._categoryDialogContent.categoryList) {
                    let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
                    if (adapter && adapter.data && adapter.data.length > 0) {
                        let list = "";
                        for (let i = 0; i < adapter.data.length; i++) {
                            list += (<ICategoryData> adapter.data[i]).category;
                            if (i !== adapter.data.length - 1) {
                                list += ",";
                            }
                        }
                        log.I(TAG, "category list:", list);
                        paramObj.result = iUserTrackHelper.SUCCESS;
                        paramObj.category_list = list;
                    } else {
                        paramObj.result = iUserTrackHelper.FAIL;
                        paramObj.reason = iTrafficHelper.failReason; 
                    }
                } else {
                    paramObj.result = iUserTrackHelper.FAIL;
                    paramObj.reason = iTrafficHelper.failReason;
                }
            } else {
                paramObj.result = iUserTrackHelper.FAIL;
                paramObj.reason = iTrafficHelper.failReason;
            }
            iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_ONLINE_LIST_CATE, paramObj);

            Utils.setOnTapListener(this._categoryDialogContent.retryBtn, null);
            this._categoryDialog.destroy(true);
            this._categoryDialog = null;
        });
        this._categoryDialog = popup;
        this._showCategoryList();
    }

    /**
     * 显示视频分类对话框
     */
    _showCategoryDialog() {
        if (this._hidden) {
            log.I(TAG, "_showCategoryDialog, page hidden");
            return;
        }

        if (this._qrCodeDialog && this._qrCodeDialog.isShowing()) {
            log.I(TAG, "_showCategoryDialog, qrCode dialog is showing");
            return;
        }

        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_ONLINE_LIST_CATE);
        if (!this._categoryDialog) {
            this._createCategoryDialog();
        }

        if (this._categoryDialog) {
            log.I(TAG, "_showCategoryDialog, show");
            this._categoryDialog.soundEffectsEnabled = true;
            this._categoryDialog.show();
        }
    }

    /**
     * 显示视频分类列表
     */
    _showCategoryList() {
        if (!iNetworkState.networkConnected || !iTrafficHelper.checkTrafficState()) {
            this._categoryDialogContent.loading.visibility = Hidden;
            this._categoryDialogContent.categoryList.visibility = Hidden;
            this._categoryDialogContent.networkError.visibility = Visible;
        } else {
            if (iOnlineModel.categoryList && iOnlineModel.categoryList.length > 0) {
                this._categoryDialogContent.loading.visibility = Hidden;
                this._categoryDialogContent.networkError.visibility = Hidden;
                this._categoryDialogContent.categoryList.visibility = Visible;
                this._trySetCategoryList();
                this._updateCategoryListSelected(this._categoryIndex);
            } else {
                this._categoryDialogContent.networkError.visibility = Hidden;
                this._categoryDialogContent.loading.visibility = Visible;
                iOnlineModel.queryCategories((error: Object) => {
                    log.I(TAG, "queryCategories", error);
                    this._categoryDialogContent.loading.visibility = Hidden;
                    if (error) {
                        this._categoryDialogContent.categoryList.visibility = Hidden;
                        this._categoryDialogContent.networkError.visibility = Visible;
                    } else {
                        this._categoryDialogContent.categoryList.visibility = Visible;
                        this._categoryDialogContent.networkError.visibility = Hidden;
                        this._trySetCategoryList();
                        this._updateCategoryListSelected(this._categoryIndex);
                    }
                });
            }
        }
    }

    /**
     * 创建视频分类列表
     */
    _trySetCategoryList() {
        log.I(TAG, "_trySetCategoryList");
        if (this._destroyed) {
            log.I(TAG, "_trySetCategoryList, destroyed.");
            return;
        }

        if (!this._viewAttached) {
            log.I(TAG, "_trySetCategoryList, waiting for view attached.");
            return;
        }

        if (!iOnlineModel.categoryList || iOnlineModel.categoryList.length === 0) {
            log.I(TAG, "_trySetCategoryList, category list is null.");
            return;
        }

        let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
        if (!adapter) {
            adapter = new CategoryAdapter();
            adapter.registerVoiceSelectListener(this._categorySelectHandler.bind(this));
            this._categoryDialogContent.categoryList.adapter = adapter;
            this._categoryDialogContent.categoryList.on("itemselect", this._categorySelectHandler.bind(this));
        }
        adapter.data = iOnlineModel.categoryList;
    }

    /**
     * 视频分类列表选中的回调
     */
    _categorySelectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_categorySelectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        if (this._categoryDialog) {
            this._categoryDialog.soundEffectsEnabled = false;
        }

        if (this._categoryIndex === position) {
            log.I(TAG, "_categorySelectHandler, ignore the same category");
            this._hideCategoryDialog();
            return;
        }

        let categoryName = "";
        if (this._categoryDialogContent) {
            let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
            if (adapter) {
                if (adapter.data[position]) {
                    categoryName = (<ICategoryData> adapter.data[position]).category;
                }
                adapter.onDataChange();
            }
        }
        iUserTrackHelper.clickButton(
            iUserTrackHelper.PAGE_ONLINE_LIST_CATE,
            iUserTrackHelper.ONLINE_LIST_CATE_PICK,
            {category_title: categoryName}
        );

        this._hideCategoryDialog();
        this._pageNo = 1;
        this._pageInfo = [];
        this._pageFirstVideoPosition = 0;
        this._pageUnloadNum = 0;
        this._pageUnloadVideosArray = [];
        this._categoryIndex = position;
        this._categoryName = position === 0 ? "" : categoryName;
        this._savePageInfo();
        this._views.categoryTitle.text = categoryName;
        this._views.onlineList.arriveAt(0);
        if (this._views.onlineList.adapter) {
            (<OnlineAdapter> this._views.onlineList.adapter).clear();
        }
        this._loadData();
    }

    /**
     * 根据选中的分类更新列表显示
     */
    _updateCategoryListSelected(position: number) {
        log.I(TAG, "_updateCategoryListSelected", position);
        if (!this._categoryDialogContent.categoryList.adapter) {
            this._trySetCategoryList();
        }

        let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
        if (adapter && adapter.data) {
            adapter.setFocusPosition(position);
            for (let i = 0; i < adapter.data.length; i++) {
                adapter.update(i, adapter.data[i]);
            }
        }
    }

    _hideCategoryDialog() {
        if (this._categoryDialog) {
            this._categoryDialog.close();
        }
    }

    /**
     * 显示二维码对话框
     */
    _showQRCodeDialog() {
        if (this._hidden) {
            log.I(TAG, "_showQRCodeDialog, page hidden");
            return;
        }

        if (this._categoryDialog && this._categoryDialog.isShowing()) {
            log.I(TAG, "_showQRCodeDialog, category dialog is showing");
            return;
        }

        if (this._qrCodeDialog) {
            log.I(TAG, "_showQRCodeDialog, created");
            this._qrCodeDialog.show();
            return;
        }

        log.I(TAG, "_showQRCodeDialog, creating");
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_ONLINE_LIST_QR);
        this._qrCodeDialogContent.container = <IQrCodeDialogContent> LayoutManager.loadSync("online_popup_qr_code");
        this._qrCodeDialogContent.loadingContainer = <CompositeView> this._qrCodeDialogContent
            .container.findViewById("id_qr_code_loading_container");
        this._qrCodeDialogContent.loading = <Loading> this._qrCodeDialogContent
            .container.findViewById("id_loading");
        this._qrCodeDialogContent.networkTip = <TextView> this._qrCodeDialogContent
            .container.findViewById("id_network_tip");
        this._qrCodeDialogContent.qrCode = <OnlineImageView><Object> this._qrCodeDialogContent
            .container.findViewById("id_qr_code_icon");

        Utils.setOnTapListener(this._qrCodeDialogContent.networkTip, () => {
            log.I(TAG, "tap qrCode loading error.");
            this._showQrCode();
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST_QR, iUserTrackHelper.ONLINE_LIST_QR_RETRY);
        });

        const popup = new PopupBM();
        popup.title = iRes.getString("CP_TITLE");
        popup.setContentView(this._qrCodeDialogContent.container);
        popup.on("close", () => {
            log.I(TAG, "qrCode dialog close");
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_ONLINE_LIST_QR, iUserTrackHelper.ONLINE_LIST_QR_CLOSE);

            let paramObj = <IParamObj>{};
            if (iNetworkState.networkConnected && iOnlineModel.cpInfo && iOnlineModel.cpInfo.qrcode) {
                paramObj.result = iUserTrackHelper.SUCCESS;
                paramObj.reason = "NA";
                paramObj.qrcode_link = iOnlineModel.cpInfo.qrcode;
            } else {
                paramObj.result = iUserTrackHelper.FAIL;
                paramObj.reason = iTrafficHelper.failReason;
                paramObj.qrcode_link = "NA";
            }
            iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_ONLINE_LIST_QR, paramObj);

            Utils.setOnTapListener(this._qrCodeDialogContent.networkTip, null);
            this._qrCodeDialog.destroy(true);
            this._qrCodeDialog = null;
        });
        popup.show();
        this._qrCodeDialog = popup;
        this._showQrCode();
    }

    /**
     * 显示二维码
     */
    _showQrCode() {
        if (!iNetworkState.networkConnected || !iTrafficHelper.checkTrafficState()) {
            this._qrCodeDialogContent.qrCode.visibility = Hidden;
            this._queryCPInfo();
        } else {
            if (iOnlineModel.cpInfo && iOnlineModel.cpInfo.qrcode) {
                this._qrCodeDialogContent.loadingContainer.visibility = Hidden;
                this._qrCodeDialogContent.qrCode.visibility = Visible;
                this._loadQRImage();
            } else {
                this._queryCPInfo();
            }
        }
    }

    /**
     * 查询cp信息，logo和二维码
     */
    _queryCPInfo() {
        this._qrCodeDialogContent.loadingContainer.visibility = Visible;
        this._showQrCodeLoading(true);
        iOnlineModel.queryCPInfo((error: Object, cpInfo: { qrcode: Object }) => {
            if (!error && cpInfo && cpInfo.qrcode) {
                this._qrCodeDialogContent.loadingContainer.visibility = Hidden;
                this._qrCodeDialogContent.qrCode.visibility = Visible;
                this._loadQRImage();
            } else {
                this._qrCodeDialogContent.loadingContainer.visibility = Visible;
                this._qrCodeDialogContent.qrCode.visibility = Hidden;
                this._showQrCodeLoading();
            }
        });
    }

    _loadQRImage() {
        this._qrCodeDialogContent.qrCode.loadUrl(iOnlineModel.cpInfo.qrcode, (error: Object) => {
            log.I(TAG, "_loadQRImage, callback", error);
            if (error && this._qrCodeDialogContent && this._qrCodeDialogContent.qrCode) {
                this._qrCodeDialogContent.qrCode.propertySetName = Consts.SRC_KSQR;
            }
        });
    }

    _hideQRCodeDialog() {
        if (this._qrCodeDialog) {
            this._qrCodeDialog.close();
        }
    }

    _closeAllDialog() {
        log.I(TAG, "_closeAllDialog");
        this._hideCategoryDialog();
        this._hideQRCodeDialog();
    }

    /**
     * 视频列表滑动结束事件
     */
    _onScrollStateChange(state: number) {
        log.I(TAG, "_onScrollStateChange", state);
        if (state !== ListView.ScrollState.IDLE) {
            return;
        }
        this._savePageInfo();
    }

    _onPanstart(e: GestureEvent) {
        if (e.deltaX <= 0 || (<IListView>(<Object> this._views.onlineList)).canScrollHorizontal(e.deltaX)) {
            log.W(TAG, "_onPanstart, not reaching the top");
            return;
        }
        this._onReachstart();
    }

    _onPanend(e: GestureEvent) {
        if (e.deltaX >= 0 || (<IListView>(<Object> this._views.onlineList)).canScrollHorizontal(e.deltaX)) {
            log.W(TAG, "_onPanend, not reaching the end");
            return;
        }
        this._onReachend();
    }

    /**
     * 视频列表顶部时触发
     */
    _onReachstart() {
        if (!this._pageUnloadVideosArray || this._pageUnloadVideosArray.length === 0) {
            if (this._pageUnloadNum <= 0) {
                log.W(TAG, "_onReachstart, all pages loaded");
                return;
            }

            log.I(TAG, "_onReachstart", this._categoryName, this._pageUnloadNum);
            if (this._categoryName) {
                iOnlineModel.queryVideoByCategory(this._categoryName, this._pageUnloadNum, false, this._onReachstartCallback.bind(this));
            } else {
                iOnlineModel.queryVideoToday(this._pageUnloadNum, false, this._onReachstartCallback.bind(this));
            }
        } else {
            log.I(TAG, "_onReachstart");
            this._insertOneRowData();
            this._savePageInfo();
        }
    }

    _onReachstartCallback(error: object, changed: boolean, ret: VideoInfo[]) {
        if (error) {
            log.W(TAG, "_onReachstartCallback", error);
            return;
        }

        if (ret && ret.length > 0) {
            log.I(TAG, "_onReachstartCallback", ret.length);
            this._recordPageInfo(this._pageUnloadNum, ret.length, true);
            this._pageUnloadVideosArray = ret;
            this._insertOneRowData();
            this._savePageInfo();
            this._pageUnloadNum--;
        } else {
            log.I(TAG, "_onReachstartCallback, empty");
        }
    }

    /**
     * 加载一行视频数据，如进入视频应用时非第一页，则在向下滑动时每次加载一行数据防止界面有跳跃感
     */
    _insertOneRowData() {
        let len = this._pageUnloadVideosArray.length;
        log.I(TAG, "_insertOneRowData len", len);
        let oneRowData = [];
        if (len <= Consts.LAND_LOAD_VIDEO_NUM_ONE_TIME) {
            oneRowData = this._pageUnloadVideosArray.splice(0, len);
        } else {
            oneRowData = this._pageUnloadVideosArray.splice(
                len - Consts.LAND_LOAD_VIDEO_NUM_ONE_TIME, Consts.LAND_LOAD_VIDEO_NUM_ONE_TIME
            );
        }
        (<OnlineAdapter> this._views.onlineList.adapter).insertAll(0, oneRowData);
    }

    _onReachend() {
        log.I(TAG, "_onReachend");
        if (!this._loadingMoreView) {
            this._loadingMoreView = LayoutManager.loadSync("online_loading_more");
        }

        if (!this._loadingMore) {
            iUserTrackHelper.sendEvent(iUserTrackHelper.PAGE_ONLINE_LIST,
                iUserTrackHelper.ONLINE_LIST_SLIDING, {page_index: this._pageNo + 1}
            );
            this._loadingMore = true;
            this._views.onlineList.addFooter(this._loadingMoreView);

            let pageNo = this._pageNo + 1;
            if (this._categoryName) {
                iOnlineModel.queryVideoByCategory(this._categoryName, pageNo, false, this._onReachendCallback.bind(this));
            } else {
                iOnlineModel.queryVideoToday(pageNo, false, this._onReachendCallback.bind(this));
            }

            this._removeTimeout(this._loadingMoreTimeout);
            this._loadingMoreTimeout = setTimeout(() => {
                this._removeLoadingMoreView();
            }, Consts.LOADING_MORE_TIMEOUT);
        }
    }

    _onReachendCallback(error: object, changed: boolean, ret: VideoInfo[]) {
        log.I(TAG, "_onReachendCallback", error);
        this._removeLoadingMoreView();
        if (!error) {
            if (ret && ret.length > 0) {
                this._pageNo++;
                (<OnlineAdapter> this._views.onlineList.adapter).addAll(ret);
                this._recordPageInfo(this._pageNo, ret.length, false);
            } else {
                Utils.showToast(iRes.getString("LOAD_MORE_NO_MORE"));
            }
        } else {
            Utils.showToast(iRes.getString("LOAD_MORE_FAILED"));
        }
    }

    _removeLoadingMoreView() {
        this._views.onlineList.removeFooter(this._loadingMoreView);
        this._loadingMore = false;
        this._removeTimeout(this._loadingMoreTimeout);
    }

    _navigate(path: string, launchData?: Object, newConfig = {}) {
        if (path === RoutePath.BACK) {
            this.context.router.back();
        } else {
            if (!this._hidden) {
                if (!launchData) {
                    launchData = {};
                }
                this.context.router.navigate(path, launchData, newConfig);
            } else {
                log.W(TAG, "_navigate negtive", path);
            }
        }
    }

    /**
     * 显示二维码loading信息
     */
    _showQrCodeLoading(showLoading?: boolean) {
        if (!this._views || this._destroyed) {
            return;
        }
        if (showLoading) {
            this._qrCodeDialogContent.networkTip.visibility = Hidden;
            this._qrCodeDialogContent.loading.visibility = Visible;
        } else {
            this._qrCodeDialogContent.networkTip.visibility = Visible;
            this._qrCodeDialogContent.loading.visibility = Hidden;
        }
    }

    /**
     * 关闭二维码loading信息
     */
    _hideQrCodeLoading() {
        if (!this._views || this._destroyed) {
            return;
        }
        this._qrCodeDialogContent.loadingContainer.visibility = Hidden;
    }

    /**
     * 显示loading、无网络、无数据等信息
     */
    _showLoadingPage(type: number) {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_showLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_showLoadingPage", type);
        if (type === LoadingPageType.LOADING) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.LoadingMode;
            this._views.loadingPage.errorTitleVisible = false;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = false;
            this._views.loadingPage.retryButtonVisible = false;
            this._views.loadingPage.loadingText = iRes.getString("LOADING");
            this._resetListView();
        } else if (type === LoadingPageType.SERVER || type === LoadingPageType.NETWORK) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.ErrorMode;
            this._views.loadingPage.errorTitleVisible = true;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = true;
            this._views.loadingPage.retryButtonVisible = true;
            if (type === LoadingPageType.SERVER) {
                this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
                this._views.loadingPage.errorTitle = iRes.getString("SERVER_ERROR");
            } else {
                this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
                this._views.loadingPage.errorTitle = iRes.getString("NETWORK_ERROR");
            }
            this._resetListView();
        } else if (type === LoadingPageType.EMPTY) {
            this._views.empty.visibility = Visible;
            this._views.loadingPage.visibility = None;
            this._resetListView();
        } else {
            log.W(TAG, "_showLoadingPage, type is undefine");
        }
    }

    _hideLoadingPage() {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_hideLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_hideLoadingPage");
        this._views.empty.visibility = None;
        this._views.loadingPage.visibility = None;
    }

    _resetListView() {
        if (this._views.onlineList) {
            this._views.onlineList.arriveAt(0);
            let adapter = <OnlineAdapter> this._views.onlineList.adapter;
            if (adapter) {
                adapter.clear();
            }
        }
    }

    _calculatePosition(index: number) {
        let firstVisiblePos = this._views.onlineList.getFirstVisiblePosition();
        let position = 0;
        if (firstVisiblePos === 0) {
            position = index;
        } else {
            position = firstVisiblePos - 1 + index;
        }
        log.I(TAG, "_calculatePosition", firstVisiblePos, position);
        return position;
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        log.I(TAG, "_addVoiceCommands");
        // jshint unused:false

        let cmdKeys = [];
        if (Features.SUPPORT_USB) {
            cmdKeys = ["VOICECMD_USB_VIDEO", "VOICECMD_USB_VIDEO_ALIAS"];
            if (this._isNarrowScreen) {
                Utils.registerVoiceCommand(this._views.usbFirstNarrow, cmdKeys, RecognitionMode.Both, (cmdKey: string, index: number) => {
                    log.I(TAG, "voice command", cmdKey, index);
                    this._enterUsbDetail(USB_FIRST_INDEX);
                }, true);
            } else {
                Utils.registerVoiceCommand(this._views.usbFirst, cmdKeys, RecognitionMode.Both, (cmdKey: string, index: number) => {
                    log.I(TAG, "voice command", cmdKey, index);
                    this._enterUsbDetail(USB_FIRST_INDEX);
                }, true);
            }
        }

        cmdKeys = ["CATEGORY_TITLE"];
        Utils.registerVoiceCommand(this._views.categoryIcon, cmdKeys, RecognitionMode.Both, (cmdKey: string, index: number) => {
            log.I(TAG, "voice command", cmdKey, index);
            this._showCategoryDialog();
        }, true);

        if (Consts.SUPPORT_VOICE_CMD) {
            let selectCommand = (<IVoiceCommand> <Object> VoiceCommand).createSelectCommand();
            this._views.onlineList.addVoiceCommand(selectCommand);
            this._views.onlineList.on("voice", (e: IVoiceEvent) => {
                if (e.command === selectCommand) {
                    let index = parseInt(e.result) - 1;
                    log.I(TAG, "onlineList.onVoice, selectCommand", index);
                    if (index < 0 || index >= (<OnlineAdapter> this._views.onlineList.adapter).data.length) {
                        log.W(TAG, "onlineList.onVoice negative, index out of list range");
                        e.endLocalTask();
                        return;
                    }

                    let position = this._calculatePosition(index);
                    if (position < 0 || position >= (<OnlineAdapter> this._views.onlineList.adapter).data.length) {
                        log.W(TAG, "onlineList.onVoice negative, position out of list range");
                        e.endLocalTask();
                        return;
                    }
                    this._playVideo(position);
                }
                e.endLocalTask();
            });

            cmdKeys = ["VOICECMD_ONLINE_LIST_1", "VOICECMD_ONLINE_LIST_2", "VOICECMD_ONLINE_LIST_3",
                "VOICECMD_ONLINE_LIST_4", "VOICECMD_ONLINE_LIST_5", "VOICECMD_ONLINE_LIST_6",
                "VOICECMD_ONLINE_LIST_7", "VOICECMD_ONLINE_LIST_8", "VOICECMD_ONLINE_LIST_9"];
            Utils.registerVoiceCommand(this._views.onlineList, cmdKeys, RecognitionMode.QuickWord, (cmdKey: string, index: number) => {
                log.I(TAG, "onlineList.onVoice, quickWord", cmdKey, index);
                if (index < 0 || index >= cmdKeys.length) {
                    log.W(TAG, "onlineList.onVoice negative, index out of list range");
                    return;
                }

                let position = this._calculatePosition(index);
                if (position < 0 || position >= (<OnlineAdapter> this._views.onlineList.adapter).data.length) {
                    log.W(TAG, "onlineList.onVoice negative, position out of list range");
                    return;
                }
                this._playVideo(position);
            }, true);
        }
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }
        if (this._isNarrowScreen) {
            Utils.removeVoiceCommand(this._views.usbFirstNarrow);
        } else {
            Utils.removeVoiceCommand(this._views.usbFirst);
        }
        Utils.removeVoiceCommand(this._views.categoryIcon);
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];

        this._views.onlineList.removeAllListeners("itemselect");
        this._views.onlineList.removeAllListeners("scrollstatechange");
        this._views.onlineList.removeAllListeners("panstart");
        this._views.onlineList.removeAllListeners("panend");
        this._views.onlineList.removeAllListeners("reachstart");
        this._views.onlineList.removeAllListeners("reachend");
        if (this._views.onlineList.adapter) {
            this._views.onlineList.adapter = null;
        }

        if (this._views.scrollbar) {
            this._views.scrollbar.destroy();
            this._views.scrollbar = null;
        }

        if (this._categoryDialogContent.categoryList) {
            this._categoryDialogContent.categoryList.removeAllListeners("itemselect");
            let adapter = <CategoryAdapter> this._categoryDialogContent.categoryList.adapter;
            if (adapter) {
                adapter.registerVoiceSelectListener(null);
                adapter = null;
            }
        }

        if (this._views.loadingPage) {
            this._views.loadingPage.removeEventListener("RetryButtonReleased", this._retryBtnListener);
            this._retryBtnListener = null;
        }

        this._views = null;
        iVideoModel.off(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
        iOnlineModel.off(Consts.EV_ITEM_LIST_CHANGED, this._onlineListChanged);
        log.I(TAG, "_removeAllListeners done");
    }

    /**
     * 移除所有timer
     */
    _removeAllTimeout() {
        this._removeTimeout(this._loadingMoreTimeout);
    }

    _removeTimeout(timeout: NodeJS.Timer) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
    }

    _destroyDialog() {
        if (this._categoryDialog) {
            if (this._categoryDialogContent.retryBtn) {
                Utils.setOnTapListener(this._categoryDialogContent.retryBtn, null);
            }
            this._categoryDialog.destroy(true);
            this._categoryDialog = null;
        }

        if (this._qrCodeDialog) {
            if (this._qrCodeDialogContent.networkTip) {
                Utils.setOnTapListener(this._qrCodeDialogContent.networkTip, null);
            }
            this._qrCodeDialog.destroy(true);
            this._qrCodeDialog = null;
        }
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._removeAllTimeout();
        this._removeVoiceCommands();
        this._removeAllListeners();
        this._destroyDialog();
    }
}

export = OnlinePresenter;
