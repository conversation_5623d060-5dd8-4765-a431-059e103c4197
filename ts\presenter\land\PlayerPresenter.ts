/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

const AudioManager = require("yunos/device/AudioManager");
import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import ImageView = require("yunos/ui/view/ImageView");
import View = require("yunos/ui/view/View");
import Slider = require("yunos/ui/widget/Slider");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
const {Visible, None} = require("yunos/ui/view/View").Visibility;
const RecognitionMode = VoiceCommand.RecognitionMode;
import PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
import AnimationGroup = require("yunos/ui/animation/AnimationGroup");
import AlertDialog = require("yunos/ui/widget/AlertDialog");
import VideoView = require("yunos/ui/widget/VideoView");
import SwipeRecognizer = require("yunos/ui/gesture/SwipeRecognizer");
import PageLink = require("yunos/page/PageLink");
import CompositeView = require("yunos/ui/view/CompositeView");
import TextView = require("yunos/ui/view/TextView");
import RichTextView = require("yunos/ui/view/RichTextView");
import Cursor = require("yunos/provider/Cursor");
import {IEvent, IConfig, ITitleItem} from "../../Types";
const MediaMeta = require("yunos/multimedia/MediaMeta");
import OnlineImageView = require("extend/hdt/control/OnlineImageViewBM");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import Window = require("yunos/ui/view/Window");
import RelativeLayout = require("yunos/ui/layout/RelativeLayout");
import LoadingBM = require("extend/hdt/control/LoadingBM");
import ButtonBM = require("extend/hdt/control/ButtonBM");
import LocalAdapter = require("./adapter/LocalAdapter");
import VideoInfo = require("../../model/VideoInfo");
const iPageInstance = require("../../index").getInstance();
const iOnlineModel = require("../../model/OnlineModel").getInstance();
const iLocalModel = require("../../model/LocalModel").getInstance();
const iVideoModel = require("../../model/VideoModel").getInstance();
const iNetworkState = require("../../monitor/NetworkState").getInstance();
const iAudioSession = require("../../monitor/AudioSession").getInstance();
const iCacheVideo = require("../../utils/CacheVideo").getInstance();
const iTrafficHelper = require("../../utils/TrafficHelper").getInstance();
const iUserTrackHelper = require("../../utils/UserTrackHelper").getInstance();
import Utils = require("../../utils/Utils");
import log = require("../../utils/log");
import Consts = require("../../Consts");
import Features = require("../../Features");
const TAG = "PlayerPresenter";

const UPDATE_PROGRASS_INTERVAL = 1000;
const RECORD_PROGRASS_INTERVAL = 5000;
const CONTROL_VIEW_ANIM_DURATION = 200;
const CONTROL_VIEW_DELAY_TIMEOUT = 5000;
const DNS_LOOK_UP_TIMEOUT = 15000;
const PLAY_WAIT_TIMEOUT = 300;
const PLAY_DELAY_TIMEOUT = 10;
const COUNT_DOWN_INTERVAL = 1000;
const ONE_HOUR_SECOND = 3600000;
const TYPE_FIRST_DRAW_READY = 306;
const SURFACE_RESIZE_MODE = 1;
const VIDEO_WIDTH_UNIT = 16;
const VIDEO_HEIGHT_UNIT = 32;
const DEFAULT_VALUE = "NA";

const State = {
    IDLE: "idle",
    LOADING: "loading",
    PLAY_START: "play_start",
    PLAYING: "playing",
    PAUSE_START: "pause_start",
    PAUSE: "pause",
    FAIL: "fail"
};
const ExitType = {
    AUTO: "auto",
    CLICK: "click"
};
const PopupDialogType = {
    PHONE: 0,
    OFFLINE: 1,
    DECODE: 2,
    NOT_EXIST: 3,
    UNKNOWN: 4,
    DLNA: 5
};
const InterruptReason = {
    PHONE: 0,
    XIAOYUN: 1,
    SETTING: 2,
    MULTIMEDIA: 3,
    SELF: 4,
    CHAT: 5,
    OTHER: 6
};
const LoadingPageType = {
    LOADING: 0,
    NETWORK: 1
};

interface IRetJson {
    ErrorMessage: string;
    CurrentTransportStatus: string;
    CurrentSpeed: string;
    CurrentTransportState: string;
    CurrentVolume: number;
    Track: string;
    TrackMetaData: string;
    TrackURI: string;
    RelCount: string;
    AbsCount: string;
    RelTime: string;
    AbsTime: string;
    TrackDuration: string;
    CurrentURI: string;
    CurrentURIMetaData: string;
    NextURI: string;
    NextURIMetaData: string;
    NrTracks: string;
    MediaDuration: string;
    PlayMedium: string;
    RecordMedium: string;
    WriteStatus: string;
}

interface IReply {
    writeInt32: (value: number) => void;
    writeString: (value: string) => void;
}

interface IVideoView extends VideoView {
    setBufferScale: (value: boolean) => void;
}

interface IAlertDialog extends AlertDialog {
    type: number;
}

let Config: IConfig = {};

class PlayerPresenter extends Presenter {
    private _from: string;
    private _path: string;
    private _index: number;
    private _playList: VideoInfo[];
    private _playCursor: Cursor;
    private _playAdapter: LocalAdapter;
    private _pageNo: number;
    private _keyword: string;
    private _categoryIndex: number;
    private _categoryName: string;
    private _playVideoInfo: VideoInfo;
    private _playListLength: number;

    private _elapsedTime: number;
    private _duration: number;

    private _playExitType: string;
    private _videoStartTime: number;
    private _playTotalTime: number;
    private _playStartTime: number;

    private _isReverseRangeState: boolean;
    private _isPlayWaiting: boolean;
    private _isFirstDrawCompleted: boolean;
    private _isNeedToSeek: boolean;
    private _isPlaybackComplete: boolean;
    private _isPlayAfterPaused: boolean;
    private _isNeedPromptTraffic: boolean;
    private _isNeedCheckNetworkType: boolean;
    private _networkChangedOperations: boolean;
    private _interruptReason: number;
    private _lastState: string;
    private _currentState: string;
    private _errorCode: string;
    private _activated: boolean;
    private _hidden: boolean;
    private _destroyed: boolean;

    private _tapAbleViews: View[];
    private _controlWindow: Window;
    private _previewImage: OnlineImageView;

    private _views: {
        navigationBar?: NavigationBar;
        backIcon?: ImageView,
        videoView?: VideoView,
        errorDialog?: View,
        controlRootView?: CompositeView,
        header?: CompositeView,
        title?: RichTextView,
        elapsedTime?: TextView,
        totalTime?: TextView,
        loading?: LoadingBM,
        network?: CompositeView,
        retryBtn?: ButtonBM,
        videoViewContainer?: CompositeView,
        ctrlBar?: View,
        warningDialog?: View,
        warningDialogTips?: TextView,
        decorateContainer?: CompositeView,
        decorateLeftFiller?: View,
        decorateTopFiller?: View,
        decorateRightFiller?: View,
        decorateBottomFiller?: View
    };

    private _ctrlViews: {
        prev?: View,
        next?: View,
        seekBar?: CompositeView,
        progressSlider?: Slider,
        playPause?: View
    };

    private _checkOfflineResult: {
        id?: Object,
        networkError?: boolean,
        invalid?: boolean
    };

    private _onPlayPause: (triggerType: string, playEvent?: Object) => void;
    private _handleAudioSessionChanged: (clientName: string, gainedSession: boolean) => void;

    private _sliderValue: number;
    private _sliderTouchStart: boolean;
    private _onSliderTouchStart: () => void;
    private _onSliderTouchEnd: () => void;
    private _onSliderValueChanged: (value: number) => void;

    private _dialog: IAlertDialog;
    private _errorDialogShowing: boolean;
    private _warningDialogShowing: boolean;
    private _warningCountDown: number;
    private _controlViewShowing: boolean;

    private _swipeRecognizer: SwipeRecognizer;
    private _swipeLeftListener: () => void;
    private _swipeRightListener: () => void;

    private _hideControlViewAnim: AnimationGroup;
    private _showControlViewAnim: AnimationGroup;

    private _playWaitTimer: NodeJS.Timer;
    private _playDelayTimer: NodeJS.Timer;
    private _dnsLookupTimer: NodeJS.Timer;
    private _updateProgressTimer: NodeJS.Timer;
    private _recordProgressTimer: NodeJS.Timer;
    private _hidePageTimer: NodeJS.Timer;
    private _hideControlViewTimer: NodeJS.Timer;

    onCreate() {
        log.I(TAG, "onCreate");
        iAudioSession.requestAudioSession();
        if (iAudioSession.isPhoneGainedAudioSession()) {
            Utils.showToast(iRes.getString("TOAST_BT_PHONE"));
            this.destroy();
            return;
        }

        this._parseData(this.context.data);
        this._init();
        this.attachView("player");
    }

    _parseData(data: object) {
        this._from = (<{ from: string }> data).from;
        this._path = (<{ path: string }> data).path;
        this._index = (<{ index: number }> data).index;
        this._pageNo = (<{ pageNo: number }> data).pageNo;
        this._keyword = (<{ keyword: string }> data).keyword;
        this._categoryIndex = (<{ categoryIndex: number }> data).categoryIndex;
        this._categoryName = (<{ categoryName: string }> data).categoryName;
        log.I(TAG, "_parseData", this._from, this._path, this._index, this._pageNo, this._keyword
            , this._categoryIndex, this._categoryName);

        // 视频播放列表来源
        // 1.数组类型：在线视频、搜索的在线视频、投屏
        // 2.Cursor类型：本地视频、搜索的本地视频
        // 3.LocalAdapter类型：搜索的本地视频（仅显示部分视频）
        if (this._from === Consts.FromType.ONLINE || this._from ===
            Consts.FromType.SEARCH_ONLINE || this._from === Consts.FromType.DLNA) {
            this._playList = (<{ list: VideoInfo[] }> data).list;
        } else if ((<{ list: Object }> data).list instanceof Cursor) {
            this._playCursor = (<{ list: Cursor }> data).list;
        } else if ((<{ list: Object }> data).list instanceof LocalAdapter) {
            this._playAdapter = (<{ list: LocalAdapter }> data).list;
        } else {
            this._playList = (<{ list: VideoInfo[] }> data).list;
            if (!this._playList || this._playList.length === 0) {
                log.E(TAG, "_parseData, play list is null");
                this.destroy();
            }
        }

        this._setPlayVideoInfo();
    }

    /**
     * 从视频列表信息获取当前播放视频的信息
     */
    _setPlayVideoInfo() {
        if (this._playList) {
            log.I(TAG, "_setPlayVideoInfo, data is array, length is", this._playList.length);
            this._playListLength = this._playList.length;
            this._playVideoInfo = this._playList[this._index];
            if (!this._playVideoInfo) {
                log.E(TAG, "_setPlayVideoInfo, _playVideoInfo is null");
                this.destroy();
            }
            return;
        }

        if (this._playCursor && !this._playCursor.isClosed()) {
            log.I(TAG, "_setPlayVideoInfo, data is cursor, count is ", this._playCursor.count);
            this._playListLength = this._playCursor.count;
            if (this._index >= this._playListLength) {
                this._index = this._playListLength;
            }
            this._playVideoInfo = iLocalModel.getVideoInfo(this._playCursor, this._index);
            if (!this._playVideoInfo) {
                log.E(TAG, "_setPlayVideoInfo, _playVideoInfo is null");
                this.destroy();
            }
            return;
        }

        if (this._playAdapter && this._playAdapter.cursor &&
            !this._playAdapter.cursor.isClosed()) {
            log.I(TAG, "_setPlayVideoInfo, count is", this._playAdapter.cursor.count);
            this._playListLength = this._playAdapter.cursor.count;
            if (this._index >= this._playListLength) {
                this._index = this._playListLength;
            }
            this._playVideoInfo = iLocalModel.getVideoInfo(this._playAdapter.cursor, this._index);
            if (!this._playVideoInfo) {
                log.E(TAG, "_setPlayVideoInfo, _playVideoInfo is null");
                this.destroy();
            }
            return;
        }

        if (!this._playVideoInfo) {
            log.E(TAG, "_setPlayVideoInfo, fail");
            this.destroy();
        }
    }

    _init() {
        Config.PAGE_WIDTH = Utils.getPageWidth();
        Config.PAGE_HEIGHT = Utils.getPageHeight();
        Config.ITEM_SPACE = Utils.getDimen("ITEM_SPACE");
        Config.BORDER_RADIUS = Utils.getDimen("PAGE_BORDER_RADIUS");
        Config.HEADER_HEIGHT = Utils.getDimen("HEADER_HEIGHT");
        Config.HEADER_ICON_SIZE = Utils.getDimen("HEADER_ICON_LARGE_SIZE");
        Config.PLAYER_BG_COLOR = "black";
        Config.PLAYER_CTRL_BAR_HEIGHT = Utils.getDimen("PLAYER_CTRL_BAR_HEIGHT");
        Config.PLAYER_TIME_TEXT_WIDTH = Utils.getDimen("PLAYER_TIME_TEXT_WIDTH");
        Config.PLAYER_PROGRESS_BAR_HEIGHT = Utils.getDimen("PLAYER_PROGRESS_BAR_HEIGHT");
        Config.PLAYER_CORNER_SIZE = Utils.getDimen("PLAYER_CORNER_SIZE");
        Config.SLIDER_TIP_LARGE_WIDTH = Utils.getDimen("PLAYER_SLIDER_TIP_LARGE_WIDTH");
        Config.SLIDER_TIP_WIDTH = Utils.getDimen("PLAYER_SLIDER_TIP_WIDTH");
        Config.SLIDER_TIP_HEIGHT = Utils.getDimen("PLAYER_SLIDER_TIP_HEIGHT");

        this._interruptReason = undefined;
        this._checkOfflineResult = {};
        this._controlViewShowing = false;
        this._currentState = State.IDLE;
        this._lastState = State.IDLE;
        this._elapsedTime = -1;
        this._duration = 0;
        this._videoStartTime = 0;
        this._playTotalTime = 0;
        this._playExitType = ExitType.CLICK;
        this._errorCode = DEFAULT_VALUE;
        this._destroyed = false;
        this._isReverseRangeState = false;
        this._isPlaybackComplete = false;
        this._isPlayAfterPaused = false;
        this._isNeedPromptTraffic = true;
        this._isNeedCheckNetworkType = false;
        this._isNeedToSeek = false;
        this._warningCountDown = 10;

        // 打开空调/车辆设置后再投屏，由于presenter无法接收到onPageDeactivate消息导致投屏继续播放
        if (this._from === Consts.FromType.DLNA) {
            this._activated = iPageInstance.isPageActivated();
        } else {
            this._activated = true;
        }
        log.D(TAG, "_init, activated", this._activated);
        this._initListener();
    }

    /**
     * 视频播放控制窗口
     * 1.系统默认会给应用增加PageHeader（例如“X”按钮）控件，且层级比应用窗口高
     * 2.新建控制窗口，主要显示标题和控制栏，且层级在PageHeader之上，以实现全屏效果
     */
    _initControlWindow() {
        log.I(TAG, "_initControlWindow, start");
        this._controlWindow = new Window({
            x: iPageInstance.getPagePositionX(),
            y: iPageInstance.getPagePositionY(),
            width: Config.PAGE_WIDTH,
            height: Config.PAGE_HEIGHT,
            type: 1000,
            attachWindowId: iPageInstance.getWindowSurfaceId()
        });
        this._controlWindow.focusable = false;

        let controlLayer = LayoutManager.loadSync("player_control");

        let layout = new RelativeLayout();
        layout.setLayoutParam("id_player_control", "align", {
            left: "parent",
            top: "parent",
            right: "parent",
            bottom: "parent"
        });
        this._controlWindow.layout = layout;
        this._controlWindow.addChild(controlLayer);
        this._controlWindow.show();
        log.I(TAG, "_initControlWindow, end");
    }

    windowPositionChanged() {
        if (this._controlWindow) {
            this._controlWindow.top = iPageInstance.getWindowTop();
        }
    }

    /**
     * 初始化监听器，监听车速、倒车、U盘、音频焦点等变化消息
     */
    _initListener() {
        log.I(TAG, "_initListener");
        this._handleAudioSessionChanged = (clientName: string, gainedSession: boolean) => {
            if (this._destroyed) {
                log.W(TAG, "_handleAudioSessionChanged, presenter is destroyed");
                return;
            }

            log.I(TAG, "_handleAudioSessionChanged", clientName, gainedSession);
            if (gainedSession) {
                this._obtainAudioSession();
            } else {
                this._lossAudioSession(clientName);
            }
        };
        iAudioSession.registerAudioSessionChangeListener(this._handleAudioSessionChanged);
    }

    /**
     * 视频应用音频焦点被其他应用抢占
     * 1.被电话抢占，需要disable播放控制栏并暂停播放
     * 2.被其他多媒体应用抢占，则需要暂停
     */
    _lossAudioSession(clientName = "") {
        let isPhone = !clientName || iAudioSession.isPhoneGainedAudioSession(clientName);
        log.I(TAG, "_lossAudioSession start,", isPhone, this._interruptReason, this._activated, this._lastState, this._currentState);
        if (isPhone) {
            if (this._sliderTouchStart) {
                this._onSliderTouchEnd();
            }

            if (this._activated && this._interruptReason !== InterruptReason.XIAOYUN) {
                this._lastState = this._currentState;
            }
            this._interruptReason = InterruptReason.PHONE;
            this._setCtrlBarButtonState(false);
        } else if (iAudioSession.isXiaoYunGainedAudioSession(clientName)) {
            if (this._sliderTouchStart) {
                this._onSliderTouchEnd();
            }

            if (this._activated) {
                this._lastState = this._currentState;
            }
            this._interruptReason = InterruptReason.XIAOYUN;
        } else if (clientName === Consts.SETTING_PACKAGE_NAME) {
            if (this._activated) {
                this._lastState = this._currentState;
            }
            this._interruptReason = InterruptReason.SETTING;
            this._setCtrlBarButtonState(true);
        } else if (clientName === Consts.VIDEO_PACKAGE_NAME) {
            this._lastState = this._currentState;
            this._interruptReason = InterruptReason.SELF;
            this._setCtrlBarButtonState(true);
        } else if (iAudioSession.isChatGainedAudioSession(clientName)) {
            if (this._activated) {
                this._lastState = this._currentState;
            }
            this._interruptReason = InterruptReason.CHAT;
            this._setCtrlBarButtonState(true);
        } else if (iAudioSession.isMediaGainedAudioSession(clientName)) {
            if (!this._hidden) {
                this._lastState = State.PAUSE;
            }
            this._interruptReason = InterruptReason.MULTIMEDIA;
            this._setCtrlBarButtonState(true);
            this._removeVoiceCommands();
        } else {
            if (!this._hidden) {
                this._lastState = State.PAUSE;
            }
            this._interruptReason = InterruptReason.OTHER;
            this._setCtrlBarButtonState(true);
        }

        this._pause();
        log.I(TAG, "_lossAudioSession end,", this._lastState, this._currentState);
    }

    /**
     * 视频应用获得音频焦点，并根据之前记录的状态来恢复
     */
    _obtainAudioSession() {
        log.I(TAG, "_obtainAudioSession", this._interruptReason, this._activated);
        this._interruptReason = undefined;
        this._hidePopupDialog(PopupDialogType.PHONE);
        this._setCtrlBarButtonState(true);
        this._hideControlViewDelay();

        log.I(TAG, "_obtainAudioSession", this._lastState, this._currentState);
        if (this._lastState === State.IDLE || this._lastState === State.LOADING ||
            this._lastState === State.PLAY_START || this._lastState === State.PLAYING) {
            this._play();
        }
    }

    _savePlayVideoInfo() {
        log.I(TAG, "_savePlayVideoInfo");
        this._reportPlayTotalTime();
        this._saveElapsedTime();
        this._saveHighlightUrl();
    }

    /**
     * 当播放最后一个在线视频时，需要请求下一页数据
     */
    _loadMoreOnlineVideo() {
        if (this._index !== this._playListLength - 1) {
            return;
        }

        log.I(TAG, "_loadMoreOnlineVideo", this._from);
        if (this._from === Consts.FromType.ONLINE) {
            let pageNo = this._pageNo + 1;
            if (this._categoryIndex) {
                iOnlineModel.queryVideoByCategory(this._categoryName, pageNo, false,
                    (error: Object, cacheChanged: boolean, itemList: VideoInfo[]) => {
                        this._loadMoreOnlineVideoCallback(error, itemList);
                    }
                );
            } else {
                iOnlineModel.queryVideoToday(pageNo, false,
                    (error: Object, cacheChanged: boolean, itemList: VideoInfo[]) => {
                        this._loadMoreOnlineVideoCallback(error, itemList);
                    }
                );
            }
            return;
        }

        if (this._from === Consts.FromType.SEARCH_ONLINE) {
            if (this._keyword) {
                let pageNo = this._pageNo + 1;
                iOnlineModel.searchVideo(this._keyword, pageNo, (error: Object, itemList: VideoInfo[]) => {
                    this._loadMoreOnlineVideoCallback(error, itemList);
                });
            }
            return;
        }
    }

    _loadMoreOnlineVideoCallback(error: object, itemList: VideoInfo[]) {
        if (error) {
            log.I(TAG, "_loadMoreOnlineVideoCallback", error);
            return;
        }

        if (!itemList || itemList.length === 0) {
            log.I(TAG, "_loadMoreOnlineVideoCallback, itemList is null or empty");
            return;
        }

        if (!this._playList) {
            log.I(TAG, "_loadMoreOnlineVideoCallback, _playList is null");
            return;
        }

        this._pageNo++;
        this._playList = this._playList.concat(itemList);
        this._playListLength = this._playList.length;
        log.I(TAG, "_loadMoreOnlineVideoCallback", this._playListLength);
        if (this._from === Consts.FromType.ONLINE) {
            iOnlineModel.notifyItemListChanged(this._playList, this._pageNo, itemList.length);
        }
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        if (this._controlWindow) {
            this._controlWindow.show();
        }
        this._restorePlayStatus();
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_PLAYER);
    }

    /**
     * 恢复播放界面隐藏前的状态
     */
    _restorePlayStatus() {
        if (iAudioSession.isPhoneGainedAudioSession() || iAudioSession.isXiaoYunGainedAudioSession()) {
            return;
        }

        log.I(TAG, "_restorePlayStatus", this._lastState);
        if (this._lastState === State.LOADING || this._lastState === State.PLAY_START || this._lastState === State.PLAYING) {
            let isOnlineVideo = Utils.isOnlineVideo(this._playVideoInfo.url);
            let isVideoExist = iCacheVideo.isVideoExist(this._playVideoInfo.id);
            if (isOnlineVideo && !iTrafficHelper.checkTrafficState(null, false) && !isVideoExist) {
                log.I(TAG, "_restorePlayStatus, traffic issue");
                this._pause();
                this._showLoadingPage(LoadingPageType.NETWORK);
                return;
            }

            // bugid:22071923, Unable to restore playback of last sendcond
            if (!isOnlineVideo && this._duration > 0) {
                this._checkCurrentPosition();
                let elapsedSecond = Math.floor(this._elapsedTime / 1000);
                let durationSecond = Math.floor(this._duration / 1000);
                log.I(TAG, "_restorePlayStatus", this._elapsedTime
                    , elapsedSecond, this._duration, durationSecond);
                if (elapsedSecond === durationSecond) {
                    this._playbackComplete();
                    return;
                }
            }

            log.I(TAG, "_restorePlayStatus", this._isNeedCheckNetworkType);
            if (this._isNeedCheckNetworkType) {
                this._isNeedCheckNetworkType = false;
                if (this._checkNetworkType()) {
                    log.I(TAG, "_restorePlayStatus, wait for user to confirm");
                    this._pause();
                } else {
                    this._play();
                }
            } else {
                this._play();
            }
        }
    }

    onHide() {
        this._hidden = true;
        if (this._controlWindow) {
            this._controlWindow.hide();
        }

        log.I(TAG, "onHide", this._activated, this._lastState, this._currentState);
        let currentState = this._activated ? this._currentState : this._lastState;
        if (currentState === State.PLAY_START) {
            currentState = State.PLAYING;
        } else if (currentState === State.PAUSE_START) {
            currentState = State.PAUSE;
        }

        // onPageDeactivate message not received
        if (this._currentState === State.PLAYING) {
            this._pause();
        }
        this._savePlayVideoInfo();
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_PLAYER, {state: currentState});
    }

    onPageActivate() {
        this._activated = true;
        if (this._errorDialogShowing || this._warningDialogShowing) {
            return;
        }

        let clientName = iAudioSession.getTopSessionClientName();
        log.I(TAG, "onPageActivate", clientName, this._lastState, this._currentState);
        if (clientName !== Consts.VIDEO_PACKAGE_NAME) {
            return;
        }

        if (this._currentState === State.IDLE || this._currentState === State.LOADING ||
            this._currentState === State.PLAY_START || this._currentState === State.PLAYING) {
            return;
        }

        if (this._lastState === State.IDLE || this._lastState === State.LOADING ||
            this._lastState === State.PLAY_START || this._lastState === State.PLAYING) {
            this._play();
        }
    }

    onPageDeactivate() {
        this._activated = false;
        let clientName = iAudioSession.getTopSessionClientName();
        log.I(TAG, "onPageDeactivate", clientName, this._lastState, this._currentState, this._errorDialogShowing);
        if (clientName === Consts.VIDEO_PACKAGE_NAME) {
            if (!this._errorDialogShowing) {
                this._lastState = this._currentState;
            }
        }

        if (this._currentState === State.PLAY_START || this._currentState === State.PLAYING) {
            this._pause();
        }
    }

    onPageLink(pageLink: PageLink) {
        log.I(TAG, "onPageLink called");
        let rawdata = pageLink.data;
        if (rawdata) {
            try {
                log.I(TAG, "onPageLink, rawdata:", rawdata);
                let data = JSON.parse(rawdata);
                if (!data) {
                    return;
                }
                if (data.startType === "voice") {
                    let isInCall = iAudioSession.isPhoneGainedAudioSession();
                    if (data.controlType === Consts.ControlType.PREV) {
                        if (this._from !== Consts.FromType.DLNA && !isInCall) {
                            this._lastState = State.PLAYING;
                            this._playPrev(iUserTrackHelper.TriggerType.VOICE);
                        }
                    } else if (data.controlType === Consts.ControlType.NEXT) {
                        if (this._from !== Consts.FromType.DLNA && !isInCall) {
                            this._lastState = State.PLAYING;
                            this._playNext(iUserTrackHelper.TriggerType.VOICE);
                        }
                    } else if (data.controlType === Consts.ControlType.PAUSE) {
                        iUserTrackHelper.clickButton(
                            iUserTrackHelper.PAGE_PLAYER,
                            iUserTrackHelper.PLAYER_PLAY_PAUSE,
                            {
                                triggerType: iUserTrackHelper.TriggerType.VOICE,
                                contenttype: "pause"
                            }
                        );
                        if (!isInCall) {
                            this._lastState = State.PAUSE;
                            this._pause();
                        }
                    } else if (data.controlType === Consts.ControlType.PLAY) {
                        iUserTrackHelper.clickButton(
                            iUserTrackHelper.PAGE_PLAYER,
                            iUserTrackHelper.PLAYER_PLAY_PAUSE,
                            {
                                triggerType: iUserTrackHelper.TriggerType.VOICE,
                                contenttype: "play"
                            }
                        );
                        if (!isInCall) {
                            this._lastState = State.PLAYING;
                            this._play();
                        }
                    } else if (data.controlType === Consts.ControlType.STOP) {
                        this._onBack(ExitType.CLICK);
                    }
                } else if (data.type === "video") {
                    if (data.from === Consts.FromType.DLNA) {
                        if (!Features.SUPPORT_DLNA) {
                            log.W(TAG, "onPageLink, dlna not support");
                            return;
                        }
                    }
                }
            } catch (e) {
                log.E(TAG, "onPageLink exception:", e);
            }
        }
    }

    /**
     * 响应Power mode变化消息，暂停视频
     */
    pauseVideo() {
        if (this._destroyed) {
            log.W(TAG, "pauseVideo, presenter is destroyed");
            return;
        }

        log.I(TAG, "pauseVideo");
        this._pause();
    }

    /**
     * 响应“上一个”，“下一个” key事件
     */
    playVideo(isPlayNext: boolean) {
        if (this._destroyed) {
            log.W(TAG, "playVideo, presenter is destroyed");
            return;
        }

        if (this._views.loading.visibility === Visible) {
            log.I(TAG, "playVideo, loading");
            return;
        }

        if (iAudioSession.isPhoneGainedAudioSession()) {
            log.I(TAG, "playVideo, in calling");
            return;
        }

        if (iAudioSession.isXiaoYunGainedAudioSession()) {
            log.I(TAG, "playVideo, xiaoyun is showing");
            return;
        }

        log.I(TAG, "playVideo", isPlayNext);
        if (isPlayNext) {
            this._playNext(iUserTrackHelper.TriggerType.HARDKEY);
        } else {
            this._playPrev(iUserTrackHelper.TriggerType.HARDKEY);
        }
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        this._initControlWindow();
        this._setupViews(parentView);
        this._setVideoView();
        this._setupTapHandler();
        setImmediate(() => {
            iPageInstance.setPageContainerBackground(true);
        });

        this._loadMoreOnlineVideo();
    }

    _setupViews(parentView: View) {
        this._views = {};
        this._views.videoViewContainer = <CompositeView> parentView.findViewById("id_videoview_container");
        this._views.navigationBar = <NavigationBar> this._controlWindow.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = Visible;
        this._views.navigationBar.titleItem.title.align = TextView.Align.Left;
        this._views.navigationBar.rightItem.visibility = None;

        let backIcon = new ImageView();
        backIcon.width = Config.HEADER_ICON_SIZE;
        backIcon.height = Config.HEADER_ICON_SIZE;
        backIcon.scaleType = ImageView.ScaleType.Fitxy;
        backIcon.propertySetName = Consts.PLAYER_BACK_MULTISTATE;
        this._views.navigationBar.leftItem = backIcon;
        this._views.backIcon = backIcon;

        this._views.navigationBar.preTitleItem.visibility = None;
        let rich = new RichTextView();
        rich.propertySetName = Consts.PLAYER_TITLE;
        rich.fontSize = this._views.navigationBar.titleItem.title.fontSize;
        rich.fontWeight = this._views.navigationBar.titleItem.title.fontWeight;
        this._views.navigationBar.titleItem = <ITitleItem> <Object> rich;
        this._views.title = rich;
        this._views.title.elideMode = TextView.ElideMode.ElideMiddle;
        this._views.controlRootView = <CompositeView> this._controlWindow.findViewById("id_player_control");
        this._views.header = <CompositeView> this._controlWindow.findViewById("id_header");
        this._views.elapsedTime = <TextView> this._controlWindow.findViewById("id_elapsed_time");
        this._views.totalTime = <TextView> this._controlWindow.findViewById("id_total_time");
        this._views.decorateContainer = <CompositeView> this._controlWindow.findViewById("id_decorate_container");
        this._views.decorateLeftFiller = this._views.decorateContainer.findViewById("id_decorate_left_filler");
        this._views.decorateTopFiller = this._views.decorateContainer.findViewById("id_decorate_top_filler");
        this._views.decorateRightFiller = this._views.decorateContainer.findViewById("id_decorate_right_filler");
        this._views.decorateBottomFiller = this._views.decorateContainer.findViewById("id_decorate_bottom_filler");
        this._views.loading = <LoadingBM> this._controlWindow.findViewById("id_loading");
        this._views.network = <CompositeView> this._controlWindow.findViewById("id_network");
        this._views.retryBtn = <ButtonBM> this._controlWindow.findViewById("id_error_btn");

        this._ctrlViews = {};
        let ctrlBar = this._controlWindow.findViewById("id_ctrlbar");
        this._ctrlViews.prev = ctrlBar.findViewById("id_prev");
        this._ctrlViews.playPause = ctrlBar.findViewById("id_play_pause");
        this._ctrlViews.next = ctrlBar.findViewById("id_next");
        this._ctrlViews.seekBar = <CompositeView> ctrlBar.findViewById("id_seek_bar");
        this._ctrlViews.progressSlider = <Slider> this._ctrlViews.seekBar.findViewById("id_progress_slider");
        if (!this._ctrlViews.progressSlider) {
            this._ctrlViews.progressSlider = this._tryAddProgressSlider(this._ctrlViews.seekBar);
        }
        this._views.ctrlBar = ctrlBar;
    }

    _tryAddProgressSlider(seekBar: CompositeView) {
        let progressSlider = <Slider> seekBar.findViewById("id_progress_slider");
        if (!progressSlider) {
            progressSlider = new Slider();
            progressSlider.id = "id_progress_slider";
            progressSlider.left = Config.PLAYER_TIME_TEXT_WIDTH + Config.ITEM_SPACE;
            progressSlider.width = Config.PAGE_WIDTH - 2 * (Config.PLAYER_TIME_TEXT_WIDTH + Config.ITEM_SPACE);
            progressSlider.height = Config.PLAYER_PROGRESS_BAR_HEIGHT;
            progressSlider.handleView.id = "id_handle_view";
            progressSlider.handleView.scaleType = ImageView.ScaleType.Fitxy;
            progressSlider.minValue = 0;
            progressSlider.decimalPlaces = 0;
            seekBar.addChild(progressSlider);
        }
        return progressSlider;
    }

    /**
     * 设置控制栏元素的状态
     */
    _setCtrlBarButtonState(enabled: boolean) {
        if (this._destroyed) {
            log.W(TAG, "_setCtrlBarButtonState, presenter is destroyed");
            return;
        }

        log.I(TAG, "_setCtrlBarButtonState", enabled);
        if (this._from === Consts.FromType.DLNA) {
            this._ctrlViews.prev.enabled = false;
            this._ctrlViews.next.enabled = false;
        } else {
            this._ctrlViews.prev.enabled = enabled;
            this._ctrlViews.next.enabled = enabled;
        }
        this._ctrlViews.playPause.enabled = enabled;
        this._ctrlViews.progressSlider.enabled = enabled;
    }

    /**
     * 返回操作，先销毁videoview再执行router的退出操作
     */
    _onBack(exitType = ExitType.CLICK) {
        this._playExitType = exitType;
        this._views.controlRootView.background = Config.PLAYER_BG_COLOR;
        if (this._views.videoView) {
            this._views.videoView.opacity = 0;
        }
        this._clearVideoView();

        setImmediate(() => {
            if (this._destroyed) {
                log.W(TAG, "_onBack, presenter is destroyed");
                return;
            }
            let ret = this.context.router.back();
            if (!ret) {
                log.W(TAG, "_onBack, replace to online");
                this.context.router.replace(Consts.RoutePath.ONLINE);
            }
        });
    }

    /**
     * 设置控件的press相关操作
     */
    _setupTapHandler() {
        this._views.header.on("touchstart", (event: IEvent) => {
            event.stopPropagation();
        });

        this._views.ctrlBar.on("touchstart", (event: IEvent) => {
            event.stopPropagation();
        });

        this._views.network.on("touchstart", (event: IEvent) => {
            event.stopPropagation();
        });

        this._tapAbleViews = [];
        Utils.setOnTapListener(this._views.backIcon, () => {
            log.I(TAG, "back button pressed!");
            if (this._warningDialogShowing) {
                log.I(TAG, "speed warning, ignore");
                return;
            }

            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER, iUserTrackHelper.PLAYER_BACK);
            this._onBack(ExitType.CLICK);
        });
        this._tapAbleViews.push(this._views.backIcon);

        Utils.setOnTapListener(this._views.retryBtn, () => {
            log.I(TAG, "retry btn pressed");
            if (iNetworkState.networkConnected && iTrafficHelper.checkTrafficState()) {
                this._setVideoView();
            }
        });
        this._tapAbleViews.push(this._views.retryBtn);

        this._onPlayPause = (triggerType, playEvent) => {
            log.I(TAG, "_onPlayPause", triggerType, playEvent);
            if (!this._views.videoView) {
                return;
            }

            if (this._errorDialogShowing) {
                log.I(TAG, "_onPlayPause, error dialog showing");
                return;
            }

            let isPlaying = this._views.videoView.isPlaying();
            let playState = "";
            if (triggerType === iUserTrackHelper.TriggerType.CLICK) {
                playState = isPlaying ? "pause" : "play";
            } else {
                playState = playEvent ? "play" : "pause";
            }
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER, iUserTrackHelper.PLAYER_PLAY_PAUSE,
                {
                    triggerType: triggerType,
                    contenttype: playState
                }
            );

            if (isPlaying === playEvent) {
                log.I(TAG, "_onPlayPause, play or pause status unchanged");
                if (this._lastState === State.PAUSE && !playEvent) {
                    let isVoice = triggerType === iUserTrackHelper.TriggerType.VOICE;
                    this._xiaoyunPlayTTS(isVoice, State.PAUSE, iPageInstance.getTTS("TTS_PAUSED"));
                }
                return;
            }

            if (isPlaying) {
                let result = this._pause();
                if (result) {
                    this._lastState = State.PAUSE;
                }
            } else {
                let result = this._play();
                if (result) {
                    this._lastState = State.PLAYING;
                }
            }
            log.I(TAG, "_onPlayPause", this._lastState);

            this._hideControlViewDelay();
        };

        Utils.setOnTapListener(this._ctrlViews.playPause, () => {
            log.I(TAG, "play or pause pressed!");
            if (this._warningDialogShowing) {
                log.I(TAG, "speed warning, ignore");
                return;
            }

            if (this._isPlaybackComplete) {
                this._replay();
            } else {
                this._onPlayPause(iUserTrackHelper.TriggerType.CLICK);
            }
        });
        this._tapAbleViews.push(this._ctrlViews.playPause);

        Utils.setOnTapListener(this._ctrlViews.prev, () => {
            log.I(TAG, "play prev pressed!");
            if (this._warningDialogShowing) {
                log.I(TAG, "speed warning, ignore");
                return;
            }

            this._playPrev(iUserTrackHelper.TriggerType.CLICK);
        });
        this._tapAbleViews.push(this._ctrlViews.prev);

        Utils.setOnTapListener(this._ctrlViews.next, () => {
            log.I(TAG, "play next pressed!");
            if (this._warningDialogShowing) {
                log.I(TAG, "speed warning, ignore");
                return;
            }

            this._playNext(iUserTrackHelper.TriggerType.CLICK);
        });
        this._tapAbleViews.push(this._ctrlViews.next);

        Utils.setOnTapListener(this._views.controlRootView, () => {
            if (this._warningDialogShowing) {
                log.I(TAG, "speed warning, ignore");
                return;
            }

            log.I(TAG, "onTap controlRootView", this._currentState);
            if (this._currentState !== State.IDLE) {
                if (this._controlViewShowing) {
                    this._hideControlView();
                } else {
                    this._showControlView();
                    this._hideControlViewDelay();
                }
            } else {
                this._showControlView();
                this._hideControlViewDelay();
            }
        });
        this._tapAbleViews.push(this._views.controlRootView);

        this._swipeRecognizer = new SwipeRecognizer();
        this._views.decorateContainer.addGestureRecognizer(this._swipeRecognizer);
        this._views.decorateContainer.addEventListener("swipeleft", this._swipeLeftListener = () => {
            let paramObj = {
                direction: "left"
            };
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER,
                iUserTrackHelper.PLAYER_SLIDE, paramObj);

            if (iAudioSession.isXiaoYunGainedAudioSession()) {
                log.I(TAG, "swipeleft, xiaoyun is showing");
                return;
            }

            if (iAudioSession.isPhoneGainedAudioSession()) {
                log.I(TAG, "swipeleft, in calling");
                return;
            }

            if (!this._isFirstDrawCompleted) {
                log.I(TAG, "swipeleft, surface first draw is not completed");
                return;
            }

            log.I(TAG, "swipeleft");
            this._playNext(iUserTrackHelper.TriggerType.SLIDE);
        });
        this._views.decorateContainer.addEventListener("swiperight", this._swipeRightListener = () => {
            let paramObj = {
                direction: "right"
            };
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER,
                iUserTrackHelper.PLAYER_SLIDE, paramObj);

            if (iAudioSession.isXiaoYunGainedAudioSession()) {
                log.I(TAG, "swiperight, xiaoyun is showing");
                return;
            }

            if (iAudioSession.isPhoneGainedAudioSession()) {
                log.I(TAG, "swiperight, in calling");
                return;
            }

            if (!this._isFirstDrawCompleted) {
                log.I(TAG, "swiperight, surface first draw is not completed");
                return;
            }

            log.I(TAG, "swiperight");
            this._playPrev(iUserTrackHelper.TriggerType.SLIDE);
        });

        this._onSliderValueChanged = (value) => {
            if (this._warningDialogShowing || this._dialog) {
                log.I(TAG, "_onSliderValueChanged, dialog showing, ignore");
                return;
            }
            if (iAudioSession.isPhoneGainedAudioSession()) {
                return;
            }
            if (!this._isFirstDrawCompleted) {
                log.I(TAG, "_onSliderValueChanged, surface first draw is not completed");
                return;
            }
            if (this._sliderTouchStart) {
                log.D(TAG, "_onSliderValueChanged", Math.round(value));
                let longTimeStyle = this._duration >= ONE_HOUR_SECOND;
                this._sliderValue = Math.round(value);
                this._ctrlViews.progressSlider.tip = Utils.secondToTime(this._sliderValue, longTimeStyle);
            }
        };

        this._onSliderTouchStart = () => {
            let paramObj = {
                operate: "drag"
            };
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER, iUserTrackHelper.PLAYER_DRAG, paramObj);

            if (this._warningDialogShowing || this._dialog) {
                log.I(TAG, "_onSliderTouchStart, dialog showing, ignore");
                return;
            }

            if (!this._isFirstDrawCompleted) {
                log.I(TAG, "_onSliderTouchStart, surface first draw is not completed");
                return;
            }

            this._sliderTouchStart = true;
            this._ctrlViews.progressSlider.tipVisible = Slider.TipVisible.Visible;
            let longTimeStyle = this._duration >= ONE_HOUR_SECOND;
            let progressSlider = <{ tipView: TextView }><object> this._ctrlViews.progressSlider;
            progressSlider.tipView.fontWeight = TextView.FontWeight.DemiBold;
            progressSlider.tipView.width = longTimeStyle ? Config.SLIDER_TIP_LARGE_WIDTH : Config.SLIDER_TIP_WIDTH;
            progressSlider.tipView.height = Config.SLIDER_TIP_HEIGHT;
            progressSlider.tipView.align = TextView.Align.Center;
            progressSlider.tipView.verticalAlign = TextView.VerticalAlign.Middle;
            progressSlider.tipView.text = Utils.secondToTime(this._elapsedTime, longTimeStyle);
            this._sliderValue = this._elapsedTime;
        };

        this._onSliderTouchEnd = () => {
            let paramObj = {
                operate: "drop"
            };
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER, iUserTrackHelper.PLAYER_DRAG, paramObj);

            this._ctrlViews.progressSlider.tipVisible = Slider.TipVisible.None;
            if (this._warningDialogShowing || this._dialog) {
                log.I(TAG, "_onSliderTouchEnd, dialog is showing");
                return;
            }

            if (!this._isFirstDrawCompleted) {
                log.I(TAG, "_onSliderTouchEnd, surface first draw is not completed");
                return;
            }

            log.I(TAG, "_onSliderTouchEnd", this._ctrlViews.progressSlider.value);
            if (!this._specialCheckInterrupt()) {
                if (this._views.videoView) {
                    this._views.videoView.seekTo(this._sliderValue);
                }
                this._elapsedTime = this._sliderValue;
            } else {
                this._elapsedTime = this._views.videoView.getCurrentPosition();
            }
            this._sliderTouchStart = false;
            this._updateProgress(this._elapsedTime, this._duration);
            this._hideControlViewDelay();
        };
        this._ctrlViews.progressSlider.on("valuechanged", this._onSliderValueChanged);
        this._ctrlViews.progressSlider.on("trackingtouchstart", this._onSliderTouchStart);
        this._ctrlViews.progressSlider.on("trackingtouchend", this._onSliderTouchEnd);
    }

    /**
     * 播放视频
     * 1.确保当前系统处于非静音状态
     * 2.清除上一次创建的videoview
     * 3.获取本地存储的“播放时间”记录 elapseTime
     *   3.1.仅U盘视频和投屏视频有此时间记录
     *   3.2.在视频started回调后执行seek操作
     * 4.初始化videoview缩放尺寸、周边装饰黑色背景、预览图
     * 5.初始化videoview相关回调，ready, prepared,started等
     * 6.设置src，触发视频加载
     */
    _setVideoView() {
        log.I(TAG, "_setVideoView");
        iAudioSession.setRingerModeNormal();
        this._views.controlRootView.background = Config.PLAYER_BG_COLOR;
        this._closePopupDialog();
        this._updateProgress(0, 0);
        this._clearVideoView();
        this._changeState(State.IDLE);
        this._setPlayPauseBtnState(false);
        this._setCtrlBarButtonState(false);
        this._removeTimeout(this._dnsLookupTimer);
        this._errorCode = DEFAULT_VALUE;
        this._isPlayWaiting = false;
        this._isPlaybackComplete = false;
        this._isNeedToSeek = false;
        this._isFirstDrawCompleted = false;

        if (!this._playVideoInfo) {
            log.I(TAG, "_setVideoView, _playVideoInfo is null");
            return;
        }
        this._views.title.text = this._playVideoInfo.title;
        this._playTotalTime = 0;
        this._elapsedTime = 0;
        this._duration = 0;
        if (Utils.isOnlineVideo(this._playVideoInfo.url)) {
            let isVideoExist = iCacheVideo.isVideoExist(this._playVideoInfo.id);
            if (!isVideoExist) {
                let pageInfo = {
                    page_name: "play",
                    video_id: this._playVideoInfo.id,
                    video_title: this._playVideoInfo.title
                };
                if (!iNetworkState.networkConnected || !iTrafficHelper.checkTrafficState(pageInfo)) {
                    log.I(TAG, "_setVideoView, traffic issue");
                    this._showLoadingPage(LoadingPageType.NETWORK);
                    return;
                }

                if (this._from === Consts.FromType.DLNA) {
                    this._elapsedTime = iVideoModel.loadElapsedTime(Consts.FromType.DLNA,
                        Utils.extractUrl(this._playVideoInfo.url));
                } else {
                    this._checkVideo(this._playVideoInfo.id);
                }
            }
        } else {
            this._elapsedTime = iVideoModel.loadElapsedTime(Consts.FromType.LOCAL, this._playVideoInfo.url);
        }
        log.I(TAG, "_setVideoView", this._elapsedTime);
        if (this._elapsedTime > 0) {
            this._isNeedToSeek = true;
        }

        this._initVideoView(true);
        this._setVideoViewListener(this._playVideoInfo.id);
        this._views.videoView.src = this._playVideoInfo.url;

        this._showLoadingPage(LoadingPageType.LOADING);
        this._changeState(State.LOADING);
        this._addVoiceCommands();
        log.I(TAG, "_setVideoView, done");
    }

    /**
     * 初始化videoview缩放尺寸、周边装饰黑色背景、预览图
     * 1.根据视频原始尺寸和车机尺寸等比拉伸
     * 2.根据视频的缩放后的尺寸，添加周边装饰黑色背景
     * 3.预览图原本是根据视频缩放尺寸，但由于mtk在decode要求，宽度为16倍数，高度为32倍数，因此需要根据缩放后的尺寸再取整
     */
    _initVideoView(showPreview: boolean) {
        let isVertical = true;
        let originalWidth = this._playVideoInfo.videoWidth;
        let originalHeight = this._playVideoInfo.videoHeight;
        let scaleWidth = 0;
        let scaleHeight = 0;

        // 由于播放页是圆角，防止视频尺寸过大超出范围，需要设置最大宽度
        let maxWidth = Config.PAGE_WIDTH - Config.PLAYER_CORNER_SIZE * 2;
        if (originalWidth && originalHeight) {
            isVertical = maxWidth / originalWidth * originalHeight > Config.PAGE_HEIGHT;
            if (isVertical) {
                scaleHeight = Config.PAGE_HEIGHT;
                scaleWidth = Math.floor(originalWidth * Config.PAGE_HEIGHT / originalHeight);
            } else {
                scaleWidth = maxWidth;
                scaleHeight = Math.floor(maxWidth / originalWidth * originalHeight);
            }
        } else {
            isVertical = maxWidth < Config.PAGE_HEIGHT;
            if (isVertical) {
                scaleWidth = maxWidth;
                scaleHeight = Config.PAGE_HEIGHT / 2;
            } else {
                scaleWidth = maxWidth / 2;
                scaleHeight = Config.PAGE_HEIGHT;
            }
        }

        if (!this._views.videoView) {
            this._views.videoView = new VideoView();
            this._views.videoView.id = "id_videoView";
            this._views.videoViewContainer.addChild(this._views.videoView);
        }
        this._views.videoView.width = scaleWidth;
        this._views.videoView.height = scaleHeight;
        log.I(TAG, "_initVideoView, original to scale", originalWidth, "*", originalHeight, "->", scaleWidth, "*", scaleHeight);

        if (isVertical) {
            this._views.videoView.left = Config.PLAYER_CORNER_SIZE + (maxWidth - scaleWidth) / 2;
            this._views.videoView.top = 0;
            this._views.decorateLeftFiller.width = this._views.videoView.left - Config.PLAYER_CORNER_SIZE;
            this._views.decorateRightFiller.width = this._views.decorateLeftFiller.width;
            this._views.decorateTopFiller.height = 0;
            this._views.decorateBottomFiller.height = 0;
        } else {
            this._views.videoView.left = Config.PLAYER_CORNER_SIZE;
            this._views.videoView.top = (Config.PAGE_HEIGHT - scaleHeight) / 2;
            this._views.decorateTopFiller.height = this._views.videoView.top;
            this._views.decorateBottomFiller.height = this._views.decorateTopFiller.height;
            this._views.decorateLeftFiller.width = 0;
            this._views.decorateRightFiller.width = 0;
        }

        if (showPreview && !this._previewImage) {
            let thumbnail = this._playVideoInfo.thumbnail;
            if (thumbnail && thumbnail.startsWith("http")) {
                let actualWidth = Math.floor(scaleWidth / VIDEO_WIDTH_UNIT) * VIDEO_WIDTH_UNIT;
                let actualHeight = Math.floor(scaleHeight / VIDEO_HEIGHT_UNIT) * VIDEO_HEIGHT_UNIT;
                log.I(TAG, "_initVideoView", actualWidth, actualHeight);
                this._previewImage = new OnlineImageView(iPageInstance);
                this._previewImage.width = actualWidth;
                this._previewImage.height = actualHeight;
                this._previewImage.left = (Config.PAGE_WIDTH - actualWidth) / 2;
                this._previewImage.top = (Config.PAGE_HEIGHT - actualHeight) / 2;
                try {
                    this._previewImage.sourceSize = [actualWidth, actualHeight];
                    this._previewImage.src = thumbnail;
                } catch (e) {
                    log.E(TAG, "set thumbnail", e);
                }
                this._views.decorateContainer.addChild(this._previewImage);
            }
        }
    }

    /**
     * 初始化videoview相关回调
     * 1.ready, videoview已ready
     *   1.1.设置cache、buffer、resize等标记
     *   1.2.检查dns，防止弱网的情况下视频prepare回调慢
     *   1.3.setVideoViewPreparedTimer，防止检查dns或prepare长时间未返回
     *   1.4.调用prepare()方法
     * 2.prepared, videoview已准备好，若无打断事件（电话，空调面板等），则调用play()方法
     * 3.started, videoview已开始播放
     * 4.paused, videoview已暂停
     * 5.infoext, 当type为306时，表示视频第一帧已回调
     * 6.playbackcomplete，视频播放完
     * 7.videosizechanged, 回调告知视频解码后的真实尺寸
     * 8.error, videoview播放视频时出错
     */
    _setVideoViewListener(id: string) {
        this._views.videoView.on("videosizechanged", (width: number, height: number) => {
            let videoInfo = this._playVideoInfo;
            if (videoInfo && (videoInfo.videoWidth !== width || videoInfo.videoHeight !== height)) {
                log.I(TAG, "videosizechanged, " + videoInfo.videoWidth + " " + videoInfo.videoHeight);
                log.I(TAG, "-> " + width + " " + height);
                if (this._from === Consts.FromType.DLNA) {
                    videoInfo.videoWidth = width;
                    videoInfo.videoHeight = height;
                    this._initVideoView(false);

                    let mediaMeta = new MediaMeta();
                    mediaMeta.setInt32("video_resize_width", this._views.videoView.width);
                    mediaMeta.setInt32("video_resize_height", this._views.videoView.height);
                    this._views.videoView.setParameter(mediaMeta);
                }
            } else {
                log.I(TAG, "videosizechanged, skip to update " + width + " " + height);
            }
        });

        this._views.videoView.on("ready", () => {
            if (this._errorCode !== DEFAULT_VALUE) {
                log.W(TAG, "ready, error");
                return;
            }

            if (this._isOnlineVideoOffline(id)) {
                log.W(TAG, "ready, video is offline");
                return;
            }

            try {
                log.I(TAG, "ready");
                (<IVideoView> this._views.videoView).setBufferScale(false);

                let mediaMeta = new MediaMeta();
                if (this._from === Consts.FromType.DLNA) {
                    mediaMeta.setInt32("video_resize", SURFACE_RESIZE_MODE);
                    this._views.videoView.setParameter(mediaMeta);
                } else if (Utils.isOnlineVideo(this._playVideoInfo.url)) {
                    mediaMeta.setString("file-download-path", iCacheVideo.getCachePath(this._playVideoInfo.id));
                    this._views.videoView.setParameter(mediaMeta);
                }

                if (Utils.isOnlineVideo(this._playVideoInfo.url)) {
                    let isVideoExist = iCacheVideo.isVideoExist(this._playVideoInfo.id);
                    if (isVideoExist) {
                        this._views.videoView.prepare();
                    } else {
                        this._checkDns(this._views.videoView.src, (err?: Object) => {
                            this._removeTimeout(this._dnsLookupTimer);
                            if (this._isFirstDrawCompleted) {
                                log.I(TAG, "_checkDns, first draw completed");
                                return;
                            }
                            if (err) {
                                this._showLoadingPage(LoadingPageType.NETWORK);
                                return;
                            }
                            try {
                                this._views.videoView.prepare();
                            } catch (e) {
                                log.E(TAG, "_checkDns, ready to prepare() ERROR:", e);
                            }
                        });
                    }
                } else {
                    this._views.videoView.prepare();
                }
            } catch (err) {
                log.E(TAG, "ready", err);
                this._handleError(err.code);
                return;
            }
        });

        this._views.videoView.on("prepared", () => {
            if (this._errorCode !== DEFAULT_VALUE) {
                log.W(TAG, "prepared, error");
                return;
            }

            if (this._isOnlineVideoOffline(id)) {
                log.W(TAG, "prepared, video is offline");
                return;
            }

            try {
                log.I(TAG, "prepared");
                this._videoStartTime = new Date().getTime();
                let duration = this._views.videoView.getDuration();
                if (duration > 0) {
                    this._duration = duration;
                }
                this._updateProgress(this._elapsedTime, this._duration);
            } catch (err) {
                log.E(TAG, "prepared", err.code);
                this._duration = 0;
                this._handleError(err.code);
                return;
            }

            this._play();
        });

        this._views.videoView.on("started", () => {
            if (this._errorCode !== DEFAULT_VALUE) {
                log.W(TAG, "started, error");
                this._handleError(this._errorCode);
                return;
            }

            this._playStartTime = new Date().getTime();
            log.I(TAG, "started", this._playStartTime);
            this._changeState(State.PLAYING);
            this._hideLoadingPage();
            this._seekToPosition();
            this._setUpdateProgressTimer();
            this._setRecordProgressTimer();
            this._setPlayPauseBtnState(true);
            this._setCtrlBarButtonState(true);

            if (this._hidden) {
                log.I(TAG, "started, hidden");
                this._pause();
                return;
            }

            if (this._checkNetworkType()) {
                log.I(TAG, "started, wait for user to confirm");
                this._pause();
                return;
            }

            // 需要等第一帧显示后再判断打断情况，防止画面显示全黑
            if (this._isFirstDrawCompleted) {
                this._postCheckInterrupt();
            }
        });

        this._views.videoView.on("paused", () => {
            log.I(TAG, "paused");
            this._saveElapsedTime();
            this._removeRecordProgressTimer();
            this._removeUpdateProgressTimer();
            this._setPlayPauseBtnState(false);
            this._computePlayTotalTime();
            this._checkCurrentPosition();
            this._changeState(State.PAUSE);

            if (this._isPlayAfterPaused) {
                this._play();
                this._isPlayAfterPaused = false;
            }
        });

        this._views.videoView.on("infoext", (type, param) => {
            if (this._errorCode !== DEFAULT_VALUE) {
                log.W(TAG, "infoext, error");
                return;
            }

            log.I(TAG, "infoext", type);
            if (type !== TYPE_FIRST_DRAW_READY) {
                return;
            }

            this._isFirstDrawCompleted = true;
            this._views.controlRootView.background = "transparent";
            this._hideLoadingPage();
            this._setCtrlBarButtonState(true);
            this._postCheckInterrupt();
            setImmediate(() => {
                this._clearPreview();
            });
        });

        this._views.videoView.on("playbackcomplete", () => {
            if (this._errorCode !== DEFAULT_VALUE) {
                log.W(TAG, "playbackcomplete, error");
                return;
            }
            this._playbackComplete();
        });

        this._views.videoView.on("error", (errorCode: string) => {
            log.I(TAG, "error", errorCode);
            if (this._errorCode === errorCode) {
                return;
            }
            iAudioSession.requestAudioSession();
            this._handleError(errorCode);
        });
    }

    /**
     * 执行视频的seek操作
     */
    _seekToPosition() {
        if (this._elapsedTime === 0 || this._elapsedTime > this._duration) {
            log.I(TAG, "_seekToPosition, ignore", this._elapsedTime, this._duration);
            return;
        }

        if (this._isNeedToSeek) {
            this._isNeedToSeek = false;
            let currentPosition = this._views.videoView.getCurrentPosition();
            log.I(TAG, "_seekToPosition", this._elapsedTime, currentPosition);
            this._views.videoView.seekTo(this._elapsedTime);
        }
    }

    /**
     * 检查dns
     */
    _checkDns(mp4Url: string, callback: (arg0?: Object) => void) {
        let lookupTimeout = false;
        this._removeTimeout(this._dnsLookupTimer);
        this._dnsLookupTimer = setTimeout(() => {
            log.I(TAG, "dns lookup timeout");
            lookupTimeout = true;
            this._dnsLookupTimer = null;
            this._showLoadingPage(LoadingPageType.NETWORK);
        }, DNS_LOOK_UP_TIMEOUT);

        const Uri = require("yunos/net/Uri");
        let uri = new Uri(mp4Url);
        if (uri.scheme === "http" || uri.scheme === "https") {
            log.I(TAG, "_checkDns", uri.host);
            const dns = require("dns");
            dns.lookup(uri.host, (err: Object, address: Object, family: Object) => {
                log.I(TAG, "_checkDns.cb", err, address, family, lookupTimeout);
                if (!lookupTimeout) {
                    callback(err);
                }
            });
        } else {
            callback();
        }
    }

    /**
     * 视频播放完
     */
    _playbackComplete() {
        log.I(TAG, "_playbackComplete");
        this._isPlaybackComplete = true;
        this._elapsedTime = this._duration;
        this._updateProgress(this._duration, this._duration);
        this._removeUpdateProgressTimer();
        this._computePlayTotalTime();

        let url = this._playVideoInfo.url;
        if (Utils.isOnlineVideo(url)) {
            if (this._from === Consts.FromType.DLNA) {
                log.I(TAG, "_playbackComplete, dlna");
                return;
            } else {
                this._reportPlayTotalTime(ExitType.AUTO);
                this._setVideoView();
                this._recordVideo(this._playVideoInfo);
            }
        } else {
            this._playNext(iUserTrackHelper.TriggerType.SYSTEM);
            this._hideLoadingPage();
            this._setPlayPauseBtnState(false);
        }
    }

    /**
     * 记录视频缓存信息
     */
    _recordVideo(playVideoInfo: VideoInfo) {
        if (!playVideoInfo) {
            return;
        }

        if (!Utils.isOnlineVideo(playVideoInfo.url)) {
            return;
        }

        iCacheVideo.recordVideo(playVideoInfo.id, playVideoInfo.videoSize);
    }

    /**
     * 检查在线视频是否已下线
     */
    _checkVideo(id: string | Object) {
        log.I(TAG, "_checkVideo", id);
        if (!id) {
            log.I(TAG, "_checkVideo, id is null");
            return;
        }
        iOnlineModel.checkVideos(id, (error: Object, invalidList: Object[]) => {
            log.I(TAG, "checkVideo", id, error);
            this._checkOfflineResult.id = id;
            if (error === "NETWORK_ERROR") {
                this._checkOfflineResult.networkError = true;
                log.I(TAG, "checkVideo", this._isFirstDrawCompleted);
                if (!this._isFirstDrawCompleted) {
                    this._removeTimeout(this._dnsLookupTimer);
                    this._showLoadingPage(LoadingPageType.NETWORK);
                }
            } else {
                if (invalidList && invalidList.length > 0 && invalidList[0] === id) {
                    this._checkOfflineResult.invalid = true;
                    this._showPopupDialog(PopupDialogType.OFFLINE);
                } else {
                    this._checkOfflineResult.invalid = false;
                }
            }
        });
    }

    _isOnlineVideoOffline(id: Object) {
        if (this._checkOfflineResult.id === id && this._checkOfflineResult.invalid) {
            this._showPopupDialog(PopupDialogType.OFFLINE);
            return true;
        }
        return false;
    }

    /**
     * 暂停视频
     */
    _pause() {
        log.I(TAG, "_pause");
        if (this._isOnlineVideoOffline(this._playVideoInfo.id)) {
            return false;
        }

        if (!this._views.videoView) {
            log.W(TAG, "_pause, videoView is null");
            return false;
        }

        try {
            if (this._views.videoView.isPlaying()) {
                iUserTrackHelper.sendEvent(
                    iUserTrackHelper.PAGE_PLAYER,
                    iUserTrackHelper.PLAYING_STATUS,
                    {
                        contenttype: "pause",
                        video_stoptime: this._elapsedTime,
                        video_id: this._playVideoInfo.id,
                        cpId: iUserTrackHelper.CP_ID
                    }
                );
                this._changeState(State.PAUSE_START);
                this._views.videoView.pause();
            }
        } catch (err) {
            this._handleError(err.code);
        }
        return true;
    }

    /**
     * 播放视频
     */
    _play() {
        if (this._hidden) {
            log.I(TAG, "_play, hidden");
            return false;
        }

        if (this._preCheckInterrupt()) {
            log.I(TAG, "_play, interrupted");
            this._pause();
            return false;
        }

        if (this._warningDialogShowing) {
            log.I(TAG, "_play, speed warning, ignore");
            return false;
        }

        if (this._isOnlineVideoOffline(this._playVideoInfo.id)) {
            return false;
        }

        if (this._isPlaybackComplete) {
            this._replay();
            return false;
        }

        if (!this._views.videoView) {
            log.W(TAG, "_play, videoView is null");
            return false;
        }

        if (this._views.videoView.isPlaying()) {
            log.D(TAG, "_play, video is playing", this._currentState);
            // 如果当前状态是在暂停中，则等暂停完成后再播放
            if (this._currentState === State.PAUSE_START) {
                this._isPlayAfterPaused = true;
            }
            return false;
        }

        iUserTrackHelper.sendEvent(
            iUserTrackHelper.PAGE_PLAYER,
            iUserTrackHelper.PLAYING_STATUS,
            {
                contenttype: "play",
                video_stoptime: this._elapsedTime,
                video_id: this._playVideoInfo.id,
                cpId: iUserTrackHelper.CP_ID
            }
        );

        if (this._isPlayWaiting) {
            log.I(TAG, "_play waiting");
            return true;
        }

        // 播放前是“静音”模式，防止其他模块（音乐）等收到“取消静音”后抢占音频焦点，“取消静音”后需要延迟播放视频
        let isMute = !iAudioSession.isRingerModeNormal();
        log.I(TAG, "_play, isMute", isMute);
        if (isMute) {
            iAudioSession.setRingerModeNormal();
            this._isPlayWaiting = true;
            this._removeTimeout(this._playWaitTimer);
            this._playWaitTimer = setTimeout(() => {
                log.I(TAG, "_play wait timeout");
                this._playStart();
                this._isPlayWaiting = false;
                this._playWaitTimer = null;
            }, PLAY_WAIT_TIMEOUT);
        } else {
            this._playStart();
        }
        return true;
    }

    /**
     * 检查音频焦点和第一帧画面情况
     * 1.若申请到音频焦点则进行播放
     * 2.若申请不到音频焦点，若第一帧未显示则继续播放
     */
    _playStart() {
        try {
            log.I(TAG, "_playStart", this._isFirstDrawCompleted);
            if (!this._specialCheckInterrupt() || !this._isFirstDrawCompleted) {
                this._changeState(State.PLAY_START);
                this._views.videoView.start();
                this._addVoiceCommands();
            } else {
                this._hideLoadingPage();
            }
        } catch (err) {
            this._handleError(err.code);
        }
    }

    /**
     * 切换视频时，需要延迟播放，防止PageHeader闪现
     */
    _playDelay() {
        this._views.controlRootView.background = Config.PLAYER_BG_COLOR;
        this._removeTimeout(this._playDelayTimer);
        this._playDelayTimer = setTimeout(() => {
            log.I(TAG, "_playDelay timeout");
            this._setVideoView();
            this._playDelayTimer = null;
        }, PLAY_DELAY_TIMEOUT);
    }

    /**
     * 播放上一个视频
     */
    _playPrev(triggerType: string) {
        log.I(TAG, "_playPrev", triggerType);
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER,
            iUserTrackHelper.PLAYER_PREV, {triggerType: triggerType});

        if (this._preCheckInterrupt()) {
            this._pause();
            return;
        }

        if (this._sliderTouchStart) {
            log.I(TAG, "_playPrev, slider touch");
            return;
        }

        // play pre and next:
        // do nothing here, if current interrupted by xiaoyun.
        // interrupted state will handled by function "handleAudioSessionChanged".
        // function "handleAudioSessionChanged" may be later by voice command.
        let isVoice = triggerType === iUserTrackHelper.TriggerType.VOICE;
        if (!isVoice) {
            this._interruptReason = undefined;
            this._specialCheckInterrupt();
        }

        let prevIndex = this._index - 1;
        if (prevIndex >= 0) {
            let lastVideoInfo = this._playVideoInfo;
            this._reportPlayTotalTime(ExitType.CLICK);
            this._saveElapsedTime();
            this._index = prevIndex;
            this._setPlayVideoInfo();
            this._playDelay();
            this._recordVideo(lastVideoInfo);
        } else {
            if (isVoice) {
                if (this._isPlaybackComplete) {
                    this._xiaoyunPlayTTS(isVoice, State.PAUSE, iPageInstance.getTTS("TTS_FIRST"));
                } else {
                    this._xiaoyunPlayTTS(isVoice, State.PLAYING, iPageInstance.getTTS("TTS_FIRST"));
                }
            } else {
                Utils.showToast(iRes.getString("TOAST_FIRST"));
            }
        }
        this._hideControlViewDelay();
    }

    /**
     * 播放下一个视频
     */
    _playNext(triggerType: string) {
        log.I(TAG, "_playNext", triggerType);
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAYER,
            iUserTrackHelper.PLAYER_NEXT, {triggerType: triggerType});

        if (this._preCheckInterrupt()) {
            this._pause();
            return;
        }

        if (this._sliderTouchStart) {
            log.I(TAG, "_playNext, slider touch");
            return;
        }

        // if current interrupted by xiaoyun, do nothing here.
        // interrupted state will handled by function "handleAudioSessionChanged".
        // function "handleAudioSessionChanged" may be later by voice command.
        let isVoice = triggerType === iUserTrackHelper.TriggerType.VOICE;
        if (!isVoice) {
            this._interruptReason = undefined;
            this._specialCheckInterrupt();
        }

        let isAutoPlay = triggerType === iUserTrackHelper.TriggerType.SYSTEM;
        let nextIndex = this._index + 1;
        if (nextIndex < this._playListLength) {
            let lastVideoInfo = this._playVideoInfo;
            this._reportPlayTotalTime(isAutoPlay ? ExitType.AUTO : ExitType.CLICK);
            this._saveElapsedTime();
            this._index = nextIndex;
            this._setPlayVideoInfo();
            this._playDelay();
            this._recordVideo(lastVideoInfo);
            this._loadMoreOnlineVideo();
        } else {
            if (isAutoPlay) {
                this._onBack(ExitType.AUTO);
            } else {
                if (isVoice) {
                    if (this._isPlaybackComplete) {
                        this._xiaoyunPlayTTS(isVoice, State.PAUSE, iPageInstance.getTTS("TTS_LAST"));
                    } else {
                        this._xiaoyunPlayTTS(isVoice, State.PLAYING, iPageInstance.getTTS("TTS_LAST"));
                    }
                } else {
                    Utils.showToast(iRes.getString("TOAST_LAST"));
                }
            }
        }
        this._hideControlViewDelay();
    }

    /**
     * 小云播报tts
     */
    _xiaoyunPlayTTS(playtts: boolean | Object, state: string, spokenText: string) {
        log.I(TAG, "_xiaoyunPlayTTS", playtts, state);
        if (!playtts) {
            log.D(TAG, "_xiaoyunPlayTTS, ignore");
            return;
        }

        Utils.sendXiaoYunSpoken(iPageInstance, spokenText, () => {
            this._lastState = state;
        });
    }

    /**
     * 重播
     */
    _replay() {
        log.I(TAG, "_replay");
        this._updateProgress(0, this._duration);
        this._setVideoView();
        this._hideControlViewDelay();
    }

    /**
     * 检查网络类型，在投屏时，若使用的是4G网络，需要提示并暂停播放
     */
    _checkNetworkType() {
        if (this._from !== Consts.FromType.DLNA) {
            return false;
        }

        if (!this._isNeedPromptTraffic) {
            return false;
        }

        if (iNetworkState.networkConnected && !iNetworkState.isWiFi()) {
            this._isNeedPromptTraffic = false;
            this._hideLoadingPage();
            if (iVideoModel.getDlnaPlayFlag()) {
                Utils.showToast(iRes.getString("TOAST_DLNA_PLAY"));
                return false;
            } else {
                this._showPopupDialog(PopupDialogType.DLNA);
                iVideoModel.saveDlnaPlayFlag(true);
                return true;
            }
        }
        return false;
    }

    /**
     * 前置检查项，播放前检查打断情况
     * 返回 true 表示被打断需要暂停播放
     */
    _preCheckInterrupt() {
        if (iAudioSession.isPhoneGainedAudioSession()) {
            // bugid:21323815/21323847，curret interrupted reason is multimedia or xiaoyun, set to undefined onShow()
            this._showPopupDialog(PopupDialogType.PHONE);
            return true;
        } else {
            this._hidePopupDialog(PopupDialogType.PHONE);
        }
        return false;
    }

    /**
     * 后置检查项，播放后检查打断情况
     */
    _postCheckInterrupt() {
        if (iAudioSession.isPhoneGainedAudioSession()) {
            this._pause();
            return;
        } else {
            this._hidePopupDialog(PopupDialogType.PHONE);
        }

        if (iAudioSession.isXiaoYunGainedAudioSession()) {
            log.I(TAG, "_postCheckInterrupt, audio session is xiaoyun");
            this._lossAudioSession(Consts.XIAOYUN_PACKAGE_NAME);
        }

        if (!iAudioSession.isRingerModeNormal()) {
            log.I(TAG, "_postCheckInterrupt, system mute");
            this._pause();
            return;
        }

        if (this._hidden) {
            log.I(TAG, "_postCheckInterrupt, hidden");
            this._pause();
            return;
        }

        if (!this._activated) {
            log.I(TAG, "_postCheckInterrupt, deactivated");
            this._pause();
            return;
        }
    }

    /**
     * 特殊检查项，点击“播放”、“上一个”、“下一个” 或 拖拽进度条时，检查焦点情况
     * 返回 true 表示被打断需要暂停播放或不执行相关操作
     */
    _specialCheckInterrupt() {
        let gainedSession = iAudioSession.requestAudioSession();
        if (!gainedSession) {
            log.I(TAG, "_specialCheckInterrupt, unable to get audio focus");
            return true;
        }

        if (iAudioSession.isXiaoYunGainedAudioSession()) {
            log.I(TAG, "_specialCheckInterrupt, audio session is xiaoyun");
            return true;
        }
        return false;
    }

    /**
     * 设置播放按钮的状态
     */
    _setPlayPauseBtnState(isPlaying: boolean) {
        if (isPlaying) {
            this._ctrlViews.playPause.propertySetName = Consts.PLAYER_PAUSE_MULTISTATE;
        } else {
            this._ctrlViews.playPause.propertySetName = Consts.PLAYER_PLAY_MULTISTATE;
        }
    }

    /**
     * 处理播放出错的逻辑
     */
    _handleError(errorCode: string) {
        log.I(TAG, "_handleError", errorCode);
        this._errorCode = errorCode;
        this._pause();
        this._setPlayPauseBtnState(false);
        this._removeUpdateProgressTimer();

        this._reportPlayTotalTime(ExitType.AUTO);
        this._saveElapsedTime();
        this._changeState(State.FAIL);
        this._hideLoadingPage();

        if (Utils.isOnlineVideo(this._playVideoInfo.url)) {
            log.I(TAG, "_handleError", iNetworkState.networkConnected, this._checkOfflineResult.networkError);
            if (!iNetworkState.networkConnected || this._checkOfflineResult.networkError
                || !iTrafficHelper.checkTrafficState(null, false)) {
                log.I(TAG, "_handleError, network error");
                this._showLoadingPage(LoadingPageType.NETWORK);
            } else {
                if (!this._isOnlineVideoOffline(this._playVideoInfo.id)) {
                    this._showPopupDialog(PopupDialogType.UNKNOWN);
                }
            }
        } else {
            const fs = require("fs");
            if (!fs.existsSync(this._playVideoInfo.url)) {
                this._showPopupDialog(PopupDialogType.NOT_EXIST);
                iLocalModel.deleteVideoById(this._playVideoInfo.id);
            } else if (this._isDecodeError(errorCode)) {
                this._showPopupDialog(PopupDialogType.DECODE);
            } else {
                this._showPopupDialog(PopupDialogType.UNKNOWN);
            }
        }
    }

    /**
     * 检查播放出错的类型
     */
    _isDecodeError(errorCode: string | number) {
        log.I(TAG, "_isDecodeError", errorCode);
        let ErrorCode = require("yunos/multimedia/MMError").ErrorCode;
        // MM_ERROR_AUDIO_DECODE = 43, /*audio decode error*/
        if (errorCode === ErrorCode.NO_AUDIODECODER
            || errorCode === ErrorCode.NO_VIDEODECODER
            || errorCode === 43) {
            return true;
        }
        return false;
    }

    /**
     * 设置视频更新进度条的timer，每秒更新一次
     */
    _setUpdateProgressTimer() {
        if (!this._controlViewShowing) {
            log.I(TAG, "_setUpdateProgressTimer, control bar is hidden");
            return;
        }
        if (this._isPlaybackComplete) {
            log.I(TAG, "_setUpdateProgressTimer, play completed");
            return;
        }

        this._checkCurrentPosition();
        this._removeUpdateProgressTimer();
        this._updateProgressTimer = setInterval(() => {
            try {
                if (this._sliderTouchStart) {
                    log.I(TAG, "_setUpdateProgressTimer, slider touch");
                    return;
                }

                if (this._isPlaybackComplete) {
                    log.I(TAG, "_setUpdateProgressTimer, play completed");
                    this._pause();
                    return;
                }

                if (this._views.videoView && this._views.videoView.isPlaying()) {
                    this._checkCurrentPosition();
                }
            } catch (err) {
                this._handleError(err.code);
            }
        }, UPDATE_PROGRASS_INTERVAL);
    }

    /**
     * 设置记录视频播放进度的timer，每5秒记录一次
     */
    _setRecordProgressTimer() {
        this._removeRecordProgressTimer();
        this._recordProgressTimer = setInterval(() => {
            try {
                if (this._views.videoView) {
                    this._saveElapsedTime();
                }
            } catch (e) {
                log.E(TAG, "_setRecordProgressTimer", e);
            }
        }, RECORD_PROGRASS_INTERVAL);
    }

    /**
     * 检查视频当前位置并更新进度条和elapsedTime
     */
    _checkCurrentPosition() {
        if (!this._views.videoView) {
            log.W(TAG, "_checkCurrentPosition, videoView is null");
            return;
        }

        if (!this._ctrlViews.progressSlider.enabled) {
            log.I(TAG, "_checkCurrentPosition, slider disabled");
            return;
        }

        if (this._duration !== 0 && this._elapsedTime === this._duration) {
            log.I(TAG, "_checkCurrentPosition, playback complete");
            return;
        }

        let currentPosition = this._views.videoView.getCurrentPosition();
        if (this._elapsedTime !== currentPosition && currentPosition <= this._duration) {
            this._elapsedTime = currentPosition;
            this._updateProgress(this._elapsedTime, this._duration);
        }
    }

    /**
     * 更新进度信息，时间和进度
     */
    _updateProgress(elapsedTime: number, totalTime: number) {
        if (this._destroyed) {
            log.W(TAG, "_updateProgress, presenter is destroyed");
            return;
        }

        if (elapsedTime === undefined || totalTime === undefined) {
            log.W(TAG, "_updateProgress, elapsedTime or totalTime is undefined");
            return;
        }

        log.I(TAG, "_updateProgress", elapsedTime, totalTime);
        this._updateProgressText(elapsedTime, totalTime);
        this._updateProgressSlider(elapsedTime, totalTime);
    }

    /**
     * 更新进度条时间
     */
    _updateProgressText(elapsedTime: number, totalTime: number) {
        let longTimeStyle = totalTime >= ONE_HOUR_SECOND;
        this._views.elapsedTime.text = Utils.secondToTime(elapsedTime, longTimeStyle);
        this._views.totalTime.text = Utils.secondToTime(totalTime, longTimeStyle);
    }

    /**
     * 更新进度信息
     */
    _updateProgressSlider(elapsedTime: number, totalTime: number) {
        if (this._destroyed) {
            log.W(TAG, "_updateProgressSlider, presenter is destroyed");
            return;
        }

        if (this._sliderTouchStart) {
            log.W(TAG, "_updateProgressSlider, slider touch start");
            return;
        }

        if (this._ctrlViews.progressSlider) {
            if (this._ctrlViews.progressSlider.maxValue !== totalTime) {
                this._ctrlViews.progressSlider.maxValue = totalTime;
                if (this._isFirstDrawCompleted) {
                    let enable = totalTime > 0;
                    if (enable !== this._ctrlViews.progressSlider.enabled) {
                        this._ctrlViews.progressSlider.enabled = enable;
                        log.I(TAG, "_updateProgressSlider", enable);
                    }
                }
            }

            if (this._ctrlViews.progressSlider.value !== elapsedTime) {
                this._ctrlViews.progressSlider.value = elapsedTime;
            }
        }
    }

    /**
     * 车速超过服务端配速时，弹出提示信息，并在10秒倒计时后退出播放界面
     */
    _showWarningDialog() {
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_LIMIT);
        this._warningDialogShowing = true;
        this._hideLoadingPage();
        this._closePopupDialog();
        if (!this._views.warningDialog) {
            let warningDialog = LayoutManager.loadSync("player_warning_dialog");
            warningDialog.width = Config.PAGE_WIDTH;
            warningDialog.height = Config.PAGE_HEIGHT;
            this._views.controlRootView.addChild(warningDialog);
            this._views.warningDialog = warningDialog;
            this._views.warningDialogTips = <TextView> warningDialog.findViewById("id_tips");
            this._views.warningDialogTips.text = iRes.getString("PLAY_WARNING_MSG", {
                countDown: this._warningCountDown
            });

            Utils.setOnTapListener(this._views.warningDialog, () => {
                log.I(TAG, "prompt dialog pressed!");
                this._hideWarningDialog(ExitType.CLICK);
            });
            this._tapAbleViews.push(this._views.warningDialog);
        }
        this._views.warningDialog.visibility = Visible;

        this._removeHidePageTimer();
        this._hidePageTimer = setInterval(() => {
            if (this._destroyed) {
                return;
            }
            this._warningCountDown--;
            this._views.warningDialogTips.text = iRes.getString("PLAY_WARNING_MSG", {
                countDown: this._warningCountDown
            });
            if (this._warningCountDown === 0) {
                this._hideWarningDialog(ExitType.AUTO);
                this._hidePageTimer = null;
            }
        }, COUNT_DOWN_INTERVAL);
    }

    /**
     * 关闭超速提示信息并退出播放页，返回地图界面
     */
    _hideWarningDialog(playExitType: string) {
        if (!this._warningDialogShowing) {
            log.I(TAG, "_hideWarningDialog, ignore");
            return;
        }
        this._reportLimitWarning();
        this._onBack(playExitType);
        Utils.sendLink("page://com.yunos.map/map", "video", "");
    }

    /**
     * 显示标题和控制栏
     */
    _showControlView() {
        if (this._destroyed) {
            log.W(TAG, "_showControlView, presenter is destroyed");
            return;
        }
        if (this._controlViewShowing) {
            log.I(TAG, "_showControlView, control view is showing");
            return;
        }
        if (this._hideControlViewAnim && this._hideControlViewAnim.animating) {
            log.W(TAG, "_showControlView, enter fullscreen animating");
            return;
        }

        log.I(TAG, "_showControlView");
        this._controlViewShowing = true;
        this._setUpdateProgressTimer();

        if (this._showControlViewAnim) {
            this._showControlViewAnim.restart();
            return;
        }
        this._views.header.visibility = Visible;
        let headerAnim = new PropertyAnimation(this._views.header);
        headerAnim.from = {opacity: 0, translationY: -Config.HEADER_HEIGHT};
        headerAnim.to = {opacity: 1, translationY: 0};
        headerAnim.duration = CONTROL_VIEW_ANIM_DURATION;
        headerAnim.timingFunction = "easeInOut";

        this._views.ctrlBar.visibility = Visible;
        let ctrlBarAnim = new PropertyAnimation(this._views.ctrlBar);
        ctrlBarAnim.from = {opacity: 0, translationY: Config.PLAYER_CTRL_BAR_HEIGHT};
        ctrlBarAnim.to = {opacity: 1, translationY: 0};
        ctrlBarAnim.duration = CONTROL_VIEW_ANIM_DURATION;
        ctrlBarAnim.timingFunction = "easeInOut";

        this._showControlViewAnim = new AnimationGroup();
        this._showControlViewAnim.add(headerAnim);
        this._showControlViewAnim.add(ctrlBarAnim);
        this._showControlViewAnim.start();
    }

    /**
     * 无网络的情况下，仅显示标题，控制栏会遮挡无网络视图
     */
    _showHeaderView() {
        this._views.header.visibility = Visible;
        this._views.header.opacity = 1;
        this._views.header.translationY = 0;
        this._views.ctrlBar.visibility = Visible;
        this._views.ctrlBar.opacity = 0;
        this._views.ctrlBar.translationY = Config.PLAYER_CTRL_BAR_HEIGHT;
        this._controlViewShowing = true;
    }

    /**
     * 延迟5秒隐藏标题和控制栏
     */
    _hideControlViewDelay() {
        if (this._destroyed) {
            log.W(TAG, "_hideControlViewDelay, presenter is destroyed");
            return;
        }
        if (!this._controlViewShowing) {
            log.D(TAG, "_hideControlViewDelay, controlView is hidden");
            return;
        }
        if (this._hideControlViewTimer) {
            clearTimeout(this._hideControlViewTimer);
            this._hideControlViewTimer = null;
        }
        this._hideControlViewTimer = setTimeout(() => {
            if (this._views.videoView && this._views.videoView.isPlaying()) {
                this._hideControlView();
            }
            this._hideControlViewTimer = null;
        }, CONTROL_VIEW_DELAY_TIMEOUT);
    }

    /**
     * 隐藏标题和控制栏
     */
    _hideControlView() {
        if (this._destroyed) {
            log.W(TAG, "_hideControlView, presenter is destroyed");
            return;
        }
        if (!this._controlViewShowing) {
            log.W(TAG, "_hideControlView, control view is hidden");
            return;
        }
        if (this._views.network.visibility === Visible) {
            log.W(TAG, "_hideControlView, network dialog showing");
            return;
        }
        if (this._sliderTouchStart) {
            log.W(TAG, "_hideControlView, progress slider touch start");
            return;
        }
        if (this._isDialogShowing() || this._isPlaybackComplete || this._warningDialogShowing) {
            log.W(TAG, "_hideControlView, dialog showing");
            return;
        }

        if (this._errorCode !== DEFAULT_VALUE) {
            log.I(TAG, "_hideControlView, is error");
            return;
        }
        if (this._showControlViewAnim && this._showControlViewAnim.animating) {
            log.W(TAG, "_hideControlView, exit fullscreen animating");
            return;
        }

        log.I(TAG, "_hideControlView");
        this._controlViewShowing = false;
        this._removeUpdateProgressTimer();
        if (this._hideControlViewAnim) {
            this._hideControlViewAnim.restart();
            return;
        }
        let headerAnim = new PropertyAnimation(this._views.header);
        headerAnim.from = {opacity: 1, translationY: 0};
        headerAnim.to = {opacity: 0, translationY: -Config.HEADER_HEIGHT};
        headerAnim.duration = CONTROL_VIEW_ANIM_DURATION;
        headerAnim.timingFunction = "easeInOut";

        let ctrlBarAnim = new PropertyAnimation(this._views.ctrlBar);
        ctrlBarAnim.from = {opacity: 1, translationY: 0};
        ctrlBarAnim.to = {opacity: 0, translationY: Config.PLAYER_CTRL_BAR_HEIGHT};
        ctrlBarAnim.duration = CONTROL_VIEW_ANIM_DURATION;
        ctrlBarAnim.timingFunction = "easeInOut";

        this._hideControlViewAnim = new AnimationGroup();
        this._hideControlViewAnim.add(headerAnim);
        this._hideControlViewAnim.add(ctrlBarAnim);
        this._hideControlViewAnim.start();
    }

    /**
     * 显示对话框，提示播放错误、投屏流量提醒等信息
     */
    _showPopupDialog(type: number = PopupDialogType.UNKNOWN) {
        if (this._destroyed) {
            return;
        }

        if (this._warningDialogShowing) {
            log.W(TAG, "_showPopupDialog, warning dialog is showing");
            return;
        }

        if (type !== PopupDialogType.PHONE) {
            this._showControlView();
        }

        if (this._dialog) {
            log.I(TAG, "_showPopupDialog", this._dialog.type, type);
            if (this._dialog.type !== type) {
                this._setContentView(this._dialog, type);
            }
            return;
        }

        let keyEvent = "";
        let operateType = "";
        let dialog = <IAlertDialog> new AlertDialog();
        dialog.once("result", (index) => {
            if (this._destroyed) {
                return;
            }

            log.I(TAG, "_showPopupDialog, result", index, dialog.type);
            if (dialog.type === PopupDialogType.PHONE) {
                return;
            }

            if (index === 0) {
                operateType = "cancel";
                if (dialog.type === PopupDialogType.DLNA) {
                    this._lastState = State.PLAYING;
                } else {
                    this._sendPlayErrorPopEvent(operateType);
                    this._onBack(ExitType.CLICK);
                }
            } else if (index === 1) {
                operateType = "next";
                if (dialog.type === PopupDialogType.DLNA) {
                    this._lastState = State.PAUSE;
                    this._setCtrlBarButtonState(true);
                    this._setPlayPauseBtnState(false);
                    this._showControlView();
                } else {
                    if (iAudioSession.isPhoneGainedAudioSession()) {
                        setImmediate(() => {
                            this._showPopupDialog(PopupDialogType.PHONE);
                        });
                    } else {
                        this._playNext(iUserTrackHelper.TriggerType.CLICK);
                    }
                }
            }
        });

        dialog.once("close", () => {
            if (this._destroyed) {
                return;
            }

            if (dialog.type !== PopupDialogType.PHONE && !this._warningDialogShowing) {
                this._sendPlayErrorPopEvent(operateType);
            }

            log.I(TAG, "_showPopupDialog, close", keyEvent);
            if (keyEvent === Consts.KEY_MEDIA_NEXT) {
                this._playNext(iUserTrackHelper.TriggerType.CLICK);
            } else if (keyEvent === Consts.KEY_MEDIA_PREVIOUS) {
                this._playPrev(iUserTrackHelper.TriggerType.CLICK);
            }
            dialog.destroy(true);
            this._dialog = null;
        });

        dialog.on("windowattached", (win: Window) => {
            log.D(TAG, "get window attached");
            win.on("keydown", (e: { key: string, preventDefault: () => void }) => {
                log.I(TAG, "get keydown", e.key);
                if (e.key === Consts.KEY_MEDIA_NEXT || e.key === Consts.KEY_MEDIA_PREVIOUS) {
                    keyEvent = e.key;
                    e.preventDefault();
                    dialog.close();
                }
            });
            win.on("keyup", (e: { key: string, preventDefault: () => void }) => {
                log.I(TAG, "get keyup", e.key);
                if (e.key === Consts.KEY_MEDIA_NEXT || e.key === Consts.KEY_MEDIA_PREVIOUS) {
                    e.preventDefault();
                }
            });
        });

        this._setContentView(dialog, type);
        dialog.show();
        this._dialog = dialog;
    }

    _sendPlayErrorPopEvent(operateType: string) {
        let paramObj = {
            operate_type: operateType ? operateType : "close"
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_PLAY_ERROR_POP, iUserTrackHelper.PLAY_ERROR_CLICK, paramObj);
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_PLAY_ERROR_POP, {error_code: this._errorCode});
    }

    _setContentView(dialog: IAlertDialog, type: number) {
        dialog.type = type;
        switch (type) {
            case PopupDialogType.PHONE:
                dialog.title = iRes.getString("POPUP_TITLE");
                dialog.message = iRes.getString("POPUP_MESSAGE_PHONE");
                dialog.buttons = [
                    {
                        text: iRes.getString("POPUP_KNOWN"),
                        style: AlertDialog.ButtonStyle.Positive
                    }
                ];
                break;
            case PopupDialogType.OFFLINE:
                dialog.title = iRes.getString("POPUP_TITLE");
                dialog.message = iRes.getString("POPUP_MESSAGE_OFFLINE");
                dialog.buttons = [
                    {
                        text: iRes.getString("POPUP_BACK"),
                        style: AlertDialog.ButtonStyle.Positive
                    },
                    {
                        text: iRes.getString("POPUP_NEXT"),
                        style: AlertDialog.ButtonStyle.Normal
                    }
                ];
                iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_PLAY_ERROR_POP);
                break;
            case PopupDialogType.NOT_EXIST:
                dialog.title = iRes.getString("POPUP_TITLE_ERROR");
                dialog.message = iRes.getString("POPUP_MESSAGE_NOT_EXIST");
                dialog.buttons = [
                    {
                        text: iRes.getString("POPUP_BACK"),
                        style: AlertDialog.ButtonStyle.Positive
                    },
                    {
                        text: iRes.getString("POPUP_NEXT"),
                        style: AlertDialog.ButtonStyle.Normal
                    }
                ];
                iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_PLAY_ERROR_POP);
                break;
            case PopupDialogType.DECODE:
                dialog.title = iRes.getString("POPUP_TITLE_ERROR");
                dialog.message = iRes.getString("POPUP_MESSAGE_DECODE");
                if (this._from === Consts.FromType.DLNA) {
                    dialog.buttons = [
                        {
                            text: iRes.getString("POPUP_KNOWN"),
                            style: AlertDialog.ButtonStyle.Positive
                        }
                    ];
                } else {
                    dialog.buttons = [
                        {
                            text: iRes.getString("POPUP_BACK"),
                            style: AlertDialog.ButtonStyle.Positive
                        },
                        {
                            text: iRes.getString("POPUP_NEXT"),
                            style: AlertDialog.ButtonStyle.Normal
                        }
                    ];
                }
                iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_PLAY_ERROR_POP);
                break;
            case PopupDialogType.UNKNOWN:
                dialog.title = iRes.getString("POPUP_TITLE_ERROR");
                dialog.message = iRes.getString("POPUP_MESSAGE_UNKNOWN");
                if (this._from === Consts.FromType.DLNA) {
                    dialog.buttons = [
                        {
                            text: iRes.getString("POPUP_KNOWN"),
                            style: AlertDialog.ButtonStyle.Positive
                        }
                    ];
                } else {
                    dialog.buttons = [
                        {
                            text: iRes.getString("POPUP_BACK"),
                            style: AlertDialog.ButtonStyle.Positive
                        },
                        {
                            text: iRes.getString("POPUP_NEXT"),
                            style: AlertDialog.ButtonStyle.Normal
                        }
                    ];
                }
                iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_PLAY_ERROR_POP);
                break;
            case PopupDialogType.DLNA:
                dialog.title = iRes.getString("POPUP_TITLE");
                dialog.message = iRes.getString("POPUP_MESSAGE_DLNA");
                dialog.buttons = [
                    {
                        text: iRes.getString("POPUP_PLAY"),
                        style: AlertDialog.ButtonStyle.Positive
                    },
                    {
                        text: iRes.getString("POPUP_CANCEL"),
                        style: AlertDialog.ButtonStyle.Positive
                    }
                ];
                break;
        }

        // 播放U盘视频最后一个视频时，若弹出提示框，则需要 disable “播放下一个” 按钮
        if (this._from === Consts.FromType.LOCAL || this._from === Consts.FromType.SEARCH_LOCAL) {
            if (this._index === this._playListLength - 1) {
                let lastButtonIndex = dialog.buttons.length - 1;
                if (lastButtonIndex > 0) {
                    dialog.getButton(lastButtonIndex).enabled = false;
                }
            }
        }
    }

    _hidePopupDialog(type: number) {
        if (!this._dialog) {
            return;
        }
        log.I(TAG, "_hidePopupDialog", type);
        if (type === this._dialog.type) {
            this._dialog.close();
        }
    }

    _closePopupDialog() {
        if (this._dialog) {
            this._dialog.close();
        }
    }

    _isDialogShowing() {
        return this._dialog && this._dialog.isShowing();
    }

    /**
     * 显示loading或无网络界面
     */
    _showLoadingPage(type: number) {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_showLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_showLoadingPage", type);
        if (type === LoadingPageType.LOADING) {
            this._views.loading.visibility = Visible;
            this._views.network.visibility = None;
            this._errorDialogShowing = false;
        } else if (type === LoadingPageType.NETWORK) {
            this._views.loading.visibility = None;
            this._views.network.visibility = Visible;
            this._views.controlRootView.background = Config.PLAYER_BG_COLOR;
            this._errorDialogShowing = true;
            this._changeState(State.FAIL);
            this._clearPreview();
            this._sendPlayErrorEvent();
            this._setCtrlBarButtonState(false);
            this._showHeaderView();
        } else {
            this._errorDialogShowing = false;
        }
    }

    _hideLoadingPage() {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_hideLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_hideLoadingPage");
        this._errorDialogShowing = false;
        if (this._views.loading) {
            this._views.loading.visibility = None;
        }

        if (this._views.network) {
            this._views.network.visibility = None;
        }

        if (this._isFirstDrawCompleted) {
            this._views.controlRootView.background = "transparent";
        }
    }

    _sendPlayErrorEvent() {
        let paramObj = {
            video_index: this._index,
            video_title: this._playVideoInfo.title,
            video_id: this._playVideoInfo.id,
            video_from: iUserTrackHelper.VideoFrom.ONLINE,
            cpId: iUserTrackHelper.CP_ID
        };
        iUserTrackHelper.sendEvent(
            iUserTrackHelper.PAGE_PLAYER,
            iUserTrackHelper.PLAY_ERROR,
            paramObj
        );
    }

    _changeState(state: string) {
        log.I(TAG, "_changeState", state);
        if (state !== this._currentState) {
            this._currentState = state;
        }
    }

    /**
     * 记录高亮视频的URL
     * 1.主要是记录在线视频和本地视频播放的URL，并通知列表更新
     * 2.产品设计上投屏不需要记录高亮，但由于从在线视频列表页进入全屏播放页时会重新layout，
     *   导致显示重复元素，所以需要触发在线视频列表的更新
     */
    _saveHighlightUrl() {
        log.I(TAG, "_saveHighlightUrl", this._from, this._playVideoInfo.title, this._playVideoInfo.url);
        if (Utils.isOnlineVideo(this._playVideoInfo.url)) {
            iVideoModel.saveHighlightUrl(this._from, Consts.FromType.ONLINE, this._playVideoInfo.url, this._index);
        } else {
            let path = this._findPathByUrl(this._playVideoInfo.url);
            log.I(TAG, "_saveHighlightUrl", path);
            if (path) {
                iVideoModel.saveHighlightUrl(this._from, path, this._playVideoInfo.url, this._index);
            }
        }
    }

    _findPathByUrl(url: string) {
        if (this._path) {
            // bugid:21841931, maybe u-disk removed
            log.I(TAG, "_findPathByUrl", this._path);
            if (url.indexOf(this._path) >= 0) {
                return this._path;
            }
        }
        return "";
    }

    /**
     * 记录视频播放时间
     */
    _saveElapsedTime() {
        let isFromDLNA = this._from === Consts.FromType.DLNA;
        let url = this._playVideoInfo.url;
        if (Utils.isOnlineVideo(url) && !isFromDLNA) {
            return;
        }
        this._checkCurrentPosition();
        if (this._elapsedTime === -1) {
            log.W(TAG, "_saveElapsedTime, elapsed time is -1");
            return;
        }

        if (!this._isFirstDrawCompleted) {
            log.I(TAG, "_saveElapsedTime, surface first draw is not completed");
            return;
        }

        try {
            let path = isFromDLNA ? Consts.FromType.DLNA : Consts.FromType.LOCAL;
            let saveUrl = url;
            if (isFromDLNA) {
                saveUrl = Utils.extractUrl(url);
            }

            if (this._elapsedTime === this._duration) {
                iVideoModel.saveElapsedTime(path, saveUrl, 0);
            } else if (this._elapsedTime < this._duration) {
                iVideoModel.saveElapsedTime(path, saveUrl, this._elapsedTime);
            }
        } catch (err) {
            log.E(TAG, "_saveElapsedTime", err.code);
        }
    }

    /**
     * 计算视频从start到pause的实际播放时间
     */
    _computePlayTotalTime() {
        log.I(TAG, "_computePlayTotalTime", this._playStartTime);
        if (this._playStartTime === 0) {
            return;
        }

        let playEndTime = new Date().getTime();
        this._playTotalTime += playEndTime - this._playStartTime;
        this._playStartTime = 0;
    }

    /**
     * 计算视频在退出或切换后播放的总时长
     */
    _reportPlayTotalTime(playExitType = ExitType.CLICK) {
        if (!this._playVideoInfo) {
            log.I(TAG, "_reportPlayTotalTime, play video info is null");
            return;
        }

        this._computePlayTotalTime();
        let videoFrom = "";
        if (this._from === Consts.FromType.DLNA) {
            videoFrom = iUserTrackHelper.VideoFrom.AIRPLAY;
        } else if (Utils.isOnlineVideo(this._playVideoInfo.url)) {
            videoFrom = iUserTrackHelper.VideoFrom.ONLINE;
        } else {
            videoFrom = iUserTrackHelper.VideoFrom.USB;
        }
        log.I(TAG, "_reportPlayTotalTime", playExitType);
        let paramObj = {
            operate_type: playExitType ? playExitType : this._playExitType,
            video_index: this._index,
            video_id: this._playVideoInfo.id,
            video_title: this._playVideoInfo.title ? this._playVideoInfo.title : DEFAULT_VALUE,
            video_size: this._playVideoInfo.videoSize,
            video_tags: this._playVideoInfo.tags,
            video_duration: this._playVideoInfo.duration,
            video_from: videoFrom,
            video_format: Utils.isOnlineVideo(this._playVideoInfo.url)
                ? "mp4" : this._playVideoInfo.mimeType,
            video_counttime: this._playTotalTime,
            video_stoptime: this._elapsedTime,
            category_name: this._playVideoInfo.category,
            cpId: iUserTrackHelper.CP_ID,
            video_starttime: this._videoStartTime
        };
        iUserTrackHelper.sendEvent(
            iUserTrackHelper.PAGE_PLAYER,
            iUserTrackHelper.PLAY_TOTAL_TIME,
            paramObj
        );
        this._playTotalTime = 0;
        this._videoStartTime = 0;
    }

    _reportLimitWarning() {
        let paramObj = {
            vidoe_title: this._playVideoInfo.title,
            video_from: Utils.isOnlineVideo(this._playVideoInfo.url) ? "online" : "usb"
        };
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_LIMIT, paramObj);
    }

    /**
     * 清除预览图
     */
    _clearPreview() {
        if (this._previewImage) {
            this._views.decorateContainer.removeChild(this._previewImage);
            this._previewImage.destroy(true);
            this._previewImage = null;
        }
    }

    /**
     * 销毁videoview
     */
    _clearVideoView() {
        if (!this._views) {
            log.W(TAG, "_clearVideoView, views is null");
            return;
        }

        this._clearPreview();

        if (!this._views.videoView) {
            log.W(TAG, "_clearVideoView, videoView is null");
            return;
        }

        try {
            log.D(TAG, "_clearVideoView, destroy");
            this._views.videoViewContainer.removeChild(this._views.videoView);
            this._views.videoView.removeAllListeners("videosizechanged");
            this._views.videoView.removeAllListeners("ready");
            this._views.videoView.removeAllListeners("prepared");
            this._views.videoView.removeAllListeners("started");
            this._views.videoView.removeAllListeners("paused");
            this._views.videoView.removeAllListeners("playbackcomplete");
            this._views.videoView.removeAllListeners("infoext");
            this._views.videoView.removeAllListeners("error");
            this._views.videoView.destroy();
        } catch (err) {
            log.E(TAG, "_clearVideoView", err);
        } finally {
            this._views.videoView = null;
        }
    }

    /**
     * 注册语音指令，由于需要考虑音频焦点是否在视频应用，因此需要动态注册
     */
    _addVoiceCommands() {
        if (this._views.videoViewContainer.voiceSelectMode === View.VoiceSelectMode.Custom) {
            log.I(TAG, "_addVoiceCommands, already registered");
            return;
        }

        log.I(TAG, "_addVoiceCommands");
        // jshint unused:false
        const cmdKeys = [
            "VOICECMD_NEXT_1",
            "VOICECMD_NEXT_2"
        ];
        const INDEX_OF_PREV = cmdKeys.length;
        cmdKeys.push(
            "VOICECMD_PREV_1",
            "VOICECMD_PREV_2"
        );
        const INDEX_OF_PLAY = cmdKeys.length;
        cmdKeys.push(
            "VOICECMD_PLAY_1",
            "VOICECMD_PLAY_2",
            "VOICECMD_PLAY_3",
            "VOICECMD_PLAY_4",
            "VOICECMD_PLAY_5"
        );
        const INDEX_OF_PAUSE = cmdKeys.length;
        cmdKeys.push(
            "VOICECMD_PAUSE_1",
            "VOICECMD_PAUSE_2",
            "VOICECMD_PAUSE_3",
            "VOICECMD_PAUSE_4",
            "VOICECMD_PAUSE_5",
            "VOICECMD_PAUSE_6",
            "VOICECMD_PAUSE_7",
            "VOICECMD_PAUSE_8"
        );
        const INDEX_OF_STOP = cmdKeys.length;
        cmdKeys.push(
            "VOICECMD_STOP_1",
            "VOICECMD_STOP_2",
            "VOICECMD_STOP_3",
            "VOICECMD_STOP_4",
            "VOICECMD_STOP_5",
            "VOICECMD_STOP_6",
            "VOICECMD_STOP_7",
            "VOICECMD_STOP_8",
            "VOICECMD_STOP_9",
            "VOICECMD_STOP_10",
            "VOICECMD_STOP_11",
            "VOICECMD_BACK_1",
            "VOICECMD_BACK_2"
        );

        Utils.registerVoiceCommand(this._views.videoViewContainer, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            if (this._warningDialogShowing || this._isDialogShowing() || iTrafficHelper.isDialogShowing()) {
                log.I(TAG, "voice command, dialog is showing");
                return;
            }

            log.I(TAG, "voice command", cmdKey, index);
            let isInCall = iAudioSession.isPhoneGainedAudioSession();
            if (index >= 0 && index < INDEX_OF_PREV) {
                log.I(TAG, "voice command, next");
                if (this._from !== Consts.FromType.DLNA && !isInCall) {
                    // set last state playing to make play next video
                    this._lastState = State.PLAYING;
                    this._playNext(iUserTrackHelper.TriggerType.VOICE);
                }
            } else if (index >= INDEX_OF_PREV && index < INDEX_OF_PLAY) {
                log.I(TAG, "voice command, prev");
                if (this._from !== Consts.FromType.DLNA && !isInCall) {
                    // note: set last state playing to make play previous video
                    this._lastState = State.PLAYING;
                    this._playPrev(iUserTrackHelper.TriggerType.VOICE);
                }
            } else if (index >= INDEX_OF_PLAY && index < INDEX_OF_PAUSE) {
                log.I(TAG, "voice command, play");
                if (!isInCall) {
                    this._lastState = State.PLAYING;
                    this._onPlayPause(iUserTrackHelper.TriggerType.VOICE, true);
                }
            } else if (index >= INDEX_OF_PAUSE && index < INDEX_OF_STOP) {
                log.I(TAG, "voice command, pause");
                if (!isInCall) {
                    this._onPlayPause(iUserTrackHelper.TriggerType.VOICE, false);
                    this._lastState = State.PAUSE;
                }
            } else if (index >= INDEX_OF_STOP && index < cmdKeys.length) {
                log.I(TAG, "voice command, stop");
                this._onBack(ExitType.CLICK);
            } else {
                log.I(TAG, "voice command, nothing");
            }
        }, true);
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null");
            return;
        }

        log.I(TAG, "_removeVoiceCommands");
        Utils.removeVoiceCommand(this._views.controlRootView);
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (!this._views) {
            log.W(TAG, "_removeAllListeners, views is null");
            return;
        }

        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];

        if (this._views.header) {
            this._views.header.removeAllListeners("touchstart");
        }

        if (this._views.ctrlBar) {
            this._views.ctrlBar.removeAllListeners("touchstart");
        }

        if (this._views.network) {
            this._views.network.removeAllListeners("touchstart");
        }

        if (this._swipeLeftListener) {
            this._views.decorateContainer.removeEventListener("swipeleft", this._swipeLeftListener);
        }

        if (this._swipeRightListener) {
            this._views.decorateContainer.removeEventListener("swiperight", this._swipeRightListener);
        }

        if (this._swipeRecognizer) {
            this._views.decorateContainer.removeGestureRecognizer(this._swipeRecognizer);
            this._swipeRecognizer.destroy();
        }

        if (this._ctrlViews.progressSlider) {
            this._ctrlViews.progressSlider.removeListener("valuechanged", this._onSliderValueChanged);
            this._ctrlViews.progressSlider.removeListener("trackingtouchstart", this._onSliderTouchStart);
            this._ctrlViews.progressSlider.removeListener("trackingtouchend", this._onSliderTouchEnd);
            this._ctrlViews.progressSlider.destroy();
        }

        if (this._views.elapsedTime) {
            this._views.elapsedTime.destroy();
        }

        if (this._views.title) {
            this._views.title.destroy();
        }

        if (this._views.backIcon) {
            this._views.backIcon.destroy();
        }

        if (this._showControlViewAnim) {
            this._showControlViewAnim.destroy();
        }

        if (this._hideControlViewAnim) {
            this._hideControlViewAnim.destroy();
        }

        iAudioSession.unRegisterAudioSessionChangeListener();
        log.I(TAG, "_removeAllListeners done");
    }

    /**
     * 移除所有timer
     */
    _removeAllTimer() {
        this._removeTimeout(this._hideControlViewTimer);
        this._removeTimeout(this._dnsLookupTimer);
        this._removeTimeout(this._playWaitTimer);
        this._removeTimeout(this._playDelayTimer);
        this._removeUpdateProgressTimer();
        this._removeRecordProgressTimer();
        this._removeHidePageTimer();
    }

    _removeUpdateProgressTimer() {
        if (this._updateProgressTimer) {
            clearInterval(this._updateProgressTimer);
            this._updateProgressTimer = null;
        }
    }

    _removeRecordProgressTimer() {
        if (this._recordProgressTimer) {
            clearInterval(this._recordProgressTimer);
            this._recordProgressTimer = null;
        }
    }

    _removeHidePageTimer() {
        if (this._hidePageTimer) {
            clearInterval(this._hidePageTimer);
            this._hidePageTimer = null;
        }
    }

    _removeTimeout(timeout: NodeJS.Timer) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
    }

    _stopAllAnimation() {
        this._stopAnimation(this._hideControlViewAnim);
        this._stopAnimation(this._showControlViewAnim);
    }

    _stopAnimation(animation: { stop: () => void; destroy: () => void; }) {
        if (animation) {
            animation.stop();
            animation.destroy();
            animation = null;
        }
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._warningDialogShowing = false;
        if (this._dialog) {
            this._dialog.destroy(true);
        }
        if (this._views && this._views.loading) {
            this._views.loading.destroy(true);
        }

        iPageInstance.setPageContainerBackground(false);
        this._clearVideoView();
        this._stopAllAnimation();
        this._removeAllTimer();
        this._removeAllListeners();
        this._removeVoiceCommands();
        try {
            if (this._controlWindow) {
                this._controlWindow.destroy(true);
            }
        } catch (e) {
            log.E(TAG, e);
        }
        this._recordVideo(this._playVideoInfo);
    }
}

export = PlayerPresenter;
