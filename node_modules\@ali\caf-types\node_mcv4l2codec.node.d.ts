import {NodeMediaMeta} from "node_multimedia.node";
export declare interface NodeV4l2Codec{
    new(mime: string, isEncoder: number): NodeV4l2Codec;
    configure(meta: NodeMediaMeta, surface: number, flag: number): void;
    start(): void;
    stop(): void;
    flush(): void;
    reset(): void;
    setParameter(meta: NodeMediaMeta): void;
    getInputFormat(): NodeMediaMeta;
    getOutputFormat(): NodeMediaMeta;
    requestIDRFrame(): void;
    queueInputBuffer(index: number, buffer: Buffer, offset: number, size: number,
        time: number, flags: number): void;
    dequeueInputBuffer(timeoutUs: number): void;
    dequeueOutputBuffer(timeoutUs: number): void;
    releaseOutputBuffer(index: number, render: number): void;
}

export const NodeV4l2Codec: NodeV4l2Codec;
