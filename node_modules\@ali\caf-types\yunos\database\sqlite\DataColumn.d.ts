import YObject = require("yunos/core/YObject");
/**
 * <p>DataColumn class, which contains the information of a table column.</p>
 * <p>It can be used to create table in SQLite database.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class DataColumn extends YObject {
    private _name;
    private _type;
    private _isPrimary;
    private _isAutoInc;
    private _isNotNull;
    private syncable: boolean;
    /**
     * Create a DataColumn with given column name and column data type etc.
     * @param {string} name - the given column name
     * @param {string} type - the given column data type
     * @param {boolean} isNotNull - if the column can be null
     * @param {boolean} isPrimary - if the column is primary key
     * @param {boolean} isAutoInc - if the column is auto incrementable
     * @public
     * @since 2
     */
    public constructor(name: string, type: number, isNotNull: boolean, isPrimary: boolean, isAutoInc: boolean);
    /**
     * Column name.
     * @name yunos.database.sqlite.DataColumn#name
     * @type {string}
     * @public
     * @since 2
     */
    public name: string;
    /**
     * The data type of the column.
     * @name yunos.database.sqlite.DataColumn#type
     * @type {yunos.database.sqlite.SQLiteDatabase.DataType}
     * @public
     * @since 2
     */
    public type: number;
    /**
     * The flag that indicates if the column is primary key.
     * @name yunos.database.sqlite.DataColumn#isPrimaryKey
     * @type {boolean}
     * @public
     * @since 2
     */
    public isPrimaryKey: boolean;
    /**
     * The flag that indicates if the column is auto increment.
     * @name yunos.database.sqlite.DataColumn#isAutoIncrement
     * @type {boolean}
     * @public
     * @since 2
     */
    public isAutoIncrement: boolean;
    /**
     * The flag that indicates if the column can be null.
     * @name yunos.database.sqlite.DataColumn#isNotNull
     * @type {boolean}
     * @public
     * @since 2
     */
    public isNotNull: boolean;
}
export = DataColumn;
