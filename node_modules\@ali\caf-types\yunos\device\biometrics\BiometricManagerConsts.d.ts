/**
 * BiometricManagerConsts json, constants value of BiometricManager
 * @json BiometricManagerConsts
 */
declare const BiometricManagerConsts: {
    BIO_NATIVE_SERVICE_NAME: string;
    BIO_NATIVE_SERVICE_PATH: string;
    BIO_NATIVE_SERVICE_INTF: string;
    REGISTERED: string;
    RECOGNIZED: string;
    FACELIVENESSDETECTED: string;
    FACEBOUND: string;
    REGISTER_FACE: string;
    RECOGNIZE_FACE: string;
    STOP_REGISTER: string;
    STOP_RECOGNIZE: string;
    RESET_RECOGNIZE_RESULT: string;
    DELETE_FACEID: string;
    QUERY_RECOGNIZED_RESULT: string;
    QUERY_RECOGNIZED_RESULT_2: string;
    QUERY_REGISTERED_FACE: string;
    HAS_REGISTERED_FACE_SYNC: string;
    CONFIG_BIO_UT: string;
    DETECT_FACE_LIVENESS: string;
    STOP_DETECT_FACE_LIVENESS: string;
    BIND_FACE: string;
    STOP_BIND_FACE: string;
    UNBIND_FACE: string;
    IS_FACE_BOUND: string;
    GET_APP_ID: string;
    ENABLE_FACEID: string;
    DISABLE_FACEID: string;
    IS_FACEID_ENABLED: string;
    QUERY_ALL_REGISTERED_FACE: string;
    ERROR_MSG: string;
    ERROR_CODE: string;
    RESULT: string;
    FaceResult: {
        SUCCESS: int;
        ERROR: int;
        REGISTER_SAVE_FAIL: int;
        ID_EXISTED: int;
        ID_NOT_EXIST: int;
        LIVE_DETECT_FAIL: int;
    };
};
export = BiometricManagerConsts;
