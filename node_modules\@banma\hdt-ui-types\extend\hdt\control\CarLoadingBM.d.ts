import SpriteView = require("yunos/ui/view/SpriteView");
interface IStyle {
    loadingSpriteFrameWidth: number;
    loadingSpriteFrameHeight: number;
    loadingSpriteWidth: number;
    loadingSpriteHeight: number;
    loadingSpriteFrameCount: number;
    loadingSpriteFrameDuration: number;
    loadingSpritePath: string;
}
declare class CarLoadingBM extends SpriteView {
    readonly defaultStyleName: string;
    readonly animating: boolean;
    visibility: number;
    private _defaultSrc;
    private _defaultFrameWidth;
    private _defaultFrameHeight;
    private _defaultFrameCount;
    private _defaultFrameDuration;
    private _defaultHeight;
    private _defaultWidth;
    constructor();
    protected applyStyle(style: IStyle): void;
    private _applyStyle;
    updateStyle(style: IStyle, diffStyle: IStyle): void;
}
export = CarLoadingBM;
