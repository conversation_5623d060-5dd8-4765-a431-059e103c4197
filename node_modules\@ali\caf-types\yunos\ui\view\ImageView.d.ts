/// <reference types="node" />
import View = require("./View");
import Bitmap = require("../../graphics/Bitmap");
/**
 * <p>Image view that displays an arbitrary image, such as an icon.</p>
 * <p>The ImageView class can load images from various sources (such as local or online resources),
 * takes care of computing its measurement from the image so that it can be used in any layout,
 * and provides various display options such as scaling and tinting.</p>
 * @extends yunos.ui.view.View
 * @memberof yunos.ui.view
 * @public
 * @since 1
 */
declare class ImageView extends View {
    protected _src: string | Bitmap | Buffer | null;
    private _scaleType;
    private _indexWidth;
    private _indexHeight;
    private _asynchronous;
    protected _paddingLeft: number;
    protected _paddingTop: number;
    protected _paddingRight: number;
    protected _paddingBottom: number;
    private _sourceRect;
    private _hasSourceRect;
    private _tint;
    private _autoCommand;
    /**
     * <p>Constructor that create a image view.</p>
     * @public
     * @since 1
     */
    /**
     * <p>The image url, which indicates a local path currently.
     * Note that once you set this value, the image loading process will start asynchronously.
     * Set null to clear the shown image</p>
     * @name yunos.ui.view.ImageView#src
     * @type {string|yunos.graphics.Bitmap|null}
     * @throws {TypeError} If the value is not an uri or Bitmap
     * @public
     * @since 1
     */
    public src: string | Buffer | Bitmap;
    private resetSize(value: string | Bitmap | Buffer): void;
    /**
     * <p>The desired scaling mode, such as "ImageView.ScaleType.Matrix", "ImageView.ScaleType.Fitxy",<br>
     * "ImageView.ScaleType.Fitstart", "ImageView.ScaleType.Fitend", "ImageView.ScaleType.Fitcenter",<br>
     * "ImageView.ScaleType.Center", "ImageView.ScaleType.Centercrop" and "ImageView.ScaleType.Centerinside"</p>
     * @name yunos.ui.view.ImageView#scaleType
     * @type {yunos.ui.view.ImageView.ScaleType}
     * @throws {TypeError} If the value is not in ImageView.ScaleType
     * @public
     * @since 1
     */
    public scaleType: number;
    /**
     * <p>Width of this view, in pixels, if not set, will using target image's size.</p>
     * @name yunos.ui.view.ImageView#width
     * @type {number}
     * @override
     * @public
     * @since 1
     */
    public width: number;
    /**
     * <p>The height of this view, in pixels, if not set, will using target image's size. </p>
     * @name yunos.ui.view.ImageView#height
     * @type {number}
     * @override
     * @public
     * @since 1
     */
    public height: number;
    /**
     * <p>Get the source image size of this ImageView. The first number is width and the second is height.</p>
     * @name yunos.ui.view.ImageView#sourceSize
     * @type {number[]}
     * @readonly
     * @public
     * @since 1
     */
    /**
     * <p>Get the source image size of this ImageView.</p>
     * @name yunos.ui.view.ImageView#sourceSize
     * @type {number[]} the first number is width and the second is height
     * @public
     * @since 3
     *
     */
    public sourceSize: number[];
    /**
     * <p>Get the source rect of this ImageView.</p>
     * @name yunos.ui.view.ImageView#sourceRect
     * @type {number[]} the first number is x, the second is y, the third is w, the last is h
     * @public
     * @since 3
     *
     */
    public sourceRect: number[];
    /**
     * <p>The tint color of this ImageView.</p>
     * @name yunos.ui.view.ImageView#tint
     * @type {string} Color string, restore to default color by null value
     * @public
     * @since 5
     */
    public tint: string | number;
    /**
     * <p>voiceViewType.</p>
     * @override
     * @protected
     * @since 6
     */
    protected readonly voiceViewType: string;
    /**
     * <p>Defines voiceBinding of the Switch, if not empty to bind one view text.<br/>
     * The attributes currently supports controls bound to text component(Button,TextView and so on),After being bound, <br/>
     * the bound control cannot be registered and responded to if it has voice events.<br/>
     * In the following example, if voiceBinding is used, it responds to the events of the bound View.</p>
     * @example
     * //Use in code
     * //The bound component
     * var textView = new TextView();
     * textView.id = "textViewId";
     * textView.text = "Sound";
     * this.voiceBinding = textView.id;
     *
     * @example
     * //Use in xml
     * <TextView id="textViewId" text="Sound"></TextView>
     * <ImageView id="imageview" voiceBinding="textViewId"/>
     * @name yunos.ui.view.ImageView#voiceBinding
     * @type {string}
     * @override
     * @public
     * @since 6
     */
    public voiceBinding: string;
    private setCustomFillRect(): void;
    private calCustomFillRect(): number[];
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.view.ImageView#defaultStyleName
     * @default "ImageView"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    private getBuffer(): string | Buffer;
    /**
     * <p>Implement this to apply style. </p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - Style config from theme.
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    private asynchronous: boolean;
    private reload(): void;
    /**
     * <p>Enum for ImageView ScaleType.
     * The Value set the ImageView image show scale mode.</p>
     * @default yunos.ui.view.ImageView.ScaleType.Matrix
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly ScaleType: {
        /**
         * Scale the width and height of the image by the matrix.
         * @public
         * @since 1
         */
        Matrix: int;
        /**
         * Scale the width and height of the image individually. And the image will full the view.
         * @public
         * @since 1
         */
        Fitxy: int;
        /**
         * Scale the width and height by the aspect ratio of the image. At least one of width or height will be equal to the view's. And the iamge will align center in both horizontal and vertical.
         * @public
         * @since 1
         */
        Fitcenter: int;
        /**
         * Scale the width and height by the aspect ratio of the image. At least one of the width or height will be equal to the width or height of the view. And the image will align the left and top.
         * @public
         * @since 1
         */
        Fitstart: int;
        /**
         * Scale the width and height by the aspect ratio of the image. At least one of the width or height will be equal to the width or height of the view. And the image will align the right and bottom.
         * @public
         * @since 1
         */
        Fitend: int;
        /**
         * Not scale the image, but will crop by view. And the iamge will align center in both horizontal and vertical.
         * @public
         * @since 1
         */
        Center: int;
        /**
         * Scale the width and height by the aspect ratio of the image. So the both of width and height is equal to or less than view's. And the iamge will align center in both horizontal and vertical.
         * If the image is smaller than view, there is no need to scale.
         * @public
         * @since 1
         */
        Centerinside: int;
        /**
         * Scale the width and height by the aspect ratio of the image. At least the both of width and height is equal or larger than view's. And the iamge will align center in both horizontal and vertical.
         * @public
         * @since 1
         */
        Centercrop: int;
        Tile: int;
        TileHorizontally: int;
        TileVertically: int;
        WidthFix: int;
        HeightFix: int;
    };
}
export = ImageView;
