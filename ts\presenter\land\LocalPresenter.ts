/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
const {Visible, Hidden, None} = require("yunos/ui/view/View").Visibility;
import PopupMenu = require("yunos/ui/widget/PopupMenu");
import GridView = require("yunos/ui/view/GridView");
import QuickIndex = require("yunos/ui/widget/QuickIndex");
import View = require("yunos/ui/view/View");
import CompositeView = require("yunos/ui/view/CompositeView");
import TextView = require("yunos/ui/view/TextView");
import Cursor = require("yunos/provider/Cursor");
import ScrollBar = require("yunos/ui/widget/ScrollBar");
import ImageView = require("yunos/ui/view/ImageView");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
const RecognitionMode = VoiceCommand.RecognitionMode;
import LocalAdapter = require("./adapter/LocalAdapter");
import localModel = require("../../model/LocalModel");
const iLocalModel = localModel.getInstance();
const iVideoModel = require("../../model/VideoModel").getInstance();
import MediaMonitor = require("../../monitor/MediaMonitor");
const iUserTrackHelper = require("../../utils/UserTrackHelper").getInstance();
const iPageInstance = require("../../index").getInstance();
import Consts = require("../../Consts");
const RoutePath = Consts.RoutePath;
import log = require("../../utils/log");
import Utils = require("../../utils/Utils");
import VideoInfo = require("../../model/VideoInfo");
const TAG = "LocalPresenter";

const sortOrder = {
    DEFAULT: "_id",
    TITLE: "_display_name",
    DATE_MODIFIED: "date_modified"
};
const DEFAULT_USB_INDEX = 1;

interface IContextData {
    videoIndex: number;
    startType: string;
    index: number;
    path: string;
}

interface IViews {
    navigationBar?: NavigationBar;
    title: TextView;
    order: CompositeView;
    orderView: CompositeView;
    orderText: TextView;
    gridview: GridView;
    scrollbar: ScrollBar;
    quickindex: QuickIndex;
    empty: CompositeView;
    emptyIcon: ImageView;
    emptyInfo: TextView;
    loading: CompositeView;
}

interface IConfig {
    PAGE_WIDTH?: number;
    PAGE_HEIGHT?: number;
    PAGE_MARGIN?: number;
    HEADER_HEIGHT?: number;
    ITEM_SPACE?: number;
    LOCAL_QUICK_INDEX_WIDTH?: number;
}
const Config: IConfig = {};

class LocalPresenter extends Presenter {
    private _index: number;
    private _path: string;
    private _order: string;
    private _hidden: boolean;
    private _destroyed: boolean;
    private _sortMenu: PopupMenu;
    private _mediaMonitor: MediaMonitor;
    private _needCheckStorageState: boolean;
    private _views: IViews;
    private _tapAbleViews: View[];
    private _highlightUrlChanged: (path: string, url: string, index: number) => void;
    private _navBackListener: () => void;

    onCreate() {
        this._init();
        this.attachView("local");
        this._initListener();
    }

    _init() {
        Config.PAGE_WIDTH = Utils.getPageWidth();
        Config.PAGE_HEIGHT = Utils.getPageHeight();
        Config.PAGE_MARGIN = Utils.getDimen("PAGE_MARGIN");
        Config.HEADER_HEIGHT = Utils.getDimen("HEADER_HEIGHT");
        Config.ITEM_SPACE = Utils.getDimen("ITEM_SPACE");
        Config.LOCAL_QUICK_INDEX_WIDTH = Utils.getDimen("LOCAL_QUICK_INDEX_WIDTH");

        this._destroyed = false;
        this._index = DEFAULT_USB_INDEX;
        this._path = "";
        this._order = sortOrder.DEFAULT;
        if (this.context.data) {
            this._index = (<IContextData> this.context.data).index;
            this._path = (<IContextData> this.context.data).path;
        }
        this._needCheckStorageState = false;
    }

    /**
     * 初始化监听器，监听U盘、高亮、扫描结果的变化消息
     */
    _initListener() {
        log.I(TAG, "_initListener");
        this._highlightUrlChanged = (path, url, index) => {
            if (this._destroyed) {
                log.W(TAG, "_highlightUrlChanged, presenter is destroyed");
                return;
            }

            if (!url) {
                log.W(TAG, "_highlightUrlChanged, url is null");
                return;
            }

            log.I(TAG, "_highlightUrlChanged", path, url, index);
            if (this._path === path) {
                this._views.gridview.adapter.onDataChange();
            }
        };
        iVideoModel.on(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);

        this._mediaMonitor = new MediaMonitor(iPageInstance, (uri: string) => {
            if (this._destroyed) {
                log.W(TAG, "_dbDataChanged, presenter is destroyed");
                return;
            }

            // 批量或全部视频扫描完成
            log.D(TAG, "_dbDataChanged", uri);
            if (uri === this._path) {
                this._loadData(true);
            }
        });
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_USB);
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        this._closeSortMenu();
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_USB);
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._setupTapHandler();
        this._initViews();
        this._addVoiceCommands();
    }

    _setupViews(parentView: View) {
        this._views = <IViews>{};
        this._views.navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = Visible;
        this._views.navigationBar.titleItem.title.align = TextView.Align.Left;
        this._views.navigationBar.preTitleItem.visibility = None;
        this._views.navigationBar.rightItem.visibility = Visible;
        let rightItem = <CompositeView> LayoutManager.loadSync("local_nav_right_item");
        this._views.order = <CompositeView> rightItem.findViewById("id_container");
        this._views.orderView = <CompositeView> rightItem.findViewById("id_order");
        this._views.orderText = <TextView> rightItem.findViewById("id_order_title");
        this._views.navigationBar.rightItem = rightItem;
        this._views.navigationBar.addEventListener("back", this._navBackListener = () => {
            log.I(TAG, "back button pressed!");
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_USB, iUserTrackHelper.USB_BACK);
            this._onBack();
        });
        this._views.title = this._views.navigationBar.titleItem.title;

        this._views.gridview = <GridView> parentView.findViewById("id_list_grid");
        this._views.scrollbar = <ScrollBar> parentView.findViewById("id_scrollbar");
        this._views.quickindex = <QuickIndex> parentView.findViewById("id_quickindex");
        this._views.empty = <CompositeView> parentView.findViewById("id_empty");
        this._views.emptyIcon = <ImageView> this._views.empty.findViewById("id_icon");
        this._views.emptyInfo = <TextView> this._views.empty.findViewById("id_info");
        this._views.loading = <CompositeView> parentView.findViewById("id_loading");
    }

    _onBack() {
        if (this._destroyed) {
            log.W(TAG, "_onBack, presenter is destroyed");
            return;
        }

        this._views.loading.visibility = Hidden;
        let ret = this.context.router.back();
        if (!ret) {
            log.W(TAG, "_onBack, replace to local");
            this.context.router.replace(Consts.RoutePath.ONLINE);
        }
    }

    _setupTapHandler() {
        this._tapAbleViews = [];
        Utils.setOnTapListener(this._views.order, () => {
            log.I(TAG, "order pressed!");
            if (this._views.gridview) {
                let adapter = <LocalAdapter> this._views.gridview.adapter;
                if (adapter && adapter.cursor && adapter.cursor.count > 0) {
                    this._showSortMenu();
                }
            }
        });
        this._tapAbleViews.push(this._views.order);
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        log.I(TAG, "_addVoiceCommands");
        const cmdKeys = [
            "VOICECMD_BACK_1",
            "VOICECMD_BACK_2"
        ];

        Utils.registerVoiceCommand(this._views.navigationBar, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            log.I(TAG, "voice command", cmdKey, index);
            this._onBack();
            Utils.cancelSpeech(iPageInstance);
        });
    }

    _initViews() {
        this._views.gridview.width = Config.PAGE_WIDTH - 2 * Config.PAGE_MARGIN;
        this._views.gridview.height = Config.PAGE_HEIGHT - Config.HEADER_HEIGHT - Config.ITEM_SPACE - Config.PAGE_MARGIN;
        this._views.gridview.adapter = new LocalAdapter(false);
        let adapter = <LocalAdapter> this._views.gridview.adapter;
        adapter.registerVoiceSelectListener(this._selectHandler.bind(this));
        this._views.gridview.on("itemselect", this._selectHandler.bind(this));
        this._views.gridview.horizontalScrollBar = this._views.scrollbar;

        this._views.quickindex.tipCenter = [iPageInstance.getWindowWidth() / 2, iPageInstance.getWindowHeight() / 2];
        this._views.quickindex.showTip = true;
        this._views.quickindex.orientation = (<{ Orientation: { Horizontal: number } }><object>QuickIndex).Orientation.Horizontal;

        this._views.quickindex.quickIndexStr = [];
        this._views.quickindex.on("currentchanged", (index: number, value) => {
            log.I(TAG, "currentchanged", index, value);
            if (this._views.quickindex.visibility !== Visible) {
                return;
            }

            let positionArray = (<{ positionArray: number[] }><object> this._views.quickindex).positionArray;
            if (index >= 0 && index < positionArray.length) {
                this._views.gridview.arriveAt(positionArray[index]);
            }
        });

        this._views.title.text = iRes.getString("USB_TITLE");
        this._views.loading.visibility = Hidden;
        this._views.emptyInfo.text = iRes.getString("ERR_INSERT_USB");
    }

    /**
     * 查询数据库数据，scanCompleted表示触发的扫描操作是否已经收到完成的消息，若完成则显示查询结果
     */
    _loadData(scanCompleted?: boolean) {
        if (this._destroyed) {
            log.W(TAG, "_loadData, presenter is destroyed");
            return;
        }

        if (!this._path) {
            log.W(TAG, "_loadData, path is null");
            return;
        }

        log.I(TAG, "_loadData", scanCompleted, this._path, this._order);
        if (!scanCompleted) {
            this._views.order.visibility = Hidden;
            this._views.gridview.visibility = Hidden;
            this._views.emptyIcon.visibility = Hidden;
            this._views.emptyInfo.text = "";
            this._views.loading.visibility = Visible;
        }
        iLocalModel.loadVideo(this._path, this._order, (error: Object, cursor: Cursor) => {
            if (this._destroyed) {
                log.W(TAG, "loadVideo, presenter is destroyed");
                return;
            }

            if (!this._path) {
                log.W(TAG, "loadVideo, path is null");
                return;
            }

            log.I(TAG, "loadVideo", error);
            if (error) {
                this._views.loading.visibility = Hidden;
                this._views.order.visibility = Hidden;
                this._views.gridview.visibility = Hidden;
                this._views.empty.visibility = Visible;
                this._views.emptyIcon.visibility = Visible;
                this._views.emptyInfo.text = iRes.getString("ERR_QUERY");
            } else {
                if (cursor && !cursor.isClosed() && cursor.count > 0) {
                    log.I(TAG, "loadVideo, count is", cursor.count);
                    this._views.loading.visibility = Hidden;
                    this._views.order.visibility = Visible;
                    this._views.order.enabled = true; // scanCompleted ? true : false;
                    this._views.gridview.visibility = Visible;
                    let adapter = <LocalAdapter> this._views.gridview.adapter;
                    adapter.setOrderByDate(this._order === sortOrder.DATE_MODIFIED);
                    adapter.cursor = cursor;
                    if (this._order === sortOrder.TITLE) {
                        this._onSortItemTap(this._order);
                    }
                    if (!scanCompleted) {
                        this._views.gridview.arriveAt(0);
                    }
                    this._views.empty.visibility = Hidden;
                } else {
                    log.I(TAG, "loadVideo, count is zero");
                    this._views.order.visibility = Hidden;
                    this._views.gridview.visibility = Hidden;
                    this._views.empty.visibility = Visible;
                    if (scanCompleted) {
                        this._views.loading.visibility = Hidden;
                        this._views.emptyIcon.visibility = Visible;
                        this._views.emptyInfo.text = iRes.getString("ERR_NO_FILE");
                    } else {
                        this._views.emptyIcon.visibility = Hidden;
                        this._views.emptyInfo.text = "";
                    }
                }
            }
        });
    }

    /**
     * 显示排序菜单
     */
    _showSortMenu() {
        log.I(TAG, "_showSortMenu");
        if (!this._path) {
            log.I(TAG, "_showSortMenu, path is null.");
            return;
        }

        if (this._sortMenu && this._sortMenu.isShowing()) {
            log.I(TAG, "_showSortMenu, sort menu is showing");
            return;
        }

        let sortMenu = new PopupMenu();
        sortMenu.anchorView = this._views.orderView;
        sortMenu.pointerDirection = PopupMenu.PointerDirection.Up;
        if (this._order === sortOrder.DEFAULT) {
            sortMenu.addChild(new PopupMenu.PopupMenuItem(iRes.getString("ORDER_DATE_MODIFIED")));
            sortMenu.addChild(new PopupMenu.PopupMenuItem(iRes.getString("ORDER_TITLE")));
            sortMenu.once("result", (index) => {
                switch (index) {
                    case 0:
                        this._onSortItemTap(sortOrder.DATE_MODIFIED);
                        break;
                    case 1:
                        this._onSortItemTap(sortOrder.TITLE);
                        break;
                }
            });
        } else if (this._order === sortOrder.DATE_MODIFIED) {
            sortMenu.addChild(new PopupMenu.PopupMenuItem(iRes.getString("ORDER_DEFAULT")));
            sortMenu.addChild(new PopupMenu.PopupMenuItem(iRes.getString("ORDER_TITLE")));
            sortMenu.once("result", (index) => {
                switch (index) {
                    case 0:
                        this._onSortItemTap(sortOrder.DEFAULT);
                        break;
                    case 1:
                        this._onSortItemTap(sortOrder.TITLE);
                        break;
                }
            });
        } else {
            sortMenu.addChild(new PopupMenu.PopupMenuItem(iRes.getString("ORDER_DEFAULT")));
            sortMenu.addChild(new PopupMenu.PopupMenuItem(iRes.getString("ORDER_DATE_MODIFIED")));
            sortMenu.once("result", (index) => {
                switch (index) {
                    case 0:
                        this._onSortItemTap(sortOrder.DEFAULT);
                        break;
                    case 1:
                        this._onSortItemTap(sortOrder.DATE_MODIFIED);
                        break;
                }
            });
        }

        sortMenu.once("close", () => {
            log.I(TAG, "_showSortMenu, close");
            sortMenu.anchorView = null;
            sortMenu.destroy();
            this._sortMenu = null;
        });
        sortMenu.show();
        this._sortMenu = sortMenu;
    }

    /**
     * 关闭排序菜单
     */
    _closeSortMenu() {
        if (this._sortMenu && this._sortMenu.isShowing()) {
            this._sortMenu.close();
        }
    }

    /**
     * 排序列表选中的回调
     */
    _onSortItemTap(type: string) {
        log.I(TAG, "_onSortItemTap", type);
        if (this._destroyed) {
            log.W(TAG, "_onSortItemTap, presenter is destroyed");
            return;
        }

        let paramObj: { order?: string } = {};
        if (type === sortOrder.DEFAULT) {
            paramObj.order = "default_order";
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_USB, iUserTrackHelper.USB_SORT, paramObj);
            this._views.gridview.width = Config.PAGE_WIDTH - 2 * Config.PAGE_MARGIN;
            this._order = sortOrder.DEFAULT;
            this._views.orderText.text = iRes.getString("ORDER_DEFAULT");
            this._views.scrollbar.visibility = Visible;
            this._views.quickindex.visibility = Hidden;
            this._loadData(true);
        } else if (type === sortOrder.DATE_MODIFIED) {
            paramObj.order = "time_order";
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_USB, iUserTrackHelper.USB_SORT, paramObj);
            this._views.gridview.width = Config.PAGE_WIDTH - 2 * Config.PAGE_MARGIN;
            this._order = sortOrder.DATE_MODIFIED;
            this._views.orderText.text = iRes.getString("ORDER_DATE_MODIFIED");
            this._views.scrollbar.visibility = Visible;
            this._views.quickindex.visibility = Hidden;
            this._loadData(true);
        } else if (type === sortOrder.TITLE) {
            paramObj.order = "alpha_order";
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_USB, iUserTrackHelper.USB_SORT, paramObj);

            let adapter = <LocalAdapter> this._views.gridview.adapter;
            iLocalModel.sortCursor(adapter.cursor, (error, cursor: Cursor, quickIndexObject: { str: string[], array: Object }) => {
                log.I(TAG, "sortCursor", error);
                if (error) {
                    return;
                }
                if (!cursor || cursor.isClosed() || cursor.count === 0) {
                    log.I(TAG, "sortCursor, cursor is null or zero");
                    return;
                }

                this._views.gridview.width = Config.PAGE_WIDTH - Config.PAGE_MARGIN - Config.LOCAL_QUICK_INDEX_WIDTH;
                this._order = sortOrder.TITLE;
                this._views.orderText.text = iRes.getString("ORDER_TITLE");
                this._views.scrollbar.visibility = Hidden;
                this._views.quickindex.visibility = Visible;
                this._views.quickindex.quickIndexStr = quickIndexObject.str;
                (<{ positionArray: Object }><object> this._views.quickindex).positionArray = quickIndexObject.array;
                adapter.setOrderByDate(false);
                adapter.cursor = cursor;
            });
        }
        this._views.gridview.arriveAt(0);
    }

    /**
     * 视频列表选中的回调
     */
    _selectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_selectHandler", position, point, voice);
        if (position < 0 || !itemView) {
            return;
        }

        let adapter = <LocalAdapter> this._views.gridview.adapter;
        if (!adapter) {
            return;
        }

        let videoInfo = <VideoInfo> iLocalModel.getVideoInfo(adapter.cursor, position);
        if (!videoInfo) {
            return;
        }

        let paramObj = {
            usb_name: this._views.title.text,
            usb_seat: this._index,
            video_title: videoInfo.displayName,
            video_size: videoInfo.videoSize,
            video_format: videoInfo.mimeType,
            video_duration: videoInfo.duration
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_USB, iUserTrackHelper.USB_PICK, paramObj);

        let launchData = {
            from: Consts.FromType.LOCAL,
            path: this._path,
            index: position,
            list: adapter
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _navigate(path: string, launchData?: Object, newConfig = {}) {
        if (path === RoutePath.BACK) {
            this._onBack();
        } else {
            if (!this._hidden) {
                if (!launchData) {
                    launchData = {};
                }
                this.context.router.navigate(path, launchData, newConfig);
            } else {
                log.W(TAG, "_navigate negtive ", path);
            }
        }
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }

        if (this._views.navigationBar) {
            Utils.removeVoiceCommand(this._views.navigationBar);
        }
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];

        iVideoModel.off(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
        this._mediaMonitor.release();
        log.I(TAG, "_removeAllListeners done");
    }

    /**
     * 销毁所有view
     */
    _destoryViews() {
        log.I(TAG, "_destoryViews");
        if (this._views.loading) {
            this._views.loading.visibility = Hidden;
            this._views.loading.destroy();
            this._views.loading = null;
        }

        if (this._views.empty) {
            this._views.empty.destroy();
            this._views.empty = null;
        }

        if (this._views.order) {
            this._views.order.destroy();
            this._views.order = null;
        }

        if (this._views.gridview) {
            this._views.gridview.removeAllListeners("itemselect");
            let adapter = <LocalAdapter> this._views.gridview.adapter;
            if (adapter) {
                adapter.registerVoiceSelectListener(null);
                adapter.destroy();
            }
            this._views.gridview.destroy();
            this._views.gridview = null;
        }

        if (this._views.quickindex) {
            this._views.quickindex.removeAllListeners("currentchanged");
            this._views.quickindex.destroy();
            this._views.quickindex = null;
        }

        if (this._views.scrollbar) {
            this._views.scrollbar.destroy();
            this._views.scrollbar = null;
        }

        if (this._views.navigationBar) {
            this._views.navigationBar.removeEventListener("back", this._navBackListener);
            this._navBackListener = null;
        }
        this._views = null;
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._removeVoiceCommands();
        this._removeAllListeners();
        this._destoryViews();
    }
}

export = LocalPresenter;
