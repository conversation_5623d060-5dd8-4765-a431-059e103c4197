import YObject = require("../core/YObject");
import Point = require("./Point");
import Shape = require("./Shape");
import Context = require("./Context");
import Gradient = require("./Gradient");
/**
 * <p>Rectangle that represents by the coordinates of its 4 edges (left, top, right bottom).<br>
 * These fields can be accessed directly. Use width and height to retrieve the rectangle's width and height.<br>
 * Note that most methods do not check to see that the coordinates are sorted correctly (i.e. left <= right and top <= bottom).</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 1
 */
/**
 * <p>Rectangle that represents by the coordinates of its 4 edges (left, top, right bottom).<br>
 * These fields can be accessed directly. Use width and height to retrieve the rectangle's width and height.<br>
 * Note that most methods do not check to see that the coordinates are sorted correctly (i.e. left <= right and top <= bottom).</p>
 * @extends yunos.graphics.Shape
 * @memberof yunos.graphics
 * @public
 * @since 6
 */
declare class Rectangle extends Shape {
    private _left;
    private _top;
    private _right;
    private _bottom;
    private _height;
    private _width;
    /**
     * <p>Constructor that create a rectangle.</p>
     * @param {number} left - the X coordinate of the left side of the rectangle.
     * @param {number} top - the Y coordinate of the top of the rectangle.
     * @param {number} right - the X coordinate of the right side of the rectangle.
     * @param {number} bottom - the Y coordinate of the bottom of the rectangle.
     * @public
     * @since 1
     */
    public constructor(left: number, top: number, right: number, bottom: number);
    /**
     * <p>Destructor that destroy this rectangle.</p>
     * @public
     * @since 1
     */
    public destroy(): void;
    /**
     * <p>The X coordinate of the left side of the rectangle.</p>
     * @name yunos.graphics.Rectangle#left
     * @type {number}
     * @public
     * @since 1
     */
    public left: number;
    /**
     * <p>The Y coordinate of the top side of the rectangle.</p>
     * @name yunos.graphics.Rectangle#top
     * @type {number}
     * @public
     * @since 1
     */
    public top: number;
    /**
     * <p>The X coordinate of the right side of the rectangle.</p>
     * @name yunos.graphics.Rectangle#right
     * @type {number}
     * @public
     * @since 1
     */
    public right: number;
    /**
     * <p>The Y coordinate of the bottom side of the rectangle.</p>
     * @name yunos.graphics.Rectangle#bottom
     * @type {number}
     * @public
     * @since 1
     */
    public bottom: number;
    /**
     * <p>The rectangle's width.</p>
     * <p>This does not check for a valid rectangle (i.e. left <= right) so the result may be negative.</p>
     * @name yunos.graphics.Rectangle#width
     * @type {number}
     * @public
     * @since 1
     */
    public readonly width: number;
    /**
     * <p>The rectangle's height.</p>
     * <p>This does not check for a valid rectangle (i.e. top <= bottom) so the result may be negative.</p>
     * @name yunos.graphics.Rectangle#height
     * @type {number}
     * @public
     * @since 1
     */
    public readonly height: number;
    /**
     * <p>Set the rectangle to an empty rectangle (0,0,0,0).</p>
     * @public
     * @since 1
     */
    public empty(): void;
    /**
     * <p>Set the rectangle's coordinates to the specified values.</p>
     * <p>Note that no range checking is performed, so it is up to the caller to ensure that left <= right and top <= bottom.</p>
     * @method yunos.graphics.Rectangle#assign
     * @param {number} left - the X coordinate of the left side of the rectangle.
     * @param {number} top - the Y coordinate of the top of the rectangle.
     * @param {number} right - the X coordinate of the right side of the rectangle.
     * @param {number} bottom - the Y coordinate of the bottom of the rectangle.
     * @public
     * @since 1
     */
    public assign(left: number, top: number, right: number, bottom: number): void;
    /**
     * <p>Offset the rectangle by adding dx to its left and right coordinates,
     * and adding dy to its top and bottom coordinates.</p>
     * @param {number} dx - the amount to add to the rectangle's left and right coordinates.
     * @param {number} dy - the amount to add to the rectangle's top and bottom coordinates.
     * @public
     * @since 1
     */
    public offset(dx: number, dy: number): void;
    /**
     * <p>Offset the rectangle to a specific (left, top) position,
     * keeping its width and height the same.</p>
     * @param {number} left - the new left coordinate for the rectangle.
     * @param {number} top  - the new top coordinate for the rectangle.
     * @public
     * @since 1
     */
    public offsetTo(left: number, top: number): void;
    /**
     * <p>Update this rectangle to enclose itself and the specified rectangle.</p>
     * <p>If the specified rectangle is empty, nothing is done. If this rectangle is empty it is set to the specified rectangle.</p>
     * @param {yunos.graphics.Rectangle} rect - the rectangle to be combined with this rectangle.
     * @public
     * @since 1
     */
    public unite(rect: Rectangle): void;
    /**
     * <p>Update this rectangle to the intersection of this rectangle with the specified rectangle.</p>
     * @param {yunos.graphics.Rectangle} rect - the rectangle to be intersected with this rectangle.
     * @public
     * @since 1
     */
    public intersect(rect: Rectangle): void;
    /**
     * <p>Indicates whether the rectangle is empty.</p>
     * @return {boolean} true if the rectangle is empty (left >= right or top >= bottom).
     * @public
     * @since 1
     */
    public isEmpty(): boolean;
    /**
     * <p>Indicates whether this rectangle intersects the specified rectangle.</p>
     * @param {yunos.graphics.Rectangle} rect - the rectangle being intersected with this rectangle.
     * @return {boolean} true if this rectangle intersects the specified rectangle, otherwise false.
     * @public
     * @since 1
     */
    public intersects(rect: Rectangle): boolean;
    /**
     * <p>Indicates whether this rectangle touches the specified rectangle.</p>
     * @param {yunos.graphics.Rectangle} rect - the rectangle being touched with this rectangle.
     * @return {boolean} true if this rectangle touches the specified rectangle, otherwise false.
     * @public
     * @since 1
     */
    public touches(rect: Rectangle): boolean;
    /**
     * <p>Indicates whether the specified rectangle is inside or equal to this rectangle.</p>
     * <p>An empty rectangle never contains another rectangle.</p>
     * @param {yunos.graphics.Rectangle} rect - the rectangle being tested for containment.
     * @return {boolean} true if the specified rectangle is inside or equal to this rectangle, otherwise false.
     * @public
     * @since 1
     */
    public contains(rect: Rectangle): boolean;
    /**
     * <p>Indicates whether the specified point is inside this rectangle.</p>
     * <p>The left and top are considered to be inside, while the right and bottom are not.</p>
     * <p>This means that for a x,y to be contained: left <= x < right and top <= y < bottom.</p>
     * <p>An empty rectangle never contains any point.</p>
     * @param {number} x - the X coordinate of the point being tested for containment.
     * @param {number} y - the Y coordinate of the point being tested for containment.
     * @return {boolean} true if (x,y) is inside the rectangle, otherwise false.
     * @public
     * @since 1
     */
    public containsXY(x: number, y: number): boolean;
    /**
     * <p>Indicates whether the specified point is inside this rectangle.</p>
     * <p>The left and top are considered to be inside,
     * while the right and bottom are not.</p>
     * <p>This means that for a x,y to be contained: left <= x < right and top <= y < bottom.</p>
     * <p>An empty rectangle never contains any point.</p>
     * @param {yunos.graphics.Point} point - the point being tested for containment.
     * @return {boolean} true if the point is inside the rectangle, otherwise false.
     * @public
     * @since 1
     */
    public containsPoint(point: Point): boolean;
    /**
     * <p>Clone a new rectangle from this rectangle.</p>
     * @return {yunos.graphics.Rectangle} a new rectangle that has the same left, top, right and bottom value with this rectangle.
     * @override
     * @public
     * @since 1
     */
    public clone(): Rectangle;
    /**
     * <p>Check whether this rectangle equals a specified rectangle.</p>
     * @param {yunos.graphics.Rectangle} rect - the specified rectangle.
     * @return {boolean} true means equal, otherwise false.
     * @override
     * @public
     * @since 1
     */
    public equals(rect: YObject): boolean;
    /**
     * <p>Returns a human-readable rectangle string.</p>
     * @return {string} the rectangle string.
     * @override
     * @public
     * @since 1
     */
    public toString(): string;
    /**
     * <p>Draw this Rectangle in canvas</p>
     * @param {yunos.graphics.Context} ctx - the canvas context
     * @param {(string | number | yunos.graphics.Gradient)} fillStyle - the style of shape filling
     * @friend
     */
    drawCanvas(ctx: Context, fillStyle: string | number | Gradient): void;
}
export = Rectangle;
