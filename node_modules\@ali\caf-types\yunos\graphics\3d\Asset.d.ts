/// <reference types="node" />
import YObject = require("yunos/core/YObject");
/**
 * The root structure of the imported data.
 * @typedef {Object} yunos.graphics.3d.Asset~Scene
 * @property {number} flag - Any combination of the import flags.
 * @property {string} path - The path of the model file.
 * @property {string} directory - The directory of the model file.
 * @property {yunos.graphics.3d.Asset~Node} rootNode - The root node of the hierarchy.
 * @property {yunos.graphics.3d.Asset~Mesh[]} meshes - The array of meshes.meshes, can be undefined.
 * @property {yunos.graphics.3d.Asset~Material[]} materials - The array of materials, can be undefined.
 * @property {yunos.graphics.3d.Asset~Animation[]} animations - The array of animations, can be undefined.
 * @property {yunos.graphics.3d.Asset~Texture[]} textures - The array of textures, can be undefined.
 * @property {yunos.graphics.3d.Asset~Light[]} lights - The array of lights, can be undefined.
 * @property {yunos.graphics.3d.Asset~Camera[]} cameras - The array of cameras, can be undefined.
 * @public
 * @since 5
 */
/**
 * Helper structure to describe a virtual camera.
 * @typedef {Object} yunos.graphics.3d.Asset~Camera
 * @property {string} name - The name of the camera.
 * @property {number} aspect - Screen aspect ratio.
 * @property {number} clipPlaneFar - Distance of the far clipping plane from the camera.
 * @property {number} clipPlaneNear - Distance of the near clipping plane from the camera.
 * @property {number} horizontalFOV - Half horizontal field of view angle, in radians.
 * @property {number[]} up - 3D vector of the camera coordinate system relative to
 * the coordinate space defined by the corresponding node.
 * @property {number[]} lookAt - 3D vector of the camera coordinate system relative to
 * the coordinate space defined by the corresponding node.
 * @public
 * @since 5
 */
/**
 * Helper structure to describe a light source.
 * @typedef {Object} yunos.graphics.3d.Asset~Light
 * @property {string} name - The name of the light source.
 * @property {number} type - The type of the light source. The member of the Asset.LightSourceType.
 * @property {number} angleInnerCone - Inner angle of a spot light's light cone, can be undefined.
 * @property {number} angleOuterCone - Outer angle of a spot light's light cone, can be undefined.
 * @property {number} attenuationConstant - Constant light attenuation factor.
 * @property {number} attenuationLinear - Linear light attenuation factor.
 * @property {number} attenuationQuadratic - Quadratic light attenuation factor.
 * @property {number[]} colorDiffuse - Diffuse color of the light source. The value is specified as 3D vector.
 * @property {number[]} colorSpecular - Specular color of the light source. The value is specified as 3D vector.
 * @property {number[]} colorAmbient - Ambient color of the light source. The value is specified as 3D vector.
 * @property {number[]} direction - Direction of the light source in space. The value is specified as 3D vector, can be undefined.
 * @property {number[]} position - Position of the light source in space. The value is specified as 3D vector, can be undefined.
 * @public
 * @since 5
 */
/**
 * Helper structure to describe an embedded texture.
 * @typedef {Object} yunos.graphics.3d.Asset~Texture
 * @property {number} width - Width of the texture, in pixels.
 * @property {number} height - Height of the texture, in pixels.
 * If this value is zero, data points to an compressed texture
 * in any format (e.g. JPEG).
 * @property {string} achFormatHint - A hint from the loader to make it easier for applications
 * to determine the type of embedded textures.
 * @property {Buffer} data - Data of the texture.
 * @public
 * @since 5
 */
/**
 * The root structure of the imported data.
 * @typedef {Object} yunos.graphics.3d.Asset~Scene
 * @property {number} flag - Any combination of the import flags.
 * @property {string} path - The path of the model file.
 * @property {string} directory - The directory of the model file.
 * @property {yunos.graphics.3d.Asset~Node} rootNode - The root node of the hierarchy.
 * @property {yunos.graphics.3d.Asset~Mesh[]} meshes - The array of meshes.meshes, can be undefined.
 * @property {yunos.graphics.3d.Asset~Material[]} materials - The array of materials, can be undefined.
 * @property {yunos.graphics.3d.Asset~Animation[]} animations - The array of animations, can be undefined.
 * @public
 * @since 4
 */
/**
 * Data structure for material property.
 * @typedef {Object} yunos.graphics.3d.Asset~MaterialProp
 * @property {string} key - Specifies the name of the property.
 * @property {yunos.graphics.3d.Asset~TextureType} semantic - Textures: Specifies their exact usage semantic.
 * @property {number} index - Textures: Specifies the index of the texture.
 * @property {yunos.graphics.3d.Asset~PropertyTypeInfo} type - Type information for the property.
 * @property {string|number[]|number|Buffer} value - The property's value.
 * @public
 * @since 4
 */
/**
 * Data structure for a material.
 * @typedef {Object} yunos.graphics.3d.Asset~Material
 * @property {yunos.graphics.3d.Asset~MaterialProp[]} properties - The properties of material.
 * @public
 * @since 4
 */
/**
 * A mesh represents a geometry or model with a single material.
 * @typedef {Object} yunos.graphics.3d.Asset~Mesh
 * @property {string} name - Name of the mesh.
 * @property {Float32Array} positions - Vertex positions, can be undefined.
 * @property {Float32Array} normals - Vertex normals, can be undefined.
 * @property {Float32Array} tangents - Vertex tangents, can be undefined.
 * @property {Float32Array} bitangents - Vertex bitangents, can be undefined.
 * @property {Float32Array[]} textureCoords - Vertex texture coords, also known as UV channels.
 * @property {Float32Array[]} colors - Vertex color sets.
 * @property {number[]} numUVComponents - Specifies the number of components for a given UV channel.
 * @property {number[]} indices - The indices.
 * @property {yunos.graphics.3d.Asset~PrimitiveType} primitiveTypes - Bitwise combination of the members of the Asset.PrimitiveType.
 * @property {number} materialIndex - The material used by this mesh. A mesh uses only a single material.
 * @property {yunos.graphics.3d.Asset~Bone[]} bones - The bones of this mesh, can be undefined.
 * @public
 * @since 4
 */
/**
 * A single influence of a bone on a vertex.
 * @typedef {Object} yunos.graphics.3d.Asset~BoneWeight
 * @property {number} vertexId - Index of the vertex which is influenced by the bone.
 * @property {number} weight - The strength of the influence in the range (0...1).
 * @public
 * @since 4
 */
/**
 * Bone of a mesh.
 * @typedef {Object} yunos.graphics.3d.Asset~Bone
 * @property {string} name - The name of the bone.
 * @property {number[]} offsetMatrix - Matrix that transforms from mesh space to bone space in bind pose. Note: offsetMatrix is a row-major 4x4 matrix.
 * @property {yunos.graphics.3d.Asset~BoneWeight[]} weights - The vertices affected by this bone.
 * @public
 * @since 4
 */
/**
 * Node in the imported hierarchy.
 * @typedef {Object} yunos.graphics.3d.Asset~Node
 * @property {string} name - The name of the node.
 * @property {number[]} transformation - The transformation relative to the node's parent. Note: transformation is a row-major 4x4 matrix.
 * @property {number[]} meshes - The meshes of this node. Each entry is an index into the mesh list scene.
 * @property {yunos.graphics.3d.Asset~Node[]} children - The child nodes of this node.
 * @public
 * @since 4
 */
/**
 * An animation consists of key-frame data for a number of nodes.
 * @typedef {Object} yunos.graphics.3d.Asset~Animation
 * @property {string} name - The name of the animation.
 * @property {number[]} ticksPerSecond - Ticks per second. 0 if not specified in the imported file.
 * @property {number[]} duration - Duration of the animation in ticks.
 * @property {yunos.graphics.3d.Asset~NodeAnim[]} channels - The node animation channels. Each channel affects a single node.
 * @public
 * @since 4
 */
/**
 * A time-value pair specifying a certain vector for the given time.
 * @typedef {Object} yunos.graphics.3d.Asset~NodeAnimValue
 * @property {number} time - The time of this key.
 * @property {number[]} value - The value of this key. Maybe 3D/4D vectors.
 * @public
 * @since 4
 */
/**
 * Describes the animation of a single node.
 * @typedef {Object} yunos.graphics.3d.Asset~NodeAnim
 * @property {string} name - The name of the node affected by this animation.
 * @property {number} preState - Defines how the animation behaves before the first key is encountered.
 * @property {number} postState - Defines how the animation behaves after the last key was processed.
 * @property {yunos.graphics.3d.Asset~NodeAnimValue} positionKeys - The position keys of this animation channel. Positions are specified as 3D vector.
 * @property {yunos.graphics.3d.Asset~NodeAnimValue} rotationKeys - The rotation keys of this animation channel. Rotations are given as 4D vectors.
 * @property {yunos.graphics.3d.Asset~NodeAnimValue} scalingKeys - The scaling keys of this animation channel. Scalings are specified as 3D vector.
 * @public
 * @since 4
 */
/**
 * <p>Load 3D model files.</p>
 *
 * @example
 *
 * Asset.load(R.getRawFileSrc("raw/nano.obj"), (err, scene) => {});
 * let scene = Asset.loadSync(R.getRawFileSrc("raw/nano.obj"));
 * const fs = require("fs");
 * fs.writeFileSync("/tmp/scene.json", JSON.stringify(scene, null, 2), "utf8");
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics.3d
 * @public
 * @since 4
 */
declare class Asset extends YObject {
    private static getAbsPath;
    /**
     * <p>Load model and returns its contents if successful.</p>
     * @param {string} path - File's path, the path of file to be load.
     * @param {number} [flag] - Post processing flags to be executed after a successful import. <br>
     * The default value is MaxQuality | FlipUVs.
     * @return {yunos.graphics.3d.Asset~Scene} scene - The imported data, make sure do live longer than it's fields. <br>
     * Use `JSON.stringify(scene, null, 2)` to look at the structure of returned object.
     * @public
     * @since 4
     */
    public static loadSync(path: string, flag?: number): Asset.Scene;
    /**
     * <p>This function is used for the {@link load} method.</p>
     * @callback yunos.graphics.3d.Asset~loadCallback
     * @param {Error|null} err - The error object which contains details when the invocation is failed.
     * @param {yunos.graphics.3d.Asset~Scene} [scene] - The imported data, make sure do live longer than it's fields.
     * @public
     * @since 4
     */
    /**
     * <p>Asynchronous loads a model. See the {@link loadSync} method.</p>
     * @param {string} path - File's path, the path of file to be load.
     * @param {yunos.graphics.3d.Asset~loadCallback} callback - The callback will be invoked when load completed.
     * @param {number} [flag] - Post processing flags to be executed after a successful import. <br>
     * The default value is MaxQuality | FlipUVs.
     * @public
     * @since 4
     */
    public static load(path: string, callback: (err: Error, scene?: Asset.Scene) => void, flag?: number): void;
    /**
     * <p>Type information for the material property.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly PropertyTypeInfo: {
        /**
         * <p>Array of single-precision floats.</p>
         * @public
         * @since 4
         */
        Float: int;
        /**
         * <p>Array of double-precision floats.</p>
         * @public
         * @since 4
         */
        Double: int;
        /**
         * <p>The material property is a string.</p>
         * @public
         * @since 4
         */
        String: int;
        /**
         * <p>Array of integers.</p>
         * @public
         * @since 4
         */
        Integer: int;
        /**
         * <p>Simple binary buffer, content undefined.</p>
         * @public
         * @since 4
         */
        Buffer: int;
    };
    /**
     * <p>Definitions for import post processing steps. <br>
     * The flag of Fast means import speed is fast, but the Quality configuration
     * performs some extra optimizations to improve rendering speed and
     * to minimize memory usage. <br>
     * The MaxQuality enables almost every optimization step to achieve perfectly
     * optimized data.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly ImportFlag: {
        /**
         * <p>Calculates the tangents and bitangents for the imported meshes.</p>
         * @public
         * @since 4
         */
        CalcTangentSpace: int;
        /**
         * <p>Identifies and joins identical vertex data sets within all imported meshes.</p>
         * @public
         * @since 4
         */
        JoinIdenticalVertices: int;
        /**
         * <p>Converts all the imported data to a left-handed coordinate space.</p>
         * @public
         * @since 4
         */
        MakeLeftHanded: int;
        /**
         * <p>Triangulates all faces of all meshes.</p>
         * @public
         * @since 4
         */
        Triangulate: int;
        /**
         * <p>Removes some parts of the data structure (animations, materials,
         * light sources, cameras, textures, vertex components).</p>
         * @public
         * @since 4
         */
        RemoveComponent: int;
        /**
         * <p>Generates normals for all faces of all meshes.</p>
         * @public
         * @since 4
         */
        GenNormals: int;
        /**
         * <p>Generates smooth normals for all vertices in the mesh.</p>
         * @public
         * @since 4
         */
        GenSmoothNormals: int;
        /**
         * <p>Splits large meshes into smaller sub-meshes.</p>
         * @public
         * @since 4
         */
        SplitLargeMeshes: int;
        /**
         * <p>Removes the node graph and pre-transforms all vertices with
         * the local transformation matrices of their nodes.</p>
         * @public
         * @since 4
         */
        PreTransformVertices: int;
        /**
         * <p>Limits the number of bones simultaneously affecting a single vertex
         * to a maximum value.</p>
         * @public
         * @since 4
         */
        LimitBoneWeights: int;
        /**
         * <p>Validates the imported scene data structure.
         * This makes sure that all indices are valid, all animations and
         * bones are linked correctly, all material references are correct .. etc.</p>
         * @public
         * @since 4
         */
        ValidateDataStructure: int;
        /**
         * <p>Reorders triangles for better vertex cache locality.</p>
         * @public
         * @since 4
         */
        ImproveCacheLocality: int;
        /**
         * <p>Searches for redundant/unreferenced materials and removes them.</p>
         * @public
         * @since 4
         */
        RemoveRedundantMaterials: int;
        /**
         * <p>This step tries to determine which meshes have normal vectors
         * that are facing inwards and inverts them.</p>
         * @public
         * @since 4
         */
        FixInfacingNormals: int;
        /**
         * <p>This step splits meshes with more than one primitive type in
         * homogeneous sub-meshes.</p>
         * @public
         * @since 4
         */
        SortByPType: int;
        /**
         * <p>This step searches all meshes for degenerate primitives and
         * converts them to proper lines or points.</p>
         * @public
         * @since 4
         */
        FindDegenerates: int;
        /**
         * <p>This step searches all meshes for invalid data, such as zeroed
         * normal vectors or invalid UV coords and removes/fixes them. This is
         * intended to get rid of some common exporter errors.</p>
         * @public
         * @since 4
         */
        FindInvalidData: int;
        /**
         * <p>This step converts non-UV mappings (such as spherical or
         * cylindrical mapping) to proper texture coordinate channels.</p>
         * @public
         * @since 4
         */
        GenUVCoords: int;
        /**
         * <p>This step applies per-texture UV transformations and bakes
         * them into stand-alone vtexture coordinate channels.</p>
         * @public
         * @since 4
         */
        TransformUVCoords: int;
        /**
         * <p>This step searches for duplicate meshes and replaces them
         * with references to the first mesh.</p>
         * @public
         * @since 4
         */
        FindInstances: int;
        /**
         * <p>A postprocessing step to reduce the number of meshes.</p>
         * @public
         * @since 4
         */
        OptimizeMeshes: int;
        /**
         * <p>A postprocessing step to optimize the scene hierarchy.</p>
         * @public
         * @since 4
         *
         */
        OptimizeGraph: int;
        /**
         * <p>This step flips all UV coordinates along the y-axis and adjusts
         * material settings and bitangents accordingly.</p>
         * @public
         * @since 4
         */
        FlipUVs: int;
        /**
         * <p>This step adjusts the output face winding order to be CW.</p>
         * @public
         * @since 4
         */
        FlipWindingOrder: int;
        /**
         * <p>This step splits meshes with many bones into sub-meshes so that each
         * su-bmesh has fewer or as many bones as a given limit.</p>
         * @public
         * @since 4
         */
        SplitByBoneCount: int;
        /**
         * <p>This step removes bones losslessly or according to some threshold.</p>
         * @public
         * @since 4
         */
        Debone: int;
        /**
         * <p>This step will perform a global scale of the model.</p>
         * @public
         * @since 4
         */
        GlobalScale: int;
        /**
         * <p>Optimizing the data for real-time rendering. <br>
         * This preset consists of CalcTangentSpace, GenNormals, JoinIdenticalVertices,
         * Triangulatem, GenUVCoords, SortByPType.</p>
         * @public
         * @since 4
         */
        Fast: int;
        /**
         * <p>This configuration performs some extra optimizations to improve rendering speed and
         * to minimize memory usage. It could be a good choice for a level editor
         * environment where import speed is not so important. <br>
         * This preset consists of CalcTangentSpace, GenSmoothNormals, JoinIdenticalVertices,
         * ImproveCacheLocality, LimitBoneWeights, RemoveRedundantMaterials, SplitLargeMeshes,
         * Triangulate, GenUVCoords, SortByPType, FindDegenerates, FindInvalidData.</p>
         * @public
         * @since 4
         */
        Quality: int;
        /**
         * <p>This preset enables almost every optimization step to achieve perfectly
         * optimized data. It's your choice for level editor environments where import speed
         * is not important. <br>
         * This preset consists of Quality, FindInstances, ValidateDataStructure, OptimizeMeshes.</p>
         * @public
         * @since 4
         */
        MaxQuality: int;
    };
    /**
     * <p>Enumerates the types of geometric primitives for mesh.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly PrimitiveType: {
        /**
         * <p>A point primitive.</p>
         * @public
         * @since 4
         */
        POINT: int;
        /**
         * <p>A line primitive.</p>
         * @public
         * @since 4
         */
        LINE: int;
        /**
         * <p>A triangular primitive.</p>
         * @public
         * @since 4
         */
        TRIANGLE: int;
        /**
         * <p>A higher-level polygon with more than 3 edges.</p>
         * @public
         * @since 4
         */
        POLYGON: int;
    };
    /**
     * <p>Defines the purpose of a texture.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     */
    public static readonly TextureType: {
        /**
         * <p>Dummy value.</p>
         * @public
         * @since 4
         */
        NONE: int;
        /**
         * <p>The texture is combined with the result of the diffuse
         * lighting equation.</p>
         * @public
         * @since 4
         */
        DIFFUSE: int;
        /**
         * <p>The texture is combined with the result of the specular
         * lighting equation.</p>
         * @public
         * @since 4
         */
        SPECULAR: int;
        /**
         * <p>The texture is combined with the result of the ambient
         * lighting equation.</p>
         * @public
         * @since 4
         */
        AMBIENT: int;
        /**
         * <p>The texture is added to the result of the lighting
         * calculation. It isn't influenced by incoming light.</p>
         * @public
         * @since 4
         */
        EMISSIVE: int;
        /**
         * <p>The texture is a height map.</p>
         * @public
         * @since 4
         */
        HEIGHT: int;
        /**
         * <p>The texture is a (tangent space) normal-map.</p>
         * @public
         * @since 4
         */
        NORMALS: int;
        /**
         * <p>The texture defines the glossiness of the material.</p>
         * @public
         * @since 4
         */
        SHININESS: int;
        /**
         * <p>The texture defines per-pixel opacity.</p>
         * @public
         * @since 4
         */
        OPACITY: int;
        /**
         * <p>Displacement texture.</p>
         * @public
         * @since 4
         */
        DISPLACEMENT: int;
        /**
         * <p>Lightmap texture.</p>
         * @public
         * @since 4
         */
        LIGHTMAP: int;
        /**
         * <p>Reflection texture.</p>
         * @public
         * @since 4
         */
        REFLECTION: int;
        /**
         * <p>Unknown texture.</p>
         * @public
         * @since 4
         */
        UNKNOWN: int;
    };
    /**
     * <p>Defines the supported types of light sources.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly LightSourceType: {
        LightSource_UNDEFINED: int;
        /**
         * <p>A directional light source has a well-defined direction but is infinitely far away.</p>
         * @public
         * @since 5
         */
        LightSource_DIRECTIONAL: int;
        /**
         * <p>A point light source has a well-defined position in space but no direction - it emits light in all directions.</p>
         * @public
         * @since 5
         */
        LightSource_POINT: int;
        /**
         * <p>A spot light source emits light in a specific angle. It has a position and a direction it is pointing to.</p>
         * @public
         * @since 5
         */
        LightSource_SPOT: int;
        /**
         * <p>The generic light level of the world, including the bounces of all other light sources.
         * Typically, there's at most one ambient light in a scene.</p>
         * @public
         * @since 5
         */
        LightSource_AMBIENT: int;
        /**
         * <p>An area light is a rectangle with predefined size that uniformly emits light from one of its sides.</p>
         * @public
         * @since 5
         */
        LightSource_AREA: int;
    };
}
declare namespace Asset {
    /**
     * <p>The root structure of the imported data.</p>
     * @public
     * @since 4
     */
    interface Scene {
        /**
         * <p>Any combination of the import flags.</p>
         * @public
         * @since 4
         */
        readonly flag: number;
        /**
         * <p>The path of the model file.</p>
         * @public
         * @since 4
         */
        readonly path: string;
        /**
         * <p>The directory of the model file.</p>
         * @public
         * @since 4
         */
        readonly directory: string;
        /**
         * <p>The root node of the hierarchy.</p>
         * @public
         * @since 4
         */
        readonly rootNode: Asset.Node;
        /**
         * <p>The array of meshes.</p>
         * @public
         * @since 4
         */
        readonly meshes: Asset.Mesh[];
        /**
         * <p>The array of materials.</p>
         * @public
         * @since 4
         */
        readonly materials: Asset.Material[];
        /**
         * <p>The array of animations.</p>
         * @public
         * @since 4
         */
        readonly animations: Asset.Animation[];
        /**
         * <p>The array of lights.</p>
         * @public
         * @since 5
         */
        readonly lights: Asset.Light[];
        /**
         * <p>The array of cameras.</p>
         * @public
         * @since 5
         */
        readonly cameras: Asset.Camera[];
        /**
         * <p>The array of textures.</p>
         * @public
         * @since 5
         */
        readonly textures: Asset.Texture[];
    }
    /**
     * <p>Helper structure to describe an embedded texture.</p>
     * @public
     * @since 5
     */
    interface Texture {
        /**
         * <p>Width of the texture, in pixels.</p>
         * @public
         * @since 5
         */
        readonly width: number;
        /**
         * <p>Height of the texture, in pixels.
         * If this value is zero, data points to an compressed texture
         * in any format (e.g. JPEG).</p>
         * @public
         * @since 5
         */
        readonly height: number;
        /**
         * <p>A hint from the loader to make it easier for applications
         * to determine the type of embedded textures.</p>
         * @public
         * @since 5
         */
        readonly achFormatHint: string;
        /**
         * <p>Data of the texture.</p>
         * @public
         * @since 5
         */
        readonly data: Buffer;
    }
    /**
     * <p>Helper structure to describe a virtual camera.</p>
     * @public
     * @since 5
     */
    interface Camera {
        /**
         * <p>The name of the camera.</p>
         * @public
         * @since 5
         */
        readonly name: string;
        /**
         * <p>Screen aspect ratio.</p>
         * @public
         * @since 5
         */
        readonly aspect: number;
        /**
         * <p>Distance of the far clipping plane from the camera.</p>
         * @public
         * @since 5
         */
        readonly clipPlaneFar: number;
        /**
         * <p>Distance of the near clipping plane from the camera.</p>
         * @public
         * @since 5
         */
        readonly clipPlaneNear: number;
        /**
         * <p>Half horizontal field of view angle, in radians.</p>
         * @public
         * @since 5
         */
        readonly horizontalFOV: number;
        /**
         * <p>3D vector of the camera coordinate system relative to
         * the coordinate space defined by the corresponding node.</p>
         * @public
         * @since 5
         */
        readonly up: number[];
        /**
         * <p>3D vector of the camera coordinate system relative to
         * the coordinate space defined by the corresponding node.</p>
         * @public
         * @since 5
         */
        readonly lookAt: number[];
    }
    /**
     * <p>Helper structure to describe a light source.</p>
     * @public
     * @since 5
     */
    interface Light {
        /**
         * <p>The name of the light source.</p>
         * @public
         * @since 5
         */
        readonly name: string;
        /**
         * <p>The type of the light source. The member of the Asset.LightSourceType.</p>
         * @public
         * @since 5
         */
        readonly type: number;
        /**
         * <p>Inner angle of a spot light's light cone.</p>
         * @public
         * @since 5
         */
        readonly angleInnerCone?: number;
        /**
         * <p>Outer angle of a spot light's light cone.</p>
         * @public
         * @since 5
         */
        readonly angleOuterCone?: number;
        /**
         * <p>Constant light attenuation factor.</p>
         * @public
         * @since 5
         */
        readonly attenuationConstant: number;
        /**
         * <p> Linear light attenuation factor.</p>
         * @public
         * @since 5
         */
        readonly attenuationLinear: number;
        /**
         * <p>Quadratic light attenuation factor.</p>
         * @public
         * @since 5
         */
        readonly attenuationQuadratic: number;
        /**
         * <p>Diffuse color of the light source. The value is specified as 3D vector.</p>
         * @public
         * @since 5
         */
        readonly colorDiffuse: number[];
        /**
         * <p>Specular color of the light source. The value is specified as 3D vector.</p>
         * @public
         * @since 5
         */
        readonly colorSpecular: number[];
        /**
         * <p>Ambient color of the light source. The value is specified as 3D vector.</p>
         * @public
         * @since 5
         */
        readonly colorAmbient: number;
        /**
         * <p>Direction of the light source in space. The value is specified as 3D vector.</p>
         * @public
         * @since 5
         */
        readonly direction?: number[];
        /**
         * <p>Position of the light source in space. The value is specified as 3D vector.</p>
         * @public
         * @since 5
         */
        readonly position?: number[];
    }
    /**
     * <p>Data structure for material property.</p>
     * @public
     * @since 4
     */
    interface MaterialProp {
        /**
         * <p>Specifies the name of the property.</p>
         * @public
         * @since 4
         */
        readonly key: string;
        /**
         * <p>Textures: Specifies their exact usage semantic.</p>
         * @public
         * @since 4
         */
        readonly semantic: number;
        /**
         * <p>Textures: Specifies the index of the texture.</p>
         * @public
         * @since 4
         */
        readonly index: number;
        /**
         * <p>Type information for the property.</p>
         * @public
         * @since 4
         */
        readonly type: number;
        /**
         * <p>The property's value.</p>
         * @public
         * @since 4
         */
        readonly value: string | number[] | number | Buffer;
    }
    /**
     * <p>Data structure for a material.</p>
     * @public
     * @since 4
     */
    interface Material {
        /**
         * <p>The properties of material.</p>
         * @public
         * @since 4
         */
        readonly properties: Asset.MaterialProp[];
    }
    /**
     * <p>A mesh represents a geometry or model with a single material.</p>
     * @public
     * @since 4
     */
    interface Mesh {
        /**
         * <p>Name of the mesh.</p>
         * @public
         * @since 4
         */
        readonly name: string;
        /**
         * <p>Vertex positions.</p>
         * @public
         * @since 4
         */
        readonly positions: Float32Array;
        /**
         * <p>Vertex normals.</p>
         * @public
         * @since 4
         */
        readonly normals: Float32Array;
        /**
         * <p>Vertex tangents.</p>
         * @public
         * @since 4
         */
        readonly tangents: Float32Array;
        /**
         * <p>Vertex bitangents.</p>
         * @public
         * @since 4
         */
        readonly bitangents: Float32Array;
        /**
         * <p>Vertex texture coords, also known as UV channels.</p>
         * @public
         * @since 4
         */
        readonly textureCoords: Float32Array[];
        /**
         * <p>Vertex color sets.</p>
         * @public
         * @since 4
         */
        readonly colors: Float32Array[];
        /**
         * <p>Specifies the number of components for a given UV channel.</p>
         * @public
         * @since 4
         */
        readonly numUVComponents: number[];
        /**
         * <p>The indices.</p>
         * @public
         * @since 4
         */
        readonly indices: number[];
        /**
         * <p>Bitwise combination of the members of the Asset.PrimitiveType.</p>
         * @public
         * @since 4
         */
        readonly primitiveTypes: number;
        /**
         * <p>The material used by this mesh. A mesh uses only a single material.</p>
         * @public
         * @since 4
         */
        readonly materialIndex: number;
        /**
         * <p>The bones of this mesh.</p>
         * @public
         * @since 4
         */
        readonly bones: Asset.Bone[];
    }
    /**
     * <p>Bone of a mesh.</p>
     * @public
     * @since 4
     */
    interface Bone {
        /**
         * <p>The name of the bone.</p>
         * @public
         * @since 4
         */
        name: string;
        /**
         * <p>Matrix that transforms from mesh space to bone space in bind pose. Note: offsetMatrix is a row-major 4x4 matrix.</p>
         * @public
         * @since 4
         */
        offsetMatrix: number[];
        /**
         * <p>The vertices affected by this bone.</p>
         * @public
         * @since 4
         */
        weights: {
            vertexId: number;
            weight: number;
        }[];
    }
    /**
     * <p>Node in the imported hierarchy.</p>
     * @public
     * @since 4
     */
    interface Node {
        /**
         * <p>The name of the node.</p>
         * @public
         * @since 4
         */
        readonly name: string;
        /**
         * <p>The transformation relative to the node's parent. Note: transformation is a row-major 4x4 matrix.</p>
         * @public
         * @since 4
         */
        readonly transformation: number[];
        /**
         * <p>The meshes of this node. Each entry is an index into the mesh list scene.</p>
         * @public
         * @since 4
         */
        readonly meshes: number[];
        /**
         * <p>The child nodes of this node.</p>
         * @public
         * @since 4
         */
        readonly children: Asset.Node[];
    }
    /**
     * <p>An animation consists of key-frame data for a number of nodes.</p>
     * @public
     * @since 4
     */
    interface Animation {
        /**
         * <p>The name of the animation.</p>
         * @public
         * @since 4
         */
        readonly name: string;
        /**
         * <p>Ticks per second. 0 if not specified in the imported file.</p>
         * @public
         * @since 4
         */
        readonly ticksPerSecond: number[];
        /**
         * <p>Duration of the animation in ticks.</p>
         * @public
         * @since 4
         */
        readonly duration: number[];
        /**
         * <p>The node animation channels. Each channel affects a single node.</p>
         * @public
         * @since 4
         */
        readonly channels: Asset.NodeAnim[];
    }
    /**
     * <p>Describes the animation of a single node.</p>
     * @public
     * @since 4
     */
    interface NodeAnim {
        /**
         * <p>The name of the node affected by this animation.</p>
         * @public
         * @since 4
         */
        name: string;
        /**
         * <p>Defines how the animation behaves before the first key is encountered.</p>
         * @public
         * @since 4
         */
        preState: number;
        /**
         * <p>Defines how the animation behaves after the last key was processed.</p>
         * @public
         * @since 4
         */
        postState: number;
        /**
         * <p>The position keys of this animation channel. Positions are specified as 3D vector.</p>
         * @public
         * @since 4
         */
        positionKeys: {
            time: number;
            value: number[];
        }[];
        /**
         * <p>The rotation keys of this animation channel. Rotations are
         * given as quaternions, which are 4D vectors.</p>
         * @public
         * @since 4
         */
        rotationKeys: {
            time: number;
            value: number[];
        }[];
        /**
         * <p>The scaling keys of this animation channel. Scalings are specified as 3D vector.</p>
         * @public
         * @since 4
         */
        scalingKeys: {
            time: number;
            value: number[];
        }[];
    }
}
export = Asset;
