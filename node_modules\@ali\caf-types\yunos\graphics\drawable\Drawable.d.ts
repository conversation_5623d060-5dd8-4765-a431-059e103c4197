import YObject = require("../../core/YObject");
import View = require("../../ui/view/View");
/**
 * Drawable is a abstract class which can customize view's display.
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics.drawable
 * @public
 * @abstract
 * @since 6
 */
declare abstract class Drawable extends YObject {
    private _isDrawing: boolean;
    private _associateView: View;
    /**
     * this drawable's associated view
     * @type {yunos.ui.view.View}
     * @name yunos.graphics.drawable.Drawable#associateView
     * @public
     * @since 6
     */
    public associateView: View;
    /**
     * current drawable is drawing or not
     *
     * @readonly
     * @type {boolean}
     * @name yunos.graphics.drawable.Drawable#isDrawing
     * @public
     * @since 6
     */
    public readonly isDrawing: boolean;
    /**
     * start drawing process
     *
     * @public
     * @since 6
     */
    public abstract startDraw(): void;
    /**
     * stop drawing process
     *
     * @public
     * @since 6
     */
    public abstract stopDraw(): void;
    /**
     * this method will be called right after set associateView.
     * implement this function to add event listeners after binding to a view if needed.
     *
     * @abstract
     * @protected
     * @since 6
     */
    protected abstract onBindView(): void;
}
export = Drawable;
