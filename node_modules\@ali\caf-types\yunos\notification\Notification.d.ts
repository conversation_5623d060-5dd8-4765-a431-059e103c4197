import { MultiState } from "yunos/ui/util/TypeHelper";
import YObject = require("../core/YObject");
import PageLink = require("../page/PageLink");
/**
 * <p>Notification is the object containing information to be displayed in the notification bar.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.notification
 * @relyon YUNOS_SYSCAP_NOTIFICATION
 * @public
 * @since 2
 */
declare class Notification extends YObject {
    private _contentLink;
    private _deleteLink;
    private _priority;
    private _flags;
    private _template;
    private _style;
    private _timestamp;
    private _stateFlag;
    private _gearFlag;
    /**
     * <p>Create a notification object</p>
     * @public
     * @since 2
     */
    public constructor();
    /**
     * <p>Destructor that cleans this object.</p>
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * <p>Set the page link that links to the desired page when the notification is clicked.</p>
     * @name yunos.notification.Notification#contentLink
     * @type {yunos.page.PageLink}
     * @throws {TypeError} If value is not a instance of PageLink.
     * @public
     * @since 2
     */
    public contentLink: PageLink;
    /**
     * <p>Set the page links that links to the desired page when the notification is deleted.</p>
     * @name yunos.notification.Notification#deleteLink
     * @type {yunos.page.PageLink}
     * @throws {TypeError} If value is not a instance of PageLink.
     * @public
     * @since 2
     */
    public deleteLink: PageLink;
    /**
     * <p>The priority of the notification, defaults to 0.</p>
     * @name yunos.notification.Notification#priority
     * @type {number}
     * @default 0
     * @throws {TypeError} If type of paramter is not number.
     * @public
     * @since 2
     */
    public priority: number;
    /**
     * <p>The flags of the notification, defaults to Notification.Flags#AUTO_REMOVE</p>
     * @name yunos.notification.Notification#flags
     * @type {number}
     * @throws {TypeError} If type of paramter is not number.
     * @public
     * @since 2
     */
    public flags: number;
    /**
     * <p>The template of notification which decides how notification "looks"</p>
     * @name yunos.notification.Notification#template
     * @type {yunos.notification.Notification.Template}
     * @throws {TypeError} If type of paramter is not Template.
     * @public
     * @since 2
     */
    public template: Notification.Template;
    /**
     * <p>The state flags of the notification.</p>
     * @name yunos.notification.Notification#stateFlag
     * @type {number}
     * @throws {TypeError} If type of paramter is not number.
     * @default yunos.notification.Notification.StateFlags#STATE_A
     * @public
     * @since 5
     */
    public stateFlag: number;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private style: string;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private actions: Notification.Action[];
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private when: number;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private showWhen: boolean;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private icon: string;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private title: string;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private content: string;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private progress: number;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private progressMax: number;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private progressIndeterminate: boolean;
    /**
     * obsolete fields, only for compatible reason
     * @private
     */
    private picture: string;
    /**
     * <p>Enum for the style of the notification template.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Style: {
        [key: string]: string;
    };
    /**
     * <p>Only used for auto.</p>
     * <p>Enum for the flags of the notification priority.</p>
     * @enum {number}
     * @default yunos.notification.Notification.PriorityLevel.P4
     * @readonly
     * @public
     * @since 5
     *
     */
    public static readonly PriorityLevel: {
        [key: string]: number;
    };
    /**
     * <p>Enum for the flags of the notification state.</p>
     * <p>Notification will autoly dismiss, static or other state.</p>
     * @enum {number}
     * @default yunos.notification.Notification.StateFlags#STATE_A
     * @readonly
     * @public
     * @since 5
     *
     */
    public static readonly StateFlags: {
        [key: string]: number;
    };
    /**
     * <p>Enum for the flags of the notification.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Flags: {
        [key: string]: number;
    };
}
declare namespace Notification {
    /**
     * <p>The action used for the notification.</p>
     * @example
     * new Notification.Action("title", "file://xxx", new PageLink("page://xxx");
     * @extends yunos.core.YObject
     * @memberof yunos.notification.Notification
     * @public
     * @since 2
     */
    class Action extends YObject {
        private _title;
        private _icon;
        private _pageLink;
        private _toCollapsePanel;
        private _multiState;
        private _actionType;
        private _isPositive;
        /**
         * Create a notification action
         * @param {string} title - title
         * @param {string} icon - icon
         * @param {yunos.page.PageLink} pageLink - pageLink
         * @param {boolean} toCollapsePanel - collpase notification panel or not
         * @public
         * @since 2
         */
        public constructor(title: string, icon: string, pageLink: PageLink, toCollapsePanel?: boolean);
        /**
         * <p>Cleans up this action.</p>
         * @public
         * @since 2
         */
        public destroy(): void;
        /**
         * <p>The title of this action.</p>
         * @name yunos.notification.Notification.Action#title
         * @type {string}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public title: string;
        /**
         * <p>The icon of this action.</p>
         * @name yunos.notification.Notification.Action#icon
         * @type {string}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public icon: string;
        /**
         * <p>The page link to be sent when the action is clicked.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.Action#pageLink
         * @type {yunos.page.PageLink|null}
         * @throws {TypeError} If parameter is not a instance of PageLink.
         * @public
         * @since 2
         */
        public pageLink: PageLink;
        /**
         * <p>decide whether collapsing notification panel or not when the action is clicked.</p>
         * @name yunos.notification.Notification.Action#toCollapsePanel
         * @type {boolean|undefined}
         * @private
         */
        private toCollapsePanel: boolean;
        /**
         * <p> Set action type for notification action. </p>
         * <p> only number type can be setted. </p>
         * <p> Support text, icon or text & icon.</p>
         * @name yunos.notification.Notification.Action#actionType
         * @type {number} - {yunos.notification.Notification.ActionType#SHOW_TEXT} show text.
         * {yunos.notification.Notification.ActionType#SHOW_ICON} show icon.
         * {yunos.notification.Notification.ActionType#SHOW_TEXT} | {yunos.notification.Notification.ActionType#SHOW_ICON} show icon & text
         * @throws {TypeError} If parameter is not a instance of number.
         * @friend
         */
        actionType: number;
        /**
         * <p>Set action positive or not for notification action. </p>
         * <p>only boolean type can be setted</p>
         * @name yunos.notification.Notification.Action#isPositive
         * @type {boolean} - true: action text is positive, false: action text is not positive.
         * @throws {TypeError} If parameter is not a instance of boolean.
         * @friend
         */
        isPositive: boolean;
    }
    /**
     * <p>Template is attached to a notification
     * and defines how the notification "looks"</p>
     * <p>Normally BasicTemplate is a much better choice</p>
     * @extends yunos.core.YObject
     * @memberof yunos.notification.Notification
     * @public
     * @since 2
     */
    class Template extends YObject {
        protected _templateName: string;
        protected _templateData: TemplateData;
        protected _actions: Notification.Action[];
        /**
         * <p>Create a notification template object</p>
         * @param {string} templateName - name of the template
         * @public
         * @since 2
         */
        public constructor(templateName: string);
        /**
         * <p>Destructor that cleans this object.</p>
         * @public
         * @since 2
         */
        public destroy(): void;
        /**
         * <p>Specifications of the actions displayed along with the notification.
         * Each item in the array is a [Notification.Action]{@link yunos.notification.Notification.Action}.</p>
         * <p>Instance containing at least one of the following 2 fields:&nbsp;
         * icon, title. <code>icon</code> is the file address of the icon displayed in the notification,&nbsp;
         * and title is the title of the action.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.Template#actions
         * @type {yunos.notification.Notification.Action[]|null}
         * @throws {TypeError} If items of value is not a instance of Notification Actions.
         * @throws {TypeError} If value is not a array.
         * @public
         * @since 2
         */
        public actions: Action[];
        /**
         * <p>Set raw template data. Normally {yunos.notification.Notification.BasicTemplate}
         * will be a better choice</p>
         * @param {Object} templateData - a raw template data of the Notification, defined by client
         * @public
         * @since 2
         */
        public set(templateData: Notification.TemplateData): void;
        /**
         * <p>Returns name of this template</p>
         * @returns {string} template name
         * @public
         * @since 2
         */
        public getName(): string;
        private getPageLinks(): {
            [key: string]: string;
        };
    }
    /**
     * <p>Basic template is the desired one in almost all scenarios</p>
     * @extends yunos.notification.Notification.Template
     * @memberof yunos.notification.Notification
     * @public
     * @since 2
     */
    class BasicTemplate extends Template {
        /**
         * Create a notification basic template
         * @param {string} title - title
         * @param {string} icon - icon
         * @public
         * @since 2
         */
        public constructor(title: string, icon: string);
        /**
         * <p>Destructor that cleans this object.</p>
         * @public
         * @since 2
         */
        public destroy(): void;
        /**
         * <p>The title of the notification.</p>
         * @name yunos.notification.Notification.BasicTemplate#title
         * @throws {TypeError} If type of parameter is not string.
         * @type {string}
         * @public
         * @since 2
         */
        public title: string;
        /**
         * <p>The address of the icon to be displayed.</p>
         * @name yunos.notification.Notification.BasicTemplate#icon
         * @type {string}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public icon: string;
        /**
         * <p>The time associated with the notification.</p>
         * @name yunos.notification.Notification.BasicTemplate#when
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public when: number;
        /**
         * <p>Whether display the time associated with the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#showWhen
         * @type {boolean|null}
         * @throws {TypeError} If type of parameter is not boolean.
         * @public
         * @since 2
         */
        public showWhen: boolean;
        /**
         * <p>The large icon of the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#contentIcon
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public contentIcon: string;
        /**
         * <p>Whether this notification is expandable.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#expandable
         * @type {boolean|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public expandable: boolean;
        /**
         * <p>Whether this notification is expanded by default.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#expanded
         * @type {boolean|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public expanded: boolean;
        /**
         * <p>The text content of the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#content
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public content: string;
        /**
         * <p>The text that flows by in the status bar when the notification comes
         * <p>only app recorded in the important list can use this field
         * @name yunos.notification.Notification.BasicTemplate#ticker
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public ticker: string;
        /**
         * <p>Display a progress bar and set its progress with this attribute.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#progress
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public progress: number;
        /**
         * <p>Set the max of the progress bar displayed.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#progressMax
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public progressMax: number;
        /**
         * <p>Set the progress bar to the indeterminate state.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#progressIndeterminate
         * @type {boolean|null}
         * @public
         * @since 2
         */
        public progressIndeterminate: boolean;
        /**
         * <p>Set the format of showing text of the progress bar.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#progressInPercentFormat
         * @type {boolean|null}
         * @public
         * @since 2
         */
        public progressInPercentFormat: boolean;
        /**
         * <p>The big picture displayed along with the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#picture
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public picture: string;
        /**
         * <p>The sound to be played with the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#sound
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public sound: string;
        /**
         * <p>The LED flashs in this color, alone with this notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#ledARGB
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public ledARGB: number;
        /**
         * <p>How long the LED keeps on in a period.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#ledOnMS
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public ledOnMS: number;
        /**
         * <p>How long the LED keeps off in a period.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#ledOffMS
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public ledOffMS: number;
        /**
         * <p>The vibrating pattern, alone with this notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#vibrate
         * @type {number[]|null}
         * @throws {TypeError} If type of parameter is not array.
         * @public
         * @since 2
         */
        public vibrate: number[];
        /**
         * <p>ExtraKey will be passed to some "backend", which can understand it</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BasicTemplate#extraKey
         * @type {Object|null}
         * @throws {TypeError} If type of parameter is not object.
         * @private
         */
        private extraKey: object;
        /**
         * <p>Set tts of this template. </p>
         * @name yunos.notification.Notification.BasicTemplate#tts
         * @type {string} value Tts string
         * @throws {TypeError} If type of parameter is not string.
         * @return {string}  Tts string
         * @public
         * @since 5
         *
         */
        public tts: string;
    }
    /**
     * <p>A template for the "Announcement scenario".
     * Use it only in limit scenarios, e.g. showing advertisements</p>
     * @extends yunos.notification.Notification.Template
     * @memberof yunos.notification.Notification
     * @public
     * @since 2
     */
    class BigPictureTemplate extends Template {
        /**
         * Create a notification bigPicture template
         * @param {string} title - title
         * @param {string} icon - icon
         * @public
         * @since 2
         */
        public constructor(title: string, icon: string);
        /**
         * <p>Destructor that cleans this object.</p>
         * @public
         * @since 2
         */
        public destroy(): void;
        /**
         * <p>The title of the notification.</p>
         * @name yunos.notification.Notification.BigPictureTemplate#title
         * @throws {TypeError} If type of parameter is not string.
         * @type {string}
         * @public
         * @since 2
         */
        public title: string;
        /**
         * <p>The address of the icon to be displayed.</p>
         * @name yunos.notification.Notification.BigPictureTemplate#icon
         * @type {string}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public icon: string;
        /**
         * <p>The time associated with the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BigPictureTemplate#when
         * @type {number|null}
         * @throws {TypeError} If type of parameter is not number.
         * @public
         * @since 2
         */
        public when: number;
        /**
         * <p>Whether display the time associated with the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BigPictureTemplate#showWhen
         * @type {boolean|null}
         * @throws {TypeError} If type of parameter is not boolean.
         * @public
         * @since 2
         */
        public showWhen: boolean;
        /**
         * <p>The large icon of the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BigPictureTemplate#contentIcon
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public contentIcon: string;
        /**
         * <p>The text content of the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BigPictureTemplate#content
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public content: string;
        /**
         * <p>The big picture displayed along with the notification.</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BigPictureTemplate#picture
         * @type {string|null}
         * @throws {TypeError} If type of parameter is not string.
         * @public
         * @since 2
         */
        public picture: string;
        /**
         * <p>ExtraKey will be passed to some "backend", which can understand it</p>
         * <p>Note: pass "null" will unset it</p>
         * @name yunos.notification.Notification.BigPictureTemplate#extraKey
         * @type {Object|null}
         * @throws {TypeError} If type of parameter is not object.
         * @public
         * @since 2
         */
        public extraKey: object;
        /**
         * @protected
         * @override
         */
        protected getData(): TemplateData;
    }
    interface NotificationData {
        timestamp: string | number;
        priority: number;
        flags: number;
        stateFlag: number;
        gearFlag: number;
        pageLinks?: {
            [key: string]: string;
        };
        templateData?: TemplateData;
        templateName?: string;
    }
    interface ActionDesc {
        isPositive?: boolean;
        actionType?: number;
        multiState?: MultiState;
        title?: string;
        icon?: string;
        pageLink?: string;
        toCollapsePanel?: boolean;
    }
    interface TemplateData {
        tts?: string;
        title?: string;
        icon?: string;
        when?: number;
        showWhen?: boolean;
        contentIcon?: string;
        expandable?: boolean;
        expanded?: boolean;
        content?: string;
        ticker?: string;
        progress?: number;
        progressMax?: number;
        progressIndeterminate?: boolean;
        progressInPercentFormat?: boolean;
        picture?: string;
        sound?: string;
        ledARGB?: number;
        ledOnMS?: number;
        ledOffMS?: number;
        vibrate?: number[];
        extraKey?: object;
        actions?: ActionDesc[];
    }
}
export = Notification;
