/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
import ProviderFramework = require("yunos/provider");
const DataObserver = ProviderFramework.DataObserver;
const MediaStore = require("yunos/multimedia/MediaStore");
import Page = require("yunos/page/Page");
import DataResolver = require("yunos/provider/DataResolver");
import PageLink = require("yunos/page/PageLink");
import log = require("../utils/log");
const TAG = "MediaMonitor";
const DEBUG = true;

class VideoObserver extends DataObserver {
    private _monitor: MediaMonitor;

    constructor(uri: string, monitor: MediaMonitor) {
        if (DEBUG) {
            log.I(TAG, "constructor", uri);
        }
        super(uri);
        this._monitor = monitor;
    }

    onChange(uri: string) {
        if (DEBUG) {
            log.D(TAG, "[changed]" + uri);
        }
        // sample: page://provider.media.yunos.com/mediaprovider?storage=external&type=3
        if (this._monitor) {
            this._monitor.notifyUriChange(uri);
        }
    }

    destroy() {
        if (DEBUG) {
            log.I(TAG, "destroy");
        }
    }
}

class MediaMonitor {
    private _page: Page;
    private videoOB: VideoObserver;
    private resolver: DataResolver;
    private _notifyChangeCallback: (arg0: string) => void;
    private _scanComplete: (...args: Object[]) => void | Function[];
    private _collectBulk: (...args: Object[]) => void | Function[];

    constructor(page: Page, notifyChangeCallback: (arg0: string) => void) {
        this._page = page;
        this._notifyChangeCallback = notifyChangeCallback;
        let uri = MediaStore.getUri(MediaStore.VolumeName.EXTERNAL, MediaStore.MediaType.VIDEO);
        this.videoOB = new VideoObserver(uri, this);
        this.resolver = require("yunos/provider/DataResolver").getInstance(page);
        this.resolver.registerObserver(this.videoOB, null);
        // 全部视频扫描完成广播
        this._scanComplete = <(...args: Object[]) => void | Function[]> this._page.receiveBroadcast(
            MediaStore.COLLECT_COMPLETE_EVENT, (err: { message: Object }, pageLink: PageLink) => {
                if (!err && pageLink) {
                    log.D(TAG, "complete", pageLink.eventName, pageLink.data);
                    if (pageLink.eventName && pageLink.data) {
                        try {
                            let data = JSON.parse(pageLink.data);
                            if (data) {
                                this._notifyChangeCallback(data.uri);
                            }
                        } catch (e) {
                            log.E(TAG, "onLink exception:", e);
                        }
                    }
                } else {
                    log.E(TAG, err.message);
                }
            }
        );

        // 批量视频扫描完成广播，第一批10个视频，第二批10个视频，第三批剩余视频
        this._collectBulk = <(...args: Object[]) => void | Function[]> this._page.receiveBroadcast(
            MediaStore.COLLECT_BULK_VIDEO_EVENT, (err: { message: Object }, pageLink: PageLink) => {
                if (!err && pageLink) {
                    log.D(TAG, "bulk", pageLink.eventName, pageLink.data);
                    if (pageLink.eventName && pageLink.data) {
                        try {
                            let data = JSON.parse(pageLink.data);
                            if (data) {
                                this._notifyChangeCallback(data.uri);
                            }
                        } catch (e) {
                            log.E(TAG, "onLink exception:", e);
                        }
                    }
                } else {
                    log.E(TAG, err.message);
                }
            }
        );
    }

    /**
     * 扫描完成的回调
     */
    notifyUriChange(uri: string) {
        if (this._notifyChangeCallback) {
            this._notifyChangeCallback(uri);
        }
    }

    /**
     * 释放相关监听信息
     */
    release() {
        if (this._page) {
            log.I(TAG, "unreceiveBroadcasts release");
            if (this._scanComplete) {
                this._page.unreceiveBroadcasts(this._scanComplete);
            }
            if (this._collectBulk) {
                this._page.unreceiveBroadcasts(this._collectBulk);
            }
        }
        if (this.videoOB && this.resolver) {
            log.I(TAG, "unregisterObserver release");
            this.resolver.unregisterObserver(this.videoOB, null);
            this.videoOB.destroy();
            this.videoOB = null;
        }
        this.resolver = null;
        this._page = null;
    }
}
export = MediaMonitor;
