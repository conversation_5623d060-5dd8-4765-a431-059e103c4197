import FloatPageRouter = require("yunos/appmodel/FloatPageRouter");
import CompositeView = require("yunos/ui/view/CompositeView");
import BaseFloatPage = require("../base/BaseFloatPage");
declare class BMFloatPage extends BaseFloatPage {
    readonly carMode: string;
    router: FloatPageRouter;
    readonly modeConfig: import("../system/Types").IModeConfig;
    readonly rootContainer: CompositeView;
    private _rootContainer;
    private mRouter;
    onCreate(): void;
    onStart(): void;
    protected onStop(): void;
    protected getWindowConfig(): {};
    protected isUsingFastRender(): boolean;
    protected usingCommonBackground(): boolean;
    protected useRouter(): boolean;
    protected usingPresenterFallback(): boolean;
    private initRootView;
    private _updateRootContainer;
    private _getRootContainerPropertySetName;
    private _getAppMargin;
    private _getRootContainerSize;
}
export = BMFloatPage;
