/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets

import Presenter = require("yunos/appmodel/Presenter");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import View = require("yunos/ui/view/View");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
const RecognitionMode = VoiceCommand.RecognitionMode;
import ScrollableView = require("yunos/ui/view/ScrollableView");
import ScrollBar = require("yunos/ui/widget/ScrollBar");
import Utils = require("../utils/Utils");
import log = require("../utils/log");
const TAG = "DlnaPresenter";

class DlnaPresenter extends Presenter {
    private _destroyed: boolean;
    private _hidden: boolean;
    private _navigationBar: NavigationBar;
    private _navBackListener: () => void;

    onCreate() {
        log.I(TAG, "onCreate");
        this._destroyed = false;
        this.attachView("dlna");
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._addVoiceCommands();
    }

    _setupViews(parentView: View) {
        this._navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._navigationBar.addEventListener("back", this._navBackListener = () => {
            log.I(TAG, "back button pressed!");
            this.context.router.back();
        });
        let container = <ScrollableView> parentView.findViewById("id_container");
        if (container) {
            let scrollbar = <ScrollBar> parentView.findViewById("id_scrollbar");
            container.horizontalScrollBar = scrollbar;
        }
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        log.I(TAG, "_addVoiceCommands");
        // jshint unused:false
        const cmdKeys = [
            "VOICECMD_BACK_1",
            "VOICECMD_BACK_2"
        ];

        Utils.registerVoiceCommand(this._navigationBar, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            log.I(TAG, "voice command", cmdKey, index);
            this.context.router.back();

            const iPageInstance = require("../index").getInstance();
            Utils.cancelSpeech(iPageInstance);
        });
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (this._navigationBar) {
            Utils.removeVoiceCommand(this._navigationBar);
        }
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._navigationBar) {
            this._navigationBar.removeEventListener("back", this._navBackListener);
            this._navBackListener = null;
        }
    }

    onDestroy() {
        log.D(TAG, "onDestroy");
        this._destroyed = true;
        this._removeVoiceCommands();
        this._removeAllListeners();
    }
}

export = DlnaPresenter;
