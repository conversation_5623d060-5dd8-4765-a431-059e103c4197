export declare class NmsSocket {
    constructor(type: string, ip: string, port: number);
    ondata(data: Buffer|string, ip: string, port: number): void;
    accept(callback: (code: number, msg: string, sock: NmsSocket) => void): void;
    onerror(code: number, msg: string): void;
    ondrain(): void;
    close(): void;
    send(data: string|Buffer, callback:(code: number, msg: string) => void): void;
    sendTo(data: string|Buffer, ip: string, port: number,
            callback:(code: number, msg: string) => void): void;
    joinGroup(ip: string): void;
    leaveGroup(ip: string): void;
}

