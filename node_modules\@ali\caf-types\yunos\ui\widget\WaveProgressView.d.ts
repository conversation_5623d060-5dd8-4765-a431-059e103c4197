import ImageView = require("../view/ImageView");
interface IWaveOption {
    enable?: boolean;
    src?: string;
    frameDuration?: number;
    repeatDuration?: number;
    fadeInDuration?: number;
    fadeOutDuration?: number;
    panSpeed?: number;
    panSpeedOffset?: number;
}
interface IStyle {
    waveEnable: boolean;
    waveSrc: string;
    waveFadeInDuration: number;
    waveFadeOutDuration: number;
    waveFrameDuration: number;
    waveRepeatDuration: number;
    wavePanSpeed: number;
    wavePanSpeedOffset: number;
}
/**
 * <p>The view of <PERSON><PERSON><PERSON>'s progress, support wave animation.</p>
 * @extends ui.view.ImageView
 * @memberof yunos.ui.widget
 * @private
 */
declare class WaveProgressView extends ImageView {
    private _isHorizontal;
    private _progressPercentage;
    private _waveEnable;
    private _waveSrc;
    private _waveFrameDuration;
    private _waveRepeatDuration;
    private _waveFadeInDuration;
    private _waveFadeOutDuration;
    private _wavePanSpeed;
    private _wavePanSpeedOffset;
    private _waveAvailable;
    private _waveRunning;
    private _waveAlphaUpdate;
    private _waveDrawBitmapUpdate;
    private _waveFrameAnimation;
    private _waveFadeInAnimation;
    private _waveFadeOutAnimation;
    private _waveRatio;
    private _waveAlpha;
    private _waveStartTime;
    private _waveFrameLastTime;
    private _waveFrameTimes;
    private _waveTranslate;
    private _waveAnimPercentage;
    private _waveDrawBitmap;
    private startWave(): void;
    private stopWave(): void;
    /**
     * Set wave animation enable.
     * @type {boolean}
     */
    private waveEnable: boolean;
    /**
     * Set wave animation frame's src path.
     * @type {string}
     */
    private waveSrc: string;
    /**
     * Set progress orientation.
     * @type {boolean}
     */
    private isHorizontal: boolean;
    /**
     * Set wave animation options.
     * @type {IWaveOption}
     */
    private waveOption: IWaveOption;
    /**
     * Set progress view percentage, but it is only associated with the pan speed of wave animation now.
     * From 0 to 1.
     * @type {number}
     */
    private progressPercentage: number;
    /**
     * Set percentage for wave animation, but it is only used to trigger redrawing the progress view now.
     * From 0 to 100.
     * @type {number}
     */
    private waveAnimPercentage: number;
    /**
     * Set alpha for wave animation which realize the fade in/out animation for wave.
     * From 0 to 1.
     * @type {number}
     */
    private waveAlpha: number;
    private onPropertyChange(property: string, oldValue: Object, value: Object): void;
    private updateWaveOption(option?: IWaveOption): void;
    private refreshWaveAnimations(): void;
    private createWaveAnimations(): void;
    private releaseWaveAnimations(): void;
    private refreshWaveState(): void;
    private refreshWaveDrawBitmap;
    private releaseWaveDrawBitmap;
    private onDrawWave;
    /**
     * Implement this to apply style
     * @param {IStyle} style - style config
     * @override
     * @protected
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.WaveProgressView#defaultStyleName
     * @type {string}
     * @default "WaveProgressView"
     * @readonly
     * @public
     * @override
     *
     */
    public readonly defaultStyleName: string;
}
export = WaveProgressView;
