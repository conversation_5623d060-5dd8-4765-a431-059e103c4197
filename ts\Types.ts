/**
 * Copyright (C) 2016-2017 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import TextView = require("yunos/ui/view/TextView");
import ColumnLayout = require("yunos/ui/layout/ColumnLayout");
import CompositeView = require("yunos/ui/view/CompositeView");
import Rectangle = require("yunos/graphics/Rectangle");

export interface IOnlineVideo {
    id: string;
    title: string;
    videoId: number;
    videoWidth: number;
    videoHeight: number;
    videoSize: number;
    tags: string;
    category: string;
    mp4Url: string;
    thumbnail: string;
    duration: number;
    viewCount: number;
    likeCount: number;
}

export interface IVoiceCommand {
    requestVoiceState: string;
    keepEffective: boolean;
    VoiceState: {
        Processing: string;
        KWS: string;
        Auto: string;
    };
    createSelectCommand(): VoiceCommand;
    createNextCommand(): VoiceCommand;
    createPreviousCommand(): VoiceCommand;
}

export interface IVoiceEvent {
    result: string;
    queryId: string;
    querywords: string;
    localTaskEnd: boolean;
    command: VoiceCommand;
    endLocalTask: () => void;
    _querywords: string;
}

export interface IVolume {
    description: string;
    primary: string;
    fsUuid: string;
    emulated: string;
    allowUMS: string;
    mtpReserveSpace: string;
    removable: string;
    fsType: string;
    mediaType: string;
    label: string;
    path: Object;
    id: string;
}

export interface IConfig {
    HEADER_HEIGHT?: number;
    HEADER_ICON_SIZE?: number;
    PAGE_HEIGHT?: number;
    PAGE_WIDTH?: number;
    ONLINE_USB_BTN_HEIGHT?: number;
    ONLINE_USB_LARGE_WIDTH?: number;
    ONLINE_USB_SMALL_WIDTH?: number;
    ONLINE_USB_ICON_SIZE?: number;
    ONLINE_USB_TITLE_SPACE?: number;
    SLIDER_TIP_LARGE_WIDTH?: number;
    SLIDER_TIP_WIDTH?: number;
    SLIDER_TIP_HEIGHT?: number;
    PLAYER_BG_COLOR?: number | string;
    PLAYER_CTRL_BAR_HEIGHT?: number;
    PLAYER_TIME_TEXT_WIDTH?: number;
    PLAYER_PROGRESS_BAR_HEIGHT?: number;
    PLAYER_CORNER_SIZE?: number;
    ITEM_SPACE?: number;
    BORDER_RADIUS?: number;
}

export interface IEvent {
    key: string;
    shiftKey: boolean;
    stopPropagation: () => void;
}

export interface ITitleItem extends CompositeView {
    title: TextView;
    subTitle: TextView;
    visibility: number;
    layout: ColumnLayout;
    width: number;
    height: number;
    scaleType: number;
    touchRegion: Rectangle[];
}
