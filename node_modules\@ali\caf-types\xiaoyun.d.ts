declare class XiaoyunAPI {
    bindService(config: string): void;

    unBindService(): void;

    setASRResultCallback(callback: (resultState: number, result: string) => void): void;

    setASRStatusCallback(callback: (status: number) => void): void;

    setASRVolumeCallback(callback: (volume: number) => void): void;

    setASRErrorCallback(callback: (errCode: number, description: string) => void): void;

    setServiceStatusCallback(callback: (serviceStatus: number) => void): void;

    startSpeech(): string;

    stopSpeech(): void;

    cancelSpeech(): void;
}

declare var xiaoyunApi: XiaoyunAPI;

export = xiaoyunApi;
