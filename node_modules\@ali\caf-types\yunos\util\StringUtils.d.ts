import YObject = require("../core/YObject");
/**
 * <p>StringUtils is a lightweight library for processing string.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 3
 */
declare class StringUtils extends YObject {
    /**
     * <p>Converts string to camel case.</p>
     * @example
     * StringUtils.camelCase("Foo Bar");
     * // result -> fooBar
     *
     * StringUtils.camelCase("Foo_Bar");
     * // result -> fooBar
     *
     * @param {string} string - The string to convert.
     * @return {string} Returns the converted string.
     * @public
     * @since 3
     */
    public static camelCase(string: string): string;
    /**
     * <p>Converts the first character of string to upper case and the remaining to lower case.</p>
     * @example
     * StringUtils.camelCase("FOO");
     * // result -> Foo
     *
     * @param {string} string - The string to capitalize.
     * @return {string} Returns the converted string.
     * @public
     * @since 3
     */
    public static capitalize(string: string): string;
    /**
     * <p>Converts the special characters such as "&" in string to their corresponding HTML or URI entities.</p>
     * @example
     * StringUtils.escape("foo & bar");
     * // result -> foo &amp; bar
     *
     * StringUtils.escape("foo & bar", "html");
     * // result -> foo &amp; bar
     *
     * StringUtils.escape("foo & bar", "uri");
     * // result -> foo%20%26%20bar
     *
     * @param {string} string - The string to escape.
     * @param {string} [type="html"] - Rule for escaping, html by default.
     * @return {string} Returns the escaped string.
     * @public
     * @since 3
     */
    public static escape(string: string, type?: string): string;
    /**
     * <p>Converts string to snake case.</p>
     * @example
     * StringUtils.snakeCase("fooBar");
     * // result ->  foo_bar
     *
     * @param {string} string - The string to convert.
     * @return {string} Returns the converted string.
     * @public
     * @since 3
     */
    public static snakeCase(string: string): string;
    /**
     * <p>Creates a compiled template function that can interpolate data properties.</p>
     * @example
     * var compiled = StringUtils.template("foo <%= user %>");  // function(){}
     * compiled({user: "bar"}) // foo bar
     *
     * var compiled = StringUtils.template("foo {{ user }}", {interpolate: /{{([\s\S]+?)}}/g});  // function(){}
     * compiled({user: "bar"}) // foo bar
     *
     * @param {string} string - The template string.
     * @param {Object} [options = {}] - The options object.
     * @param {RegExp} [options.interpolate] - The "interpolate" delimiter.
     * @param {string} [options.variable="obj"] - The data object variable name.
     * @return {function} The compiled function can interpolate data properties.
     * @public
     * @since 3
     */
    public static template(string: string, options?: {
        interpolate?: RegExp;
        variable?: string;
    }): Function;
    /**
     * <p>Get fragment from the given string by providing a more readable match rule in string other than a regexp.</p>
     * @example
     * StringUtils.parse("key={{dp(20)}}", "key={{dp(%d)}}");
     * // result -> [20]
     *
     * StringUtils.parse("width={view.width}, height={view.height}", "width={%s}, height={%s}");
     * // result -> ["view.width", "view.height"]
     *
     * @param {string} string - The string to parse.
     * @param {string} rule - The rule to pick up string.
     * @return {Array} The matched string array.
     * @public
     * @since 3
     */
    public static parse(string: string, rule: string): (string | number)[];
    /**
     * <p>Truncates string if it's longer than the given maximum string length.</p>
     * @example
     * StringUtils.truncate("YunOS is the internet operating system produced by Alibaba Group");
     * // result -> YunOS is the internet opera...
     *
     * StringUtils.truncate("YunOS is the internet operating system produced by Alibaba Group", {length: 24, separator: " "});
     * // result -> YunOS is the internet...
     *
     * StringUtils.truncate("YunOS is the internet operating system produced by Alibaba Group", {omission: " [...]"});
     * // result -> YunOS is the internet op [...]
     *
     * @param {string} string - The template string.
     * @param {Object} [options] - The options object.
     * @param {number} [options.length=30] - The maximum string length.
     * @param {string} [options.omission="..."] - The string to indicate text is omitted.
     * @param {RegExp|string} [options.separator] - The separator pattern to truncate to.
     * @return {string} Returns the truncated string.
     * @public
     * @since 3
     */
    public static truncate(string: string, options?: {
        length?: number;
        omission?: string;
        separator?: string | RegExp;
    }): string;
    /**
     * <p>The inverse of escape.</p>
     * @example
     * StringUtils.unescape("foo &amp; bar", "html");
     * // result -> foo & bar
     *
     * StringUtils.unescape("foo%20%26%20bar", "uri");
     * // result -> foo & bar
     *
     * @param {string} string - The string to unescape.
     * @param {string} [type="html"] - The type to unescape, html by default.
     * @return {string} Returns the unescaped string.
     * @public
     * @since 3
     */
    public static unescape(string: string, type?: string): string;
    /**
     * <p>Converts string to an integer of the specified radix.</p>
     * @example
     * StringUtils.toInt("08");
     * // result -> 8
     *
     * StringUtils.toInt("10", 8);
     * // result -> 8
     *
     * @param {string} string - The string to convert.
     * @param {number} [radix=10] - The radix to interpret value by.
     * @return {number} Returns the converted integer.
     * @public
     * @since 3
     */
    public static toInt(string: string, radix?: int): number;
    /**
     * <p>Pads string on the left and right sides util the target string reaches the target length.</p>
     * @example
     * StringUtils.pad("8", 3, "0");
     * // result -> "080"
     *
     * @param {string} string - The string to pad.
     * @param {number} targetLength - The target length.
     * @param {string} [chars=" "] - The string used as padding.
     * @return {string}  Returns the padded string.
     * @public
     * @since 3
     */
    public static pad(string: string, targetLength: number, chars?: string): string;
    /**
     * <p>Pads string on the left side util the target string reaches the target length.</p>
     * @example
     * StringUtils.padStart("8", 3, "0");
     * // result -> "008"
     *
     * @param {string} string - The string to pad.
     * @param {number} targetLength - The target length.
     * @param {string} [chars=" "] - The string used as padding.
     * @return {string}  Returns the padded string.
     * @public
     * @since 3
     */
    public static padStart(string: string, targetLength: number, chars?: string): string;
    /**
     * <p>Pads string on the right side util the target string reaches the target length.</p>
     * @example
     * StringUtils.padEnd("8", 3, "0");
     * // result -> "800"
     *
     * @param {string} string - The string to pad.
     * @param {number} targetLength - The target length.
     * @param {string} [chars=" "] - The string used as padding.
     * @return {string}  Returns the padded string.
     * @public
     * @since 3
     *
     */
    public static padEnd(string: string, targetLength: number, chars?: string): string;
}
export = StringUtils;
