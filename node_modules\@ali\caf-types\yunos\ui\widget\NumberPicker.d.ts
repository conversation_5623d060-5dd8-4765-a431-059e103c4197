import CompositeView = require("../view/CompositeView");
import TextView = require("../view/TextView");
import KeyEvent = require("../event/KeyEvent");
import GestureEvent = require("../event/GestureEvent");
import VoiceEvent = require("../event/VoiceEvent");
/**
 * A widget that enables the user to select a number form a predefined range.
 * @example
 * const numberPicker = new NumberPicker();
 * numberPicker.displayedValues = ["banana", "apple", "peach", "orange", "grapefruit", "watermelon", "Strawberry", "blueberry", "pear"];
 * numberPicker.minValue = 0;
 * numberPicker.maxValue = 8;
 * numberPicker.addEventListener("valuechanged", (oldValue, value) => {
 *     console.log(`您选择的是${this.numberPicker.displayedValues[value]}`);
 * });
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class NumberPicker extends CompositeView {
    private _count;
    private _textSize;
    private _lineHeight;
    private _lineColor;
    private _needDrawLine;
    private _selectedTextSize;
    private _selectedTextColor;
    private _textFontFamily;
    private _needLine;
    private _minOpacity;
    private _maxOpacity;
    private _minValue;
    private _maxValue;
    private _displayedValues;
    private _loopMode;
    private _scrollY;
    private _pos;
    private _itemHeight;
    private _textAlign;
    private _innerHeight;
    private _refreshTime;
    private _textPaddingLeft;
    private _textPaddingRight;
    private _startScrollY;
    private _velocityX;
    private _velocityY;
    private _velocity;
    private _maxVelocity;
    private _minVelocity;
    private _textColor;
    private _contentArray: Array<string>;
    private _tryComputeTextView;
    private _defaultWidth;
    private _defaultHeight;
    private _mLastHandledDownDpadKeyCode;
    private _contentHeight;
    private _rebackAnimationY;
    private _animationY;
    private _flingAnimTimeFunction;
    private _rebackAnimTimeFunction;
    private _rebackAnimDuration;
    private _line1View;
    private _line2View;
    private _tapRecognizer;
    private _panRecognizer;
    private _content;
    private _offsetVerticalMiddle;
    private _beginAnimationTime;
    private _deceleration;
    private _enableFadeShader;
    private _fadingShader;
    private _defaultBottomLength;
    private _defaultTopLength;
    private _highlightTextChild;
    private _defaultVoiceCommand;
    private _voiceHandler;
    /**
     * Create a numberPicker.
     * @public
     * @since 1
     */
    /**
     * Destroy this numberPicker.
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * Destroy all of this numberPicker.
     * @override
     * @public
     * @since 1
     */
    public destroyAll(): void;
    /**
     * This property holds the count of line in the picker.
     * @name yunos.ui.widget.NumberPicker#count
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not great than or equal to 3.
     * @throws {RangeError} If parameter is not odd number.
     * @default 3
     * @public
     * @since 3
     *
     */
    public count: number;
    /**
     * This property holds the fontSize of the unpicked item, the unit is px.
     * @name yunos.ui.widget.NumberPicker#textSize
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public textSize: number;
    /**
     * This property holds the fontSize of the picked item, the unit is px.
     * @name yunos.ui.widget.NumberPicker#selectedTextSize
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public selectedTextSize: number;
    /**
     * This property holds the fontFamily of all texts.
     * @name yunos.ui.widget.NumberPicker#textFontFamily
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @public
     * @since 6
     */
    public textFontFamily: string;
    /**
     * This property holds the color of the divider line.
     * @name yunos.ui.widget.NumberPicker#lineColor
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @public
     * @since 1
     */
    public lineColor: string;
    /**
     * This property determines whether to draw the seperator lines
     * @name yunos.ui.widget.NumberPicker#needLine
     * @type {boolean}
     * @public
     * @since 4
     *
     */
    public needLine: boolean;
    /**
     * This property holds the color of the text, when the text is not picked use it.
     * @name yunos.ui.widget.NumberPicker#textColor
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @public
     * @since 1
     */
    public textColor: string;
    /**
     * This property holds the color of the text, when the text is picked use it.
     * @name yunos.ui.widget.NumberPicker#selectedTextColor
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @private
     */
    private selectedTextColor: string;
    /**
     * This property holds the min opacity of the text, when the text is not picked use it.
     * @name yunos.ui.widget.NumberPicker#minOpacity
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not between 0 and 1.
     * @public
     * @since 1
     */
    public minOpacity: number;
    /**
     * This property holds the max opacity of the text, when the text is picked use it.
     * @name yunos.ui.widget.NumberPicker#maxOpacity
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not between 0 and 1.
     * @public
     * @since 1
     */
    public maxOpacity: number;
    /**
     * Lower value of the range of numbers allowed for the NumberPicker.
     * @name yunos.ui.widget.NumberPicker#minValue
     * @type {number}
     * @throws {TypeError} If parameter is not an integer.
     * @public
     * @since 1
     */
    public minValue: number;
    /**
     * Upper value of the range of numbers allowed for the NumberPicker.
     * @name yunos.ui.widget.NumberPicker#maxValue
     * @type {number}
     * @throws {TypeError} If parameter is not an integer.
     * @public
     * @since 1
     */
    public maxValue: number;
    /**
     * This property holds the value of the picker.
     * @name yunos.ui.widget.NumberPicker#value
     * @type {number}
     * @throws {TypeError} If parameter is not an integer.
     * @throws {RangeError} If parameter is not between minValue and maxValue.
     * @public
     * @since 1
     */
    public value: number;
    /**
     * <p>This property holds the array of the picker content, sets the values to be displayed.</p>
     * <p>The length of the displayed values array must be equal to the range of <br>
     * selectable numbers which is equal to {@link #maxValue()} - {@link #minValue()} + 1.</p>
     * @name yunos.ui.widget.NumberPicker#displayedValues
     * @type {string[]}
     * @throws {TypeError} If parameter is not an array.
     * @throws {TypeError} If parameter is not a string array.
     * @public
     * @since 1
     */
    public displayedValues: string[];
    /**
     * <p>This property holds Whether the picker data cycling.</p>
     * <p>you can choose NumberPicker.LoopMode.Loop or NumberPicker.LoopMode.NoLoop.</p>
     * @name yunos.ui.widget.NumberPicker#loopMode
     * @type {yunos.ui.widget.NumberPicker.LoopMode}
     * @default yunos.ui.widget.NumberPicker.LoopMode.Loop.
     * @throws {TypeError} If type of parameter is not a NumberPicker.LoopMode.
     * @public
     * @since 1
     */
    public loopMode: number;
    /**
     * <p>This property holds the picker's text align.</p>
     * <p>you can choose NumberPicker.Align.Left, NumberPicker.Align.Right or NumberPicker.Align.Center.</p>
     * @name yunos.ui.widget.NumberPicker#textAlign
     * @type {yunos.ui.widget.NumberPicker.Align}
     * @default yunos.ui.widget.NumberPicker.Align.Center.
     * @throws {TypeError} If type of parameter is not a NumberPicker.Align.
     * @private
     */
    private textAlign: number;
    /**
     * <p>This property holds the paddingLeft of the text.</p>
     * <p>It is worked just when you set textAlign as NumberPicker.Align.Left.</p>
     * @name yunos.ui.widget.NumberPicker#textPaddingLeft
     * @type {number}
     * @throws {TypeError} If type of parameter is not a number.
     * @private
     */
    private textPaddingLeft: number;
    /**
     * <p>This property holds the paddingRight of the text.</p>
     * <p>It is worked just when you set textAlign as NumberPicker.Align.Right.</p>
     * @name yunos.ui.widget.NumberPicker#textPaddingRight
     * @type {number}
     * @throws {TypeError} If type of parameter is not a number.
     * @private
     */
    private textPaddingRight: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.NumberPicker#defaultStyleName
     * @type {string}
     * @default "NumberPicker"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>voiceViewType.</p>
     * @override
     * @protected
     * @since 6
     */
    protected readonly voiceViewType: string;
    /**
     * <p>Defines voiceBinding of the NumberPicker, if not empty to bind one view text.<br/>
     * The attributes currently supports controls bound to text component(Button,TextView and so on),After being bound, <br/>
     * the bound control cannot be registered and responded to if it has voice events.<br/>
     * In the following example, if voiceBinding is used, it responds to the events of the bound View.</p>
     * @example
     * //Use in code
     * //The bound component
     * var textView = new TextView();
     * textView.id = "textViewId";
     * textView.text = "Sound";
     * this.voiceBinding = textView.id;
     *
     * @example
     * //Use in xml
     * //The bound component
     * <TextView id="textViewId" text="Sound"></TextView>
     * <NumberPicker id="numberpicker" voiceBinding="textViewId"/>
     * @name yunos.ui.view.NumberPicker#voiceBinding
     * @type {string}
     * @override
     * @public
     * @since 6
     */
    public voiceBinding: string;
    private _voiceEventHandler(e: VoiceEvent): void;
    private _bindVoiceEvent(handler: (...args: Object[]) => void): void;
    private _unbindVoiceEvent(handler: (...args: Object[]) => void): void;
    /**
     * Computes the max contentWidth of text.
     * @return {number} max contentWidth of text.
     * @public
     * @since 1
     */
    public tryComputeMaxWidth(): int;
    /**
     * Computes the contentHeight of text.
     * @return {number} contentHeight of text.
     * @public
     * @since 1
     */
    public tryComputeHeight(): number;
    /**
     * Computes contentWidth of the given text.
     * @param {string} text - The text to compute
     * @return {number} target text's contentWidth
     * @protected
     * @since 1
     */
    protected tryComputeTextWidth(text: string): number;
    /**
     * Computes contentHeight of the given text.
     * @param {string} text - The text to compute
     * @return {number} target text's contentHeight
     * @protected
     * @since 1
     */
    protected tryComputeTextHeight(text: string): number;
    private initAnimationY(): void;
    private updateAnimationY(): void;
    private initRebackAnimationY(): void;
    private updateRebackAnimationY(): void;
    private createContent(): CompositeView;
    private removeContent(): void;
    /**
     * <p>Apply theme style for NumberPicker.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * @name NumberPicker#scrollY
     * @type {Number}
     * @private
     */
    private scrollY: number;
    /**
     * This property holds the index of current selected.
     * @name NumberPicker#pos
     * @type {number}
     * @private
     */
    private pos: number;
    private smoothScrollYTo(y: number): void;
    private stopAutoScroll(): void;
    private onPanStart(): void;
    private onPanMove(e: GestureEvent): void;
    private onPanEnd(e: GestureEvent): void;
    private onPanCancel(e: GestureEvent): void;
    private getInnerHeight(): number;
    /**
     * Handle the key down event.
     * @param {yunos.ui.event.KeyEvent} e - the keyevent info
     * @protected
     * @since 3
     *
     */
    protected onKeyDown(e: KeyEvent): boolean;
    /**
     * Handle the key up event.
     * @param {yunos.ui.event.KeyEvent} e - the keyevent info
     * @protected
     * @since 3
     *
     */
    protected onKeyUp(e: KeyEvent): boolean;
    private startFlingY(velocityY: number, decelerationY: number): void;
    private onRebackY(): void;
    private drawSeparateLines(): void;
    private refresh(): boolean;
    private drawNumber(): void;
    private updateNumber(): void;
    private clearTimer(timerFunc: Object): void;
    private updateScrollerHeight(): void;
    private onTap(e: Object): void;
    private changeTextStyle(text?: TextView): void;
    private onScrollChange(property: string): void;
    private static readonly Align: {
        Left: int;
        Right: int;
        Center: int;
    };
    /**
     * Enum for NumberPicker loop's mode.
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly LoopMode: {
        /**
         * (default) - the NumberPicker will loop.
         * @public
         * @since 1
         */
        Loop: int;
        /**
         * the NumberPicker will not loop.
         * @public
         * @since 1
         */
        NoLoop: int;
    };
}
export = NumberPicker;
