import CompositeView = require("yunos/ui/view/CompositeView");
import Loading = require("yunos/ui/widget/Loading");
import View = require("yunos/ui/view/View");
import Bitmap = require("yunos/graphics/Bitmap");
import Gradient = require("yunos/graphics/Gradient");
import Event = require("yunos/ui/event/Event");
import { MultiState } from "../util/TypeHelper";
interface IButtonOption {
    text: string;
    multiState?: MultiState;
}
interface ILoadingOption {
    loadingSizeStyle?: number;
    tipText?: string;
    tipTextStyle?: Object;
}
interface IStaticOption {
    imgSrc?: string;
    tipText?: string;
    tipTextStyle?: Object;
    buttonOptions?: IButtonOption[];
}
interface IState {
    name: string;
    displayMode: string;
    option: ILoadingOption | IStaticOption;
}
interface IStyle {
    width: number;
    height: number;
    background: string | number | Bitmap | Gradient;
    loadingSizeStyle: number;
    loadingText: string;
    loadingTextStyle: Object;
    errorImgSrc: string;
    errorText: string;
    errorTextStyle: Object;
    errorButtonTexts: string | string[];
    errorButtonMultiStates: MultiState | MultiState[];
    emptyImgSrc: string;
    emptyText: string;
    emptyTextStyle: Object;
    emptyButtonTexts: string | string[];
    emptyButtonMultiStates: MultiState | MultiState[];
}
/**
 * <p>The placeholer view for loading, it offer three display state [Loading, Empty, Error] by default.</p>
 * @example
 * let loadingView = new LoadingView();
   loadingView.width = 600;
   loadingView.height = 400;
   loadingView.loadingSizeStyle = Loading.SizeStyle.Big;
   loadingView.loadingTextStyle = {
        "color": "red",
        "fontSize": "28px"
   };
   loadingView.loadingText = "loading";
   loadingView.errorText = "error";
   loadingView.emptyText = "empty";
 * loadingView.errorImageSrc = "page://systemres.yunos.com/assets/caf_images/aui/caf_playbar_cover.png";
   loadingView.emptyImageSrc = null;
 * loadingView.errorButtons = ["left", {
        text: "right",
        multiState: {
            normal: {
                background: "yellow"
            },
            pressed: {
                background: "#3399FF"
            }
        }
   }];
   loadingView.emptyButtons = null;
 * loadingView.displayState = LoadingView.DisplayState.Error;
 * loadingView.on("buttontap", (index, event) => {
        Log.d("LoadingView", "button %d tap", index);
   });
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 6
 */
declare class LoadingView extends CompositeView {
    private _defaultWidth;
    private _defaultHeight;
    private _defaultBackground;
    private _displayStates;
    private _curState;
    private _curContainer;
    private _loading;
    private _loadingSizeStyle;
    private _loadingText;
    private _loadingTextStyle;
    private _errorImgSrc;
    private _errorText;
    private _errorTextStyle;
    private _errorButtonOptions;
    private _emptyImgSrc;
    private _emptyText;
    private _emptyTextStyle;
    private _emptyButtonOptions;
    /**
     * <p>The state of display.</p>
     * <p>LoadingView offer [Loading, Error, Empty] in LoadingView.DisplayState by default.</p>
     * @type {string}
     * @default yunos.ui.widget.LoadingView.DisplayState.Loading
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 6
     */
    public displayState: string;
    /**
     * <p>Loading sizeStyle for Loading state.</p>
     * @type {yunos.ui.widget.Loading.SizeStyle}
     * @default yunos.ui.widget.Loading.SizeStyle.Big
     * @throws {TypeError} If this value is not a string of Loading.SizeStyle.
     * @public
     * @since 6
     */
    public loadingSizeStyle: number;
    /**
     * <p>Tip text for Loading state.</p>
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 6
     */
    public loadingText: string;
    /**
     * <p>Tip text style for Loading state.</p>
     * @type {Object}
     * @throws {TypeError} If this value is not a Object.
     * @public
     * @since 6
     */
    public loadingTextStyle: Object;
    /**
     * <p>Image src for Error state.</p>
     * @type {string}
     * @throws {TypeError} If this value is not a string | null.
     * @public
     * @since 6
     */
    public errorImageSrc: string;
    /**
     * <p>Tip text for Error state.</p>
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 6
     */
    public errorText: string;
    /**
     * <p>Tip text style for Error state.</p>
     * @type {Object}
     * @throws {TypeError} If this value is not a Object.
     * @public
     * @since 6
     */
    public errorTextStyle: Object;
    /**
     * <p>Image src for Empty state.</p>
     * @type {string}
     * @throws {TypeError} If this value is not a string | null.
     * @public
     * @since 6
     */
    public emptyImageSrc: string;
    /**
     * <p>Tip text for Empty state.</p>
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 6
     */
    public emptyText: string;
    /**
     * <p>Tip text style for Empty state.</p>
     * @type {Object}
     * @throws {TypeError} If this value is not a Object.
     * @public
     * @since 6
     */
    public emptyTextStyle: Object;
    /**
     * <p>View container of current state.</p>
     * @type {yunons.ui.view.View}
     * @readonly
     * @public
     * @since 6
     */
    public readonly container: View;
    /**
     * <p>Loading of this LoadingView.</p>
     * <p>It will return null if current dispaly state is not LoadingView.DisplayMode.LoadingMode.</p>
     * @type {yunons.ui.widget.Loading}
     * @readonly
     * @public
     * @since 6
     */
    public readonly loading: Loading;
    /**
     * <p>Set buttons for Error state, and it can be null.</p>
     * <p>Each item in array can be two types:<br>
     * 1.string, which means that the text of button.<br>
     * 2.object, which should include key-value pairs: text, multistate. Only text is necessary.</p>
     * @type {string | IButtonOption)[] | null}
     * @throws {TypeError} If this value is not a Array or null.
     * @public
     * @since 6
     */
    public errorButtons: (string | IButtonOption)[] | null;
    /**
     * <p>Set buttons for Empty state, and it can be null.</p>
     * <p>Each item in array can be two types:<br>
     * 1.string, which means that the text of button.<br>
     * 2.object, which should include key-value pairs: text, multistate. Only text is necessary.</p>
     * @type {string | IButtonOption)[] | null}
     * @throws {TypeError} If this value is not a Array or null.
     * @public
     * @since 6
     */
    public emptyButtons: (string | IButtonOption)[] | null;
    /**
     * <p>Set buttons for states of which DispalyMode is StaticMode.</p>
     * @protected
     * @since 6
     */
    protected setButtons(stateName: string, buttons: (string | IButtonOption)[] | null): void;
    /**
     * <p>Add new display state to LoadingView._displayStates.</p>
     * @param {string} name The name of new display state
     * @param {string} mode The display mode of new display state
     * @param {ILoadingOption | IStaticOption} option The option of new display state
     * @throws {TypeError} If this name is not a string | mode is not a string | option is not a Object.
     * @protected
     * @since 6
     */
    protected addDisplayState(name: string, mode: string, option: ILoadingOption | IStaticOption): void;
    /**
     * <p>Update display state option in LoadingView._displayStates.</p>
     * @param {string} name The name of the display state
     * @param {ILoadingOption | IStaticOption} diffOption The diffevent option of the display state
     * @throws {TypeError} If this name is not a string | diffOption is not a Object.
     * @protected
     * @since 6
     */
    protected updateDisplayState(name: string, diffOption: ILoadingOption | IStaticOption): void;
    /**
     * Implement this to apply style
     * @param {IStyle} style - style config
     * @override
     * @protected
     * @since 4
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {IStyle} style - New style config from theme.
     * @param {IStyle} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     */
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    private getLayoutPath(mode: string): string;
    private initDisplayStates(): void;
    /**
     * <p>Find display state from LoadingView._displayStates by name.</p>
     * @param {string} name the name of display state
     * @returns {Object | null} state : the display state found from array | index : the index of array
     * @protected
     * @since 6
     */
    protected findStateFromArray(name: string): {
        state: IState;
        index: number;
    };
    /**
     * <p>Reload view container by display state.</p>
     * @param {IState} state the display state to reload
     * @protected
     * @since 6
     */
    protected reloadViewContainer(state: IState): void;
    /**
     * <p>Refresh display.</p>
     * @param {ILoadingOption | IStaticOption} diffOption the diffevent option to refresh
     * @protected
     * @since 6
     */
    protected refreshDisplay(diffOption?: ILoadingOption | IStaticOption): void;
    private refreshLoadingDiaplay(diffOption?: ILoadingOption): void;
    private refreshStaticDiaplay(diffOption?: IStaticOption): void;
    private displayButtonContainer(buttonContainer: CompositeView, options: (string | IButtonOption)[] | null): void;
    private parseButtonOption(texts: string | string[], multiStates: MultiState | MultiState[]): IButtonOption[];
    /**
     * <p>Listener function for button tap event.</p>
     * @param {number} index - the button event
     * @param {yunos.ui.event.Event} event - the event
     * @protected
     * @since 6
     */
    protected onButtonTap(index: number, event?: Event): void;
    /**
     * <p>The display state of loadingview.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 6
     */
    public static readonly DisplayState: {
        /**
         * Display state for loading.
         * @type {string}
         * @public
         * @since 6
         */
        Loading: string;
        /**
         * Display state for error.
         * @type {string}
         * @public
         * @since 6
         */
        Error: string;
        /**
         * Display state for empty.
         * @type {string}
         * @public
         * @since 6
         */
        Empty: string;
    };
    /**
     * <p>The display mode of loadingview.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 6
     */
    public static readonly DisplayMode: {
        /**
        * Display mode for loading layout.
        * @type {string}
        * @public
        * @since 6
        */
        LoadingMode: string;
        /**
        * Display mode for static layout.
        * @type {string}
        * @public
        * @since 6
        */
        StaticMode: string;
    };
}
export = LoadingView;
