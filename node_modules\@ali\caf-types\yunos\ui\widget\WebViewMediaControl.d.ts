import CompositeView = require("../view/CompositeView");
/**
 * @private
 */
declare class WebViewMediaControl extends CompositeView {
    private _isControlShown;
    private _isFullScreen;
    private _isReleased;
    private _isHidden;
    private res;
    private _isLandscape;
    private _originBrightness;
    private _originBrightnessMode;
    private _brightness;
    private _brightnessModeModified;
    private _hasGotOriginBrightness;
    private _volume;
    private _currentTime;
    private _duration;
    private _gestureType;
    private _currentPlayerId;
    private _surfaceViewArray;
    private _title;
    private _useImageRotation;
    private _rotationTimer;
    private _webNode;
    private _controlWindow;
    private _windowId;
    private windowObject;
    private _remindButton;
    private _footBar;
    private _lastTouchX;
    private _lastTouchY;
    private _firstTouchX;
    private _firstTouchY;
    private _centerPanel;
    private _imageView;
    private _titleBar;
    private _loadingPanel;
    private _leftview;
    private _rightview;
    private _topview;
    private _bottomview;
    private _surfaceId;
    private _surfaceHandle;
    private _fullscreen;
    private _brightnessMode;
    private bindBackKey(): void;
    private createParentWindow(isLandscape: boolean): void;
    private prepare(playerId: number, surfaceType: number, rotationType: number): void;
    private initConnections(): void;
    private handleGesture(distance: number, type: number): void;
    private initGesture(): void;
    private createControls(): void;
    private updateBuffer(percent: number): void;
    private setFullscreen(fullscreen: boolean): void;
    private show(): void;
    private hide(): void;
    private showControls(): void;
    private hideControls(): void;
    private setControlHiddenTimeout(): void;
    private clearControlHiddenTimeout(): void;
    private windowId: Object;
    private surfaceId: Object;
    private surfaceHandle: Object;
    private title: string;
    private play(): void;
    private pause(): void;
    private seek(value: number): void;
    private exitFullscreen(): void;
    private isPlaying(): boolean;
    private isEnded(): boolean;
    private handleVolume(value: number): void;
    private handleLight(value: number): void;
    private handleProgress(value: number): void;
    private getOriginBrightness(): void;
    private setBrightness(value: number): void;
    private restoreBrightness(): void;
    private getData(name: string, callback: (error: Object, value: number) => void): void;
    private insertData(name: string, value: number, callback?: Function): void;
    private volume: number;
    private brightness: number;
    private currentTime: number;
    private sizeChanged(width: number, height: number): void;
    private useImageRotation: number;
    private readonly videoRect: int[];
    private readonly canRotate: boolean;
    private release(): void;
    private fullscreenChanged(isFullscreen: boolean, webview: Object, webMediaPrivate: Object): void;
}
declare namespace WebViewMediaControl {
    /**
     * @private
     */
    class TitleBar extends CompositeView {
        private _title;
        private _mainPanel;
        public constructor(context: Object, mainPanel: Object);
        private setTitle(title: string): void;
    }
    /**
     * @private
     */
    class LoadingPanel extends CompositeView {
        private _loading;
        private _percent;
        public constructor(context: Object);
        private show(percent: number): void;
        private hide(): void;
    }
}
export = WebViewMediaControl;
