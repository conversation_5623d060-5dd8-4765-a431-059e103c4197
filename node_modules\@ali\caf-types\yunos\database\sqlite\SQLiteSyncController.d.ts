import DataColumn = require("yunos/database/sqlite/DataColumn");
import DataError = require("yunos/database/sqlite/DataError");
import ForeignKey = require("yunos/database/sqlite/ForeignKey");
import SQLiteDatabase = require("yunos/database/sqlite/SQLiteDatabase");
import SQLiteTransaction = require("yunos/database/sqlite/SQLiteTransaction");
import YObject = require("yunos/core/YObject");
/**
 * <p>SQLiteSyncController class, which provides APIs to support database sync with cloud</p>
 *
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @private
 */
declare class SQLiteSyncController extends YObject {
    /**
     * Create a SQLiteSyncController instance.
     *
     * @private
     */
    private constructor();
    private static createTable(tableName: string, columns: DataColumn[], foreignKeys: ForeignKey[], dbInstance: SQLiteDatabase, transaction: SQLiteTransaction, callback: (error: DataError) => void): void;
    private static setSyncParameterForTable(tableName: string, paramName: string, paramValue: string, dbInstance: SQLiteDatabase, callback: (error: DataError) => void): void;
    private static getSyncParameterForTable(tableName: string, paramName: string, dbInstance: SQLiteDatabase, callback: (error: DataError, param: Object) => void): void;
    private static readonly SyncParameter: {
        SyncTo: string;
        SortColumn: string;
    };
}
export = SQLiteSyncController;
