/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

class Log {

    static D(m: string, ...string: Object[]) {
        /* jshint unused: false */
    }

    static I(m: string, ...string: Object[]) {
        /* jshint unused: false */
    }

    static W(m: string, ...string: Object[]) {
        /* jshint unused: false */
    }

    static E(m: string, ...string: Object[]) {
        /* jshint unused: false */
    }

    static PI(m: string, ...string: Object[]) {
        /* jshint unused: false */
    }

    static PE(m: string, ...string: Object[]) {
        /* jshint unused: false */
    }
}

export = Log;
