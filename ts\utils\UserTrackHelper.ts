/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable disallowQuotedKeysInObjects
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import DataAgent = require("yunos/datadriver/DataAgent");
const log = require("./log");

const TAG = "UserTrackHelper";
const DEBUG = true;
const AppKey = "be8986d26f63e81a";
const TRACK_VERSION = "1.0.2";
let sInstance: UserTrackHelper = null;

class UserTrackHelper {
    PAGE_NOTICE: string;
    PAGE_NOTICE_DETAIL: string;
    PAGE_ONLINE_LIST: string;
    PAGE_ONLINE_LIST_QR: string;
    PAGE_ONLINE_LIST_CATE: string;
    PAGE_PLAYER: string;
    PAGE_SEARCH: string;
    PAGE_USB: string;
    PAGE_SEARCH_RESULT: string;
    PAGE_PLAY_ERROR_POP: string;
    PAGE_SWITCH_SET_POP: string;
    PAGE_BUY_TRAFFIC_POP: string;
    PAGE_LIMIT: string;
    NOTICE_TICK: string;
    NOTICE_DETAIL_CLICK: string;
    NOTICE_START: string;
    ONLINE_LIST_DLNA: string;
    ONLINE_LIST_SEARCH: string;
    ONLINE_LIST_USBENTRY: string;
    ONLINE_LIST_SLIDING: string;
    ONLINE_LIST_PICK: string;
    ONLINE_LIST_USB_STATE: string;
    ONLINE_LIST_CP_ICON: string;
    ONLINE_LIST_QR_CLOSE: string;
    ONLINE_LIST_CATEGORY_ICON: string;
    ONLINE_LIST_CATE_PICK: string;
    ONLINE_LIST_QR_RETRY: string;
    ONLINE_LIST_CATE_CLOSE: string;
    SEARCH_GO_SEARCH: string;
    SEARCH_CLEAR_HISTROY: string;
    SEARCH_CLICK_HISTROY: string;
    SEARCH_RESULT_PICK: string;
    USB_SORT: string;
    USB_PICK: string;
    USB_SLIDING: string;
    USB_BACK: string;
    PLAYER_SLIDE: string;
    PLAYER_PLAY_PAUSE: string;
    PLAYER_NEXT: string;
    PLAYER_DRAG: string;
    PLAYER_PREV: string;
    PLAYER_BACK: string;
    PLAYING_STATUS: string;
    PLAY_TOTAL_TIME: string;
    SWITCH_SET_POP_CLICK: string;
    PLAY_ERROR_CLICK: string;
    BUY_TRAFFIC_POP_CLICK: string;
    SUCCESS: string;
    FAIL: string;
    EMPTY: string;
    PLAY_ERROR: string;

    CP_ID: string;
    VideoFrom: {ONLINE: string; USB: string; AIRPLAY: string;};
    TriggerType: {CLICK: string; SLIDE: string; HARDKEY: string; VOICE: string; SYSTEM: string};
    constructor(pageName: string) {
        this.getDataAgent(pageName).configure(AppKey, {"TrackVersion": TRACK_VERSION});
        this.PAGE_NOTICE = "video_notice";
        this.PAGE_NOTICE_DETAIL = "video_noticedetail";
        this.PAGE_ONLINE_LIST = "video_onlinelist";
        this.PAGE_ONLINE_LIST_QR = "video_onlinelist_qr";
        this.PAGE_ONLINE_LIST_CATE = "video_onlinelist_cate";
        this.PAGE_PLAYER = "video_player";
        this.PAGE_SEARCH = "video_search";
        this.PAGE_SEARCH_RESULT = "video_searchresult";
        this.PAGE_USB = "video_usb";
        this.PAGE_BUY_TRAFFIC_POP = "video_buytrafficpop";
        this.PAGE_SWITCH_SET_POP = "video_switchsetpop";
        this.PAGE_PLAY_ERROR_POP = "video_playerrorpop";
        this.PAGE_LIMIT = "video_limit";

        this.NOTICE_TICK = "video_notice_tick";
        this.NOTICE_DETAIL_CLICK = "video_notice_detailclick";
        this.NOTICE_START = "video_notice_start";

        this.ONLINE_LIST_DLNA = "video_onlinelist_airplay";
        this.ONLINE_LIST_SEARCH = "video_onlinelist_search";
        this.ONLINE_LIST_USBENTRY = "video_onlinelist_usbentry";
        this.ONLINE_LIST_USB_STATE = "video_usb_state";
        this.ONLINE_LIST_SLIDING = "video_onlinelist_sliding";
        this.ONLINE_LIST_PICK = "video_onlinelist_pick";
        this.ONLINE_LIST_CP_ICON = "video_onlinelist_cpicon";
        this.ONLINE_LIST_QR_CLOSE = "video_onlinelist_qr_close";
        this.ONLINE_LIST_QR_RETRY = "video_onlinelist_qr_retry";
        this.ONLINE_LIST_CATEGORY_ICON = "video_onlinelist_categoryicon";
        this.ONLINE_LIST_CATE_PICK = "video_onlinelist_cate_pick";
        this.ONLINE_LIST_CATE_CLOSE = "video_onlinelist_cate_close";

        this.SEARCH_GO_SEARCH = "video_search_gosearch";
        this.SEARCH_CLEAR_HISTROY = "video_search_clearhistroy";
        this.SEARCH_CLICK_HISTROY = "video_search_clickhistroy";
        this.SEARCH_RESULT_PICK = "video_searchresult_pick";

        this.USB_SORT = "video_usb_sort";
        this.USB_PICK = "video_usb_pick";
        this.USB_SLIDING = "video_usb_sliding";
        this.USB_BACK = "video_usb_back";

        this.PLAYER_SLIDE = "video_player_slide";
        this.PLAYER_PLAY_PAUSE = "video_player_play";
        this.PLAYER_PREV = "video_player_last";
        this.PLAYER_NEXT = "video_player_next";
        this.PLAYER_DRAG = "video_player_drag";
        this.PLAYER_BACK = "video_player_back";
        this.PLAYING_STATUS = "video_playing_status";
        this.PLAY_TOTAL_TIME = "video_play";
        this.PLAY_ERROR = "video_error";

        this.BUY_TRAFFIC_POP_CLICK = "video_buytrafficpop_click";
        this.SWITCH_SET_POP_CLICK = "video_switchsetpop_click";
        this.PLAY_ERROR_CLICK = "video_playerrorpop_click";

        this.SUCCESS = "success";
        this.FAIL = "fail";
        this.EMPTY = "empty";

        this.CP_ID = "kuaishou";
        this.VideoFrom = {
            ONLINE: "online",
            USB: "usb",
            AIRPLAY: "airplay"
        };
        this.TriggerType = {
            CLICK: "click",
            SLIDE: "slide",
            HARDKEY: "hardkey",
            VOICE: "voice",
            SYSTEM: "sys"
        };
    }

    clickButton(pageName: string, ctrlName: string, paramObj: Object) {
        paramObj = paramObj || {};
        this.getDataAgent(pageName).clickButton(ctrlName, paramObj);
        if (DEBUG) {
            log.I(TAG, "clickButton", pageName, ctrlName, paramObj);
        }
    }

    sendEvent(pageName: string, eventName: string, paramObj: Object) {
        paramObj = paramObj || {};
        this.getDataAgent(pageName).sendEvent(eventName, paramObj);
        if (DEBUG) {
            log.I(TAG, "sendEvent", eventName, pageName, paramObj);
        }
    }

    enterPage(pageName: string, paramObj?: Object) {
        paramObj = paramObj || {};
        this.getDataAgent(pageName).enterPage(paramObj);
        if (DEBUG) {
            log.I(TAG, "enterPage", pageName, paramObj);
        }
    }

    leavePage(pageName: string, paramObj: Object) {
        paramObj = paramObj || {};
        this.getDataAgent(pageName).leavePage(paramObj);
        if (DEBUG) {
            log.I(TAG, "leavePage", pageName, paramObj);
        }
    }

    getDataAgent(pageName: string) {
        if (typeof pageName !== "string" || pageName.length === 0) {
            pageName = "page://video.alios.cn/video";
        }
        return DataAgent.getInstance(pageName);
    }

    static getInstance(pageName?: string) {
        if (!sInstance) {
            sInstance = new UserTrackHelper(pageName);
        }
        return sInstance;
    }
}

export = UserTrackHelper;
