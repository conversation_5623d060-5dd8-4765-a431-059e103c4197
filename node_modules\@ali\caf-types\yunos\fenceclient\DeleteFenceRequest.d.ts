import YObject = require("../core/YObject");
import { DeleteFenceRequestObj } from "./TypeInner";
/**
 * <p>A data object that contains request parameters to FenceClient.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class DeleteFenceRequest extends YObject {
    public _native: DeleteFenceRequestObj;
    public constructor();
    /**
     * <p>When the value is true, it means to delete the cloud integrated fence,
     * when the value is false, delete the local fence</p>
     * @name yunos.fenceclient.DeleteFenceRequest#cloudFence
     * @type {yunos.fenceclient.DeleteFenceRequest}
     * @public
     * @since 5
     */
    public cloudFence: boolean;
    /**
     * <p>businessId of the request.</p>
     * @name yunos.fenceclient.DeleteFenceRequest#businessId
     * @type {yunos.fenceclient.DeleteFenceRequest}
     * @public
     * @since 5
     */
    public businessId: string;
    /**
     * <p>groupId of the request.</p>
     * @name yunos.fenceclient.DeleteFenceRequest#groupId
     * @type {yunos.fenceclient.DeleteFenceRequest}
     * @public
     * @since 5
     */
    public groupId: string;
    /**
     * <p>fenceId of the request.</p>
     * @name yunos.fenceclient.DeleteFenceRequest#fenceId
     * @type {yunos.fenceclient.DeleteFenceRequest}
     * @public
     * @since 5
     */
    public fenceId: string;
}
export = DeleteFenceRequest;
