import YObject = require("../core/YObject");
/**
 * <p>The Color class defines methods for converting color type.</p>
 * <p>Colors are represented as hex(include 3-tunnel hex and 4-tunnel hex, supported formats are: #rgb, #rgba, #rrggbb, #rrggbbaa),<br>
 * rgb(include rgb and rgba, supported formats are: rgb(r,g,b), rgba(r,g,b,a)),<br>
 * hsl(include hsl and hsla, supported formats are: hsl(h,s,l), hsla(h,s,l,a)),<br>
 * extended color keywords of w3c supported and transparent.</p>
 * <p>The format of 3-tunnel hex is a ‘#’ immediately followed by either three or six hexadecimal characters.<br>
 * The three-digit (#rgb) is converted into six-digit form (#rrggbb) by replicating digits, not by adding zeros.<br>
 * For example, #fb0 expands to #ffbb00. This ensures that white (#ffffff)<br>
 * can be specified with the short notation (#fff) and removes any dependencies on the color depth of the display.</p>
 * <p>The format of 4-tunnel hex is a ‘#’ immediately followed by either four or eight hexadecimal characters.<br>
 * The four-digit (#rgba) is converted into eight-digit form (#rrggbbaa), according to the rules of 3-tunnel hex.</p>
 * <p>the format of an RGB is ‘rgb(’ followed by a comma-separated list of three numerical values<br>
 * (either three integer values or three percentage values) followed by ‘)’.<br>
 * The integer value 255 corresponds to 100%, and to F or FF in the hexadecimal notation:<br>
 * rgb(255,255,255) = rgb(100%,100%,100%) = #FFF. White space characters are allowed around the numerical values.</p>
 * <p>The format of an RGBA is ‘rgba(’ followed by a comma-separated list of three numerical values<br>
 * (either three integer values or three percentage values), followed by an <alphavalue>, followed by ‘)’.<br>
 * The integer value 255 corresponds to 100%, rgba(255,255,255,0.8) = rgba(100%,100%,100%,0.8).<br>
 * White space characters are allowed around the numerical values.</p>
 * <p>HSL colors are encoding as a triple (hue, saturation, lightness).<br>
 * Hue is represented as an angle of the color circle (i.e. the rainbow represented in a circle).<br>
 * This angle is so typically measured in degrees that the unit is implicit; syntactically, only a <number> is given.<br>
 * By definition red=0=360, and the other colors are spread around the circle, so green=120, blue=240, etc.<br>
 * As an angle, it implicitly wraps around such that -120=240 and 480=120.<br>
 * One way an implementation could normalize such an angle x to the range [0,360)<br>
 * (i.e. zero degrees, inclusive, to 360 degrees, exclusive) is to compute (((x mod 360) + 360) mod 360).<br>
 * Saturation and lightness are represented as percentages. 100% is full saturation, and 0% is a shade of gray.<br>
 * 0% lightness is black, 100% lightness is white, and 50% lightness is “normal”.</p>
 * <p>The format of an HSLA is ‘hsla(’ followed by the hue in degrees,<br>
 * saturation and lightness as a percentage, and an <alphavalue>, followed by ‘)’.<br>
 * White space characters are allowed around the numerical values.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 1
 */
declare class Color extends YObject {
    private _alpha;
    private _blue;
    private _green;
    private _red;
    /**
     * @public
     * @since 1
     * @param {string|number} color Creating a Color from given color value.
     */
    public constructor(color?: string | number | object);
    /**
     * <p>Alpha component of the color.</p>
     * <p>Alpha value is in the range [0.0,1.0], if the set value is not in this range, the value will be reset to the closest legal value.</p>
     * <p>if the color is six digit hex value or rgb(...) or hsl(...), with an alpha value of 1.</p>
     * @name yunos.graphics.Color#alpha
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public alpha: number;
    /**
     * <p>Blue component of the color.</p>
     * <p>Blue value is in the range [0,255], if the set value is not in this range, the value will be reset to the closest legal value.</p>
     * @name yunos.graphics.Color#blue
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public blue: number;
    /**
     * <p>Green component of the color.</p>
     * <p>Green value is in the range [0,255], if the set value is not in this range, the value will be reset to the closest legal value.</p>
     * @name yunos.graphics.Color#green
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public green: number;
    /**
     * <p>Red component of the color.</p>
     * <p>Red value is in the range [0,255], if the set value is not in this range, the value will be reset to the closest legal value.</p>
     * @name yunos.graphics.Color#red
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public red: number;
    /** ****************************   public property method *************************************** */
    /**
     * <p>Transform color string to hex.</p>
     * <p>Returned 3-tunnel hex or 4-tunnel hex in the format(#rrggbb or #rrggbbaa).</p>
     * @return {string} 3-tunnel hex or 4-tunnel hex of current color
     * @public
     * @since 1
     */
    public toHex(): string;
    /**
     * <p>Transform color string to hsl or hsla string.</p>
     * <p>Returned hsl or hsla string in the format(hsl(h,s,l) or hsla(h,s,l,a)).</p>
     * @return {string} hsl or hsla string of current color
     * @public
     * @since 1
     */
    public toHslString(): string;
    /**
     * <p>Transform color string to rgb or rgba string.</p>
     * <p>Returned rgb or rgba string in the format(rgb(r,g,b) or rgba(r,g,b,a)).</p>
     * @returns {string} rgb or rgba string of current color
     * @public
     * @since 1
     */
    public toRgbString(): string;
    /**
     * <p>Transform color string to hsv or hsva string.</p>
     * <p>Returned hsv or hslv string in the format(hsv(h,s,l) or hsva(h,s,l,a)).</p>
     * @return {string} hsv or hsva string of current color
     * @public
     * @since 2
     */
    public toHsvString(): string;
    public toRgbaNumber(): number;
    public toArgbNumber(): number;
    public toBgraNumber(): number;
    /** ****************************   public static method  **************************************** */
    /**
     * <p>Return the corresponding hex value of the color name.</p>
     * <p>Return undefined if there isn't one.</p>
     * @param {string} colorName - the color name
     * @return {string} hex value of the color
     * @public
     * @since 1
     */
    public static colorNameToHex(colorName: string): string;
    /**
     * Transform a valid color string to its hex value.
     * @param {string} color - color-related values
     * @return {string} 3-tunnel hex or 4-tunnel hex
     * @throws {TypeError} If parameter is not valid color.
     * @public
     * @since 1
     */
    public static colorToHex(color: string): string;
    /**
     * <p>Convert the hex to the given format.</p>
     * <p>The format is a ‘#’ immediately followed by the arrangement of Letters r, g, b, a or rr, gg, bb, aa.<br>
     * or may not have a or aa.</p>
     * @example
     * // return #55223344
     * Color.convertHex("#22334455", "#rrggbbaa", "#aarrggbb");
     * // return #5234
     * Color.convertHex("#2345", "#rgba", "#argb");
     * @param {string} hex - the hex to convert
     * @param {string} inputFormat - the format of the hex to be converted
     * @param {string} outFormat - the converted hex format
     * @return {string} hex with the fixed format
     * @throws {TypeError} If hex is not valid.
     * @throws {TypeError} If the fomat is not string.
     * @throws {RangeError} If parameters without the same format.
     * @public
     * @since 1
     */
    public static convertHex(hex: string, inputFormat: string, outFormat: string): string;
    /**
     * <p>Create a 4-tunnel hex value from 3-tunnel hex value and transparency.<br>
     * The hex value returned is in the format(#RRGGBBAA).</p>
     * @param {string} hex - 3-tunnel hex
     * @param {number} transparency - transparency
     * @return {string} 4-tunnel hex
     * @throws {TypeError} If transparency is not a number.
     * @public
     * @since 1
     */
    public static hex3ToHex4(hex: string, transparency: number): string;
    /**
     * Transform 3-tunnel hex value to rgb array.
     * @param {string} hex - 3-tunnel hex.
     * @return {number[]} rgb array, the array's length is 3, value followed by red, green, blue
     * @throws {TypeError} If parameter is not valid hex.
     * @public
     * @since 1
     */
    public static hex3ToRgb(hex: string): Array<number>;
    /**
     * Transform 4-tunnel hex value to rgba array.
     * @param {string} hex - 4-tunnel hex.
     * @return {number[]} rgba array, the array's length is 4, value followed by red, green, alpha
     * @throws {TypeError} If parameter is not valid hex.
     * @public
     * @since 1
     */
    public static hex4ToRgba(hex: string): Array<number>;
    /**
     * Transform hsla value to hex value.
     * Accepts either: 1 hsla string, or 4 values h,s,l,a.
     * @param {number|string} h - hue-value or hsla string.
     * @param {number} s - saturation-value
     * @param {number} l - lightness-value
     * @param {number} a - alpha-value
     * @return {string} 4-tunnel hex
     * @public
     * @since 1
     */
    public static hslaToHex4(h: number, s: number, l: number, a: number): string;
    /**
     * <p>Transform hsla value to rgba value array.<br>
     * Accepts either: 1 hsla string, or 4 values h,s,l,a.</p>
     * @param {number|string} h - hue-value or hsla string.
     * @param {number} s - saturation-value
     * @param {number} l - lightness-value
     * @param {number} a - alpha-value
     * @return {number[]} rgba array, the array's length is 4, value followed by red, green, blue, alpha
     * @public
     * @since 1
     */
    public static hslaToRgba(h: number, s: number, l: number, a: number): Array<number>;
    /**
     * <p>Transform hsl value to hex.<br>
     * Accepts either: 1 hsl string, or 3 values h,s,l.</p>
     * @param {number|string} h - hue-value or hsl string.
     * @param {number} s - saturation-value
     * @param {number} l - lightness-value
     * @return {string} 3-tunnel hex
     * @public
     * @since 1
     */
    public static hslToHex3(h: number, s: number, l: number): string;
    /**
     * <p>Transform hsl value to rgb value array.<br>
     * Accepts either: 1 hsl string, or 3 values h,s,l.</p>
     * @param {number|string} h - hue-value or hsla string.
     * @param {number} s - saturation-value
     * @param {number} l - lightness-value
     * @return {number[]} rgb array, the array's length is 3, value followed by red, green, blue
     * @public
     * @since 1
     */
    public static hslToRgb(h: number, s: number, l: number): Array<number>;
    /**
     * Transform hsva value to hex value.
     * Accepts either: 1 hsla string, or 4 values h,s,v,a.
     * @param {number|string} h - hue-value or hsla string.
     * @param {number} s - saturation-value
     * @param {number} v - lightness-value
     * @param {number} a - alpha-value
     * @return {string} 4-tunnel hex
     * @public
     * @since 1
     */
    public static hsvaToHex4(h: number, s: number, v: number, a: number): string;
    /**
     * <p>Transform hsva value to rgba value array.<br>
     * Accepts either: 1 hsva string, or 4 values h,s,v,a.</p>
     * @param {number|string} h - hue-value or hsva string.
     * @param {number} s - saturation-value
     * @param {number} v - value-value
     * @param {number} a - alpha-value
     * @return {number[]} rgba array, the array's length is 4, value followed by red, green, blue, alpha
     * @public
     * @since 1
     */
    public static hsvaToRgba(h: number, s: number, v: number, a: number): Array<number>;
    /**
     * <p>Transform hsv value to hex.<br>
     * Accepts either: 1 hsv string, or 3 values h,s,v.</p>
     * @param {number|string} h - hue-value or hsv string.
     * @param {number} s - saturation-value
     * @param {number} l - value-value
     * @return {string} 3-tunnel hex
     * @public
     * @since 2
     */
    public static hsvToHex3(h: number, s: number, v: number): string;
    /**
     * <p>Transform hsv value to rgb value array.<br>
     * Accepts either: 1 hsl string, or 3 values h,s,v.</p>
     * @param {number|string} h - hue-value or hsla string.
     * @param {number} s - saturation-value
     * @param {number} v - value-value
     * @return {number[]} rgb array, the array's length is 3, value followed by red, green, blue
     * @public
     * @since 2
     */
    public static hsvToRgb(h: number, s: number, v: number): Array<number>;
    /**
     * Check if the string is a valid color string.
     * @param {string} color - the color string to check
     * @return {boolean} true if color is valid, false otherwise.
     * @public
     * @since 1
     */
    public static isValidColor(color: string | number): boolean;
    /**
     * Color superimposition with alpha channel.
     * @param {string} background - color-related values
     * @param {string} foreground - color-related values
     * @return {Color.ColorOverlay} components of the color, object.red, object.green, object.blue and object.alpha.
     * @public
     * @since 4
     *
     */
    public static overlay(background: string, foreground: string): Color.ColorOverlay;
    /**
     * <p>Transform a valid color string to rgba object.</p>
     * @param {string|number} color - color-related values
     * @return {Color.ColorOverlay} components of the color, object.red, object.green, object.blue and object.alpha.
     * @throws {TypeError} If parameter is not valid color.
     * @public
     * @since 1
     */
    public static parse(color: string | number): Color.ColorOverlay;
    /**
     * <p>Transform rgba value to hex value.<br>
     * Accepts either: 1 rgba string, or 4 values r,g,b,a.</p>
     * @param {number|string} r - red-value or rgba string.
     * @param {number} g - green-value
     * @param {number} b - blue-value
     * @param {number} a - alpha-value
     * @return {string} 4-tunnel hex
     * @public
     * @since 1
     */
    public static rgbaToHex4(r: number, g: number, b: number, a: number): string;
    /**
     * <p>Transform rgba value to hsla value array.<br>
     * Accepts either: 1 rgba string, or 4 values r,g,b,a.</p>
     * @param {number|string} r - red-value or rgba string.
     * @param {number} g - green-value
     * @param {number} b - blue-value
     * @param {number} a - alpha-value
     * @return {number[]} hsla array, the array's length is 3, value followed by hue, saturation, lightness, alpha
     * @public
     * @since 1
     */
    public static rgbaToHsla(r: number, g: number, b: number, a: number): Array<number>;
    /**
     * <p>Transform rgba value to hsva value array.<br>
     * Accepts either: 1 rgba string, or 4 values r,g,b,a.</p>
     * @param {number|string} r - red-value or rgba string.
     * @param {number} g - green-value
     * @param {number} b - blue-value
     * @param {number} a - alpha-value
     * @return {number[]} hsva array, the array's length is 4, value followed by hue, saturation, value, alpha
     * @public
     * @since 2
     */
    public static rgbaToHsva(r: number, g: number, b: number, a: number): Array<number>;
    /**
     * <p>Transform rgb value to hex value.<br>
     * Accepts either: 1 rgb string, or 3 values r,g,b.</p>
     * @param {number|string} r - red-value or rgb string
     * @param {number} g - green-value
     * @param {number} b - blue-value
     * @return {string} 3-tunnel hex
     * @public
     * @since 1
     */
    public static rgbToHex3(r: number, g: number, b: number): string;
    /**
     * <p>Transform rgb value to hsl value array.<br>
     * Accepts either: 1 rgb string, or 3 values r,g,b.</p>
     * @param {number|string} r - red-value or rgb string
     * @param {number} g - green-value
     * @param {number} b - blue-value
     * @return {number[]} hsl array, the array's length is 3, value followed by hue, saturation, lightness
     * @public
     * @since 1
     */
    public static rgbToHsl(r: number, g: number, b: number): Array<number>;
    /**
     * <p>Transform rgb value to hsv value array.<br>
     * Accepts either: 1 rgb string, or 3 values r,g,b.</p>
     * @param {number|string} r - red-value or rgb string
     * @param {number} g - green-value
     * @param {number} b - blue-value
     * @return {number[]} hsl array, the array's length is 3, value followed by hue, saturation, value
     * @public
     * @since 2
     */
    public static rgbToHsv(r: number, g: number, b: number): Array<number>;
}
declare namespace Color {
    /**
     * @public
     * @since 4
     *
     */
    interface ColorOverlay {
        alpha: number;
        red: number;
        green: number;
        blue: number;
    }
}
export = Color;
