declare const _default: {
    gregorian: {
        "generic-GENERIC": {
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
        "zh-CN": {
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
        "zh-TW": {
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
        "en-US": {
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
    };
    chinese: {
        "generic-GENERIC": {
            regularYearDecorations: int[];
            regularDayDecorations: string[];
            regularLeapMonthSymbol: string;
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
        "zh-CN": {
            regularYearDecorations: string[];
            regularDayDecorations: string[];
            regularLeapMonthSymbol: string;
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
        "zh-TW": {
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
        "en-US": {
            regularYearDecorations: string[];
            regularDayDecorations: string[];
            regularLeapMonthSymbol: string;
            regularEraSymbols: string[];
            longEraSymbols: string[];
            AMPMSymbols: string[];
            regularMonthSymbols: string[];
            shortMonthSymbols: string[];
            veryShortMonthSymbols: string[];
            regularQuarterSymbols: string[];
            shortQuarterSymbols: string[];
            regularWeekdaySymbols: string[];
            shortWeekdaySymbols: string[];
            veryShortWeekdaySymbols: string[];
            formats: {
                connector: string;
                output: string;
                dateTemplate: {
                    [x: number]: string;
                };
                timeTemplate: {
                    [x: number]: string;
                };
            };
        };
    };
};
export = _default;
