import View = require("./View");
import FocusManager = require("../FocusManager");
import Point = require("../../graphics/Point");
import Rectangle = require("../../graphics/Rectangle");
import { WindowInsets } from "./Window";
import Layout = require("../layout/Layout");
import Transition = require("../transition/Transition");
import LayoutTransition = require("../transition/LayoutTransition");
/**
 * <p>Composite view is a special view that can contain other views (called children.) <br>
 * The composite view is the base class for view containers.</p>
 * @extends yunos.ui.view.View
 * @memberof yunos.ui.view
 * @public
 * @since 1
 */
declare class CompositeView extends View {
    private _internalClipBound: boolean;
    private _clipBound: boolean;
    private _maxZorder: number;
    private _children: View[];
    private _orderedViewArray: View[];
    private _clipRadius;
    private _onNeedRelayoutFunc;
    protected _needRelayout: boolean;
    private _layoutCount;
    public _focusMode: number;
    private _focusedChild;
    public focusManager: FocusManager;
    protected _layout: Layout;
    protected _clipboardShowing: boolean;
    protected _originView: View;
    private _needLayoutNode;
    private _noSaveAbsoluteInfo;
    private _layoutTransition;
    private _transitingViews;
    private _disappearingChildren;
    private _startTransitionFunc;
    private _endTransitionFunc;
    private _needSaveAbsoluteInfo;
    /**
     * <p>Constructor that create a composite view.</p>
     * @public
     * @since 1
     */
    public constructor(...args: Object[]);
    /**
     * <p>Destructor that destroy this composite view.</p>
     * @param {boolean} recursive - destroy the children in the CompositeView if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * <p>Destructor that destroy this composite view.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>DestroyAll will call all the children's destroyAll in the CompositeView, and finily destroy it self.</p>
     * @public
     * @since 1
     */
    public destroyAll(): void;
    /**
     * <p>The layout usded in the CompositeView.</p>
     * @name yunos.ui.view.CompositeView#layout
     * @type {yunos.ui.layout.Layout}
     * @fires yunos.ui.view.View#propertychange
     * @throws {TypeError} If value is not instanceof Layout
     * @public
     * @since 1
     */
    public layout: Layout;
    /**
     * <p>The array of all children in this composite view.</p>
     * @name yunos.ui.view.CompositeView#children
     * @type {yunos.ui.view.View[]}
     * @readonly
     * @public
     * @since 1
     */
    public readonly children: View[];
    private needLayoutNode: boolean;
    private getChildViewCount(): number;
    /**
     * <p>Whether this composite view's children are clipped to
     * their bounds before drawing. By default, children are clipped to their bounds before drawing.</p>
     * @name yunos.ui.view.CompositeView#clipBound
     * @type {boolean}
     * @default true
     * @throws {TypeError} If value is not type of boolean.
     * @public
     * @since 1
     */
    public clipBound: boolean;
    /**
     * <p>Defines the clip radius of the four corners.</p>
     * @type {number}
     * @default 0
     * @throws {TypeError} If value is not a number or number array.
     * @private
     */
    private clipRadius: number | number[];
    /**
     * <p>Default clip.</p>
     * @private
     */
    private internalClipBound: boolean;
    /**
     * <p>Get firstChild of compositeView.</p>
     * @name yunos.ui.view.CompositeView#firstChild
     * @type {yunos.ui.view.View}
     * @readonly
     * @public
     * @since 1
     */
    public readonly firstChild: View;
    /**
     * <p>Get lastChild of compositeView.</p>
     * @name yunos.ui.view.CompositeView#lastChild
     * @type {yunos.ui.view.View}
     * @readonly
     * @public
     * @since 1
     */
    public readonly lastChild: View;
    /**
     * <p>The relationship between the compositeView and its descendants when looking for a view to take focus.</p>
     * <p>Allow type of CompositeView.FocusMode.SelfFirst, CompositeView.FocusMode.DescendantFirst.</p>
     * <p>SelfFirst means the compositeView can get focus before its descendants.</p>
     * <p>DescendantFirst means the compositeView can get focus only if none of its descendants can get focus.</p>
     * @name yunos.ui.view.CompositeView#focusMode
     * @type {yunos.ui.view.CompositeView.FocusMode}
     * @default yunos.ui.view.CompositeView.FocusMode.SelfFirst
     * @throws {TypeError} If type of parameter is not CompositeView.FocusMode
     * @public
     * @since 3
     *
     */
    public focusMode: number;
    private lastFocusedChild: View;
    /**
     * <p>Search the view by tagName.</p>
     * @param {string} tagName - the tag name of view.
     * @return {yunos.ui.view.View[]} Returns the view's array with the fixed tagName.
     * @override
     * @public
     * @since 1
     */
    public findViewByTagName(tagName: string): View[];
    /**
     * <p>Search the view by className.</p>
     * @param {string} className - the class name of view, ignore uppercase lowercase.
     * @return {yunos.ui.view.View[]} Returns the view's array with the fixed className.
     * @override
     * @public
     * @since 1
     */
    public findViewByClass(className: string): View[];
    /**
     * <p>Add a view to this composite view and it is on top of all other children.</p>
     * @param {yunos.ui.view.View} view - child view that added to the end of parent children.
     * @fires yunos.ui.view.CompositeView#childwilladd
     * @fires yunos.ui.view.CompositeView#childadded
     * @throws {TypeError} If view is not instance of View or view's parent has already been set.
     * @public
     * @since 1
     */
    public addChild(view: View): void;
    /**
     * <p>Insert a child view in this composite view by the specified position.</p>
     * @param {yunos.ui.view.View} view - the child view that inserted to the specified position.
     * @param {number} index - the position at which to insert the child to.
     * @fires yunos.ui.view.CompositeView#childwilladd
     * @fires yunos.ui.view.CompositeView#childadded
     * @throws {TypeError} If view is not instanceofView or arguments[1] is not typeof number.
     * @throws {RangeError} If index is less than 0 or not an integer or larger than children length.
     * @public
     * @since 1
     */
    public insertChild(view: View, index: number): void;
    /**
     * <p>Replace a child view in this composite view by the specified position.</p>
     * @param {yunos.ui.view.View} view - the child view that replace to the specified position.
     * @param {number} index - the position at which to be replaced.
     * @throws {TypeError} If view is not instanceofView or arguments[1] is not typeof number.
     * @throws {RangeError} If index is less than 0 or not an integer.
     * @public
     * @since 2
     */
    public replaceChild(view: View, index: number): void;
    private checkThemeChanged(): void;
    /**
     * <p>Remove the specified view from this composite view.</p>
     * @param {yunos.ui.view.View} view - the child view to remove from its parent children.
     * @fires yunos.ui.view.CompositeView#childwillremove
     * @fires yunos.ui.view.CompositeView#childremoved
     * @throws {TypeError} If view is not an instance of View.
     * @public
     * @since 1
     */
    public removeChild(view: View): void;
    /**
     * <p>Remove all children views from compositeview.</p>
     * @public
     * @since 1
     */
    public removeAllChildren(): void;
    /**
     * <p>Change the z order of the child so it's on top of all other children.</p>
     * @param {yunos.ui.view.View} view - the child to bring to the top of the z order.
     * @throws {TypeError} If view is not instanceof View.
     * @public
     * @since 1
     */
    public bringChildToFront(view: View): void;
    private bringChildToOtherFront(view: View, target: View): void;
    /**
     * <p>Change the z order of the child so it's on bottom of all other children.</p>
     * @param {yunos.ui.view.View} view - the child to bring to the bottom of the z order.
     * @throws {TypeError} If view is not an instance of View.
     * @public
     * @since 1
     */
    public sendChildToBack(view: View): void;
    private getLayoutTransition(): LayoutTransition;
    /**
     * <p>Add a transition in view.</p>
     * <p>A CompositeView instance can only bind one LayoutTransition instance</p>
     * @param {yunos.ui.transition.Transition} transition - the transition set in View
     * @override
     * @public
     * @since 4
     *
     */
    public addTransition(transition: Transition): void;
    /**
     * remove transition in view
     * @param {yunos.ui.transition.Transition} transition - the need remove transition
     * @override
     * @public
     * @since 4
     *
     */
    public removeTransition(transition: Transition): void;
    /**
     * remove all transition in view
     * @override
     * @public
     * @since 4
     *
     */
    public removeAllTransitions(): void;
    private addDisappearingView;
    private startViewTransition;
    private endViewTransition;
    private sendChildToOtherBack(view: View, target: View): void;
    private exchangeChild(view0: View, view1: View): void;
    /**
     * <p>Search the view by id. </p>
     * @param {string} id - the id of view.
     * @return {?yunos.ui.view.View} if find the view then return, otherwise return null.
     * @public
     * @since 1
     */
    public findViewById(id: string): View;
    /**
     * <p>Search the view's index by id. </p>
     * @param {string} id - the id of view.
     * @return {number} the index of view in the children array, if not find return -1.
     * @public
     * @since 1
     */
    public findIndexById(id: string): int;
    /**
     * <p>Find the direct child view at point.</p>
     * @param {yunos.graphics.Point} point - the point in the view.
     * @return {yunos.ui.view.View} Returns the direct child of this composite view which contains the specified point.
     * @protected
     * @since 1
     */
    protected findDirectChildViewAtPoint(point: Point): View;
    /**
     * <p>Find the view at the point.</p>
     * @param {yunos.graphics.Point} point - the point.
     * @return {yunos.ui.view.View} return a child view which contains the specified point and is on the top of all children,
     * otherwise return this composite view if it contains the specified point.
     * @override
     * @protected
     * @since 1
     */
    protected findViewAtPoint(point: Point): View;
    /**
     * Calculate the point whether in the CompositeView.
     * @param {yunos.graphics.Point} point - the point in put.
     * @return {boolean} whether point in the view or in the children's region.
     * @override
     * @public
     * @since 2
     */
    public containsPoint(point: Point): boolean;
    private layoutImmediately;
    private getRectToCurrentCoords(view: View, rect?: Rectangle): Rectangle;
    private transformPointToCurrentCoords;
    private compositeViewDoLayout(windowInsets: WindowInsets): boolean;
    /**
     * Get the focus for the CompositeView.
     * @fires yunos.ui.view.View#propertychange
     * @public
     * @since 3
     *
     */
    public focus(): void;
    private requestChildFocus(child: View, focused: View): void;
    private startLayout(windowInsets?: WindowInsets): boolean;
    /**
     * Relayout
     * @method CompositeView#relayout
     * @param {boolean} self - whether relayout this view.
     * @protected
     * @override
     * @since 1
     */
    protected relayout(isSelf?: boolean, immediately?: boolean): void;
    /**
     * autoRegisterVoice.
     * @name autoRegisterVoice
     * @type {boolean}
     * @override
     * @protected
     * @since 6
     */
    protected autoRegisterVoice: boolean;
    private registerTextVoice;
    private checkVoiceChildChange;
    /**
     * Whether need to relayout
     * @name CompositeView#needRelayout
     * @type {boolean}
     * @private
     */
    private needRelayout: boolean;
    /**
     * <p>The array of children that will be laid out by corresponding layout.</p>
     * @name yunos.ui.view.CompositeView#logicalChildren
     * @type {yunos.ui.view.View[]}
     * @readOnly
     * @private
     */
    private readonly logicalChildren: View[];
    private onNeedReLayout(): void;
    private onChildWillAdd(view: View): void;
    private onChildWillRemove(view: View): void;
    private getChildGlobalVisibleRect(target: View, rect: Rectangle): boolean;
    private showClipboardMenuForChild(originView: View, scrollY?: number): void;
    private hideClipboardMenuForChild(): void;
    private updateClipboardMenuForChild(): void;
    private onViewPropertyChange(property: string, oldValue: Object, newValue: Object): void;
    /**
     * delayChildPressedState
     * @private
     */
    private readonly delayChildPressedState: boolean;
    private startFastRender;
    private stopFastRender;
    /**
     * <p>Enum for CompositeView DescendantFocusability.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public static readonly FocusMode: {
        /**
         * <p>The compositeView will get focus before its descendants if the value is BeforeDescendants.</p>
         */
        SelfFirst: int;
        /**
         * <p>The compositeView will get focus only if none of its descendants can get focus if the value is AfterDescendants.</p>
         */
        DescendantFirst: int;
    };
}
export = CompositeView;
