import YObject = require("../../core/YObject");
/**
 * Base class for formatters.
 * @extends yunos.core.YObject
 * @memberof yunos.util.formatter
 * @abstract
 * @public
 * @since 2
 * @relyon YUNOS_SYSCAP_SYSTIME
 */
declare abstract class Formatter extends YObject {
    /**
     * Get the formatted string from a given object.
     * @param {*} obj - the object used to generate the string.
     * @param {string} [template] - template of the formatted string.
     * @return {string} generated string.
     * @abstract
     * @public
     * @since 2
     */
    public getString(obj: Object, template?: string): string;
}
export = Formatter;
