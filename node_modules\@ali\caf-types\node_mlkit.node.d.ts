
export class Tensor {
    getName(): string;
    setName(value: string): void;
    getDims(): number[];
    setDims(value: number[]): void;
    getDataType(): number;
    setDataType(value: number): void;
    getData(): ArrayBuffer;
    setData(value: A<PERSON>yBuffer | Buffer): void;
}

export class MLKitNode {
    setProcessCallback(nativeCallback: Function): boolean;
    init(modelFile: string, nativeCallback: Function,
        config: Map<string, Object>): boolean;
    setInputTensors(inputs: Tensor[]): boolean;
    predict(outputNames: string[], nativeCallback: Function, isSync: boolean): boolean | Tensor[];
    destroy(): void;
}
