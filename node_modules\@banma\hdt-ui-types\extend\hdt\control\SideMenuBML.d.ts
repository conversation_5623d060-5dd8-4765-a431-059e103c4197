import Bitmap = require("yunos/graphics/Bitmap");
import TabBar = require("yunos/ui/view/TabBar");
import Gradient = require("yunos/graphics/Gradient");
interface IStyle {
    textAnimationDuration: number;
    iconSelectedColor: string;
    iconNormalColor: string;
    mainTabGapMore: number;
    fontMainSize: number;
    tabItemWidth: number;
    tabItemHeight: number;
    firstTabMarginStart: number;
    bgAnimationViewColor: string | number | Bitmap | Gradient;
    animationViewBorderRadius: number | number[];
    animationViewCapinsets: number | number[];
    animationViewBorderWidth: number;
    animationViewBorderColor: string | Gradient;
    animationViewShadowOffsetX: number;
    animationViewShadowOffsetY: number;
    animationViewShadowRadius: number;
    animationViewShadowColor: string;
    bgAnimationViewMarginLeft: number;
    sourceAnimationDuration: number;
    barWidth: number;
    barHeight: number;
    barAnimationViewColor: string | number | Bitmap | Gradient;
    barAnimationViewMarginLeft: number;
    targetAnimationDuration: number;
    textMainColor: string;
    currentTextMainColor: string;
}
declare class SideMenuBML extends TabBar {
    private __textAnimationDuration;
    private _iconNormalColor;
    private _iconSelectedColor;
    private _items;
    private _maxlength;
    private __fontMainSize;
    private __firstTabMarginStart;
    private _bgAnimationViewColor;
    private _animationViewBorderRadius;
    private _animationViewCapinsets;
    private _animationViewBorderWidth;
    private _animationViewBorderColor;
    private _animationViewShadowOffsetX;
    private _animationViewShadowOffsetY;
    private _animationViewShadowRadius;
    private _animationViewShadowColor;
    private _bgAnimationViewMarginLeft;
    private _sourceAnimationDuration;
    private _mainTabGapMore;
    private _targetAnimationDuration;
    private _sourceBgAnimationView;
    private _targetBgAnimationView;
    private _sourceBgAnimation;
    private _targetBgAnimation;
    private __animationGroup;
    readonly defaultStyleName: string;
    items: Array<{
        text: string;
        src?: string | Bitmap;
    }>;
    protected applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    constructor();
    private udpateCurrentItem;
    addItem(text: string, icon?: string | Bitmap): void;
    private _getItemIcon;
    private getSourceBgAnimationView;
    private getTargetBgAnimationView;
    protected doAnimation(currentIndex: number, oldIndex: number): void;
    private _getAnimationGroup;
    private _updateSourceBgAnimation;
    private _updateTargetBgAnimation;
    destroy(recusive?: boolean): void;
}
export = SideMenuBML;
