import View = require("../view/View");
import CompositeView = require("../view/CompositeView");
import Button = require("./Button");
/**
 * <p>ContextMenu displays a list of items in a horinzontal list on the screen.</p>
 * <p>The minimum width is 52dp and the maximum width is 296dp, when the width of items is larger than the maximum width, the contextMenu will show a "more" option on the rightest side.</p>
 * <p>An actionMenu will shown from bottom of the screen when you click the "more" option.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class ContextMenu extends CompositeView {
    private _modalLayer;
    private _items;
    private _itemsArray;
    private _menu;
    private _moreItem;
    private _hasMoreItem;
    private _moreItemIndex;
    private _moreItemClicked;
    private _isShowing;
    private _result;
    private _spacing;
    private _radius;
    private _windowWidth;
    private _windowHeight;
    private _closeCalled;
    private _backgroundPressed;
    private _marginWidth;
    private _marginHeight;
    private _fontSize;
    private _itemBackground;
    private _marginToBoundary;
    private _textColor;
    private _showAnimation;
    private _showAnimationFinished;
    private _showAnimationDuration;
    private _closeAnimation;
    private _closeAnimationFinished;
    private _closeAnimationDuration;
    /**
     * <p>Create a ContextMenu.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this ContextMenu.</p>
     * @param {boolean} recursive - destroy the children in the ContextMenu if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * <p>Destructor that destroy this ContextMenu.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>Destructor that destroy this ContextMenu and its menu items.</p>
     * @method destroyAll
     * @public
     * @since 1
     */
    /**
     * <p>This property holds the menu items.</p>
     * @name yunos.ui.widget.ContextMenu#items
     * @type {string[]}
     * @readonly
     * @public
     * @since 1
     */
    public readonly items: Object[];
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.ContextMenu#defaultStyleName
     * @type {string}
     * @default "ContextMenu"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Begin to show the contextmenu on the screen.</p>
     * @public
     * @since 1
     */
    public show(): void;
    /**
     * <p>Close the context menu, removing it from the screen.</p>
     * @public
     * @since 1
     */
    public close(): void;
    /**
     * <p>Return true if the dialog is showing at the screen otherwise return false.</p>
     * @public
     * @since 1
     */
    public isShowing(): boolean;
    /**
     * <p>Add a specific item string or item array.</p>
     * @param {string|string[]} itemArray - item string or item array.
     * @throws {TypeError} If this value is not a string or array of string.
     * @public
     * @since 1
     */
    public add(itemArray: Object): void;
    /**
     * <p>Delete a specific item at index.</p>
     * @param {number} index - the item index.
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If this value is not between 0 and the length of this context menu.
     * @public
     * @since 1
     */
    public delete(index: number): void;
    /**
     * <p>Delete all items from the context menu.</p>
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If this value is not between 0 and the length of this context menu.
     * @public
     * @since 1
     */
    public deleteAll(): void;
    private bindModalLayer(): void;
    private removeModalLayer(): void;
    /**
     * <p>Apply theme style for context menu.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    private removeSelfItems(index: number): void;
    private removeActionMenuItems(index: number): void;
    private checkMenuItems(): void;
    private addContextMenuItem(item: string): void;
    private addActionMenuItem(item: string): void;
    private showAnimation(): void;
    private closeAnimation(): void;
    private updatePosition(): void;
    private getScreenPosition(): {
        left: number;
        top: number;
    };
    private onBackkey(): void;
    private initializeItem(text: string): Button;
    private onShowComplete(): void;
    private onCloseComplete(): void;
    private sendResult(): void;
    private onContextMenuTap(menuItem: Button, event: Object): void;
    private onActionMenuTap(index: number): void;
    private onMoreItemClick(): void;
    private onContextMenuChildAdded(view: View): void;
    private onContextMenuChildRemoved(view: View): void;
    /**
     * Get the surfaceId of this Contextmenu.
     * @private
     */
    private readonly surfaceId: number;
    private initializeAnimation(): void;
    private destroyAnimation(): void;
}
export = ContextMenu;
