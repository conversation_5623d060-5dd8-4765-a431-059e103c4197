import EventEmitter = require("../../core/EventEmitter");
import PageLink = require("../../page/PageLink");
import View = require("../view/View");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
/**
 * <p>A class that describes a view hierarchy that can be displayed in another process.</p>
 * <p>The hierarchy is Loaded from a layout resource file, and this class provides
 *  some basic operations for modifying the content of the inflated hierarchy.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.ui.widget
 * @public
 * @since 2
 */
declare class RemoteView extends EventEmitter {
    private _domainName;
    private _layoutId;
    private getValueCallbackCollection;
    private _markupOptions;
    private valuesCollection;
    private _actions;
    private _themeName;
    private _globalThemeReference;
    private _themeUniqueId;
    private _responseToGlobalThemeChange;
    private _extendPackages;
    /**
     * <p>Create a new RemoteView object that will display the views contained in the specified layout file.</p>
     * @public
     * @since 2
     */
    public constructor(options?: Object);
    /**
     * <p>Equivalent to calling addView(viewId) after inflating the given RemoteView.</p>
     * <p>This allows users to build "nested" RemoteView.</p>
     * <p>In cases where consumers of RemoteViews may recycle layouts, use removeAllView(viewId) to clear any existing children.</p>
     * @param {string} viewId - The viewId of the parent CompositeView to add child into.
     * @param {yunos.ui.widget.RemoteView} nestedView - The nestedView that describes the child.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of nestedView is not RemoteView.
     * @public
     * @since 2
     */
    public addView(viewId: string, nestedView: RemoteView, selfId: string): void;
    /**
     * <p>Equivalent to calling removeChild(viewId) after inflating the given RemoteView.</p>
     * @param  {string} viewId   The viewId of the parent CompositeView to remove
     * @param  {string} parentId The parentId of the view which will remove, if parentId is null, the viewId will find from root.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of parentId is not string.
     * @public
     * @since 3
     */
    public removeView(viewId: string, parentId?: string): void;
    /**
     * <p>Call this method to remove all child views from the ViewGroup.</p>
     * @param {string} viewId - The viewId of the parent CompositeView to remove all children from.
     * @throws {TypeError} If type of viewId is not string.
     * @public
     * @since 2
     */
    public removeAllView(viewId: string): void;
    /**
     * <p>Equivalent to calling View.visible.</p>
     * @param {string} viewId - The viewId of the view whose visible should change.
     * @param {yunos.ui.view.View.Visibility} visible - The new visible for the view. Allow type of View.Visibility.Visible, View.Visibility.Hidden, View.Visibility.None
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of visible is not yunos.ui.view.View.Visibility.
     * @public
     * @since 2
     */
    public setViewVisibility(viewId: string, visible: number): void;
    /**
     * <p>Equivalent to calling TextView.text.</p>
     * @param {string} viewId - The viewId of the TextView whose text should change.
     * @param {string} text - The new text for the TextView.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of text is not string.
     * @public
     * @since 2
     */
    public setTextViewText(viewId: string, text: string): void;
    /**
     * <p>Equivalent to calling TextView.color.</p>
     * @param {string} viewId - The viewId of the TextView whose text color should change.
     * @param {string} color - Sets the text color to be this color.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of color is not string.
     * @public
     * @since 2
     */
    public setTextViewColor(viewId: string, color: string): void;
    /**
     * <p>Equivalent to calling TextView.fontSize.</p>
     * @param {string} viewId - The viewId of the TextView whose text size should change.
     * @param {number} textSize - The size of the text.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of textSize is not number.
     * @public
     * @since 2
     */
    public setTextViewSize(viewId: string, textSize: number): void;
    /**
     * <p>Equivalent to calling ImageView.source.</p>
     * @param {string} viewId - The viewId of the ImageView whose source should change.
     * @param {string} source - The source for the ImageView.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of source is not string.
     * @public
     * @since 2
     */
    public setImageViewSource(viewId: string, source: string): void;
    /**
     * <p>Equivalent to calling ProgressBar.max, ProgressBar.progress, and ProgressBar.indeterminate If indeterminate is true,
     * then the values for max and progress are ignored.</p>
     * @param {string} viewId - The viewId of the ProgressBar to change.
     * @param {number} max - The 100% value for the progress bar.
     * @param {number} progress - The current value of the progress bar.
     * @param {boolean} indeterminate - True if the progress bar is indeterminate, false if not.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of max is not number.
     * @throws {TypeError} If type of progress is not number.
     * @throws {TypeError} If type of indeterminate is not boolean.
     * @public
     * @since 2
     */
    public setProgressBar(viewId: string, max: number, progress: number, indeterminate: boolean): void;
    /**
     * <p>Equivalent to calling tap(View.tap) to launch the provided PageLink.</p>
     * @param {string} viewId - The viewId of the view to change.
     * @param {yunos.page.PageLink} pageLink - The pageLink to send when user clicks.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of pageLink is not PageLink.
     * @public
     * @since 2
     */
    public setOnClickPageLink(viewId: string, pageLink: PageLink): void;
    /**
     * <p>Equivalent to emit event to launch the provided PageLink.</p>
     * @param {string} viewId - The viewId of the view to change.
     * @param {string} eventName - The name of the event.
     * @param {yunos.page.PageLink} pageLink - The pageLink to send when user clicks.
     * @throws {TypeError} If type of viewId is not string.
     * @throws {TypeError} If type of eventName is not string.
     * @throws {TypeError} If type of pageLink is not PageLink.
     * @public
     * @since 3
     */
    public setOnEventPageLink(viewId: string, eventName: string, pageLink: PageLink): void;
    /**
     * <p>Equivalent to View.{propName}.</p>
     * @param {string} viewId - The viewId of the view to change.
     * @param {string} propName - Which property hava been changed.
     * @param {string} propValue - Sets the propName to the propName of view.
     * @public
     * @since 2
     */
    public setValue(viewId: string, propName: string, propValue: string | number | boolean | object): void;
    private getValue(viewId: string, propName: string, callback: string): void;
    private setVoiceCommand(viewId: string, vcm: VoiceCommand): void;
    private setTheme(themeName: string, domain: string): void;
    /**
     * <p>Load the view hierarchy represented by this object and applies all of the actions.</p>
     * @param {yunos.ui.view.View} parent - <p>Parent that the resulting view hierarchy will be attached to.
     * This method does not attach the hierarchy. The caller should do so when appropriate.</p>
     * @throws {TypeError} If type of parameter is not CompositeView.
     * @public
     * @since 2
     */
    public apply(parent?: View): View;
    /**
     * <p>Applies all of the actions to the provided view.</p>
     * @param {yunos.ui.view.View} view - The view to apply the actions to. This should be the result of the apply(parent) call.
     * @throws {TypeError} If type of parameter is not CompositeView.
     * @public
     * @since 2
     */
    public reapply(view: View): void;
    /**
     * <p>Construct RemoteView object by reading from JSON string.</p>
     * @param {string} str - The JSON format string.
     * @throws {TypeError} If type of parameter is not JSON Object.
     * @public
     * @since 2
     */
    public fromJson(str: string): void;
    /**
     * <p>Output RemoteView object to JSON string.</p>
     * @returns {string} The JSON format string.
     * @public
     * @since 2
     */
    public toJson(): string;
    /**
     * <p>Name of the domain that contains the layout resource.</p>
     * @name yunos.ui.widget.RemoteView#domainName
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @since 2
     */
    public domainName: string;
    /**
     * <p>The id of the layout resource.</p>
     * @name yunos.ui.widget.RemoteView#layoutId
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @since 2
     */
    public layoutId: string;
    /**
     * <p>The name of the layout theme.</p>
     * @name yunos.ui.widget.RemoteView#themeName
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @since 4
     *
     */
    public themeName: string;
    /**
     * <p>Defines the key from the property which will be used when reading the global theme.</p>
     * @name yunos.ui.widget.RemoteView#globalThemeReference
     * @type {string}
     * @friend
     */
    globalThemeReference: string;
    /**
     * <p>Defines the unique id for theme.</p>
     * @name yunos.ui.widget.RemoteView#themeUniqueId
     * @type {string}
     * @friend
     */
    themeUniqueId: string;
    /**
     * <p>Defines whether the RemoteView should response to the global theme change.</p>
     * @name yunos.ui.widget.RemoteView#responseToGlobalThemeChange
     * @type {boolean}
     * @default false
     * @public
     * @since 5
     */
    public responseToGlobalThemeChange: boolean;
    /**
     * <p>Define the extend package for markup file search.</p>
     * @name yunos.ui.widget.RemoteView#extendPackages
     * @type {string[]}
     * @public
     * @since 5
     */
    public extendPackages: string[];
    /**
     * <p>Markup option</p>
     * @name  yunos.ui.widget.RemoteView#markupOptions
     * @public
     * @since 3
     */
    public markupOptions: Object;
    /**
     * @name yunos.ui.widget.RemoteView#actions
     * @private
     */
    private actions: Object[];
    private getRemoteViewsToApply(): this;
    private performApply(v: View, parent: View): void;
    private parseObject(obj: Object): void;
    private parseAction(actions: Object[]): void;
    private parseSetOnClickPageLink(a: Object): void;
    private parseSetEventPageLink(a: Object): void;
    private parseViewGroupAction(a: Object): void;
    private parseReflectionAction;
    private parseGetValueAction;
    private parseSetThemeAction;
    private parseAddVoiceCommandAction;
    private addAction(a: Object): void;
    private handleUpdate(data: Object): boolean;
    private feedBack(cover: Object, param: Object): void;
    private static readonly Type: {
        ViewGroupAction: int;
        ReflectionAction: int;
        SetOnClickPageLink: int;
        SetEventPageLink: int;
        GetValueAction: int;
        SetThemeAction: int;
        AddVoiceCommandAction: int;
    };
}
export = RemoteView;
