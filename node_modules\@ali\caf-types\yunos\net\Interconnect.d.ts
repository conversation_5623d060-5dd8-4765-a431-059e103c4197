import UBus = require("ubus");
import EventEmitter = require("yunos/core/EventEmitter");
/**
 * <p>The Interconnect controls the connect process of interconnectivity.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.net
 * @private
 */
declare class Interconnect extends EventEmitter {
    private _busName;
    private _busPath;
    private _interfaceName;
    private _ubus;
    private _iface;
    private _rule;
    /**
     * Create Interconnect Mananger.
     * @private
     */
    private constructor();
    /**
     * Destroy the Interconnect. Call this only when you want to trim memory.</p>
     * @public
     */
    public destroy(): void;
    private request(url: string, method: string, ...args: Object[]): void;
    private startService(name: string, path: string, callback: (e: number) => void): void;
    private stopService(name: string, callback: (e: number) => void): void;
    private getInterface(callback: (e: number) => void): UBus.Interface;
    private setupSignal(): void;
    private setInterface4Test(iface: UBus.Interface): void;
    private static readonly Error: {
        [key: string]: number;
    };
}
export = Interconnect;
