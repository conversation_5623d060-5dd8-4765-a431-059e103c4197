import { MultiState } from "yunos/ui/util/TypeHelper";
import CompoundButton = require("yunos/ui/widget/CompoundButton");
import { ButtonLColorType, ButtonLContentType, ButtonLIStyle, ButtonLType, ButtonLHeightSizeType } from "./Types";
declare class ButtonBML extends CompoundButton {
    static readonly ButtonType: typeof ButtonLType;
    static readonly ColorType: typeof ButtonLColorType;
    static readonly ContentType: typeof ButtonLContentType;
    static readonly HeightSizeType: typeof ButtonLHeightSizeType;
    private _defaultHeight;
    private _defaultHeightSmall;
    private _defaultHeightMedium;
    private _defaultHeightLarge;
    private _defaultBorderRadius;
    private _defaultVerticalSpacing;
    private _defaultHorizontalSpacing;
    private _defaultVerticalIconSpacing;
    private _defaultHorizontalIconSpacing;
    private _defaultIconWidthHeightL;
    private _defaultIconWidthHeightM;
    private _defaultIconWidthHeightS;
    private _defaultTypeConfig;
    private _defaultFontSize;
    private _defaultFontWeight;
    private _defaultFontColor;
    private _defaultLeftPadding;
    private _defaultRightPadding;
    private _buttonType;
    private _colorType;
    private _contentType;
    private _heightSizeType;
    private _corner;
    private _iconAutoColor;
    private _multiStateOverlay;
    private _defaultMultiStateTransition;
    private _iconSrc;
    private _iconView;
    private __height;
    private _setHeight;
    private _setLeftPadding;
    private _setRightPadding;
    readonly defaultStyleName: string;
    buttonType: ButtonLType;
    colorType: ButtonLColorType;
    iconSrc: string | MultiState;
    color: string;
    iconAutoColor: boolean;
    multiStateOverlay: MultiState;
    height: number;
    width: number;
    leftPadding: number;
    rightPadding: number;
    corner: number | number[];
    contentType: ButtonLContentType;
    heightSizeType: ButtonLHeightSizeType;
    constructor(...args: Object[]);
    destroy(recursive?: boolean): void;
    protected updateStyle(style: ButtonLIStyle, diffStyle: ButtonLIStyle): void;
    protected applyStyle(style: ButtonLIStyle): void;
    protected validMultiState(): void;
    private _valid;
    private _validContentType;
    private setPaddings;
    private _validHeightSizeType;
    protected _doMultiStateOverlay(): void;
}
export = ButtonBML;
