import YObject = require("../core/YObject");
import { RegisterSignalRequestObj } from "./TypeInner";
/**
 * <p>A data object that contains request parameters to FenceClient.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class RegisterSignalListenerRequest extends YObject {
    public _native: RegisterSignalRequestObj;
    public constructor();
    /**
     * <p>signalIds to register.</p>
     * @name yunos.fenceclient.RegisterSignalListenerRequest#signalIds
     * @type {yunos.fenceclient.RegisterSignalListenerRequest}
     * @public
     * @since 5
     */
    public signalIds: string[];
}
export = RegisterSignalListenerRequest;
