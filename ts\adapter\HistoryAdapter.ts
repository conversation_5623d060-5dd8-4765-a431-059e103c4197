/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

import View = require("yunos/ui/view/View");
import BaseAdapter = require("yunos/ui/adapter/BaseAdapter");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import TextView = require("yunos/ui/view/TextView");
import ListItemBM = require("extend/hdt/control/ListItemBM");
import {IVoiceEvent} from "../Types";
const Consts = require("../Consts");
const TAG = "HistoryAdapter";

interface IHistoryView extends ListItemBM {
    title: TextView;
    index: number;
}

class HistoryAdapter extends BaseAdapter {
    private _voiceSelectListener: (itemView: View, position: number, point: object, voice: boolean) => void;

    constructor() {
        super();
    }

    createItem(position: number, convertView: IHistoryView) {
        if (!convertView) {
            convertView = <IHistoryView> LayoutManager.loadSync("search_history_item");
            convertView.title = <TextView> convertView.findViewById("id_title");

            if (Consts.SUPPORT_VOICE_CMD) {
                convertView.title.voiceEnabled = true;
                convertView.title.voiceSelectMode = View.VoiceSelectMode.Custom;
                const VoiceCommand = require("yunos/ui/voice/VoiceCommand");
                convertView.title.defaultVoiceCommand.recognitionQuality = VoiceCommand.RecognitionQuality.LOW;
                convertView.title.defaultVoiceCommand.keepAwake = false;
                convertView.title.on("voice", (e: IVoiceEvent) => {
                    if (this._voiceSelectListener) {
                        this._voiceSelectListener(convertView, convertView.index, null, true);
                    }
                    e.endLocalTask();
                });
            }
        }

        let itemData = <string> this.data[position];
        if (itemData) {
            convertView.title.text = itemData;
        }
        convertView.index = position;
        return convertView;
    }

    registerVoiceSelectListener(callback: (itemView: View, position: number, point: object, voice: boolean) => void) {
        this._voiceSelectListener = callback;
    }
}

export = HistoryAdapter;
