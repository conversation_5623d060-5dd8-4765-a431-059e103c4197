import YObject = require("../core/YObject");
import { QueryFenceRequestObj } from "./TypeInner";
/**
 * <p>A data object that contains request parameters to FenceClient.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class QueryFenceRequest extends YObject {
    public _native: QueryFenceRequestObj;
    public constructor();
    /**
     * <p>When the value is true, it means to get the cloud integrated fence,
     * when the value is false, get the local fence</p>
     * @name yunos.fenceclient.QueryFenceRequest#cloudFence
     * @type {yunos.fenceclient.QueryFenceRequest}
     * @public
     * @since 5
     */
    public cloudFence: boolean;
    /**
     * <p>businessId of the request.</p>
     * @name yunos.fenceclient.QueryFenceRequest#businessId
     * @type {yunos.fenceclient.QueryFenceRequest}
     * @public
     * @since 5
     */
    public businessId: string;
    /**
     * <p>groupId of the request.</p>
     * @name yunos.fenceclient.QueryFenceRequest#groupId
     * @type {yunos.fenceclient.QueryFenceRequest}
     * @public
     * @since 5
     */
    public groupId: string;
    /**
     * <p>fenceId of the request.</p>
     * @name yunos.fenceclient.QueryFenceRequest#fenceId
     * @type {yunos.fenceclient.QueryFenceRequest}
     * @public
     * @since 5
     */
    public fenceId: string;
    /**
     * <p>pageNum of the request.</p>
     * @name yunos.fenceclient.QueryFenceRequest#pageNum
     * @type {yunos.fenceclient.QueryFenceRequest}
     * @public
     * @since 5
     */
    public pageNum: number;
    /**
     * <p>pageSize of the request.</p>
     * @name yunos.fenceclient.QueryFenceRequest#pageSize
     * @type {yunos.fenceclient.QueryFenceRequest}
     * @public
     * @since 5
     */
    public pageSize: number;
}
export = QueryFenceRequest;
