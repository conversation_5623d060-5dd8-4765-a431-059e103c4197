/// <reference types="node" />
import Slider = require("yunos/ui/widget/Slider");
import Bitmap = require("yunos/graphics/Bitmap");
import Gradient = require("yunos/graphics/Gradient");
interface IStyle {
    width: number;
    height: number;
    panThreshold: number;
    defaultHandleViewOffset: number;
    handleWidth: number;
    handleHeight: number;
    handleScale: number;
    lineWeight: number;
    lineColor: number | Bitmap | Gradient | string;
    lineBorderRadius: number | number[];
    lineDisabledColor: number | Bitmap | Gradient | string;
    lineProgressColor: number | Bitmap | Gradient | string;
    lineProgressDisabledColor: number | Bitmap | Gradient | string;
    tipOffsetBottom: number;
    normalHandler: string | Bitmap | Buffer;
    disabledHandler: string | Bitmap | Buffer;
    pressedHandler: string | Bitmap | Buffer;
    defaultIconSize: number;
    defaultIconMargin: number;
    defaultBorderRadius: number;
    disabledOpacity: number;
    contentAutoColor: boolean;
    defaultContentColor: string;
    hilightContentColor: string;
    contentMultiLine: boolean;
    fontId: string;
}
declare class HeroSliderBM extends Slider {
    readonly defaultStyleName: "extend/hdt/HeroSliderBM" | "extend/hdt/HeroVSliderBM";
    leftContent: string;
    rightContent: string;
    contentAutoColor: boolean;
    enabled: boolean;
    contentMultiLine: boolean;
    private defaultIconSize;
    private defaultIconMargin;
    private defaultBorderRadius;
    private _leftItem;
    private _rightItem;
    private _leftItemShader;
    private _rightItemShader;
    private _leftContent;
    private _rightContent;
    private _disabledOpacity;
    private _contentAutoColor;
    private _defaultContentColor;
    private _hilightContentColor;
    private _contentMultiLine;
    private _defaultFontId;
    constructor();
    destroy(recursive?: boolean): void;
    protected applyStyle(style: IStyle): void;
    updateStyle(style: IStyle, diffStyle: IStyle): void;
    private _isHorizontal;
    private _valid;
    private _getCoverView;
    private _validContentMultiLine;
    private _applyContent;
    private _validShader;
    private _updateShaders;
    private updateItemShader;
}
export = HeroSliderBM;
