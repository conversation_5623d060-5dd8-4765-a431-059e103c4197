<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_search"
    layout="{layout.search}"
    propertySetName="search">

    <NavigationBar id="id_nav"/>

    <CompositeView
        id="id_search_history"
        layout="{layout.search_history}">
        <TextView
            id="id_sh_title"
            height="{config.SEARCH_BANNER_HEIGHT}"
            text="{string.SEARCH_HISTORY_TITLE}"
            propertySetName="extend/hdt/FontHeadline"/>
        <ListView
            id="id_sh_list"
            spacing="{config.ITEM_SPACE}"
            orientation="{enum.GridView.Orientation.Vertical}"
            verticalFadingEdgeEnabled="true"/>
        <ButtonBM
            id="id_sh_clear"
            text="{string.SEARCH_CLEAR_HISTORY}"
            buttonType="{enum.ButtonBM.ButtonType.Flat}"/>
    </CompositeView>

    <ScrollableView
        id="id_result_container"
        orientation="{enum.ScrollableView.Orientation.Vertical}"
        overScroll="false"
        verticalFadingEdgeEnabled="true"
        visibility="{enum.View.Visibility.Hidden}"
        layout="{layout.result_container}">
        <CompositeView
            id="id_local_container"
            visibility="{enum.View.Visibility.Hidden}"
            layout="{layout.search_local_result}">
            <CompositeView
                id="id_local_banner"
                height="{config.SEARCH_BANNER_HEIGHT}"
                layout="{layout.search_result_banner}">
                <TextView
                    id="id_local_title"
                    text="{string.USB_TITLE}"
                    propertySetName="extend/hdt/FontHeadline"/>
                <CompositeView
                    id="id_local_more"
                    width="{config.SEARCH_MORE_WIDTH}"
                    height="{config.SEARCH_MORE_HEIGHT}"
                    visibility="{enum.View.Visibility.Hidden}"
                    layout="{layout.search_local_more}">
                    <TextView
                        id="id_local_more_tips"
                        text="{string.SEARCH_MORE}"
                        propertySetName="extend/hdt/FontBody2"/>
                    <ImageView
                        id="id_local_more_icon"
                        scaleType="{enum.ImageView.ScaleType.Center}"/>
                </CompositeView>
            </CompositeView>
            <ListView
                id="id_disk_list"
                spacing="{config.ITEM_SPACE}"
                orientation="{enum.ListView.Orientation.Vertical}"
                verticalFadingEdgeEnabled="true"
                focusable="false"/>
        </CompositeView>
        <CompositeView
            id="id_online_container"
            visibility="{enum.View.Visibility.None}"
            layout="{layout.search_online_result}">
            <CompositeView
                id="id_online_banner"
                height="{config.SEARCH_BANNER_HEIGHT}"
                layout="{layout.search_result_banner}">
                <TextView
                    id="id_online_title"
                    text="{string.ONLINE_TITLE}"
                    propertySetName="extend/hdt/FontHeadline"/>
                <CompositeView
                    id="id_online_more"
                    width="{config.SEARCH_MORE_WIDTH}"
                    height="{config.SEARCH_MORE_HEIGHT}"
                    visibility="{enum.View.Visibility.Hidden}"
                    layout="{layout.search_online_more}">
                    <TextView
                        id="id_online_more_tips"
                        text="{string.SEARCH_MORE}"
                        propertySetName="extend/hdt/FontBody2"/>
                    <ImageView
                        id="id_online_more_icon"
                        scaleType="{enum.ImageView.ScaleType.Center}"/>
                </CompositeView>
            </CompositeView>
            <GridView
                id="id_online_list"
                orientation="{enum.GridView.Orientation.Vertical}"
                columns="{config.ONLINE_COLUMNS_NUM}"
                rowSpacing="{config.ITEM_SPACE}"
                columnSpacing="{config.ITEM_SPACE}"
                pulDownDistance="{config.PULLDOWN_DISTANCE}"
                verticalFadingEdgeEnabled="true"
                focusable="false"/>
        </CompositeView>
    </ScrollableView>

    <LoadingPageBM
        id="id_loading_page"
        visibility="{enum.View.Visibility.None}"/>

    <include
        id="id_empty"
        markup="empty"
        visibility="{enum.View.Visibility.None}"/>
</CompositeView>
