import ImageFilter = require("./ImageFilter");
import Color = require("yunos/graphics/Color");
/**
 * <p>The DropShadowImageFilter applies a drop shadow effect to the input image.
 * A drop shadow is effectively a blurred, offset version of the input image's alpha mask drawn in a particular color, composited below the image.  </p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class DropShadowImageFilter extends ImageFilter {
    private _color;
    public constructor();
    /**
     * <p>The offsetX specifies the horizontal distance of shadow. Negative values place the shadow to the left of the element.</p>
     * @name yunos.graphics.filter.DropShadowImageFilter#offsetX
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public offsetX: number;
    /**
     * <p>The offsetY specifies the vertical distance of shadow. Negative values place the shadow above the element.</p>
     * @name yunos.graphics.filter.DropShadowImageFilter#offsetY
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public offsetY: number;
    /**
     * <p>Define the blur radius value, the larger this value, the bigger the blur, so the shadow becomes bigger and lighter.
     * Negative values are not allowed.</p>
     * @name yunos.graphics.filter.DropShadowImageFilter#radius
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public radius: number;
    /**
     * <p>The larger this value, the bigger the blur, so the shadow becomes bigger and lighter. Negative values are not allowed.</p>
     * @name yunos.graphics.filter.DropShadowImageFilter#color
     * @type {string}
     * @default "#000000"
     * @public
     * @since 5
     */
    public color: string | Color;
}
export = DropShadowImageFilter;
