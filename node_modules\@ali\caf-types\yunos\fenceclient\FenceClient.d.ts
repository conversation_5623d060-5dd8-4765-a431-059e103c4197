import EventEmitter = require("../core/EventEmitter");
import AddFenceRequest = require("./AddFenceRequest");
import QueryFenceRequest = require("./QueryFenceRequest");
import DeleteFenceRequest = require("./DeleteFenceRequest");
import FenceDetailInfo = require("./FenceDetailInfo");
import RegisterSignalListenerRequest = require("./RegisterSignalListenerRequest");
/**
 * <p>Use this object to do fence request.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.fenceclient
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class FenceClient extends EventEmitter {
    private _callback;
    /**
     * <p>create a FenceClient instance.</p>
     * @public
     * @since 5
     */
    public constructor();
    /**
     * <p>Create a fence.</p>
     * <p>FenceGroup defines behavior of a group of fences.</p>
     * <p></p>
     * <p>FenceList defines a list of fences to create,
     * The combination of businessId\groupId\fenceId build the unique identifier for each fence.
     * It will overwrite existing data when same unique identifier exists.
     * Remove the fence when no need.</p>
     * @example
     * let addFenceReq = new AddFenceRequest();
     * let fg = new FenceGroup();
     * fg.groupId = "groupId1234";
     * fg.toCloud = false;
     * fg.version = "124484";
     * fg.fixedDate = "";
     * fg.fixedTime = "";
     * fg.validTime = "";
     * fg.repeatWeekday = "";
     * fg.businessId = "businessId12455";
     * let fi = new FenceInfo();
     * fi.city = "北京市";
     * fi.data = "{}";
     * fi.name = "testJs";
     * fi.action = [FenceInfo.ActionType.Enter, FenceInfo.ActionType.Exit];
     * fi.radius = 500;
     * fi.fenceId = "fenceId123";
     * fi.province = "北京市";
     * fi.type = FenceInfo.FenceType.Circle;
     * fi.stayMins = 0;
     * fi.coordinates = "116.480237 39.994113";
     * addFenceReq.fenceGroup = fg;
     * addFenceReq.fenceList = [fi];
     * let fenceCLient = new FenceClient();
     * fenceCLient.addFence(addFenceReq, function(success, code, err, traceId) {
     *     if (!success) {
     *         console.log("add fence failed");
     *      } else {
     *         console.log("add fence success");
     *       }
     * });
     * @permission JARVIS_API.permission.yunos.com
     * @see [addFence]{@link yunos.fenceclient.FenceClient#addFence}
     * @param {yunos.fen ce.AddFenceRequest} request - the fence request object
     * @param {yunos.fenceclient.fenceclient~fenceCallback} callback -
     * the callback that handles the add fence result.
     * @public
     * @since 5
     */
    public addFence(request: AddFenceRequest, callback: (success: boolean, code: number, err: Error, traceId: string) => void): void;
    /**
     * <p>Query fence list based on pagination.</p>
     * @permission JARVIS_API.permission.yunos.com
     * @param {yunos.fenceclient.QueryFenceRequest} request - the fence request object
     * @param {yunos.fenceclient.fenceclient~fenceCallback} callback -
     * the callback that handles the query fence result.
     * @public
     * @since 5
     */
    public queryFence(request: QueryFenceRequest, callback: (success: boolean, code: number, err: Error, traceId: string, fenceList: FenceDetailInfo[]) => void): void;
    /**
     * <p>Delete fence.</p>
     * @permission JARVIS_API.permission.yunos.com
     * @see [deleteFence]{@link yunos.fenceclient.FenceClient#deleteFence}
     * @param {yunos.fenceclient.DeleteFenceRequest} request - the fence request object
     * @param {yunos.fenceclient.fenceclient~fenceCallback} callback -
     * the callback that handles the delete fence result.
     * @public
     * @since 5
     */
    public deleteFence(request: DeleteFenceRequest, callback: (success: boolean, code: number, err: Error, traceId: string) => void): void;
    /**
     * <p>Register fence signal listener.</p>
     * <p></p>
     * <p>Please contact business administrator to get signal ids in request </p>
     * @permission JARVIS_API.permission.yunos.com
     * @see [unregisterSignalListener]{@link yunos.fenceclient.FenceClient#unregisterSignalListener}
     * @param {yunos.fenceclient.RegisterSignalListenerRequest} request - the request parameters.
     * @return {boolean} request result. true: request successfully.
     * @public
     * @since 5
     */
    public registerSignalListener(request: RegisterSignalListenerRequest): boolean;
    /**
     * <p>Unregister fence signal listener.</p>
     * @permission JARVIS_API.permission.yunos.com
     * @see [registerSignalListener]{@link yunos.fenceclient.FenceClient#registerSignalListener}
     * @public
     * @since 5
     */
    public unregisterSignalListener(): void;
}
export = FenceClient;
