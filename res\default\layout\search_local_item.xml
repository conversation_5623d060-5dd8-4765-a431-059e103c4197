<?xml version="1.0" encoding="UTF-8"?>
<ListItemBM
    height="{config.ITEM_HEIGHT}"
    layout="{layout.search_local_item}"
    propertySetName="search_local_item">
    <ImageView
        id="id_icon"
        width="{config.SEARCH_ITEM_ICON_SIZE}"
        height="{config.SEARCH_ITEM_ICON_SIZE}"
        scaleType="{enum.ImageView.ScaleType.Center}"/>
    <TextView
        id="id_title"
        text=""
        align="{enum.TextView.Align.Left}"
        elideMode="{enum.TextView.ElideMode.ElideMiddle}"
        propertySetName="extend/hdt/FontBody2"/>
    <TextView
        id="id_time"
        text=""
        width="{config.SEARCH_ITEM_TIME_WIDTH}"
        align="{enum.TextView.Align.Right}"
        propertySetName="extend/hdt/FontBody4"/>
    <ImageView
        id="id_last_played"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"
        visibility="{enum.View.Visibility.None}"/>
</ListItemBM>
