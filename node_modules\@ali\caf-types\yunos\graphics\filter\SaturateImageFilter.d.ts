import ImageFilter = require("./ImageFilter");
/**
 * <p>The SaturateImageFilter can saturates the input image. </p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class SaturateImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The value of amount defines the proportion of the conversion.
     * A value of 0 is completely un-saturated. A value of 1 leaves the input unchanged.
     * Other values are linear multipliers on the effect. Values of amount over 1 are allowed, providing super-saturated results. </p>
     * @name yunos.graphics.filter.SaturateImageFilter#amount
     * @type {number}
     * @default 1
     * @public
     * @since 5
     */
    public amount: number;
}
export = SaturateImageFilter;
