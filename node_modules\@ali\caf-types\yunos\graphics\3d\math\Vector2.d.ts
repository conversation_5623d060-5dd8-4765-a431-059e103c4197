import Matrix3 = require("./Matrix3");
/**
 * <p>This class represents a 2D vector.</p>
 * <p>A 2D vecotr is an ordered pair of numbers(labeled x and y).</p>
 * @memberof yunos.graphics.3d.math
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class Vector2 {
    private _out;
    /**
     * Constructor that create a 2D vector.
     * @param {number} num1 - the first number of the 2D vector.
     * @param {number} num2 - the second number of the 2D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(a?: number, b?: number);
    /**
     * Destructor that destroy this 2D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public destroy(): void;
    /**
     * Defines the second value of this 2D vector.
     * @name yunos.graphics.3d.math.Vector2#height
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public height: number;
    /**
     * Defines the first value of this 2D vector.
     * @name yunos.graphics.3d.math.Vector2#width
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public width: number;
    /**
     * Defines the first value of this 2D vector.
     * @name yunos.graphics.3d.math.Vector2#x
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public x: number;
    /**
     * Defines the second value of this 2D vector.
     * @name yunos.graphics.3d.math.Vector2#y
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public y: number;
    /**
     * Return the Float32Array of this 2D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public data(): Float32Array;
    /**
     * Add a vector2 to this vector2
     * @param {yunos.graphics.3d.math.Vector2} - the vector2 to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public add(vec: Vector2): this;
    /**
     * Add the scalar value s to this vector's x and y values.
     * @param {number} s - the add value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addScalar(s: number): void;
    /**
     * Adds the multiple of v and s to this vector.
     * @param {yunos.graphics.3d.math.Vecotr2} vec - the add vector2
     * @param {number} s - the multiple number
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addScaledVector(vec: Vector2, s: number): void;
    /**
     * Sets this vector to a + b.
     * @param {yunos.graphics.3d.math.Vector2} vec1 - the vector2 to add.
     * @param {yunos.graphics.3d.math.Vector2} vec2 - the vector2 to add.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addVectors(vec1: Vector2, vec2: Vector2): void;
    /**
     * Computes the angle in radians of this vector with respect to the positive x-axis.
     * @return {number} value - the angle in radians of this vector with respect to the positive x-axis.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public angle(): number;
    /**
     * Multiplies this vector (with an implicit 1 as the 3rd component) by m.
     * @param {yunos.graphics.3d.math.Matrix3} m - the matrix3
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public applyMatrix3(m: Matrix3): void;
    /**
     * The x and y components of the vector are rounded up to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public ceil(): void;
    /**
     * <p>If this vector's x or y value is greater than the max vector's x or y value, it is replaced by the corresponding value.</p>
     * <p>If this vector's x or y value is less than the min vector's x or y value, it is replaced by the corresponding value.</p>
     * @param {yunos.graphics.3d.math.Vecotr2} min - the minimum x and y values
     * @param {yunos.graphics.3d.math.Vector2} max - the maximum x and y values
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clamp(min: Vector2, max: Vector2): void;
    /**
     * <p>If this vector's length is greater than the max value, it is replaced by the max value.</p>
     * <p>If this vector's length is less than the min value, it is replaced by the min value.</p>
     * @param {number} min - the minimum value the length will be clamped to.
     * @param {number} max - the maximum value the length will be clamped to.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clampLenth(min: number, max: number): void;
    /**
     * <p>If this vector's x or y values are greater than the max value, they are replaced by the max value.</p>
     * <p>If this vector's x or y values are less than the min value, they are replaced by the min value.</p>
     * @param {number} min - the minimum value the components will be clamped to.
     * @param {number} max - the maximum value the components will be clamped to.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clampScalar(min: number, max: number): void;
    /**
     * Return a new Vector2 with the same x and y values as this one.
     * @return {yunos.graphics.3d.math.Vector2} vec - the new Vector2.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clone(): Vector2;
    /**
     * Copy the values of the passed Vector2's x and y properties to this Vector2.
     * @param {yunos.graphics.3d.math.Vecotr2} vec - the passed Vector2.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copy(vec: Vector2): void;
    /**
     * Computes the distance from this vector to v.
     * @param {yunos.graphics.3d.math.Vector2} vec - the passed Vector2.
     * @return {number} the distance.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public distanceTo(vec: Vector2): number;
    /**
     * Computes the Manhattan distance from this vector to v.
     * @param {yunos.graphics.3d.math.Vector2} vec - the passed Vector2.
     * @return {number} the manhattan distance.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public manhattanDistanceTo(vec: Vector2): number;
    /**
     * Computes the squared distance from this vector to v. If you are just comparing the distance with another distance, you should compare the distance squared instead as it is slightly more efficient to calculate.
     * @param {yunos.graphics.3d.math.Vector2} vec - the passed Vector2.
     * @return {number} the squared distance.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public distanceToSquared(vec: Vector2): number;
    /**
     * Divides this vector by v.
     * @param {yunos.graphics.3d.math.Vector2} vec - the divided vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public divide(vec: Vector2): void;
    /**
     * <p>Divides this vector by scalar s.</p>
     * <p>Sets vector to ( 0, 0 ) if s = 0.</p>
     * @param {number} s - the divided number
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public divideScalar(s: number): void;
    /**
     * Calculates the dot product of this vector and v.
     * @param {yunos.graphics.3d.math.Vector2} vec - the passed Vector2
     * @return {number} the dot product
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public dot(vec: Vector2): number;
    /**
     * Calculates the cross product of this vector and v. Note that a 'cross-product' in 2D is not well-defined. This function computes a geometric cross-product often used in 2D graphics
     * @param {yunos.graphics.3d.math.Vector2} vec - the passed Vector2
     * @return {number} the cross product
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public cross(v: Vector2): number;
    /**
     * Checks for strict equality of this vector and v.
     * @param {yunos.graphics.3d.math.Vector2} v - the passed Vector2.
     * @return {boolean} the res of equals.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public equals(v: Vector2): boolean;
    /**
     * The components of the vector are rounded down to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public floor(): void;
    /**
     * Sets this vector's x value to be array[ offset ] and y value to be array[ offset + 1 ].
     * @param {number[]} arr - the passed array.
     * @param {number} offset - the passed number.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public fromArray(arr: Array<number>, offset: number): void;
    /**
     * Get the component of the Vector2 by index.
     * @param {number} index - get the component from the Vector2 by index.
     * @return {number} return the component
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getComponent(index: number): number;
    /**
     * Computes the Euclidean length (straight-line length) from (0, 0) to (x, y).
     * @return {number} the length.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public length(): number;
    /**
     * Computes the Manhattan length of this vector.
     * @return {number} the manhattan length.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public manhattanLength(): number;
    /**
     * Computes the square of the Euclidean length (straight-line length) from (0, 0) to (x, y). If you are comparing the lengths of vectors, you should compare the length squared instead as it is slightly more efficient to calculate.
     * @return {number} the squared length.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lengthSq(): number;
    /**
     * Linearly interpolates between this vector and v, where alpha is the distance along the line - alpha = 0 will be this vector, and alpha = 1 will be v.
     * @param {yunos.graphics.3d.math.Vector2} v - the passed Vector2
     * @param {number} alpha - interpolation factor in the closed interval [0, 1]
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lerp(v: Vector2, alpha: number): void;
    /**
     * Sets this vector to be the vector linearly interpolated between v1 and v2 where alpha is the distance along the line connecting the two vectors - alpha = 0 will be v1, and alpha = 1 will be v2.
     * @param {yunos.graphics.3d.math.Vector2} v1 - the starting Vector2.
     * @param {yunos.graphics.3d.math.Vector2} v2 - Vector2 to interpolate towards.
     * @param {number} alpha - interpolation factor in the closed interval [0, 1].
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lerpVectors(v1: Vector2, v2: Vector2, alpha: number): void;
    /**
     * Inverts this vector - i.e. sets x = -x and y = -y.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public negate(): void;
    /**
     * Converts this vector to a unit vector - that is, sets it equal to the vector with the same direction as this one, but length 1.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public normalize(): void;
    /**
     * If this vector's x or y value is less than v's x or y value, replace that value with the corresponding max value.
     * @param {yunos.graphics.3d.math.Vector2} vec - the maximum Vector2.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public max(vec: Vector2): void;
    /**
     * If this vector's x or y value is greater than v's x or y value, replace that value with the corresponding min value.
     * @param {yunos.graphics.3d.math.Vector2} vec - the miximum Vector2.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public min(vec: Vector2): void;
    /**
     * Multiplies this vector by v.
     * @param {yunos.graphics.3d.math.Vector2} vec - the multiplied Vector2
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiply(vec: Vector2): void;
    /**
     * Multiplies this vector by scalar s.
     * @param {number} s - the multiplied number
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyScalar(s: number): void;
    /**
     * Rotates the vector around center by angle radians.
     * @param {yunos.graphics.3d.math.Vector2} center - the point around which to rotate.
     * @param {number} - the angle to rotate, in radians.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public rotateAround(center: Vector2, angle: number): void;
    /**
     * The components of the vector are rounded to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public round(): void;
    /**
     * The components of the vector are rounded towards zero (up if negative, down if positive) to an integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public roundToZero(): void;
    /**
     * Sets the x and y components of this vector.
     * @param {number} x - the first value of this Vector2.
     * @param {number} y - the second value of this Vector2.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public set(x: number, y: number): void;
    /**
     * <p>If index equals 0 set x to value.</p>
     * <p>If index equals 1 set y to value.</p>
     * @param {number} index - set the index component.
     * @param {number} value - set the value component.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setComponent(index: number, value: number): void;
    /**
     * Sets this vector to the vector with the same direction as this one, but length l.
     * @param {number} l - the lenth of Vector2.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setLength(l: number): void;
    /**
     * Sets the x and y values of this vector both equal to scalar.
     * @param {number} s - the scalar of Vector2
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setScalar(s: number): void;
    /**
     * Subtracts v from this vector.
     * @param {yunos.graphics.3d.math.Vector2} v - the Vector2 to sub.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public sub(v: Vector2): void;
    /**
     * Subtracts s from this vector's x and y components.
     * @param {number} s - the number to sub.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public subScalar(s: number): void;
    /**
     * Sets this vector to a - b.
     * @param {yunos.graphics.3d.math.Vector2} v1 - the Vector2 to sub.
     * @param {yunos.graphics.3d.math.Vector2} v2 - the Vector2 to sub.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public subVectors(v1: Vector2, v2: Vector2): void;
    /**
     * Returns an array [x, y], or copies x and y into the provided array.
     * @param {number[]} array - array to store the vector to. If this is not provided, a new array will be created.
     * @param {number} offset - optional offset into the array.
     * @return {number[]} copies x and y into the provided array.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public toArray(array: Array<number>, offset: number): Array<number>;
}
export = Vector2;
