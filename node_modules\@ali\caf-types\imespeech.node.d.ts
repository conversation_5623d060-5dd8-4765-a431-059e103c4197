/*
 * Copyright (C) 2019 Alibaba Group Holding Limited. All Rights Reserved.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare class addon {

    static createImeSpeech(config: string) : void;
    static startImeSpeech() : string;
    static stopImeSpeech() : void;
    static cancelImeSpeech() : void;
    static destroyImeSpeech() : void;
    static setStatusCallback(callback: (status: number) => void) : void;
    static setVolumeCallback(callback: (volume: number) => void) : void;
    static setResultCallback(callback: (resultState: number, result: string) => void) : void;
    static setErrorCallback(callback: (errCode: number, description: string) => void) : void;
}

export = addon;
