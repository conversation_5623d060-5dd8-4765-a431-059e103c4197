export declare class CmdBufferClass {
    private getEvId(id: number): number | string;
    private getEcId(id: number): number | string;
    private getCmdId(id: number | string): number | string;
    private getHandlerName(id: number | string): number | string;
    private cbTextLayout: number;
    private Sig: {
        [key: string]: number;
    };
    private cbResourceLoader: number;
    private Cmd: {
        [key: string]: number;
    };
    private Ev: {
        [key: string]: number;
    };
    private Ec: {
        [key: string]: number;
    };
    private nullHandle: number;
    [key: string]: Function | number | {
        [key: string]: number;
    };
    private allocHandle(): number;
    private addReleaseCmd(v: number): void;
    private releaseScratch(v: number): void;
    private createScratch(): number;
    private setCurrent(v: number): void;
    private mergeScratch(v: number): void;
    private flush(v?: boolean): void;
    private finish(): void;
    private addCmdSIIF(cmdId: number, handle: number, str: string, v1: number, v2: number, v3: number): void;
    private addVarCmd(cmdId: number, handle: number, sig: number, ...args: Object[]): void;
    private addCmdI(v1: number, v2: number, v3: number): void;
    private addCmdZ(v1: number, v2: number): void;
    private addCmdH(v1: number, v2: number, v3: number): void;
    private addCmdF(v1: number, v2: number, v3: number): void;
    private addCmdU(v1: number, v2: number, v3: number): void;
    private addCmdS(v1: number, v2: number, v3: string): void;
    private addCmdf(v1: number, v2: number, v3: number[]): void;
    private addCmdFFFF(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;
    private addCmdIIII(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;
    private addCmdUUUU(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;
    private addCmdUU(v1: number, v2: number, v3: number, v4: number): void;
    private addCmdII(v1: number, v2: number, v3: number, v4: number): void;
    private addCmdIS(v1: number, v2: number, v3: number, v4: string): void;
    private addCmduff(v1: number, v2: number, v3: number[], v4: number[], v5: number[]): void;
    private addCmdSi(v1: number, v2: number, v3: string, v4: number[]): void;
    private addCmdi(v1: number, v2: number, v3: number[]): void;
    private addCmdHi(v1: number, v2: number, v3: number, v4: number[]): void;
    private addCmdUF(v1: number, v2: number, v3: number, v4?: number): void;
    private addCmdSH(v1: number, v2: number, v3: string, v4?: number): void;
    private addCmdIfi(cmd: number, handle: number, id: number, values: number[], opts: number[]): void;
    private setEventsHandlers(EventHandler: Object, RequestHandler: Object, OutOfBandHandler: Object): void;
    private addCmdSf(cmd: number, handle: number, url: string, arg3: number[]): void;
    private addCmdFF(cmd: number, handle: number, x: number, y: number): void;
    private addCmdIII(cmd: number, handle: number, arg2: number, iface: number, id: number): void;
    private addCmdSU(cmd: number, handle: number, name: string, arg3: number): void;
    private addCmdSF(cmd: number, handle: number, name: string, object: number): void;
    private addCmdUUI(v1: number, v2: number, v3: number, v4: number, v5: number): void;
}
export declare class AgilRoot {
    private CmdBuffer: CmdBufferClass;
    private PlatformBridge: {
        createInitialWindow(...arg: Array<Object>): Array<Object>;
        createWindow(...arg: Array<Object>): Array<Object>;
        destroyWindow(v: number): void;
    };
}
export declare class Agil {
    private static root(): AgilRoot;
}
