import ImageFilter = require("./ImageFilter");
/**
 * <p>The InvertImageFilter can invert the samples in the input image. </p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class InvertImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The value of amount defines the proportion of the conversion.
     * A value of 1 is completely inverted. A value of 0 leaves the input unchanged.
     * Values between 0 and 1 are linear multipliers on the effect. </p>
     * @name yunos.graphics.filter.InvertImageFilter#amount
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public amount: number;
}
export = InvertImageFilter;
