import YObject = require("../core/YObject");
import LinearGradient = require("./LinearGradient");
import RadialGradient = require("./RadialGradient");
import Bitmap = require("./Bitmap");
import ImageData = require("./ImageData");
import { BasicCanvas } from "./canvas/BasicCanvas";
import SurfaceView = require("../ui/view/SurfaceView");
import { CanvasContext2D } from "./CanvasContext2D";
import ConicalGradient = require("./ConicalGradient");
export declare class Pattern extends YObject {
    public image: ImageData;
    public mode: string;
    public constructor(data: ImageData, mode: string);
}
/**
 * <p>The 2D render context of canvas.</p>
 * @extends yunos.graphics.CanvasContext2D
 * @memberof yunos.graphics
 * @public
 * @since 4
 *
 */
export declare class CanvasRenderingContext2D extends YObject implements CanvasContext2D {
    protected baseCanvas: BasicCanvas;
    private states;
    private currentState;
    private internalPath;
    /**
     * Create a CanvasRenderingContext2D instance
     * @param {Bitmap | SurfaceView} drawContext
     * @param {boolean} direct if the gl context is create by self
     * @public
     * @since 4
     *
     */
    public constructor(drawContext: Bitmap | SurfaceView | number, direct?: boolean, width?: number, height?: number);
    /**
     * <p>The fill style for the context.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#fillStyle
     * @type {string| LinearGradient | RadialGradient | Pattern}
     * @throws {TypeError} If value is not a color value;
     * @public
     * @since 4
     *
     */
    public fillStyle: string | number | LinearGradient | RadialGradient | Pattern;
    /**
     * <p>The font for the canvas.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#font
     * @type {string}
     * @default "12px sans-serif"
     * @public
     * @since 4
     *
     */
    public font: string;
    /**
     * <p>Line end point style. There are three types: butt|round|square</p>
     * @type {string}
     * @name yunos.graphics.CanvasRenderingContext2D#lineCap
     * @param {string} value
     * @throws TypeError
     * @public
     * @since 4
     *
     */
    public lineCap: string;
    /**
     * <p>Sets the line dash pattern offset.</p>
     * @type {number}
     * @name yunos.graphics.CanvasRenderingContext2D#lineDashOffset
     * @param {number} offset
     * @public
     * @since 4
     *
     */
    public lineDashOffset: number;
    /**
     * <p>Sets or returns the type of the created edge angle, when the two lines meet. There are three types:
     * @type {string}
     * @name yunos.graphics.CanvasRenderingContext2D#lineJoin
     * @param value
     * @throws TypeError
     * @public
     * @since 4
     *
     */
    public lineJoin: string;
    /**
     * <p> Current line width. </p>
     * @type {number}
     * @name yunos.graphics.CanvasRenderingContext2D#lineWidth
     * @param width
     * @public
     * @since 4
     *
     */
    public lineWidth: number;
    /**
     * <p>The maximum miter length.</p>
     * @name  yunos.graphics.CanvasRenderingContext2D#miterLimit
     * @type {number}
     * @public
     * @since 4
     *
     */
    public miterLimit: number;
    /**
     * <p>Fuzzy level of shadow.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#shadowBlur
     * @type {number}
     * @public
     * @since 4
     *
     */
    public shadowBlur: number;
    /**
     * <p>The global alpha for the canvas between 0 and 1.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#globalAlpha
     * @type {number}
     * @public
     * @since 4
     */
    public globalAlpha: number;
    /**
     * <p>The text's baseline for the canvas. Values as alphabetic|top|hanging|middle|ideographic|bottom.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#textBaseline
     * @type {string}
     * @public
     * @since 4
     */
    public textBaseline: string;
    /**
     * <p>A new image to the existing image.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#globalCompositeOperation
     * @type {string}
     * @public
     * @since 4
     */
    public globalCompositeOperation: string;
    /**
     * <p>The color of shadow.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#shadowColor
     * @type {string}
     * @public
     * @since 4
     *
     */
    public shadowColor: string;
    /**
     * <p>Offset in horizontal direction of shadow.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#shadowOffsetX
     * @type {number}
     * @public
     * @since 4
     *
     */
    public shadowOffsetX: number;
    /**
     * <p>Offset in vertical direction of shadow.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#shadowOffsetY
     * @type {number}
     * @public
     * @since 4
     *
     */
    public shadowOffsetY: number;
    /**
     * <p>The stroke style for the context.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#strokeStyle
     * @type {string|yunos.graphics.Gradient| Pattern}
     * @throws {TypeError} If value is not a color value;
     * @public
     * @since 4
     *
     */
    public strokeStyle: string | number | LinearGradient | RadialGradient | Pattern;
    /**
     * <p>The text align for the canvas. Values as center|end|left|right|start.</p>
     * @name yunos.graphics.CanvasRenderingContext2D#textAlign
     * @type {string}
     * @public
     * @since 4
     *
     */
    public textAlign: string;
    /**
     * <p>Create an arc path.</p>
     * @param {number} x - x position of Circle's center
     * @param {number} y - y position of Circle's center
     * @param {number} radius - the radius for arc
     * @param {number} startAngle - start angle
     * @param {number} endAngle - end angle
     * @param {boolean} anticlockwise - use clockwise or not
     * @public
     * @since 4
     *
     */
    public arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, anticlockwise?: boolean): void;
    /**
     * <p>Create an arc path from one point to another</p>
     * @param {number} x1 - x position of start point
     * @param {number} y1 - y position of start point
     * @param {number} x2 - x position of end point
     * @param {number} y2 - y position of end point
     * @param {number} radius - radius of Circle
     * @public
     * @since 4
     *
     */
    public arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): void;
    /**
     * <p>Start a new path.</p>
     * @public
     * @since 4
     *
     */
    public beginPath(): void;
    /**
     * <p>Clean all the drawing content in the Rect.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 4
     *
     */
    public clearRect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>Create Bezier curve.</p>
     * @param {number} cp1x - x position of first control point
     * @param {number} cp1y - y position of first control point
     * @param {number} cp2x - x position of second control point
     * @param {number} cp2y - y position of second control point
     * @param {number} x - x position of end point
     * @param {number} y - y position of end point
     * @public
     * @since 4
     *
     */
    public bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void;
    /**
     * <p>Turns the path currently being built into the current clipping path. Using the non-zero or even-odd winding rule.</p>
     * @param {string} [fillRule] - The algorithm by which to determine if a point is inside a path or outside a path.
     * @public
     * @since 4
     *
     */
    public clip(fillRule?: "nonzero" | "evenodd"): void;
    /**
     * <p>Creates a path from the current point back to the starting point.</p>
     * @public
     * @since 4
     *
     */
    public closePath(): void;
    /**
     * <p>To creates a new, blank ImageData object with the specified dimensions.</p>
     * @param {number | ImageData} width - The width to give the new ImageData object.
     * @param {number} [height] - The height to give the new ImageData object.
     * @return {ImageData} A new ImageData object with the specified width and height.
     * @public
     * @since 4
     *
     */
    public createImageData(width: number | ImageData, height?: number): ImageData;
    /**
     * <p>Create a linear gradient.</p>
     * @param {number} x0 - The x axis of the coordinate of the start point.
     * @param {number} y0 - The y axis of the coordinate of the start point.
     * @param {number} x1 - The x axis of the coordinate of the end point.
     * @param {number} y1 - The y axis of the coordinate of the end point.
     * @return {LinearGradient} the LinearGradient object
     * @public
     * @since 4
     *
     */
    public createLinearGradient(x0: number, y0: number, x1: number, y1: number): LinearGradient;
    /**
     * <p>Create a pattern which repeats the source in the directions specified by the repetition argument.</p>
     * @param {string|ImageData|} image - A Bitmap or ImageData to be used as the image to repeat.
     * @param {string} mode - How to repeat the image.
     * @return {Pattern} Return the Pattern object
     * @public
     * @since 4
     *
     */
    public createPattern(image: ImageData, mode?: string): Pattern;
    /**
     * <p>Create a radial gradient.</p>
     * @param {number} x0 - The x axis of the coordinate of the start circle.
     * @param {number} y0 - The y axis of the coordinate of the start circle.
     * @param {number} r0 - The radius of the start circle.
     * @param {number} x1 - The x axis of the coordinate of the end circle.
     * @param {number} y1 - The y axis of the coordinate of the end circle.
     * @param {number} r1 - The radius of the end circle.
     * @return {RadialGradient} the RadialGradient object
     * @public
     * @since 4
     *
     */
    public createRadialGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number): RadialGradient;
    /**
     * <p>Create a conical gradient.</p>
     * @param {number} x - The x axis of the coordinate of the point.
     * @param {number} y - The y axis of the coordinate of the point.
     * @param {number} [angle] - The angle of gradient, the default value is 0.
     * @return {yunos.graphics.ConicalGradient} the ConicalGradient object
     * @public
     * @since 4
     *
     */
    public createConicalGradient(x: number, y: number, angle?: number): ConicalGradient;
    /**
     * <p>To draw the image in canvas.</p>
     * @param {Bitmap} image - Bitmap to be draw
     * @param {number} [sx] - the start x position in bitmap
     * @param {number} [sy] - the start y position in bitmap
     * @param {number} [sw] - the width in source bitmap
     * @param {number} [sh] - the height in source bitmap
     * @param {number} dx - target x position in the canvas
     * @param {number} dy - target y position in the canvas
     * @param {number} [dw] - the width show in canvas
     * @param {number} [dh] - the height show in canvas
     * @public
     * @since 4
     *
     */
    public drawImage(image: Bitmap | string, sx?: int, sy?: int, sw?: int, sh?: int, dx?: int, dy?: int, dw?: int, dh?: int): void;
    /**
     * method creates an elliptical arc centered at (x, y) with the radii radiusX and radiusY.
     * The path starts at startAngle and ends at endAngle,
     * and travels in the direction given by anticlockwise (defaulting to clockwise).
     * @param {number} x The x-axis (horizontal) coordinate of the ellipse's center
     * @param {number} y The y-axis (vertical) coordinate of the ellipse's center.
     * @param {number} radiusX The ellipse's major-axis radius. Must be non-negative.
     * @param {number} radiusY The ellipse's minor-axis radius. Must be non-negative.
     * @param {number} rotation The rotation of the ellipse, expressed in radians.
     * @param {number} startAngle The angle at which the ellipse starts, measured clockwise from the positive x-axis and expressed in radians.
     * @param {number} endAngle The angle at which the ellipse ends, measured clockwise from the positive x-axis and expressed in radians.
     * @param {boolean} antiClockWise
     */
    public ellipse(x: number, y: number, radiusX: number, radiusY: number, rotation: number, startAngle: number, endAngle: number, antiClockWise?: boolean): void;
    /**
     * <p>Fill the path with the current fill style using the non-zero or even-odd winding rule.</p>
     * @param {string} [fillRule] - The algorithm by which to determine if a point is inside a path or outside a path.
     * @public
     * @since 4
     *
     */
    public fill(fillRule?: "nonzero" | "evenodd"): void;
    /**
     * <p>The fill the Rect with fillStyle color.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 4
     *
     */
    public fillRect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>To draw the text coloring on the canvas with fillStyle.</p>
     * @param {string} str - drawing text
     * @param {number} x - left start for drawing
     * @param {number} y - top start for drawing
     * @public
     * @since 4
     *
     */
    public fillText(str: string, x: number, y: number, maxWidth?: number): void;
    /**
     * <p>To get an ImageData object representing the underlying pixel data for a specified portion of the canvas.</p>
     * @param {number} x - The x coordinate of the top-left corner of the rectangle.
     * @param {number} y - The y coordinate of the top-left corner of the rectangle.
     * @param {number} width - The width of the rectangle.
     * @param {number} height - The height of the rectangle.
     * @return {yunos.graphics.ImageData} An ImageData object containing the image data for the rectangle of the canvas specified.
     * @public
     * @since 4
     *
     */
    public getImageData(x: number, y: number, width: number, height: number): ImageData;
    /**
     * <p>Gets the current line dash pattern.</p>
     * @return {number[]} A list of numbers that specifies distances to alternately draw a line and a gap.
     * @public
     * @since 4
     *
     */
    public getLineDash(): Array<number>;
    /**
     * <p>Reports whether or not the specified point is contained in the current path.</p>
     * @param {number} x - The X coordinate of the point to check.
     * @param {number} y - The Y coordinate of the point to check.
     * @return {boolean} A Boolean, which is true if the specified point is contained in the current or specified path, otherwise false.
     * @public
     * @since 4
     *
     */
    public isPointInPath(x: number, y: number): boolean;
    /**
     * <p>Reports whether or not the specified point is inside the area contained by the stroking of a path.</p>
     * @param {number} x - The X coordinate of the point to check.
     * @param {number} y - The Y coordinate of the point to check.
     * @return {boolean} A Boolean, which is true if the point is inside the area contained by the stroking of a path, otherwise false.
     * @public
     * @since 4
     *
     */
    public isPointInStroke(x: number, y: number): boolean;
    /**
     * <p>Creates a path from the current point to the target point.</p>
     * @param {number} x - left position of point
     * @param {number} y - top position of point
     * @public
     * @since 4
     *
     */
    public lineTo(x: number, y: number): void;
    /**
     * <p>To measure text width using current font.</p>
     * @param  {string} str - drawing text
     * @return {number} pixels of text width
     * @public
     * @since 4
     *
     */
    public measureTextWidth(str: string): number;
    /**
     * <p>To measure text width using current font.</p>
     * @param {string} str - drawing text
     * @return {object} A TextMetrics object. {width: number, height?: number, lineCount?: number, ascent?: number, descent?: number}
     * @public
     * @since 4
     *
     */
    public measureText(str: string): {
        width: number;
        height: number;
        lineCount: number;
        ascent: number;
        descent: number;
    };
    /**
     * <p>Move current painer to the new position.</p>
     * @param {number} x - left position of new point
     * @param {number} y - top position of new point
     * @public
     * @since 4
     *
     */
    public moveTo(x: number, y: number): void;
    /**
     * <p>To paint data from the given ImageData object onto the canvas.</p>
     * @param {yunos.graphics.ImageData} imageData - An ImageData object containing the array of pixel values.
     * @param {number} dx - Horizontal position (x-coordinate) at which to place the image data in the destination canvas.
     * @param {number} dy - Vertical position (y-coordinate) at which to place the image data in the destination canvas.
     * @param {number} sx - Horizontal position (x-coordinate). The x coordinate of the top left hand corner of your Image data. Defaults to 0.
     * @param {number} sy - Vertical position (y-coordinate). The y coordinate of the top left hand corner of your Image data. Defaults to 0.
     * @param {number} sw - Width of the rectangle to be painted. Defaults to the width of the image data.
     * @param {number} sh - Height of the rectangle to be painted. Defaults to the height of the image data.
     * @public
     * @since 4
     *
     */
    public putImageData(imageData: ImageData, dx: number, dy: number, sx?: number, sy?: number, sw?: number, sh?: number): void;
    /**
     * <p>Create quadratic Bezier curve.</p>
     * @param {number} cpx - x position of control point
     * @param {number} cpy - y position of control point
     * @param {number} x - x position of end point
     * @param {number} y - y position of end point
     * @public
     * @since 4
     *
     */
    public quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): void;
    /**
     * Creates a rectangular path whose starting point is at (x, y) and whose size is specified by width and height.
     * @param {number} x The x-axis coordinate of the rectangle's starting point.
     * @param {number} y The y-axis coordinate of the rectangle's starting point.
     * @param {number} width The rectangle's width. Positive values are to the right, and negative to the left.
     * @param {number} height The rectangle's height. Positive values are down, and negative are up.
     * @public
     * @since 4
     *
     */
    public rect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>Define the RoundedRect path in canvas.</p>
     * @param {number} x - left position of round rect
     * @param {number} y - top position of round rect
     * @param {number} width - width of round rect
     * @param {number} height - height of round rect
     * @param {number} radiusX - x radius of round rect
     * @param {number} [radiusY=radiusX] - y radius of round rect
     * @public
     * @since 4
     *
     */
    public roundRect(x: number, y: number, width: number, height: number, radiusX: number, radiusY?: number): void;
    /**
     * <p>The draw the stroke of the path with strokeStyle color.</p>
     * @public
     * @since 4
     *
     */
    public restore(): void;
    /**
     * <p>Rotate the canvas.</p>
     * @param {number} angle - rotate's angle
     * @public
     * @since 4
     *
     */
    public rotate(angle: number): void;
    /**
     * <p>Saves the entire state of the canvas by pushing the current state onto a stack.</p>
     * @public
     * @since 4
     *
     */
    public save(): void;
    /**
     * <p>Change the scale with and height</p>
     * @param {number} x - scale of width
     * @param {number} y - scale of height
     * @public
     * @since 4
     *
     */
    public scale(x: number, y: number): void;
    /**
     * <p>Sets the current line dash pattern.</p>
     * @param {number[]} lineDash - An Array of numbers which specify distances to alternately draw a line and a gap.
     * @public
     * @since 4
     *
     */
    public setLineDash(lineDash: Array<number>): void;
    /**
     * <p>Resets the current transformation to the identity matrix and then invokes a transformation described by the arguments of this method. The Mat define as :<br>
     * { a, c, e}<br>
     * { b, d, f}<br>
     * { 0, 0, 1}</p>
     *
     * <p>See also the transform() method, which does not override the current transform matrix and multiplies it with a given one.</p>
     *
     * @param {number} a - Horizontal scaling.
     * @param {number} b - Horizontal skewing.
     * @param {number} c - Vertical skewing.
     * @param {number} d - Vertical scaling.
     * @param {number} e - Horizontal moving.
     * @param {number} f - Vertical moving.
     * @public
     * @since 4
     *
     */
    public setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    /**
     * <p>The draw the stroke of the path with strokeStyle color.</p>
     * @public
     * @since 4
     *
     */
    public stroke(): void;
    /**
     * <p>The strock color for the context.</p>
     * @param {number} x - left position of Rect
     * @param {number} y - top position of Rect
     * @param {number} width - width of Rect
     * @param {number} height - height of Rect
     * @public
     * @since 4
     *
     */
    public strokeRect(x: number, y: number, width: number, height: number): void;
    /**
     * <p>To draw the text coloring on the canvas with strokeStyle.</p>
     * @param {string} str - drawing text
     * @param {number} x - left start for drawing
     * @param {number} y - top start for drawing
     * @public
     * @since 4
     *
     */
    public strokeText(str: string, x: number, y: number): void;
    /**
     * <p>Set the transform. The Mat define as :<br>
     * { a, c, e}<br>
     * { b, d, f}<br>
     * { 0, 0, 1}</p>
     * @param {number} a - a position for Mat
     * @param {number} b - b position for Mat
     * @param {number} c - c position for Mat
     * @param {number} d - d position for Mat
     * @param {number} e - e position for Mat
     * @param {number} f - f position for Mat
     * @public
     * @since 4
     *
     */
    public transform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    /**
     * <p>Translate the canvas.</p>
     * @param {number} x - x position for translate
     * @param {number} y - y position for translate
     * @public
     * @since 4
     *
     */
    public translate(x: number, y: number): void;
    /**
     * <p>Resets the current transform to the identity matrix.</p>
     *
     * @public
     * @since 4
     *
     */
    public resetTransform(): void;
    /**
     * <p>Flush pending draw command, only effects if attached to SurfaceView</p>
     * @public
     * @since 4
     *
     */
    public flush(): void;
    /**
     * <p>Swap current Surface buffer, only effects if attached to SurfaceView</p>
     * @public
     * @since 4
     *
     */
    public postAndSwap(): boolean;
}
