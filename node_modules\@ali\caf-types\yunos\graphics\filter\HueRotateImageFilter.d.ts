import ImageFilter = require("./ImageFilter");
/**
 * <p>The HueRotateImageFilter applies a hue rotation on the input image. </p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class HueRotateImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The value of angle defines the number of degrees around the color circle the input samples will be adjusted.
     * A value of 0 leaves the input unchanged. Though there is no maximum value; the effect of values above 360deg wraps around.</p>
     * @name yunos.graphics.filter.HueRotateImageFilter#angle
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public angle: number;
}
export = HueRotateImageFilter;
