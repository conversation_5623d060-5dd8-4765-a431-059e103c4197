declare enum FONTS {
    Display1 = "extend/hdt/FontDisplay1",
    Display2 = "extend/hdt/FontDisplay2",
    Display3 = "extend/hdt/FontDisplay3",
    Title1 = "extend/hdt/FontTitle1",
    Title2 = "extend/hdt/FontTitle2",
    Title3 = "extend/hdt/FontTitle3",
    Body1 = "extend/hdt/FontBody1",
    Body2 = "extend/hdt/FontBody2",
    Body3 = "extend/hdt/FontBody3",
    Body4 = "extend/hdt/FontBody4",
    Body5 = "extend/hdt/FontBody5",
    Headline = "extend/hdt/FontHeadline",
    Caption1 = "extend/hdt/FontCaption1",
    Caption2 = "extend/hdt/FontCaption2",
    DisplayLAB1 = "extend/hdt/FontDisplayLAB1",
    DisplayLC1 = "extend/hdt/FontDisplayLC1",
    DisplayLAB2 = "extend/hdt/FontDisplayLAB2",
    DisplayLC2 = "extend/hdt/FontDisplayLC2",
    TitleLAB1 = "extend/hdt/FontTitleLAB1",
    TitleLC1 = "extend/hdt/FontTitleLC1",
    TitleLAB2 = "extend/hdt/FontTitleLAB2",
    TitleLC2 = "extend/hdt/FontTitleLC2",
    TitleLAB3 = "extend/hdt/FontTitleLAB3",
    TitleLC3 = "extend/hdt/FontTitleLC3",
    TitleLAB4 = "extend/hdt/FontTitleLAB4",
    TitleLC4 = "extend/hdt/FontTitleLC4",
    BodyLAB1 = "extend/hdt/FontBodyLAB1",
    BodyLC1 = "extend/hdt/FontBodyLC1",
    BodyLAB2 = "extend/hdt/FontBodyLAB2",
    BodyLC2 = "extend/hdt/FontBodyLC2",
    BodyLAB3 = "extend/hdt/FontBodyLAB3",
    BodyLC3 = "extend/hdt/FontBodyLC3",
    BodyLAB4 = "extend/hdt/FontBodyLAB4",
    BodyLC4 = "extend/hdt/FontBodyLC4",
    CaptionLAB1 = "extend/hdt/FontCaptionLAB1",
    CaptionLC1 = "extend/hdt/FontCaptionLC1",
    CaptionLAB2 = "extend/hdt/FontCaptionLAB2",
    CaptionLC2 = "extend/hdt/CaptionLC2"
}
export = FONTS;
