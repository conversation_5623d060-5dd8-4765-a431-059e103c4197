/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";

import Model = require("yunos/appmodel/Model");
const MediaStore = require("yunos/multimedia/MediaStore");
import Cursor = require("yunos/provider/Cursor");
const CursorDataType = Cursor.CursorDataType;
const VideoColumns = MediaStore.VideoColumns;
import provider = require("yunos/provider");
const MemoryCursor = provider.MemoryCursor;
const resolver = provider.DataResolver;
import VideoInfo = require("./VideoInfo");
const iPinYinHelper = require("../utils/PinYinHelper").getInstance();
import log = require("../utils/log");
import Utils = require("../utils/Utils");
const TAG = "LocalModel";

const VIDEO_PROJECTION = [
    VideoColumns._ID,
    VideoColumns._PATH,
    VideoColumns.DATE_MODIFIED,
    VideoColumns._SIZE,
    VideoColumns._DISPLAY_NAME,
    VideoColumns.WIDTH,
    VideoColumns.HEIGHT,
    VideoColumns.MIME_TYPE,
    VideoColumns.DURATION,
    VideoColumns.ORIENTATION
];
const VIDEO_ID_INDEX = 0;
const VIDEO_PATH_INDEX = 1;
const VIDEO_DATE_MODIFIED_INDEX = 2;
const VIDEO_SIZE_INDEX = 3;
const VIDEO_DISPLAY_NAME_INDEX = 4;
const VIDEO_WIDTH_INDEX = 5;
const VIDEO_HEIGHT_INDEX = 6;
const VIDEO_MIME_TYPE_INDEX = 7;
const VIDEO_DURATION_INDEX = 8;
const VIDEO_ORIENTATION_INDEX = 9;

const COLUMN_TYPES = [
    CursorDataType.CursorDataTypeInteger,
    CursorDataType.CursorDataTypeString,
    CursorDataType.CursorDataTypeInteger,
    CursorDataType.CursorDataTypeInteger,
    CursorDataType.CursorDataTypeString,
    CursorDataType.CursorDataTypeInteger,
    CursorDataType.CursorDataTypeInteger,
    CursorDataType.CursorDataTypeString,
    CursorDataType.CursorDataTypeInteger,
    CursorDataType.CursorDataTypeInteger
];

const ORIENTATION_NINETY_DEGREES = 90;

class LocalModel extends Model {
    private static _instance: LocalModel;
    private _preloadPath: string;
    private _preloadCursor: Cursor;

    constructor() {
        super();
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new LocalModel();
        }
        return this._instance;
    }

    /**
     * 预加载U盘数据列表，提高U盘详情页的响应时间
     */
    preloadData() {
    }

    get preloadPath() {
        return this._preloadPath;
    }

    get preloadCursor() {
        return this._preloadCursor;
    }

    /**
     * 根据U盘路径、排序规则等信息查询数据库
     */
    loadVideo(path: Object, order: string, callback: (error: object, cursor: Cursor) => void) {
        log.I(TAG, "loadVideo", path, order);
        let paramObj = {
            selection: VideoColumns._PATH + " like ? ",
            selectionArgs: [path + "%"],
            order: order + " DESC"
        };
        this._queryVideo(paramObj, callback);
    }

    /**
     * 根据关键词、限定数量等信息查询数据库
     */
    searchVideo(keyword: string, limit: number, callback: { (error: object, cursor?: Cursor): void; }) {
        // select * from videos where (_display_name like '%h%' or _display_name like '%t%')
        // and (_data like '/storage/sda%' or _data like '/storage/emulated%') order by _id limit 3
        log.I(TAG, "searchVideo", keyword, limit);
        let selection = "(";
        let selectionArgs = [];
        let keywordArr = Utils.strToArray(keyword);
        for (let i = 0; i < keywordArr.length; i++) {
            selection += VideoColumns._DISPLAY_NAME + " like ? ";
            if (i !== keywordArr.length - 1) {
                selection += " or ";
            }
            selectionArgs.push("%" + keywordArr[i] + "%");
        }
        selection += ")";
        log.D(TAG, "searchVideo", selection);

        let order = "";
        if (limit > 0) {
            order = VideoColumns._ID + " DESC limit " + limit;
        } else {
            order = VideoColumns._ID + " DESC";
        }

        let paramObj = {
            selection: selection,
            selectionArgs: selectionArgs,
            order: order
        };
        this._queryVideo(paramObj, callback);
    }

    /**
     * 根据id删除数据库对应的视频信息
     */
    deleteVideoById(id: string, callback: () => void) {
        console.trace();
        log.D(TAG, "deleteVideoById", id);
        let uri = MediaStore.getUri(MediaStore.VolumeName.EXTERNAL, MediaStore.MediaType.VIDEO);
        let selectionArgs = [];
        selectionArgs[0] = id;
        resolver.delete(uri, VideoColumns._ID + "=?", selectionArgs, (error, affectRows) => {
            log.D(TAG, "deleteVideoById", error, affectRows);
            if (callback) {
                callback();
            }
        });
    }

    /**
     * 查询数据库
     */
    _queryVideo(paramObj: { selection: string; selectionArgs: string[]; order?: string; },
        callback: (error: object, cursor?: Cursor) => void) {
        log.I(TAG, "_queryVideo");
        let uri = MediaStore.getUri(MediaStore.VolumeName.EXTERNAL, MediaStore.MediaType.VIDEO);
        resolver.query(uri, VIDEO_PROJECTION, paramObj.selection, paramObj.selectionArgs, paramObj.order, (error, cursor) => {
            if (error) {
                log.I(TAG, "_queryVideo", error);
                if (callback) {
                    callback(error);
                }
                return;
            }

            log.I(TAG, "_queryVideo", cursor.columnCount, cursor.count);
            if (callback) {
                callback(null, cursor);
            }
            log.I(TAG, "_queryVideo, done");
            return;
        });
    }

    /**
     * 根据cursor和index来获取视频信息
     */
    getVideoInfo(cursor: Cursor, index: number) {
        log.I(TAG, "getVideoInfo", index);
        if (!cursor || cursor.isClosed() || index > cursor.count) {
            return null;
        }
        let videoInfo = <VideoInfo>{};
        try {
            if (cursor.moveToPositionSync(index)) {
                videoInfo = new VideoInfo();
                videoInfo.id = <string>cursor.getValue(VIDEO_ID_INDEX);
                videoInfo.url = <string>cursor.getValue(VIDEO_PATH_INDEX);
                videoInfo.dateModified = <string>cursor.getValue(VIDEO_DATE_MODIFIED_INDEX);
                videoInfo.videoSize = <number>cursor.getValue(VIDEO_SIZE_INDEX);
                videoInfo.title = <string>cursor.getValue(VIDEO_DISPLAY_NAME_INDEX);
                videoInfo.displayName = <string>cursor.getValue(VIDEO_DISPLAY_NAME_INDEX);
                videoInfo.mimeType = <string>cursor.getValue(VIDEO_MIME_TYPE_INDEX);
                videoInfo.duration = <number>cursor.getValue(VIDEO_DURATION_INDEX);
                videoInfo.orientation = <number>cursor.getValue(VIDEO_ORIENTATION_INDEX);
                if (videoInfo.orientation === ORIENTATION_NINETY_DEGREES
                    || videoInfo.orientation === -ORIENTATION_NINETY_DEGREES) {
                    videoInfo.videoWidth = <number>cursor.getValue(VIDEO_HEIGHT_INDEX);
                    videoInfo.videoHeight = <number>cursor.getValue(VIDEO_WIDTH_INDEX);
                } else {
                    videoInfo.videoWidth = <number>cursor.getValue(VIDEO_WIDTH_INDEX);
                    videoInfo.videoHeight = <number>cursor.getValue(VIDEO_HEIGHT_INDEX);
                }
            }
        } catch (e) {
            log.E(TAG, "getVideoInfo", e);
        }
        return videoInfo;
    }

    /**
     * 获取视频id列表
     */
    getVideoIdList(cursor: Cursor) {
        log.I(TAG, "getVideoIdList");
        if (!cursor || cursor.isClosed() || cursor.count === 0) {
            return null;
        }
        let list = "";
        try {
            if (cursor.moveToFirstSync()) {
                do {
                    list += cursor.getValue(VIDEO_ID_INDEX) + ",";
                } while (cursor.moveToNextSync());
            }
        } catch (e) {
            log.E(TAG, "getVideoIdList", e);
        }
        log.D(TAG, "getVideoIdList", list);
        return list;
    }

    /**
     * 按拼音对cursor进行排序
     */
    sortCursor(cursor: Cursor, callback: (arg0: Object, arg1: Object, arg2: Object) => void) {
        log.I(TAG, "sortCursor");
        if (!cursor || cursor.isClosed() || cursor.count === 0) {
            return;
        }
        let data = [];
        try {
            if (cursor.moveToFirstSync()) {
                do {
                    let videoInfo = new VideoInfo();
                    videoInfo.id = <string>cursor.getValue(VIDEO_ID_INDEX);
                    videoInfo.url = <string>cursor.getValue(VIDEO_PATH_INDEX);
                    videoInfo.dateModified = <string>cursor.getValue(VIDEO_DATE_MODIFIED_INDEX);
                    videoInfo.videoSize = <number>cursor.getValue(VIDEO_SIZE_INDEX);
                    videoInfo.displayName = <string>cursor.getValue(VIDEO_DISPLAY_NAME_INDEX);
                    videoInfo.videoWidth = <number>cursor.getValue(VIDEO_WIDTH_INDEX);
                    videoInfo.videoHeight = <number>cursor.getValue(VIDEO_HEIGHT_INDEX);
                    videoInfo.duration = <number>cursor.getValue(VIDEO_DURATION_INDEX);
                    videoInfo.orientation = <number>cursor.getValue(VIDEO_ORIENTATION_INDEX);
                    // log.I(TAG, "sortCursor, videoInfo: " + videoInfo.dumpInfo());
                    data.push(videoInfo);
                } while (cursor.moveToNextSync());
            }
        } catch (e) {
            log.E(TAG, "sortCursor", e);
        }
        data = iPinYinHelper.sortData(data);

        let memoryCursor = new MemoryCursor(VIDEO_PROJECTION, COLUMN_TYPES);
        let quickIndexIndex: number[] = [];
        let quickIndexStr: string[] = [];
        for (let i = 0; i < data.length; i++) {
            let item = data[i];
            let letter = item.pinyin.slice(0, 1);
            if (iPinYinHelper.isLetterInAlphabet(letter)) {
                if (!this._isLetterInArray(quickIndexStr, letter.toUpperCase())) {
                    quickIndexStr.push(letter.toUpperCase());
                    quickIndexIndex.push(i);
                }
            } else if (!this._isLetterInArray(quickIndexStr, "#")) {
                quickIndexStr.push("#");
                quickIndexIndex.push(i);
            }

            try {
                let rowValues = [item.original.id,
                    item.original.url,
                    item.original.dateModified,
                    item.original.videoSize,
                    item.original.displayName,
                    item.original.videoWidth,
                    item.original.videoHeight,
                    item.original.mimeType,
                    item.original.duration,
                    item.original.orientation
                ];
                memoryCursor.addRow(rowValues);
            } catch (e) {
                log.E(TAG, "sortCursor", e);
            }
        }
        let quickIndexObject = {
            str: quickIndexStr,
            array: quickIndexIndex
        };

        if (callback) {
            callback(null, memoryCursor, quickIndexObject);
        }
    }

    _isLetterInArray(array: string[], letter: string) {
        for (let i = 0; i < array.length; i++) {
            if (array[i] === letter) {
                return true;
            }
        }
        return false;
    }
}

export = LocalModel;
