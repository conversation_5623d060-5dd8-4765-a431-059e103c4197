import EventEmitter = require("yunos/core/EventEmitter");
/**
 * <p>The MapEngine class provides access to base ar engine.</p>
 * @extends yunos.core.EventEmitter
 * @relyon YUNOS_SYSCAP_ARVIEW
 * @private
 */
declare class MapEngine extends EventEmitter {
    private _engine;
    /**
     * <p>create MapEngine.</p>
     * @param {Object} options - params for creating MapEngine
     * @private
     */
    private constructor(options: Object);
    private static getInstance(options?: Object): MapEngine;
    private getImage(overlayId: number): Object;
    private createOverlay(overlay: Object): void;
    private drawOverlay(overlayId: number, overlay: Object): boolean;
    private clearOverlay(overlayId: number): void;
    private resize(x: number, y: number, width: number, height: number): void;
    private play(videoParams?: Object): void;
    private stop(): void;
    private pause(): void;
    private resume(): void;
    private add(objType: number): void;
    private remove(objType: number): void;
    private getPointer(): Object;
    private setMaxFps(fps: number): void;
}
export = MapEngine;
