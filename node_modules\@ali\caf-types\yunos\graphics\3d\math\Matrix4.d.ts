import Vector3 = require("./Vector3");
import Quaternion = require("./Quaternion");
/**
 * <p>This class represents a 4 * 4 matrix.</p>
 * @memberof yunos.graphics.3d.math
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class Matrix4 {
    private _out;
    /**
     * Constructor that create a 4 * 4 matrix
     * @param {number} n11 - the first number of the 4 * 4 matrix.
     * @param {number} n12 - the fifth number of the 4 * 4 matrix.
     * @param {number} n13 - the ninth number of the 4 * 4 matrix.
     * @param {number} n14 - the thirteenth number of the 4 * 4 matrix.
     * @param {number} n21 - the second number of the 4 * 4 matrix.
     * @param {number} n22 - the sixth number of the 4 * 4 matrix.
     * @param {number} n23 - the tenth number of the 4 * 4 matrix.
     * @param {number} n24 - the fourteenth number of the 4 * 4 matrix.
     * @param {number} n31 - the third number of the 4 * 4 matrix.
     * @param {number} n32 - the seventh number of the 4 * 4 matrix.
     * @param {number} n33 - the eleventh number of the 4 * 4 matrix.
     * @param {number} n34 - the fifteenth number of the 4 * 4 matrix.
     * @param {number} n41 - the fourth number of the 4 * 4 matrix.
     * @param {number} n42 - the eighth number of the 4 * 4 matrix.
     * @param {number} n43 - the twelfth number of the 4 * 4 matrix.
     * @param {number} n44 - the sixteenth number of the 4 * 4 matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(n11?: number, n12?: number, n13?: number, n14?: number, n21?: number, n22?: number, n23?: number, n24?: number, n31?: number, n32?: number, n33?: number, n34?: number, n41?: number, n42?: number, n43?: number, n44?: number);
    /**
     * Destructor that destroy this 4 * 4 matrix class.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public destroy(): void;
    /**
     * Return the Float32Array of this 4 * 4 matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public data(): Float32Array;
    /**
     * Create a new Matrix4 with identical elements to this one.
     * @return {yunos.graphics.3d.math.Matrix4} return a new Matrix4
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clone(): Matrix4;
    /**
     * Sets this matrix to the transformation composed of position, quaternion and scale.
     * @param {yunos.graphics.3d.math.Vector3} position - the position.
     * @param {yunos.graphics.3d.math.Quaternion} quaternion - the rotate.
     * @param {yunos.graphics.3d.math.Vector3} scale - the scale.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public compose(position: Vector3, quaternion: Quaternion, scale: Vector3): void;
    /**
     * Copy the elements of matrix m into this matrix.
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix4 to copy
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copy(m: Matrix4): void;
    /**
     * Copy the translation component of the supplied matrix m into this matrix's translation component.
     * @param {yunos.graphics.3d.math.Matrix4} m - the Matrix4 to copy.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copyPosition(m: Matrix4): void;
    /**
     * Decomposes this matrix into it's position, quaternion and scale components.
     * @param {yunos.graphics.3d.math.Vector3} position - the position
     * @param {yunos.graphics.3d.math.Quaternion} quaternion - the retate
     * @param {yunos.graphics.3d.math.Vector3} scale - the scale
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public decompose(position: Vector3, quaternion: Quaternion, scale: Vector3): void;
    /**
     * Computes and returns the determinant of this matrix.
     * @return {number} the determinanat of this matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public determinant(): number;
    /**
     * Return true if this matrix and m are equal.
     * @return {boolean} check if this matrix and passed matrix are equal
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public equals(m: Matrix4): boolean;
    /**
     * Extracts the basis of this matrix into the three axis vectors provided.
     * @param {yunos.graphics.3d.math.Vector3} xAxis - the xAxis
     * @param {yunos.graphics.3d.math.Vector3} yAxis - the yAxis
     * @param {yunos.graphics.3d.math.Vector3} zAxis - the zAxis
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public extractBasis(xAxis: Vector3, yAxis: Vector3, zAxis: Vector3): void;
    /**
     * Extracts the rotation component of the supplied matrix m into this matrix's rotation component.
     * @param {yunos.graphics.3d.math.Matrix4} m - extract the rotation component of the supplied matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public extractRotation(m: Matrix4): void;
    /**
     * Sets the elements of this matrix based on an array in column-major format.
     * @param {number[]} array - the array to read the elements from
     * @param {number} offset - offset into the array.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public fromArray(array: (Float32Array | Array<number>), offset?: number): void;
    /**
     * Set this matrix to the inverse of the passed matrix m.
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix to take the invese of
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getInverse(m: Matrix4): void;
    /**
     * Gets the maximum scale value of the 3 axes.
     * @return {number} the maximum scale of the 3 axes.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getMaxScaleOnAxis(): number;
    /**
     * Resets the matrix to the identity matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public identity(): void;
    /**
     * Constructs a rotation matrix, looking from eye towards center oriented by the up vector.
     * @param {yunos.graphics.3d.math.Vector3} eye - the position of eye
     * @param {yunos.graphics.3d.math.Vector3} center - the position of target
     * @param {yunos.graphics.3d.math.Vector3} up - the direction of up
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lookAt(eye: Vector3, center: Vector3, up: Vector3): void;
    /**
     * Sets this matrix as rotation transform around axis by theta radians.
     * @param {yunos.graphics.3d.math.Vector3} axis - Rotation axis, should be normalized
     * @param {number} theta - Rotation angle in radians.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeRotationAxis(axis: Vector3, theta: number): void;
    /**
     * Set this to the basis matrix consisting of the three provided basis vectors.
     * @param {yunos.graphics.3d.math.Vector3} xAxis - the xAxis
     * @param {yunos.graphics.3d.math.Vector3} yAxis - the yAxis
     * @param {yunos.graphics.3d.math.Vector3} zAxis - the zAxis
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeBasis(xAxis: Vector3, yAxis: Vector3, zAxis: Vector3): void;
    /**
     * Create a perspective projection matrix.
     * @param {number} left - the left of this projection.
     * @param {number} right - the right of this projection.
     * @param {number} top - the top of this projection
     * @param {number} bottom - the bottom of this projection
     * @param {number} near - the near of this projection.
     * @param {number} far - the far of this projection.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makePerspective(left: number, right: number, top: number, bottom: number, near: number, far: number): void;
    /**
     * Create an orthographic projection matrix.
     * @param {number} left - the left of this projection.
     * @param {number} right - the right of this projection.
     * @param {number} top - the top of this projection
     * @param {number} bottom - the bottom of this projection
     * @param {number} near - the near of this projection.
     * @param {number} far - the far of this projection.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeOrthographic(left: number, right: number, top: number, bottom: number, near: number, far: number): void;
    /**
     * Sets the rotation component of this matrix to the rotation specified by Quaternion.
     * @param {yunos.graphics.3d.math.Quaternion} q - the rotation spedcified by Quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeRotationFromQuaternion(q: Quaternion): void;
    /**
     * Sets this matrix as a rotational transformation around the X axis by theta (θ) radians.
     * @param {number} theta - rotation angle in radinas.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeRotationX(theta: number): void;
    /**
     * Sets this matrix as a rotational transformation around the Y axis by theta (θ) radians.
     * @param {number} theta - rotation angle in radians.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeRotationY(theta: number): void;
    /**
     * Sets this matrix as a rotational transformation around the Z axis by theta (θ) radians.
     * @param {number} theta - rotation angle in radians.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeRotationZ(theta: number): void;
    /**
     * Set this matrix as scale transform
     * @param {number} x - the scale x
     * @param {number} y - the scale y
     * @param {number} z - the scale z
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeScale(x: number, y: number, z: number): void;
    /**
     * Set this matrix as a shear transform
     * @param {number} x - the amount of shear in the X axis.
     * @param {number} y - the amount of shear in the Y axis
     * @param {number} z - the amount of shear in the Z axis.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeShear(x: number, y: number, z: number): void;
    /**
     * Set this matrix as a translation transform
     * @param {number} x - the amount of translate in the X axis
     * @param {number} y - the amount of translate in the Y axis
     * @param {number} z - the amount of translate in the Z axis
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public makeTranslation(x: number, y: number, z: number): void;
    /**
     * Post-multiply this matrix by passed matrix
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix to post-multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiply(m: Matrix4): void;
    /**
     * Set this matrix to Matrix4 a * Matrix b.
     * @param {yunos.graphics.3d.math.Matrix4} a - the first Matrix4 to multiply
     * @param {yunos.graphics.3d.math.Matrix4} b - the second Matrix4 to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyMatrices(a: Matrix4, b: Matrix4): void;
    /**
     * Multiply every component of the matrix by a scalar value s.
     * @param {number} s - the scalar to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyScalar(s: number): void;
    /**
     * Pre-multiply this matrix by the passed matrix
     * @param {yunos.graphics.3d.math.Matrix} m - the passed matrix to pre-multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public premultiply(m: Matrix4): void;
    /**
     * Multiply the columns of this matrix by the passed vector.
     * @param {yunos.graphics.3d.math.Vector3} v - the passed vector to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public scale(v: Vector3): void;
    /**
     * Set the elements of this matrix to the supplied row-major values n11, n12, ... n44.
     * @param {number} n11 - the first number of the 4 * 4 matrix.
     * @param {number} n12 - the fifth number of the 4 * 4 matrix.
     * @param {number} n13 - the ninth number of the 4 * 4 matrix.
     * @param {number} n14 - the thirteenth number of the 4 * 4 matrix.
     * @param {number} n21 - the second number of the 4 * 4 matrix.
     * @param {number} n22 - the sixth number of the 4 * 4 matrix.
     * @param {number} n23 - the tenth number of the 4 * 4 matrix.
     * @param {number} n24 - the fourteenth number of the 4 * 4 matrix.
     * @param {number} n31 - the third number of the 4 * 4 matrix.
     * @param {number} n32 - the seventh number of the 4 * 4 matrix.
     * @param {number} n33 - the eleventh number of the 4 * 4 matrix.
     * @param {number} n34 - the fifteenth number of the 4 * 4 matrix.
     * @param {number} n41 - the fourth number of the 4 * 4 matrix.
     * @param {number} n42 - the eighth number of the 4 * 4 matrix.
     * @param {number} n43 - the twelfth number of the 4 * 4 matrix.
     * @param {number} n44 - the sixteenth number of the 4 * 4 matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public set(n11: number, n12: number, n13: number, n14: number, n21: number, n22: number, n23: number, n24: number, n31: number, n32: number, n33: number, n34: number, n41: number, n42: number, n43: number, n44: number): void;
    /**
     * Sets the position component for this matrix from vector v.
     * @param {yunos.graphics.3d.math.Vector3} v - the passed vector to set position
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setPosition(v: Vector3): void;
    /**
     * Writes the elements of this matrix to an array in column-major format.
     * @param {number[]} array - array to store the resulting vector in
     * @param {number} offset - offset in the array at which to put the result.
     * @return {number[]} array to store the resulting vector in
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public toArray(array: Array<number>, offset: number): Array<number>;
    /**
     * Transpose the matrix
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public transpose(): this;
}
export = Matrix4;
