export interface IDomainInfo {
    token?: string;
    url: string;
    extend?: object;
}
export interface IRequestData {
    token?: string;
    dId?: string;
    requestId?: string;
    [propName: string]: string | number | object;
}
export interface IRequestInfo {
    href: string;
    data?: IRequestData;
    usePki?: boolean;
    useOwnToken?: boolean;
    serviceType?: string;
}
export interface IDomainInfoData {
    [propName: string]: IDomainInfo;
}
