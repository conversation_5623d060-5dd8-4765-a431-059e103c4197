/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
import LayoutManager = require("yunos/ui/markup/LayoutManager");
const {Visible, Hidden, None} = require("yunos/ui/view/View").Visibility;
import View = require("yunos/ui/view/View");
import ListView = require("yunos/ui/view/ListView");
import GridView = require("yunos/ui/view/GridView");
import Cursor = require("yunos/provider/Cursor");
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
import TextField = require("yunos/ui/view/TextField");
import ScrollableView = require("yunos/ui/view/ScrollableView");
import VideoInfo = require("../model/VideoInfo");
import Utils = require("../utils/Utils");
import CompositeView = require("yunos/ui/view/CompositeView");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import ButtonBM = require("extend/hdt/control/ButtonBM");
import LoadingPage = require("extend/hdt/control/LoadingPageBM");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
const RecognitionMode = VoiceCommand.RecognitionMode;
import OnlineAdapter = require("../adapter/OnlineAdapter");
import LocalAdapter = require("../adapter/LocalAdapter");
import HistoryAdapter = require("../adapter/HistoryAdapter");
import log = require("../utils/log");
const iLocalModel = require("../model/LocalModel").getInstance();
const iOnlineModel = require("../model/OnlineModel").getInstance();
const iSearchModel = require("../model/SearchModel").getInstance();
const iVideoModel = require("../model/VideoModel").getInstance();
const iAccount = require("../utils/AccountHelper").getInstance();
const iUserTrackHelper = require("../utils/UserTrackHelper").getInstance();
const iNetworkState = require("../monitor/NetworkState").getInstance();
import Consts = require("../Consts");
const RoutePath = Consts.RoutePath;
import Features = require("../Features");
const TAG = "SearchPresenter";

const PAGE_WIDTH = Utils.getPageWidth();
const PAGE_HEIGHT = Utils.getPageHeight();
const PAGE_MARGIN = Utils.getDimen("PAGE_MARGIN");
const State = {
    WAIT_INPUT: 0,
    SHOW_RESULT: 1
};
const LoadingPageType = {
    LOADING: 0,
    ERROR: 1,
    EMPTY: 2,
    NOTHING: 3,
    NO_HISTORY: 4
};

interface IViews {
    navigationBar?: NavigationBar;
    loadingPage?: LoadingPage;
    errorDlg?: View;
    emptyInfo?: TextView;
    emptyIcon?: ImageView;
    empty?: View;
    historyContainer?: View;
    historyList?: ListView;
    searchInput?: TextField;
    historyClear?: ButtonBM;
    resultContainer?: ScrollableView;
    onlineContainer?: View;
    onlineMore?: CompositeView;
    onlineGrid?: GridView;
    localContainer?: View;
    localMore?: CompositeView;
    localList?: ListView;
}

class SearchPresenter extends Presenter {
    private _searchLocalResult: {
        completed?: boolean,
        error?: Object,
        data?: Cursor,
    };
    private _searchOnlineResult: {
        completed?: boolean,
        error?: Object,
        data?: Object[],
    };
    private _highlightUrlChanged: (path: string, url: string, index: number) => void;
    private _navBackListener: () => void;
    private _retryBtnListener: () => void;
    private _destroyed: boolean;
    private _pageNo: number;
    private _views: IViews;
    private _searchWord: string;
    private _hidden: boolean;
    private _originalLocalCursor: Cursor;
    private _originalOnlineData: Object[];
    private _state: number;
    private _tapAbleViews: View[];
    private _loadingMoreView: View;
    private _loadingMore: boolean;
    private _loadingMoreTimeout: NodeJS.Timer;

    onCreate() {
        log.I(TAG, "onCreate");
        this._destroyed = false;
        this._searchLocalResult = {};
        this._searchOnlineResult = {};
        this._pageNo = 1;
        this.attachView("search");
        this._initListener();
    }

    /**
     * 初始化监听器，监听U盘、高亮的变化消息
     */
    _initListener() {
        this._highlightUrlChanged = (path: string, url: string, index: number) => {
            if (this._destroyed) {
                log.W(TAG, "_highlightUrlChanged, presenter is destroyed");
                return;
            }

            if (!path) {
                log.W(TAG, "_highlightUrlChanged, path is null");
                return;
            }

            if (!url) {
                log.W(TAG, "_highlightUrlChanged, url is null");
                return;
            }

            log.D(TAG, "_highlightUrlChanged", path, url, index);
            if (path === Consts.FromType.ONLINE) {
                if (this._views.onlineGrid) {
                    let adapter = <OnlineAdapter> this._views.onlineGrid.adapter;
                    if (adapter && adapter.data) {
                        for (let i = 0; i < adapter.data.length; i++) {
                            let item = <VideoInfo> adapter.data[i];
                            item.lastPlayed = item.url === url ? true : false;
                            adapter.update(i, item);
                        }
                    }
                }
            } else {
                if (this._views.localList) {
                    let adapter = <LocalAdapter> this._views.localList.adapter;
                    if (adapter && adapter.cursor && !adapter.cursor.isClosed()) {
                        adapter.onDataChange();
                    }
                }
            }
        };
        iVideoModel.on(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_SEARCH);
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_SEARCH);
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._setupTapHandler();
        this._state = State.WAIT_INPUT;

        this._views.searchInput.on("textchange", (text: string) => {
            log.D(TAG, "textchange, search word:", text);
            if (!text || text.length === 0 || text.trim().length === 0) {
                this._backToWaitingInput();
            }
        });

        this._views.searchInput.on("accept", () => {
            log.D(TAG, "accept, search word:", this._views.searchInput.text);
            let word = this._views.searchInput.text;
            word = word ? word.trim() : word;
            if (word && word.length > 0) {
                this._startSearch(this._views.searchInput.text);
            } else {
                this._views.searchInput.text = "";
                Utils.showToast(iRes.getString("SEARCH_INPUT_TIP"));
            }
        });
        this._views.searchInput.on("clear", () => {
            log.D(TAG, "clear");
            this._backToWaitingInput();
        });

        this._showSearchHistory();
        this._addVoiceCommands();
    }

    _backToWaitingInput() {
        this._showLoadingPage(LoadingPageType.NOTHING);
        this._views.searchInput.visibility = Visible;
        this._views.resultContainer.visibility = Hidden;
        this._views.localContainer.visibility = Hidden;
        this._views.onlineContainer.visibility = Hidden;
        this._searchWord = null;
        this._originalLocalCursor = null;
        this._originalOnlineData = null;
        this._showSearchHistory();
        this._state = State.WAIT_INPUT;
    }

    _setupViews(parentView: View) {
        this._views = {};
        this._views.navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = Visible;
        this._views.navigationBar.titleItem.visibility = None;
        this._views.navigationBar.preTitleItem.visibility = None;
        this._views.navigationBar.rightItem.visibility = Visible;
        let rightItem = <CompositeView> LayoutManager.loadSync("search_nav_right_item");
        this._views.searchInput = <TextField> rightItem.findViewById("id_search_input");
        this._views.navigationBar.rightItem = rightItem;
        this._views.navigationBar.addEventListener("back", this._navBackListener = () => {
            log.I(TAG, "back button pressed!");
            this._onBack();
        });

        this._views.historyContainer = parentView.findViewById("id_search_history");
        this._views.historyClear = <ButtonBM> this._views.historyContainer.findViewById("id_sh_clear");
        this._views.historyList = <ListView> this._views.historyContainer.findViewById("id_sh_list");
        this._views.resultContainer = <ScrollableView> parentView.findViewById("id_result_container");
        this._views.localContainer = parentView.findViewById("id_local_container");
        this._views.localList = <ListView> parentView.findViewById("id_disk_list");
        this._views.localMore = <CompositeView> parentView.findViewById("id_local_more");
        this._views.onlineContainer = parentView.findViewById("id_online_container");
        this._views.onlineGrid = <GridView> parentView.findViewById("id_online_list");
        this._views.onlineMore = <CompositeView> parentView.findViewById("id_online_more");
        this._views.empty = parentView.findViewById("id_empty");
        this._views.emptyIcon = <ImageView> this._views.empty.findViewById("id_empty_icon");
        this._views.emptyInfo = <TextView> this._views.empty.findViewById("id_empty_info");
        this._views.errorDlg = parentView.findViewById("id_error_dlg");
        this._views.loadingPage = <LoadingPage> parentView.findViewById("id_loading_page");
        this._views.loadingPage.addEventListener("RetryButtonReleased", this._retryBtnListener = () => {
            log.I(TAG, "retry button pressed!");
            if (iNetworkState.networkConnected) {
                this._startSearch(this._searchWord);
            }
        });
    }

    _onBack() {
        iVideoModel.removeHighlightUrl(Consts.FromType.SEARCH_LOCAL, "");
        this.context.router.back();
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        const cmdKeys = [
            "VOICECMD_BACK_1",
            "VOICECMD_BACK_2"
        ];

        Utils.registerVoiceCommand(this._views.navigationBar, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            log.I(TAG, "voice command", cmdKey, index);
            this._onBack();

            const iPageInstance = require("../index").getInstance();
            Utils.cancelSpeech(iPageInstance);
        });
    }

    _showSearchHistory() {
        log.D(TAG, "_showSearchHistory", iAccount.account);
        let searchHistoryData = <Object[]> iSearchModel.getSearchHistory(iAccount.account);
        log.I(TAG, "_showSearchHistory", searchHistoryData.length);
        if (searchHistoryData && searchHistoryData.length > 0) {
            this._views.historyContainer.visibility = Visible;
            this._showLoadingPage(LoadingPageType.NOTHING);
            let adapter = <HistoryAdapter> this._views.historyList.adapter;
            if (!adapter) {
                adapter = new HistoryAdapter();
                adapter.registerVoiceSelectListener(this._historySelectHandler.bind(this));
                this._views.historyList.on("itemselect", this._historySelectHandler.bind(this));
                this._views.historyList.adapter = adapter;
            }
            adapter.data = searchHistoryData;

            let textHeight = Utils.getDimen("SEARCH_BANNER_HEIGHT") + Utils.getDimen("SEARCH_BANNER_MARGIN")
                + Utils.getDimen("ITEM_HEIGHT");
            let maxListHeight = PAGE_HEIGHT - Utils.getDimen("HEADER_HEIGHT") - textHeight - PAGE_MARGIN;
            let listHeight = searchHistoryData.length * Utils.getDimen("ITEM_HEIGHT") +
                (searchHistoryData.length - 1) * Utils.getDimen("ITEM_SPACE");
            this._views.historyList.height = listHeight > maxListHeight ? maxListHeight : listHeight;
            this._views.historyContainer.height = this._views.historyList.height + textHeight;
        } else {
            this._views.historyContainer.visibility = Hidden;
            this._showLoadingPage(LoadingPageType.NO_HISTORY, iRes.getString("SEARCH_HISTORY_EMPTY"));
        }
    }

    _historySelectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_historySelectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        let adapter = <HistoryAdapter> this._views.historyList.adapter;
        if (adapter && adapter.data[position]) {
            let itemText = <string> adapter.data[position];
            iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SEARCH, iUserTrackHelper.SEARCH_CLICK_HISTROY, {search_keyword: itemText});

            this._views.searchInput.text = itemText;
            this._views.searchInput.blur();
            this._startSearch(itemText);
        }
    }

    _setupTapHandler() {
        this._tapAbleViews = [];
        Utils.setOnTapListener(this._views.historyClear, () => {
            log.I(TAG, "clear button pressed!");
            let paramObj = {
                search_history_list: this._getHistoryList()
            };
            iUserTrackHelper.clickButton(
                iUserTrackHelper.PAGE_SEARCH,
                iUserTrackHelper.SEARCH_CLEAR_HISTROY,
                paramObj
            );
            iSearchModel.clearSearchHistory(iAccount.account);
            this._views.historyContainer.visibility = Hidden;
            this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("SEARCH_HISTORY_EMPTY"));
        });
        this._tapAbleViews.push(this._views.historyClear);

        Utils.setOnTapListener(this._views.localMore, () => {
            log.I(TAG, "local more button pressed!");
            this._enterSearchMore(false);
        });
        this._tapAbleViews.push(this._views.localMore);

        Utils.setOnTapListener(this._views.onlineMore, () => {
            log.I(TAG, "online more button pressed!");
            this._enterSearchMore(true);
        });
        this._tapAbleViews.push(this._views.onlineMore);
    }

    _enterSearchMore(isOnline: boolean) {
        let launchData = {
            from: isOnline ? Consts.FromType.SEARCH_ONLINE : Consts.FromType.SEARCH_LOCAL,
            keyword: this._searchWord
        };
        this._navigate(RoutePath.SEARCH_MORE, launchData);
    }

    /**
     * 搜索视频
     * 1.搜索本地视频
     * 2.搜索在线视频
     */
    _startSearch(word: string) {
        log.D(TAG, "_startSearch", word);
        if (typeof word !== "string" || word.trim() === "") {
            return;
        }
        let paramObj = {
            search_keyword: word
        };
        iUserTrackHelper.clickButton(
            iUserTrackHelper.PAGE_SEARCH,
            iUserTrackHelper.SEARCH_GO_SEARCH,
            paramObj
        );
        this._initSearchView();

        word = word ? word.trim() : word;
        if (word && word.length > 0) {
            iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_SEARCH_RESULT);
            this._searchWord = word;
            this._showLoadingPage(LoadingPageType.LOADING);
            this._searchLocalVideo(word);
            this._searchOnlineVedio(word);
            iSearchModel.addSearchHistory(iAccount.account, word);
            this._state = State.SHOW_RESULT;
        }
    }

    _initSearchView() {
        this._showLoadingPage(LoadingPageType.NOTHING);
        this._views.searchInput.blur();
        this._views.historyContainer.visibility = Hidden;
        this._views.localContainer.visibility = Hidden;
        this._views.onlineContainer.visibility = Hidden;
        this._views.resultContainer.scrollTo(0, 0);
        if (this._originalLocalCursor && !this._originalLocalCursor.isClosed()) {
            this._originalLocalCursor.close();
            this._originalLocalCursor = null;
        }
        if (this._views.onlineGrid.adapter) {
            (<OnlineAdapter> this._views.onlineGrid.adapter).clear();
        }
        this._searchLocalResult = {};
        this._searchOnlineResult = {};
    }

    /**
     * 搜索本地视频
     */
    _searchLocalVideo(keyword: string) {
        log.D(TAG, "_searchLocalVideo", keyword);
        if (!Features.SUPPORT_USB) {
            this._searchLocalResult = {
                error: null,
                data: null,
                completed: true
            };
            return;
        }

        iLocalModel.searchVideo(keyword, 0, (error: Object, cursor: Cursor) => {
            if (this._destroyed || !this._views) {
                return;
            }

            this._searchLocalResult = {
                error: error,
                data: cursor,
                completed: true
            };
            this._checkSearchResult();
        });
    }

    /**
     * 搜索在线视频
     */
    _searchOnlineVedio(keyword: string) {
        log.D(TAG, "_searchOnlineVedio", keyword);
        iOnlineModel.searchVideo(keyword, 1, (error: Object, data: Object[]) => {
            if (this._destroyed || !this._views) {
                return;
            }

            this._searchOnlineResult = {
                error: error,
                data: data,
                completed: true
            };
            this._checkSearchResult();
        });
    }

    /**
     * 根据本地视频和在线视频搜索结果来显示UI
     */
    _checkSearchResult() {
        log.I(TAG, "_checkSearchResult", this._searchLocalResult.completed, this._searchOnlineResult.completed);
        if (!this._searchLocalResult.completed || !this._searchOnlineResult.completed) {
            return;
        }

        if (this._state === State.WAIT_INPUT) {
            log.I(TAG, "_checkSearchResult", this._state);
            this._views.empty.visibility = None;
            this._views.resultContainer.visibility = None;
            return;
        }

        let isLocalEmpty = !this._searchLocalResult.data || this._searchLocalResult.data.count === 0;
        let isOnlineEmpty = !this._searchOnlineResult.data || this._searchOnlineResult.data.length === 0;
        log.I(TAG, "_checkSearchResult", this._searchLocalResult.error, isLocalEmpty,
            this._searchOnlineResult.error, isOnlineEmpty);
        if (this._searchOnlineResult.error && this._searchLocalResult.error) {
            this._views.resultContainer.visibility = None;
            this._showLoadingPage(LoadingPageType.ERROR);
        } else if (!this._searchOnlineResult.error && this._searchLocalResult.error) {
            if (isOnlineEmpty) {
                this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("ERR_QUERY"));
                this._views.resultContainer.visibility = None;
            } else {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(null, this._searchOnlineResult.data);
            }
        } else if (this._searchOnlineResult.error && !this._searchLocalResult.error) {
            if (isLocalEmpty) {
                this._views.resultContainer.visibility = None;
                this._showLoadingPage(LoadingPageType.ERROR);
            } else {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(this._searchLocalResult.data, null);
            }
        } else {
            if (isOnlineEmpty && isLocalEmpty) {
                this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("SEARCH_RESULT_EMPTY"));
            } else if (!isOnlineEmpty && isLocalEmpty) {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(null, this._searchOnlineResult.data);
            } else if (isOnlineEmpty && !isLocalEmpty) {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(this._searchLocalResult.data, null);
            } else {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(this._searchLocalResult.data, this._searchOnlineResult.data);
            }
        }
    }

    _trySetList(cursor?: Cursor, data?: Object[]) {
        let hasLocalList = cursor && !cursor.isClosed() && cursor.count > 0;
        let hasOnlineList = data && data.length > 0;

        this._views.resultContainer.visibility = Visible;
        if (hasLocalList) {
            this._views.localContainer.visibility = Visible;
            let adapter = <LocalAdapter> this._views.localList.adapter;
            if (!adapter) {
                adapter = new LocalAdapter(true);
                adapter.registerVoiceSelectListener(this._localSelectHandler.bind(this));
                this._views.localList.adapter = adapter;
                this._views.localList.on("itemselect", this._localSelectHandler.bind(this));
            }
        } else {
            this._views.localContainer.visibility = Hidden;
            this._views.localContainer.height = 0;
        }

        if (hasOnlineList) {
            this._views.onlineContainer.visibility = Visible;
            let adapter = this._views.onlineGrid.adapter;
            if (!adapter) {
                adapter = new OnlineAdapter(true);
                this._views.onlineGrid.adapter = adapter;
                this._views.onlineGrid.on("itemselect", this._onlineSelectHandler.bind(this));
                this._views.onlineGrid.on("reachend", this._onReachend.bind(this));
            }
        } else {
            this._views.onlineContainer.visibility = None;
        }

        if (hasLocalList && hasOnlineList) {
            let num = cursor.count;
            if (num > Consts.MAX_SEARCH_VIDEO_NUM) {
                this._originalLocalCursor = cursor;
                iLocalModel.searchVideo(this._searchWord, Consts.MAX_SEARCH_VIDEO_NUM, (error: Object, c: Cursor) => {
                    log.I(TAG, "error", error);
                    if (this._destroyed || !this._views || !c) {
                        return;
                    }
                    (<LocalAdapter> this._views.localList.adapter).cursor = c;
                    this._views.localMore.visibility = Visible;
                    this._views.localList.height = Consts.MAX_SEARCH_VIDEO_NUM * Utils.getDimen("ITEM_HEIGHT")
                        + (Consts.MAX_SEARCH_VIDEO_NUM - 1) * Utils.getDimen("ITEM_SPACE");
                    this._views.localContainer.height = this._views.localList.height
                        + Utils.getDimen("SEARCH_BANNER_HEIGHT")
                        + Utils.getDimen("SEARCH_BANNER_MARGIN");
                });
            } else {
                (<LocalAdapter> this._views.localList.adapter).cursor = cursor;
                this._views.localMore.visibility = None;
                this._views.localList.height = num * Utils.getDimen("ITEM_HEIGHT") + (num - 1) * Utils.getDimen("ITEM_SPACE");
                this._views.localContainer.height = this._views.localList.height
                    + Utils.getDimen("SEARCH_BANNER_HEIGHT")
                    + Utils.getDimen("SEARCH_BANNER_MARGIN");
            }

            if (data.length > Consts.MAX_SEARCH_VIDEO_NUM) {
                this._originalOnlineData = data;
                (<OnlineAdapter> this._views.onlineGrid.adapter).data = data.slice(0, Consts.MAX_SEARCH_VIDEO_NUM);
                this._views.onlineMore.visibility = Visible;
                this._views.onlineGrid.height = Utils.getDimen("ONLINE_ITEM_HEIGHT");
                this._views.onlineContainer.height = Utils.getDimen("ONLINE_ITEM_HEIGHT")
                    + Utils.getDimen("SEARCH_BANNER_HEIGHT")
                    + Utils.getDimen("SEARCH_BANNER_MARGIN");
            } else {
                (<OnlineAdapter> this._views.onlineGrid.adapter).data = data;
                this._views.onlineMore.visibility = None;
                this._views.onlineGrid.height = Utils.getDimen("ONLINE_ITEM_HEIGHT");
                this._views.onlineContainer.height = Utils.getDimen("ONLINE_ITEM_HEIGHT")
                    + Utils.getDimen("SEARCH_BANNER_HEIGHT")
                    + Utils.getDimen("SEARCH_BANNER_MARGIN");
            }
        } else if (hasLocalList) {
            (<LocalAdapter> this._views.localList.adapter).cursor = cursor;
            this._views.localMore.visibility = None;
            let listHeight = this._views.resultContainer.height
                - Utils.getDimen("SEARCH_BANNER_HEIGHT")
                + Utils.getDimen("SEARCH_BANNER_MARGIN")
                - PAGE_MARGIN;
            this._views.localList.height = listHeight;
            this._views.localContainer.height = this._views.resultContainer.height;
            this._views.localList.arriveAt(0);
            this._views.onlineContainer.height = 0;
        } else if (hasOnlineList) {
            (<OnlineAdapter> this._views.onlineGrid.adapter).data = data;
            this._views.onlineMore.visibility = None;
            this._views.onlineContainer.height = this._views.resultContainer.height;
            let listHeight = this._views.resultContainer.height
                - Utils.getDimen("SEARCH_BANNER_HEIGHT")
                - Utils.getDimen("SEARCH_BANNER_MARGIN");
            this._views.onlineGrid.height = listHeight;
            this._views.onlineGrid.arriveAt(0);
            this._views.localContainer.height = 0;
        }
    }

    _localSelectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_localSelectHandler", position, point, voice);
        if (position < 0 || !itemView) {
            return;
        }

        let adapter = <LocalAdapter> this._views.localList.adapter;
        if (!adapter || !adapter.cursor || adapter.cursor.isClosed()) {
            return;
        }

        let videoInfo = <VideoInfo> iLocalModel.getVideoInfo(adapter.cursor, position);
        if (!videoInfo) {
            return;
        }

        let paramObj = {
            video_from: "usb",
            video_id: videoInfo.id,
            video_title: videoInfo.title,
            video_size: videoInfo.videoSize,
            video_category: "NA",
            video_tags: "NA",
            video_duration: videoInfo.duration
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SEARCH_RESULT, iUserTrackHelper.SEARCH_RESULT_PICK, paramObj);

        let launchData = {
            from: Consts.FromType.SEARCH_LOCAL,
            index: position,
            list: this._originalLocalCursor ? <Cursor> this._originalLocalCursor : adapter
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _onlineSelectHandler(itemView: View, position: number) {
        log.I(TAG, "_onlineSelectHandler", position);
        if (!itemView || position < 0) {
            return;
        }

        let adapter = <OnlineAdapter> this._views.onlineGrid.adapter;
        if (!adapter) {
            return;
        }

        let itemData = <VideoInfo> adapter.data[position];
        if (!itemData) {
            return;
        }

        let paramObj = {
            video_from: Consts.FromType.ONLINE,
            video_id: itemData.id,
            video_title: itemData.title,
            video_size: itemData.videoSize,
            video_category: itemData.category,
            video_tags: itemData.tags,
            video_duration: itemData.duration
        };
        iUserTrackHelper.clickButton(
            iUserTrackHelper.PAGE_SEARCH_RESULT,
            iUserTrackHelper.SEARCH_RESULT_PICK,
            paramObj
        );

        let launchData = {
            from: Consts.FromType.SEARCH_ONLINE,
            index: position,
            list: this._originalOnlineData ? this._originalOnlineData : adapter.data,
            pageNo: this._pageNo,
            keyword: this._searchWord
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _navigate(path: string, launchData?: Object, newConfig = {}) {
        if (path === RoutePath.BACK) {
            this._onBack();
        } else {
            if (!this._hidden) {
                if (!launchData) {
                    launchData = {};
                }
                this.context.router.navigate(path, launchData, newConfig);
            } else {
                log.W(TAG, "_navigate negtive", path);
            }
        }
    }

    _onReachend() {
        log.D(TAG, "_onReachend");
        if (!this._loadingMoreView) {
            this._loadingMoreView = this._getLoadingMoreView();
        }
        if (!this._loadingMore) {
            this._loadingMore = true;
            this._views.onlineGrid.addFooter(this._loadingMoreView);

            let pageNo = this._pageNo + 1;
            iOnlineModel.searchVideo(this._searchWord, pageNo, (error: Object, ret: object[]) => {
                this._removeLoadingMoreView();
                this._removeTimeout(this._loadingMoreTimeout);
                if (!error) {
                    if (ret && ret.length > 0) {
                        this._pageNo++;
                        (<OnlineAdapter> this._views.onlineGrid.adapter).addAll(ret);
                    } else {
                        Utils.showToast(iRes.getString("LOAD_MORE_NO_MORE"));
                    }
                } else {
                    Utils.showToast(iRes.getString("LOAD_MORE_FAILED"));
                }
            });

            this._removeTimeout(this._loadingMoreTimeout);
            this._loadingMoreTimeout = setTimeout(() => {
                this._removeLoadingMoreView();
            }, Consts.LOADING_MORE_TIMEOUT);
        }
    }

    _getLoadingMoreView() {
        let view = LayoutManager.loadSync("online_loading_more");
        view.width = PAGE_WIDTH - 2 * PAGE_MARGIN;
        return view;
    }

    _removeLoadingMoreView() {
        this._views.onlineGrid.removeFooter(this._loadingMoreView);
        this._loadingMore = false;
        this._removeTimeout(this._loadingMoreTimeout);
    }

    /**
     * 显示loading、查询错误、无网络，无数据、无搜索历史等信息
     */
    _showLoadingPage(type: number, info?: string) {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_showLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_showLoadingPage", type);
        if (type === LoadingPageType.LOADING) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.LoadingMode;
            this._views.loadingPage.errorTitleVisible = false;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = false;
            this._views.loadingPage.retryButtonVisible = false;
            this._views.loadingPage.loadingText = iRes.getString("LOADING");
        } else if (type === LoadingPageType.ERROR) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.ErrorMode;
            this._views.loadingPage.errorTitleVisible = true;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = true;
            this._views.loadingPage.retryButtonVisible = true;
            this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
            this._views.loadingPage.errorTitle = iRes.getString("NETWORK_ERROR");
        } else if (type === LoadingPageType.EMPTY) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = Visible;
            this._views.emptyIcon.propertySetName = Consts.SRC_NO_VIDEO;
            if (info) {
                this._views.emptyInfo.text = info;
            }
        } else if (type === LoadingPageType.NO_HISTORY) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = Visible;
            this._views.emptyIcon.propertySetName = Consts.SRC_NO_HISTORY;
            if (info) {
                this._views.emptyInfo.text = info;
            }
        } else if (type === LoadingPageType.NOTHING) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = None;
        }
    }

    /**
     * 获取搜索历史
     */
    _getHistoryList() {
        let data = <Object[]>iSearchModel.getSearchHistory(iAccount.account);
        if (!data || data.length === 0) {
            return "NA";
        }
        let list = "";
        if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                list += data[i];
                if (i !== data.length - 1) {
                    list += ",";
                }
            }
        }
        if (!list) {
            list = "NA";
        }
        return list;
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }

        if (this._views.navigationBar) {
            Utils.removeVoiceCommand(this._views.navigationBar);
        }
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];

        if (this._originalLocalCursor) {
            this._originalLocalCursor.close();
            this._originalLocalCursor = null;
        }

        if (this._views.localList) {
            this._views.localList.removeAllListeners("itemselect");
            let adapter = <LocalAdapter> this._views.localList.adapter;
            if (adapter) {
                adapter.registerVoiceSelectListener(null);
                adapter.destroy();
            }
        }

        if (this._views.onlineGrid) {
            this._views.onlineGrid.removeAllListeners("itemselect");
            this._views.onlineGrid.removeAllListeners("reachend");
            if (this._views.onlineGrid.adapter) {
                this._views.onlineGrid.adapter.destroy();
            }
        }

        if (this._views.historyList) {
            this._views.historyList.removeAllListeners("itemselect");
            let adapter = <HistoryAdapter> this._views.historyList.adapter;
            if (adapter) {
                adapter.registerVoiceSelectListener(null);
                adapter.destroy();
            }
        }

        if (this._views.searchInput) {
            this._views.searchInput.removeAllListeners("textchange");
            this._views.searchInput.removeAllListeners("clear");
            this._views.searchInput.removeAllListeners("accept");
        }

        if (this._loadingMoreView) {
            this._loadingMoreView.destroy();
            this._loadingMoreView = null;
        }

        if (this._views.navigationBar) {
            this._views.navigationBar.removeEventListener("back", this._navBackListener);
            this._navBackListener = null;
        }

        if (this._views.loadingPage) {
            this._views.loadingPage.removeEventListener("RetryButtonReleased", this._retryBtnListener);
            this._retryBtnListener = null;
        }

        this._views = null;
        iVideoModel.off(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
        log.I(TAG, "_removeAllListeners done");
    }

    /**
     * 移除所有timer
     */
    _removeAllTimeout() {
        this._removeTimeout(this._loadingMoreTimeout);
    }

    _removeTimeout(timeout: NodeJS.Timer) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._removeVoiceCommands();
        this._removeAllTimeout();
        this._removeAllListeners();
    }
}

export = SearchPresenter;
