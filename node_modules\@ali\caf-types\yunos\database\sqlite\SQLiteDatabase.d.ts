import Cursor = require("yunos/provider/Cursor");
import DataError = require("yunos/database/sqlite/DataError");
import DataColumn = require("yunos/database/sqlite/DataColumn");
import DataValues = require("yunos/provider/DataValues");
import ForeignKey = require("yunos/database/sqlite/ForeignKey");
import Locale = require("yunos/util/Locale");
import SQLiteStatement = require("yunos/database/sqlite/SQLiteStatement");
import SQLiteTransaction = require("yunos/database/sqlite/SQLiteTransaction");
import YObject = require("yunos/core/YObject");
/**
 * <p>The function which indicates the result of a async function.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~generalCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that
 * indicates the result
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of opening database.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~openCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that
 * indicates the result of opening SQLite database
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of the execution of SQL command.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~executeCommandCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the result of the SQL command
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of retrieving database version.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~getDbVersionCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the result of retrieving database version
 * @param {number} version - The current database version
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of SQLite data query.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~queryCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the failure when querying the data
 * @param {yunos.provider.Cursor} cursor - The Cursor object which provides APIs to
 * access the query result
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of closing database.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~closeCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the result of closing SQLite database
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of data insertion.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~insertCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the failure when inserting the data
 * @param {number} rowId - The row id of the inserted data row
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of SQLite data operations such</p>
 * <p>as delete/update/bulk insert.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteDatabase~dataChangeCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the failure when executing the data operation
 * @param {number} affectRows - The number of data records that was affected
 * by the operation
 * @public
 * @since 2
 */
/**
 * <p>SQLiteDatabase class, which provides APIs to access the underlying SQLite</p>
 * <p>database.</p>
 * <p>The instance of the class MUST be created via SQLiteOpenHelper</p>
 *
 * @example
 *
 *    let instance = require("yunos/cloudfs/Context").getInstance();
 *    let dbPath = instance.getLocalDir() + "/test.db";
 *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
 *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
 *    let version = 1;
 *    let helper = new SQLiteOpenHelper(dbPath, version);
 *    let dbInstance;
 *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
 *        if (error === null) {
 *            dbInstance = db;
 *        }
 *    });
 *
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class SQLiteDatabase extends YObject {
    private _mode;
    private _path: string;
    private _config;
    private _dbInstance;
    /**
     * Create a SQLiteDatabase instance.
     *
     * @param {string} dbPath - The path of the db file
     * @param {yunos.database.SQLiteDatabase.openMode} openMode - The mode that is
     * used when openning the given database
     * @throws {yunos.database.sqlite.DataError} If given dbPath or openMode is invalid.
     * @private
     */
    private constructor(dbPath: string, openMode: number);
    /**
     * Check if the database instance can modify the database.
     *
     * @return {boolean} true: database is writable, false: database is read only
     * @public
     * @since 2
     */
    public isWritable(): boolean;
    private createTable(tableName: string, columns: DataColumn[], foreignKeys: ForeignKey[], isSyncable: boolean, transaction: SQLiteTransaction, callback: (error: DataError) => void): void;
    /**
     * <p>Async interface to open SQLite database.</p>
     * <p>Database instance MUST be created via SQLiteOpenHelper and the<p>
     * <p>database is already opened when it is got from SQLiteOpenHelper.</p>
     * <p>The open API can be used in case of database re-open.</p>
     *
     * @example
     * Note: (Only for demo. Do not recommend to close and then open again)
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let onDbClose = function(error)  {
     *                if (error === null) {
     *                    let openCallback = function(openErr) {
     *                        if (error === null) {
     *                            // database is opened successfully
     *                        }
     *                    };
     *                    // re-open the closed database
     *                    dbInstance.open(openCallback);
     *                }
     *            }
     *            dbInstance.close(onDbClose);
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteDatabase~openCallback} callback - The callback
     * function that handles the result
     * @throws {yunos.database.sqlite.DataError} If given callback is invalid.
     * @public
     * @since 2
     */
    public open(callback: (error: DataError) => void): void;
    /**
     * <p>Sync interface to open SQLite database.</p>
     * <p>Database Instance MUST be created via SQLiteOpenHelper and the</p>
     * <p>database is already opened when it is got from SQLiteOpenHelper.</p>
     * <p>The open API can be used in case in case of database re-open</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     * Note: (Only for demo. Do not recommend to close and then open again)
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            dbInstance.closeSync();
     *            // re-open the closed database
     *            dbInstance.openSync();
     *        }
     *    });
     *
     * @throws {yunos.database.sqlite.DataError} If database is openned with error.
     * @public
     * @since 2
     */
    public openSync(): boolean;
    /**
     * <p>Check whether the database is opened.</p>
     *
     * @return {boolean} true: database is opened, false: database is not opened
     * @throws {yunos.database.sqlite.DataError} If error ocurrs while check database open state.
     * @public
     * @since 2
     */
    public isOpened(): boolean;
    /**
     * <p>Create the SQLiteStatement object with specified SQL and arguments.</p>
     *
     * @param {string} sql - The SQL command in string
     * @param {string[]|number[]} args - Parameter array of the SQL command
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the statement must be executed in
     * @return {yunos.database.sqlite.SQLiteStatement} Return the SQLiteStatement
     * object in case of success, otherwise null is returned or error is thrown
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while creating statement
     * @public
     * @since 2
     */
    public createStatement(sql: string, args: string[] | number[], transaction: SQLiteTransaction): SQLiteStatement;
    /**
     * <p>Async interface to execute specified SQL command.</p>
     * <p>Note: QUERY command can't be executed using this API.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            dbInstance.executeCommand("insert into contacts values ('ccc', '3333333333')",
     *                    null, null, function(error) {
     *                if (error === null) {
     *                    console.log("---- execution is succ.");
     *                }
     *            });
     *        }
     *    });
     *
     * @param {string} sql - The SQL command in string
     * @param {string[]|number[]} args - Parameter array of the SQL command
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the SQL command must be executed in
     * @param {yunos.database.sqlite.SQLiteDatabase~executeCommandCallback} callback - The
     * callback function that handles the result
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid
     * @public
     * @since 2
     */
    public executeCommand(sql: string, args: string[] | number[], transaction: SQLiteTransaction, callback: (error: DataError) => void): void;
    /**
     * <p>Sync interface to execute specified SQL command.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     * <p>And QUERY command can't be executed using this API.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            dbInstance.executeCommandSync("insert into contacts values ('ccc', '3333333333')",
     *                    null, null);
     *        }
     *    });
     *
     * @param {string} sql - The SQL command in string
     * @param {string[]|number[]} args - Parameter array of the SQL command
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction object
     * which indicates the transaction that the SQL command must be executed in
     * @return {boolean} Return the result of the SQL command
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while executing statement
     * @public
     * @since 2
     */
    public executeCommandSync(sql: string, args: string[] | number[], transaction: SQLiteTransaction): boolean;
    /**
     * <p>Async interface query database and return query result via Cursor.</p>
     *
     * @example
     *
     *    let onQuery = function(error, cursor)  {
     *        if (error === null) {
     *            console.log("cursor.count = " + cursor.count);
     *        }
     *    }
     *
     *    let sql = "select * from table1 where arg1=?";
     *    let args = [];
     *    args.push("expectedArg1");
     *
     *    dbInstance.query(sql, args, null, onQuery);
     *
     * @param {string} sql - The URI of the table to query.
     * @param {string[]|number[]} args - Parameter array of the query conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the query operation must be executed in
     * @param {yunos.database.sqlite.SQLiteDatabase~queryCallback} callback - Callback
     * function for receiving query result
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid
     * @public
     * @since 2
     */
    public query(sql: string, args: string[] | number[], transaction: SQLiteTransaction, callback: (error: DataError, cursor: Cursor) => void): void;
    /**
     * <p>Sync interface to query SQLite and return query result via Cursor.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let sql = "select * from table1 where arg1=?";
     *    let args = [];
     *    args.push("expectedArg1");
     *
     *    dbInstance.querySync(sql, args, null);
     *
     * @param {string} sql - The URI of the table to query.
     * @param {string[]|number[]} args - Parameter array of the query conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the query operation must be executed in
     * @return {yunos.provider.Cursor} Return the query result with a Cursor object
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while executing query
     * @public
     * @since 2
     */
    public querySync(sql: string, args: string[] | number[], transaction: SQLiteTransaction): Cursor;
    /**
     * <p>Async interface to close the SQLite database.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            let onDbClose = function(error)  {
     *                if (error !== null) {
     *                    // error is returned
     *                } else {
     *                    // close is completed
     *                }
     *            }
     *
     *            dbInstance = db;
     *            dbInstance.close(onDbClose);
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteDatabase~closeCallback} callback - Callback
     * function to handle the close result
     * @throws {yunos.database.sqlite.DataError} If the given callback is invalid
     * @public
     * @since 2
     */
    public close(callback: (error: DataError) => void): void;
    /**
     * <p>Sync interface to close the SQLite database.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            dbInstance.closeSync();
     *        }
     *    });
     *
     * @throws {yunos.database.sqlite.DataError} If error ocurrs while closing database
     * @public
     * @since 2
     */
    public closeSync(): void;
    /**
     * <p>Async interface to insert a row into the specified table.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let DataValues = require("yunos/provider/DataValues");
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance = null;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let onInsert = function(error, uri)  {
     *                if (error !== null) {
     *                    console.log("---- record is falied to be inserted.----");
     *                } else {
     *                    console.log("---- record is inserted. ----");
     *                }
     *            }
     *
     *            let values = new DataValues();
     *            values.add("column1", "test_column1");
     *            values.add("column2", "test_column2");
     *            dbInstance.insert("table1", true, values, null, onInsert);
     *        }
     *    });
     *
     * @param {string} table - The name of the table to insert into.
     * @param {boolean} replace - Whether a new row is created in case a duplicate
     * data row is detected. true: replace the existing row false: create a new row
     * @param {yunos.provider.DataValues} values - The DataValues object contains the
     * data that is going to be inserted
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the insert operation must be executed in
     * @param {yunos.database.sqlite.SQLiteDatabase~insertCallback} callback - Callback
     * function to handle the result
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid
     * @public
     * @since 2
     */
    public insert(table: string, replace: boolean, values: DataValues, transaction: SQLiteTransaction, callback: (error: DataError, rowId: number) => void): void;
    /**
     * <p>Sync interface to insert a row into the specified table.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let dbInstance;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let values = new DataValues();
     *            values.add("column1", "test_column1");
     *            values.add("column2", "test_column2");
     *            let uri = dbInstance.insertSync("table1", true, values, null);
     *        }
     *    });
     *
     * @param {string} table - The name of the table to insert into.
     * @param {boolean} replace - Whether a new row is created in case a duplicate
     * data row is detected. true: replace the existing row false: create a new row
     * @param {yunos.provider.DataValues} values - The DataValues object contains the
     * data that is going to be inserted
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the insert operation must be executed in
     * @return {number} the row id the inserted row
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while executing insertion
     * @public
     * @since 2
     */
    public insertSync(table: string, replace: boolean, values: DataValues, transaction: SQLiteTransaction): number;
    /**
     * <p>Async interface to delete rows from the specified table.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let DataValues = require("yunos/provider/DataValues");
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let onDelete = function(error, affectedRows)  {
     *                if (error !== null) {
     *                    console.log("---- record is falied to be deleted.----");
     *                } else {
     *                    console.log("---- record is deleted. ----");
     *                }
     *            }
     *
     *            let whereArgs = [];
     *            whereArgs.push("test_column1");
     *            dbInstance.delete("table1", "column1=?", whereArgs, null, onDelete);
     *        }
     *    });
     *
     * @param {string} table - The name of the table where the data is deleted from.
     * @param {string} where - The filter string that declares which rows are deleted
     * @param {string[]|number[]} whereArgs - Parameter array of the delete conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the delete operation must be executed in
     * @param {yunos.database.sqlite.SQLiteDatabase~dataChangeCallback} callback - Callback
     * function to handle the result
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid
     * @public
     * @since 2
     */
    public delete(table: string, where: string, whereArgs: string[] | number[], transaction: SQLiteTransaction, callback: (error: Error, affectedRows: number) => void): void;
    /**
     * <p>Sync interface to delete rows from the specified table.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let DataValues = require("yunos/provider/DataValues");
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let whereArgs = [];
     *            whereArgs.push("test_column1");
     *            let delRows = dbInstance.deleteSync("table1", "column1=?", whereArgs, null);
     *        }
     *    });
     *
     * @param {string} table - The name of the table where the data is deleted from.
     * @param {string} where - The filter stirng that declares which rows are deleted
     * @param {string[]|number[]} whereArgs - Parameter array of the delete conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the delete operation must be executed in
     * @return {number} Return the number of deleted rows
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while executing delete operation
     * @public
     * @since 2
     */
    public deleteSync(table: string, where: string, whereArgs: string[] | number[], transaction: SQLiteTransaction): number;
    /**
     * <p>Async interface to update rows from the specified table.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let DataValues = require("yunos/provider/DataValues");
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let onUpdate = function(error, affectedRows)  {
     *                if (error !== null) {
     *                    console.log("---- record is falied to be updated.----");
     *                } else {
     *                    console.log("---- record is updated. ----");
     *                }
     *            }
     *
     *            let values = new DataValues();
     *            values.add("column1", "test_column1");
     *            values.add("column2", "test_column2");
     *            let whereArgs = [];
     *            whereArgs.push("test_column1_1");
     *            dbInstance.update("table1", values, "column1=?", whereArgs, null, onUpdate);
     *        }
     *    });
     *
     * @param {string} table - The name of the table where the data is updated
     * @param {yunos.provider.DataValues} values - The DataValues object that contains
     * the data
     * @param {string} where - The filter stirng that declares which rows are updated
     * @param {string[]|number[]} whereArgs - Parameter array of the update conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the update operation must be executed in
     * @param {yunos.database.sqlite.SQLiteDatabase~dataChangeCallback} callback - Callback
     * function to handle the result
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid
     * @public
     * @since 2
     */
    public update(table: string, values: DataValues, where: string, whereArgs: string[] | number[], transaction: SQLiteTransaction, callback: (error: Error, affectedRows: number) => void): void;
    /**
     * <p>Sync interface to update rows from the specified table.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let DataValues = require("yunos/provider/DataValues");
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let values = new DataValues();
     *            values.add("column1", "test_column1");
     *            values.add("column2", "test_column2");
     *            let whereArgs = [];
     *            whereArgs.push("test_column1_1");
     *            let updatedRows = dbInstance.updateSync("table1", values,
     *                "column1=?", whereArgs, null);
     *        }
     *    });
     *
     * @param {string} table - The name of the table where the data is update
     * @param {yunos.provider.DataValues} values - The DataValues object contains the
     * data that is going to be updated to database
     * @param {string} where - The filter stirng that declares which rows are updated
     * @param {string[]|number[]} whereArgs - Parameter array of the update conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the update operation must be executed in
     * @return {number} Return the number of updated rows
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while executing update operation
     * @public
     * @since 2
     */
    public updateSync(table: string, values: DataValues, where: string, whereArgs: string[] | number[], transaction: SQLiteTransaction): number;
    /**
     * <p>Create a SQLiteTransaction object.</p>
     *
     * @param {boolean} isExclusive - The flag indicates whether the transaction type is exclusive
     * @return {yunos.database.sqlite.SQLiteTransaction} Return the SQLiteTransaction object
     * @public
     * @since 2
     */
    public createTransaction(isExclusive: boolean): SQLiteTransaction;
    /**
     * <p>Create a statement that is used to insert data.</p>
     *
     * @param {string} table - The table name
     * @param {boolean} replace - The flag that indicates if the data can be replaced
     * @param {yunos.provider.DataValues} values - The DataValues object contains the
     * data that is going to be inserted to database
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the statement must be executed in
     * @return {yunos.database.sqlite.SQLiteStatement} Return the SQLiteStatement object
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while creating statement
     * @public
     * @since 2
     */
    public createInsertStatement(table: string, replace: boolean, values: DataValues, transaction: SQLiteTransaction): SQLiteStatement;
    /**
     * <p>Create a statement that is used to delete data.</p>
     *
     * @param {string} table - The table name
     * @param {string} where - The filter stirng that declares which rows are deleted
     * @param {string[]|number[]} whereArgs - Parameter array of the delete conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the statement must be executed in
     * @return {yunos.database.sqlite.SQLiteStatement} Return the SQLiteStatement object
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while creating statement
     * @public
     * @since 2
     */
    public createDeleteStatement(table: string, where: string, whereArgs: string[] | number[], transaction: SQLiteTransaction): SQLiteStatement;
    /**
     * <p>Create a statement that is used to update data.</p>
     *
     * @param {string} table - The table name
     * @param {yunos.provider.DataValues} values - The DataValues object contains the
     * data that is going to be updated to database
     * @param {string} where - The filter stirng that declares which rows are updated
     * @param {string[]|number[]} whereArgs - Parameter array of the update conditions
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the statement must be executed in
     * @return {yunos.database.sqlite.SQLiteStatement} Return the SQLiteStatement object
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while creating statement
     * @public
     * @since 2
     */
    public createUpdateStatement(table: string, values: DataValues, where: string, whereArgs: string[] | number[], transaction: SQLiteTransaction): SQLiteStatement;
    private setVersion(version: number, transaction: SQLiteTransaction, callback: (error: Error) => void): void;
    private setVersionSync(version: number, transaction: SQLiteTransaction): void;
    /**
     * <p>Create a statement that is used to query the database.</p>
     *
     * @param {string} table - The table name
     * @param {string} projection - The string list which indicates the columns in the
     * result set
     * @param {string} where - The filter string that declares which rows to return
     * @param {string[]|number[]} whereArgs - Parameter array of the query conditions
     * @param {string} sortOrder - The string that indicates how to order the query results
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - The transaction
     * object which indicates the transaction that the statement must be executed in
     * @return {yunos.database.sqlite.SQLiteStatement} Return the SQLiteStatement object
     * @throws {yunos.database.sqlite.DataError} If the given params are invalid or
     * error ocurrs while creating statement
     * @public
     * @since 2
     */
    public createQueryStatement(table: string, projection: string, where: string, whereArgs: string[] | number[], sortOrder: string, transaction: SQLiteTransaction): SQLiteStatement;
    /**
     * <p>Async interface to get the version of the SQLite database.</p>
     *
     * @param {yunos.database.sqlite.SQLiteDatabase~getDbVersionCallback} callback -
     * The function to handle the get version result
     * @throws {yunos.database.sqlite.DataError} If the callback is invalid
     * @public
     * @since 2
     */
    public getVersion(callback: (error: DataError, version: number) => void): void;
    /**
     * <p>Get the version of the SQLite database.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @return {number} The current version of the database
     * @throws {yunos.database.sqlite.DataError} If error ocurrs while getting database version
     * @public
     * @since 2
     */
    public getVersionSync(): number;
    private static getLocaleStr(locale: Locale): string;
    /**
     * <p>Set the locale of the SQLite database.</p>
     *
     * @param {yunos.util.Locale} locale The locale that is going to be set to database
     * @return {boolean} Return the result of configuring locale
     * @public
     * @since 2
     */
    public setLocaleSync(locale: Locale): boolean;
    /**
     * <p>Returns a string containing a concise, human-readable description of this object.</p>
     * <p>Subclasses are encouraged to override this method and provide an implementation that
     * takes into account the object's type and data.</p>
     * @return {string} the human-readable description of this object.
     * @public
     * @override
     * @since 1
     */
    public toString(): string;
    /**
     * <p>Enum for sql error messages.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ErrorMsg: {
        /**
         * status error
         * @public
         * @since 2
         */
        StateError: string;
        /**
         * parameter type is unexpected
         * @public
         * @since 2
         */
        ParamTypeUnexpected: string;
        /**
         * parameter is out of range
         * @public
         * @since 2
         */
        ParamOutOfRange: string;
        /**
         * invalid data type
         * @public
         * @since 2
         */
        InvalidType: string;
        /**
         * cursor position is out of bound
         * @public
         * @since 2
         */
        SQLiteCursorOutOfBound: string;
        /**
         * code for error from SQLite
         * @public
         * @since 2
         */
        SQLiteError: string;
    };
    /**
     * <p>Enum for sql error code.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ErrorCode: {
        /**
         * code for status error
         * @public
         * @since 2
         */
        StateError: int;
        /**
         * code for parameter with unexpected type
         * @public
         * @since 2
         */
        ParamTypeUnexpected: int;
        /**
         * code for parameter that is out of range
         * @public
         * @since 2
         */
        ParamOutOfRange: int;
        /**
         * code for invalid data type
         * @public
         * @since 2
         */
        InvalidType: int;
        /**
         * code for cursor position is out of bound
         * @public
         * @since 2
         */
        SQLiteCursorOutOfBound: int;
        /**
         * code for error from SQLite
         * @public
         * @since 2
         */
        SQLiteError: int;
    };
    /**
     * <p>Enum for the data types that are supported by SQLite.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly DataType: {
        /**
         * Constant of column data type for: INT
         * @public
         * @since 2
         */
        SQLiteTypeInteger: number;
        /**
         * Constant of column data type for: FLOAT
         * @public
         * @since 2
         */
        SQLiteTypeDouble: number;
        /**
         * Constant of column data type for: TEXT
         * @public
         * @since 2
         */
        SQLiteTypeText: number;
        /**
         * Constant of column data type for: BLOB
         * @public
         * @since 2
         */
        SQLiteTypeBlob: number;
        /**
         * Constant of column data type for: NULL
         * @public
         * @since 2
         */
        SQLiteTypeNull: number;
    };
    /**
     * <p>Enum for the mode when openning database.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly OpenMode: {
        /**
         * Only read is allowed
         * @public
         * @since 2
         */
        OpenReadOnly: number;
        /**
         * Both read and write are allowd
         * @public
         * @since 2
         */
        OpenReadWrite: number;
    };
}
export = SQLiteDatabase;
