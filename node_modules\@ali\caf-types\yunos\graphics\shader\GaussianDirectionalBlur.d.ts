import Shader = require("./Shader");
/**
 * <p>This class provide the gaussian blur shader support for user interface interface components.</p>
 * @extends yunos.graphics.shader.Shader
 * @memberof yunos.graphics.shader
 * @private
 */
declare class GaussianDirectionalBlur extends Shader {
    private _saturation;
    private _brightness;
    private _contrast;
    private _horizontalStep;
    private _verticalStep;
    private _radius;
    private _directionMode;
    private _maxRadius;
    private _transparentBorder;
    private _scale;
    private scale: number;
    /**
     * Defines how much the source saturation is increased or decreased.
     * The decrease of the saturation is linear, but the increase is applied with a non-linear curve to allow very high saturation adjustment at the high end of the value range.
     * @name yunos.graphics.shader.GaussianBlur#saturation
     * @type {number}
     * @private
     */
    private saturation: number;
    /**
     * Defines how much the source brightness is increased or decreased.
     * The value ranges from -1.0 to 1.0. By default, the property is set to 0.0 (no change).
     * @name yunos.graphics.shader.GaussianBlur#brightness
     * @type {number}
     * @private
     */
    private brightness: number;
    /**
     * Defines how much the source contrast is increased or decreased.
     * The decrease of the contrast is linear, but the increase is applied with a non-linear curve to allow very high contrast adjustment at the high end of the value range.
     * @name yunos.graphics.shader.GaussianBlur#contrast
     * @type {number}
     * @private
     */
    private contrast: number;
    /**
     * Defines the radius
     * @name yunos.graphics.shader.GaussianBlur#radius
     * @type {number}
     * @private
     */
    private radius: number;
    /**
     * Defines the maxRadius
     * @name yunos.graphics.shader.GaussianBlur#maxRadius
     * @type {number}
     * @private
     */
    private maxRadius: number;
    /**
     * Defines the directionMode
     * @name yunos.graphics.shader.GaussianBlur#directionMode
     * @type {number}
     * @private
     */
    private directionMode: number;
    /**
     * Defines the verticalStep
     * @name yunos.graphics.shader.GaussianBlur#verticalStep
     * @type {number}
     * @private
     */
    private verticalStep: number;
    /**
     * Defines the horizontalStep
     * @name yunos.graphics.shader.GaussianBlur#horizontalStep
     * @type {number}
     * @private
     */
    private horizontalStep: number;
    /**
     * Defines the horizontalStep
     * @name yunos.graphics.shader.GaussianBlur#transparentBorder
     * @type {boolean}
     * @private
     */
    private transparentBorder: boolean;
    private updateGaussianWeights(): void;
}
export = GaussianDirectionalBlur;
