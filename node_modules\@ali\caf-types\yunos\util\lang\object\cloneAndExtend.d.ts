/**
 * The base implementation of `clone` which tracks traversed objects.
 * @param {*} value - The value to clone.
 * @param {boolean} isDeep - Whether to perform a deep copy.
 * @returns {*} Returns the cloned value.
 * @private
 */
export declare const baseClone: (value: Object, isDeep?: boolean, key?: Object, object?: Object, stack?: Object) => Object;
/**
 * The base implementation of `extend` which tracks traversed objects.
 * @param {object} value - The value to extend.
 * @param {...object} sources - the object extend from.
 * @returns {*} Returns the cloned value.
 * @private
 */
export declare const baseExtend: (value: Object, sources: Object[]) => Object;
