import LocationInfo = require("yunos/location/LocationInfo");
import { INavigateOptions, NaviStatusInfo, StatusType } from "./interfaces";
declare class MapService {
    static getInstance(): MapService;
    private static _instance;
    private mapService;
    private constructor();
    getCurLocation(): LocationInfo;
    getNaviStatus(): Promise<{
        statusType: StatusType;
        data: NaviStatusInfo;
    }>;
    getHomeAddress(): Promise<{
        statusType: string;
        data: Object;
    }>;
    getCompanyAddress(): Promise<{
        statusType: string;
        data: Object;
    }>;
    getSearchDataByPoiID(poiId: string): Promise<{
        statusType: string;
        data: Object;
    }>;
    getSearchDataByKeyWords(keyWords: string): Promise<{
        statusType: string;
        data: Object;
    }>;
    navigateTo({ currentQuickNavi, name, lng, lat }: INavigateOptions): Promise<{
        statusType: string;
        data: Object;
    }>;
}
export = MapService;
