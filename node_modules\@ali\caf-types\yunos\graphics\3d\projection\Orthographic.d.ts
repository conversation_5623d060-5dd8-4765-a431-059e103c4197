import Projection = require("./Projection");
/**
 * This class represents an orthographic camera.
 * @extends yunos.graphics.3d.projection.Projection
 * @memberof yunos.graphics.3d.projection
 * @public
 * @since 5
 */
declare class Orthographic extends Projection {
    private _left: number;
    private _right: number;
    private _top: number;
    private _bottom: number;
    /**
     * Constructor that creates an orthographic projection
     * @param {number} left - projection frustum left plane
     * @param {number} right - projection frustum right plane
     * @param {number} top - projection frustum top plane
     * @param {number} bottom - projection frustum bottom plane
     * @param {number} near - projection frustum near plane
     * @param {number} far - projection furstum far plane
     * @public
     * @since 5
     */
    public constructor(left?: number, right?: number, top?: number, bottom?: number, near?: number, far?: number);
    /**
     * projection frustum left plane
     * @name yunos.graphics.3d.projection.Orthographic#left
     * @type {number}
     * @public
     * @since 5
     */
    public left: number;
    /**
     * projection frustum right plane
     * @name yunos.graphics.3d.projection.Orthographic#right
     * @type {number}
     * @public
     * @since 5
     */
    public right: number;
    /**
     * projection frustum top plane
     * @name yunos.graphics.3d.projection.Orthographic#top
     * @type {number}
     * @public
     * @since 5
     */
    public top: number;
    /**
     * projection frustum bottom plane
     * @name yunos.graphics.3d.projection.Orthographic#bottom
     * @type {number}
     * @public
     * @since 5
     */
    public bottom: number;
}
export = Orthographic;
