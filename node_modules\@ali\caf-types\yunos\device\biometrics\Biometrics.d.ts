import ServiceProxy = require("yunos/page/ServiceProxy");

declare namespace Biometrics {
    export interface BiometricServiceProxy extends ServiceProxy {
        queryBiometricInfo(callback: Function): void;
        deleteBiometricInfo(info: Object, token: string, callback: Function): void;
        uploadBiometricInfo(info: Object, token: string, callback: Function): void;
        generateBiometricID(callback: Function): void;
        getFaceValidity(callback: Function): void;
        getVersion(callback: Function): void;
        stop(): void;
        destroy(): void;
        // closeConnection(): void;

        // for SmilePay
        registerFace(id: string): void;
        authenticateFace(): void;
        destroy(): void;
            // closeConnection(): void;
        getSmilePayValidity(callback: Function): void;
        getMetaInfo(info: string, callback: Function): void;
        enableCameraStream(): void;
        disableCameraStream(): void;
        verifyFace(zimId: string, info: string, callback: Function): void;
        cancelVerifyFace(): void;
        pauseVerifyFace(): void;
        resumeVerifyFace(): void;
        verifyPhoneNum(phoneNum: string, callback: Function): void;
        authorizeFirstUse(callback: Function): void;
        verifyExit(): void;
        registerToygerCaptureResultEvent(): void;
        registerToygerFaceStateUpdateEvent(): void;
        getSmilePayConfig(callback: Function): void;
        setSmilePayConfig(config: Object): void;
        notifyStartPay(info: string): void;
        notifyStopPay(info: string): void;
    }

    export interface ToygerCaptureResult {
        stateCode: number;
        strMessage: string;
        bestImg: {
            width: number;
            height: number;
            rotation: number;
            dataLen: number;
            cf: number;
            data: ArrayBuffer;
        };
        rect: Array<number>;
    }
}

export = Biometrics;
