import YObject = require("../core/YObject");
import Page = require("../page/Page");
/**
 * @private
 */
declare class PageExtraData {
    private displayId?: number;
}
declare namespace DataAgent {
    interface Result {
        err: number;
        msg: string;
    }
}
/**
 * <p>The DataAgent is the client agent of DataDriver, which is used for
 * collecting application level data for data analytics and business intelligence purpose.</p>
 * <p>It is aiming to provide the most-easy-to-use way to integrate the DataAgent with
 * specific application.</p>
 * <p>The DataAgent is working closely with page object. Each page can get a DataAgent instance
 * at any time. And all the events collected by this DataAgent object are bound with this page.
 * If you want to overwrite the default page by different page, you can just pass different page
 * object into the getInstance() static method. </p>
 * <p>Each method of DataAgent returns the value of an object like:
 * "{err: error-code, msg: error-message}". And the "err" equals to zero if the method is called
 * successfully. If error or exception occurs in the method, the err value is not zero, and the
 * msg field shows the details.</p>
 * <p>As a common programming pattern, the application usually set app-key by calling configure
 * method at the initialization stage. The app-key can be applied at YunOS developer platform:
 * cloudapp.yunos.com. It may also need to call setAccount method to set the
 * application level signed account information(by default the OS level signed account is used if
 * the application has no account signed in). Then the application can call any specific mehtod
 * to send event to DataDriver service.</p>
 * <p>During the application lifecycle(from the time of application showing to the time of hidding),
 * a session is created and bound to the application. And the application can call setSessionProperties
 * method for any times to set any application specific properties.</p>
 * @example
 *  // Your YunOS page main entry file:
 *  const Page = require("yunos/page/Page");
 *  const logger = log("DataDriverDemo");
 *  var DataAgent = require("yunos/datadriver/DataAgent");
 *  var appKey = "********";
 *  class YourPage extends Page {
 *      onCreate() {
 *          this.dataAgent = DataAgent.getInstance(this);
 *          var ret = this.dataAgent.configure(appKey);
 *          if (ret && ret.err) {
 *              logger.D("error: " + ret.msg);
 *          }
 *          ret = this.dataAgent.setSessionProperties({prop1: "value1", prop2: "value2"});
 *          if (ret && ret.err) {
 *              logger.D("error: " + ret.msg);
 *          }
 *      }
 *
 *      showSendEvent() {
 *          ret = this.dataAgent.sendEvent("music", {title: "butterfly"}, 1200);
 *          if (ret && ret.err) {
 *              logger.D("error: " + ret.msg);
 *          }
 *      }
 *  }
 *  module.exports = YourPage;
 *
 * @extends yunos.core.YObject
 * @memberof yunos.datadriver
 * @relyon YUNOS_SYSCAP_DATADRIVER
 * @public
 * @since 2
 */
declare class DataAgent extends YObject {
    private _appName: string;
    private _pageName: string;
    private _pageExtraData: PageExtraData;
    /**
     * Get the DataAgent object according to the specific page object.
     * Usually it is got in page initialization stage, like in onCreate or onStart function.
     * @param {yunos.page.Page | string} page - the page object or page uri to get the DataAgent
     * instance, if the instance does not exist, create a new one.
     * @return {yunos.datadriver.DataAgent} Return the DataAgent instance bouond with page object.
     * @example
     *     const Page = require("yunos/page/Page");
     *     const DataAgent = require("yunos/datadriver/DataAgent");
     *     class YourPage extends Page {
     *         onStart() {
     *             this.dataAgent = DataAgent.getInstance(this);
     *         }
     *     };
     * @public
     * @since 2
     */
    public static getInstance(page: string | Page): DataAgent;
    public constructor(appName: string, pageName: string);
    /**
     * Get or Set the current app name.
     * @name yunos.datadriver.DataAgent#appName
     * @type {string}
     * @public
     * @since 2
     */
    public appName: string;
    private changeOptions(options?: Object): Object;
    private appendExtraData(options?: Object): Object;
    /**
     * Configure the DataAgent object by applicaiton key and other options.
     * @param {string} appKey - the pre-registerred application key. Each application needs to
     * register on the developer platform to apply for the unique app key. This key is also used
     * to identify the client application at server side.
     * @param {Object} [options] - the extra information the application wants to send to server.
     * For example, the application can set the channel information in the options parameter.
     * It is optional and can be left empty if there is no extra information.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.configure("your-app-key", {"channel": "YunOS"});
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in configure: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public configure(appKey: string, options?: Object): DataAgent.Result;
    /**
     * Set application level account information.
     * Usually call it at the startup time or the moment when user is signed in the application.
     * @param {string} accountName - the account name of the signed user.
     * @param {string} accountId - the account id of the signed user. It can be empty.
     * @param {Object} [options] - the extra information about the account. The extra information
     * can be related to the application. And it is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.setAccount("John", "123456");
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in setAccount: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public setAccount(accountName: string, accountId: string, options?: Object): DataAgent.Result;
    /**
     * Set properties for the current session.
     * By default the DataDriver creates a session for each application during its entire
     * lifecycle, every customized event is bound with this session.
     * The session has many properties and this method can be called to set extra properties.
     * This method can be called for many times, all the properties are combined together.
     * @param {Object} properties - the properties which are set to this session. It is JSON format.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.setSessionProperties({
     *        "param1": "value-of-param1",
     *        "param2": "value-of-param2"
     *    });
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in setSessionProperties: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public setSessionProperties(properties?: Object): DataAgent.Result;
    /**
     * Reset the event related page info according to the Page instance.
     * The page info will be collected to enrich the upload info.
     * This call will always replace the old info.
     * @param {yunos.page.Page} page - the page object to get event related page info.
     * @example
     *    this.dataAgent.setRelatedPage(this);
     * @public
     * @since 6
     */
    public setRelatedPage(page?: Page): void;
    private setPageProperties(properties?: Object): DataAgent.Result;
    /**
     * Send the button clicking event to the DataDriver. The clicking button event is bound
     * with the current page. If you want to bind to different page or view, just pass the
     * new page name into the options as the PageName.
     * @param {string} buttonName - the clicked button name.
     * @param {Object} [options] - the extra information. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.clickButton("purchase", {"total": "99"});
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in clickButton: " + ret.msg);
     *    }
     *    // bind this event to different page:
     *    ret = this.dataAgent.clickButton("showme", {
     *       PageName: "page://datadriver.yunos.com/other", Foo: "bar"
     *    });
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in clickButton: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public clickButton(buttonName: string, options?: Object): DataAgent.Result;
    /**
     * Send the item selecting event to the DataDriver. The selecting event is bound with
     * the current page. If you want to bind to different page or view, just pass the new
     * page name into the options as the PageName. See clickButton example.
     * @param {string} controlName - the name of the selected control.
     * @param {number} selectedIndex - the index of the selected sub item.
     * @param {Object} [options] - the extra information. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.selectItem("products", 9);
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in selectItem: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public selectItem(controlName: string, selectedIndex: number, options?: Object): DataAgent.Result;
    /**
     * Send the text control input event to DataDriver. The text event is bound with
     * the current page. If you want to bind to different page or view, just pass the new
     * page name into the options as the PageName. See clickButton example.
     * @param {string} controlName - the name of the text control.
     * @param {string} content - the content value of the text control.
     * @param {Object} [options] - the extra information about this event. It is opitional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.inputText("count", "10", {"input-type": "keyboard"});
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in inputText: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public inputText(controlName: string, content: string, options?: Object): DataAgent.Result;
    /**
     * Send the crash event to DataDriver. The crash event must happen in the current page.
     * @param {string} callstack - the crash call stack information.
     * @param {Object} [options] - the extra information about the crash. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    try {
     *         ...
     *    } catch (ex) {
     *        var ret = this.dataAgent.sendCrashEvent(ex.stack);
     *        if (ret && ret.err) {
     *            log.E("DataAgent", "Error occurs in sendCrashEvent: " + ret.msg);
     *        }
     *    }
     * @public
     * @since 2
     */
    public sendCrashEvent(callstack: string, options?: Object): DataAgent.Result;
    /**
     * Send the event to DataDriver about opening a current page.
     * Usually it is called in page onShow event handler.
     * @param {Object} [options] - the extra informations. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.enterPage();
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in enterPage: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public enterPage(options?: Object): DataAgent.Result;
    /**
     * Send the event to DataDriver about closing the current page.
     * The page properties are stored in the page leave event.
     * @param {Object} [options] - the extra informations. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.leavePage();
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in leavePage: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public leavePage(options?: Object): DataAgent.Result;
    /**
     * Send the event to DataDriver about opening a view in current page.
     * The view is different with page. Usually it is contained in a page. The typical
     * view is dialog or panel.
     * @param {string} viewName - the shown view name.
     * @param {Object} [options] - the extra informations. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.enterView("download-confirm");
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in enterView: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public enterView(viewName: string, options?: Object): DataAgent.Result;
    /**
     * Send the event to DataDriver about closing an existing view.
     * @param {string} viewName - the closing view name.
     * @param {Object} [options] - the extra informations. It is optional.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *    var ret = this.dataAgent.leaveView("download-confirm");
     *    if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in leaveView: " + ret.msg);
     *    }
     * @public
     * @since 2
     */
    public leaveView(viewName: string, options?: Object): DataAgent.Result;
    /**
     * Send any kind of customized event to DataDriver. The customized event is bound with
     * the current page. If you want to bind to different page or view, just pass the new
     * page name into the options as the PageName.
     * @param {string} eventName - the name of event sending to DataDriver.
     * @param {Object} options - the event detailed information, it is optional.
     * @param {number} eventValue - the numerical value of the event. For momentary event, it is
     * the count of happens(like clicking). For continuous event, it can be the duration time.
     * @return {Object} Return the error code and error message in JSON format.
     * @example
     *     var ret = this.dataAgent.sendEvent("hunting",
     *         {"type": "fish", "target": "yangzi-river"}, 100);
     *     if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in sendEvent: " + ret.msg);
     *     }
     *     // bind to different page
     *     ret = this.dataAgent.sendEvent("action", {
     *         PageName: "page://demo.yunos.com/action", "method": "download"
     *     }, 1200);
     *     if (ret && ret.err) {
     *        log.E("DataAgent", "Error occurs in sendEvent: " + ret.msg);
     *     }
     * @public
     * @since 2
     */
    public sendEvent(eventName: string, options?: Object, eventValue?: number): DataAgent.Result;
    private static configure(appKey: string, options?: Object): DataAgent.Result;
    private static setAccount(accountName: string, accountId: string, options?: Object): DataAgent.Result;
    private static setSessionProperties(properties: Object): DataAgent.Result;
    private static setPageProperties(pageName: string, properties?: Object): DataAgent.Result;
    private static clickButton(pageName: string, buttonName: string, options?: Object): DataAgent.Result;
    private static selectItem(pageName: string, controlName: string, selectedIndex: number, options?: Object): DataAgent.Result;
    private static inputText(pageName: string, controlName: string, content: string, options?: Object): DataAgent.Result;
    private static sendCrashEvent(target: string, callstack: string, options?: Object): DataAgent.Result;
    private static enterView(viewName: string, fromPageName: string, options?: Object): DataAgent.Result;
    private static leaveView(viewName: string, options?: Object): DataAgent.Result;
    private static sendEvent(pageName: string, eventName: string, options?: Object, eventValue?: number): DataAgent.Result;
}
export = DataAgent;
