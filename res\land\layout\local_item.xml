<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_local_item"
    width="{config.LOCAL_ITEM_WIDTH}"
    height="{config.LOCAL_ITEM_HEIGHT}"
    multiState="{config.ITEM_MULTISTATE}"
    borderRadius="{config.BORDER_RADIUS_SMALL}"
    layout="{layout.local_item}"
    propertySetName="local_item">
    <ImageView
        id="id_local_icon"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"
        borderRadius="{config.BORDER_RADIUS_SMALL}"/>
    <ImageView
        id="id_last_played"
        width="{config.LOCAL_ITEM_WIDTH}"
        height="{config.LOCAL_ITEM_HEIGHT}"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"
        visibility="{enum.View.Visibility.None}"/>
    <TextView
        id="id_local_title"
        text=""
        width="{config.LOCAL_ITEM_TEXT_WIDTH}"
        multiLine="true"
        maxLineCount="2"
        elideMode="{enum.TextView.ElideMode.ElideRight}"
        propertySetName="extend/hdt/FontBody4"/>
    <TextView
        id="id_local_type"
        text=""
        width="{config.LOCAL_ITEM_TEXT_WIDTH}"
        height="{config.LOCAL_ITEM_TEXT_HEIGHT}"
        elideMode="{enum.TextView.ElideMode.ElideRight}"
        propertySetName="extend/hdt/FontBody2"/>
    <TextView
        id="id_local_totletime"
        text=""
        width="{config.LOCAL_ITEM_TEXT_WIDTH}"
        height="{config.LOCAL_ITEM_TEXT_HEIGHT}"
        elideMode="{enum.TextView.ElideMode.ElideRight}"
        propertySetName="extend/hdt/FontBody4"/>
</CompositeView>
