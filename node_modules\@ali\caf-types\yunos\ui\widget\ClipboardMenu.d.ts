import TextView = require("../view/TextView");
import CompositeView = require("../view/CompositeView");
import TextField = require("../view/TextField");
import TextArea = require("../view/TextArea");
/**
 * @private
 */
declare class ClipboardMenu extends CompositeView {
    private _showAnimation;
    private _showAnimationFinished;
    private _showAnimationDuration;
    private _closeAnimation;
    private _closeAnimationFinished;
    private _closeAnimationDuration;
    private _isShowing;
    private _itemArray;
    private _marginToBoundary;
    private _marginToBoundaryH: number;
    private _marginToBoundaryV: number;
    private _marginToClipboard;
    private _spacingH: number;
    private _spacingV: number;
    private _dividerColor;
    private _dividerWidth;
    private _windowWidth;
    private _windowHeight;
    private _defaultBackground;
    private _closeCalled;
    private targetView: TextField | TextArea;
    private menuState: number;
    public readonly defaultStyleName: string;
    private readonly marginToBoundary: number;
    private readonly marginToClipboard: number;
    private show(): void;
    private close(): void;
    private isShowing(): boolean;
    private add(itemArray: Object): void;
    private delete(index: number): void;
    private deleteAll(): void;
    private readonly marginVertical: number;
    /** @private **/
    private readonly marginHorizontal: number;
    private addContextMenuItem(text: string): void;
    private updatePosition(): void;
    private getScreenPosition(): {
        left: number;
        top: number;
    };
    private showAnimation(): void;
    private closeAnimation(): void;
    private onChildItemAdded(item: ClipboardMenu.ClipboardMenuItem): void;
    private onChildItemRemoved(item: ClipboardMenu.ClipboardMenuItem): void;
    private onMenuItemTap(menuItem: ClipboardMenu.ClipboardMenuItem): void;
    private onShowComplete(): void;
    private onCloseComplete(): void;
    private setMargins(): void;
}
declare namespace ClipboardMenu {
    /**
     * @private
     */
    class ClipboardMenuItem extends TextView {
        private _defaultFontSize;
        private _marginHorizontal;
        private _marginVertical;
        private _textColor;
        private _textColorPressed;
        private _backgroundPressed;
        private _disableOpacity;
        private onItemTextChange(property: string, oldValue: Object, value: Object): void;
        public readonly defaultStyleName: string;
        /**
         * <p>Apply theme style for ClipboardMenuItem.</p>
         * @override
         * @protected
         */
        protected applyStyle(style?: Object): void;
    }
}
export = ClipboardMenu;
