import CompositeView = require("../view/CompositeView");
declare class Pagination extends CompositeView {
    private _layoutType;
    private _maxPageNum;
    private _currentPageNum;
    private _spacing;
    private _indexTextColor;
    private _indexFontSize;
    private _indexFormat;
    private _preButtonText;
    private _nextButtonText;
    private _pagingBackground;
    private _indexStyle;
    private _defaultMaxPageNum;
    private _defaultSpacing;
    private _indexPagingViewColor;
    private _indexPagingViewFontSize;
    private _defaultBackground;
    private _defaultBorderRadius;
    private _defaultWidth;
    private _defaultHeight;
    private _rowLayoutView;
    private _prePageView;
    private _nextPageView;
    private _indexView;
    private _rowLayoutViewWidth;
    private _rowLayoutViewHeight;
    private _onPreButtonClickFun;
    private _onNextButtonClickFun;
    private onPreButtonClick(): void;
    private onNextButtonClick(): void;
    private _updateButtonState(): void;
    private _updateIndexViewText(): void;
    /**
     * @property {String}
     * Set Pagination background.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.pagingBackground = "red";
     * @private
     */
    private pagingBackground: string;
    /**
     * @property {Number}
     * Set Pagination spacing.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.spacing = 10;
     * @private
     */
    private spacing: number;
    /**
     * @property {String}
     * Set preButton text.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.preButtonText = "pre";
     * @private
     */
    private preButtonText: string;
    /**
     * @property {String}
     * Set nextButton text.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.nextButtonText = "next";
     * @private
     */
    private nextButtonText: string;
    /**
     * @property {String}
     * Set index text format.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.indexFormat = "%d\/%d";
     * @private
     */
    private indexFormat: string;
    /**
     * @property {Number}
     * Set max page number.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.maxPageNum = 20;
     * @private
     */
    private maxPageNum: number;
    /**
     * @property {Number}
     * Set current page number.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.currentPageNum = 1;
     * @private
     */
    private currentPageNum: number;
    /**
     * @property {String}
     * Set index text color.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.indexTextColor = "#ffffff";
     * @private
     */
    private indexTextColor: string;
    /**
     * @property {Number|String}
     * Set index text fontSize.
     * @example
     * var mPagination = new Pagination({
     *       layoutType: Pagination.LayoutType.Horizontal
     * });
     * mPagination.indexFontSize = 30;
     * @private
     */
    private indexFontSize: number;
    /**
     * Set index fontStyle.
     * @property {Object}
     */
    private indexStyle: Object;
    private isHorizontalLayout(): boolean;
    private static readonly LayoutType: {
        Horizontal: int;
        Vertical: int;
    };
}
export = Pagination;
