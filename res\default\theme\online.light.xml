<partial>
    <property-set name="online">
        <id name="id_banner_container">
            <property name="background">#f1f4f8</property>
        </id>
        <id name="id_usb_first">
            <property name="background">#ffffff</property>
        </id>
        <id name="id_usb_second">
            <property name="background">#ffffff</property>
        </id>
        <id name="id_category_icon">
            <property name="src">{img(images/ic_category_light.png)}</property>
        </id>
        <id name="id_category_title">
            <property name="color">{theme.color.Brand_1}</property>
        </id>
        <id name="id_cp_logo">
            <property name="src">{img(images/ic_cp_logo_light.png)}</property>
        </id>
        <id name="id_header_background">
            <property name="background">#f1f4f8</property>
        </id>
        <id name="id_top_mask">
            <property name="src">{img(images/ic_top_mask_light.png)}</property>
        </id>
        <id name="id_bottom_mask">
            <property name="src">{img(images/ic_bottom_mask_light.png)}</property>
        </id>
        <id name="id_search">
            <property name="src">{img(images/ic_search_light.png)}</property>
            <property name="multiState">{config.BTN_MULTISTATE_LIGHT}</property>
        </id>
        <id name="id_dlna">
            <property name="src">{img(images/ic_dlna_light.png)}</property>
            <property name="multiState">{config.BTN_MULTISTATE_LIGHT}</property>
        </id>
        <id name="id_qr_code_loading_container">
            <property name="background">#ffffff</property>
        </id>
        <id name="id_network_tip">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_qr_code_tips_container">
            <property name="background">#F0F4F8</property>
        </id>
        <id name="id_qr_code_tips">
            <property name="color">{theme.color.Black_2}</property>
        </id>
        <id name="id_error_info">
            <property name="color">{theme.color.Black_3}</property>
        </id>
    </property-set>

    <property-set name="online_item">
        <id name="id_mask">
            <property name="src">{img(images/ic_online_item_mask_light.png)}</property>
        </id>
        <id name="id_hot_icon">
            <property name="src">{img(images/ic_hot.png)}</property>
        </id>
        <id name="id_hot_num">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_size">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_title">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_last_played">
            <property name="src">{img(images/ic_online_item_focus_light.png)}</property>
        </id>
    </property-set>

    <property-set name="online_usb_title">
        <property name="color">{theme.color.Black_1}</property>
        <property name="fontSize">{sdp(24)}</property>
        <property name="fontWeight">{enum.TextView.FontWeight.Normal}</property>
        <property name="lineHeight">{sdp(36)}</property>
        <property name="lineHeightMode">{enum.TextView.LineHeightMode.FixedHeight}</property>
    </property-set>

    <property-set name="src_usb">
        <property name="src">{img(images/ic_usb_light.png)}</property>
    </property-set>

    <property-set name="src_ksqr">
        <property name="src">{img(images/ic_ksqr.png)}</property>
    </property-set>
</partial>
