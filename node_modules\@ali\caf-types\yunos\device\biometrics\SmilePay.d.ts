import EventEmitter = require("yunos/core/EventEmitter");
import Page = require("yunos/page/Page");
/**
 * <p>SmilePay aims to deliver the capabilities of alipay payment and other, via face recognition.<br>
 *  @example
 *  let BiometricManager = require("yunos/device/biometrics/BiometricManager");
 *  let biometricManager = BiometricManager.getInstance(context);
 *  let smilepay = biometricManager.smilePay;
 * @extends yunos.core.EventEmitter
 * @memberof yunos.device.biometrics
 */
declare class SmilePay extends EventEmitter {
    private _context;
    private _xService;
    private _xSrvConnListener;
    private _toygerFaceStateSrvEventHandle;
    private _toygerCapturResultSrvEventHandle;
    /**
     * Create instance of SmilePay.
     */
    public constructor(context: Page);
    public destroy(): void;
    private getSmilePayValidity(callback: Function): void;
    private getMetaInfo(info: string, callback: Function): void;
    private enableCameraStream(): void;
    private disableCameraStream(): void;
    private _registerToygerCaptureResultEvent(): void;
    private _registerToygerFaceStateUpdateEvent(): void;
    private registerToygerCaptureResultEvent(): void;
    private registerToygerFaceStateUpdateEvent(): void;
    private verifyFace(zimId: string, info: string, callback: Function): void;
    private cancelVerifyFace(): void;
    private pauseVerifyFace(): void;
    private resumeVerifyFace(): void;
    private verifyPhoneNum(phoneNum: string, callback: Function): void;
    private authorizeFirstUse(callback: Function): void;
    private verifyExit(): void;
    private getConfig(callback: Function): void;
    private setConfig(config: Object): void;
    private getProcessInfo(): string;
    private notifyStartPay(): void;
    private notifyStopPay(): void;
    private _initListener(): void;
}
export = SmilePay;
