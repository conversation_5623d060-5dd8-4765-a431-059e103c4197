import YObject = require("../core/YObject");
/**
 * <p>fence detail information</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @see [FenceClient]{@link yunos.fenceclient.FenceClient}
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class FenceDetailInfo extends YObject {
    private _businessId;
    private _groupId;
    private _validTime;
    private _repeatWeekday;
    private _fixedDate;
    private _fixedTime;
    private _toCloud;
    private _version;
    private _fenceId;
    private _actions;
    private _type;
    private _radius;
    private _stayMins;
    private _category;
    private _poiId;
    private _address;
    private _name;
    private _province;
    private _coordinates;
    private _city;
    private _data;
    public constructor(datas?: Object);
    /**
     * <p>businessId of fence.</p>
     * @name yunos.fenceclient.FenceDetailInfo#businessId
     * @type {string}
     * @public
     * @since 5
     */
    public businessId: string;
    /**
     * <p>groupId of fence.</p>
     * @name yunos.fenceclient.FenceDetailInfo#groupId
     * @type {string}
     * @public
     * @since 5
     */
    public groupId: string;
    /**
     * <p>Indicate when this fence will expire, blank means never expire.</p>
     * @example
     * "2019-10-01"
     * @name yunos.fenceclient.FenceDetailInfo#validTime
     * @type {string}
     * @public
     * @since 5
     */
    public validTime: string;
    /**
     * <p>Weekday repeat pattern.</p>
     * @example
     * "1,2,3,4,5,6,7", Separated by commas
     * @name yunos.fenceclient.FenceDetailInfo#repeatWeekday
     * @type {string}
     * @public
     * @since 5
     */
    public repeatWeekday: string;
    /**
     * <p>Fixed date, effective for all fences in the same group,
     * as long as fixedDate and repeatWeekday meet one of them,
     * blank means the default is effective.</p>
     * @example
     * "2019-08-01;2019-10-01"
     * @name yunos.fenceclient.FenceDetailInfo#fixedDate
     * @type {string}
     * @public
     * @since 5
     */
    public fixedDate: string;
    /**
     * <p>Fixed effective time period of fence, blank means always effective.</p>
     * @example
     * "08:00-10:00;15:00-16:00"
     * @name yunos.fenceclient.FenceDetailInfo#fixedTime
     * @type {string}
     * @public
     * @since 5
     */
    public fixedTime: string;
    /**
     * <p>Indication whether to send fence detect result to cloud platform.</p>
     * @name yunos.fenceclient.FenceDetailInfo#toCloud
     * @type {boolean}
     * @public
     * @since 5
     */
    public toCloud: boolean;
    /**
     * <p>version of fence, for client to do version control, a number value in string, length cannot exceed 10</p>
     * @name yunos.fenceclient.FenceDetailInfo#version
     * @type {string}
     * @public
     * @since 5
     */
    public version: string;
    /**
     * <p>Identify a fence in a fence group.</p>
     * @name yunos.fenceclient.FenceDetailInfo#fenceId
     * @type {string}
     * @public
     * @since 5
     */
    public fenceId: string;
    /**
     * <p>Support detect actions</p>
     * @example
     * "enter,exit,stay"
     * @name yunos.fenceclient.FenceDetailInfo#action
     * @type {string[]}
     * @public
     * @since 5
     */
    public actions: string[];
    /**
     * <p>Type of fence.</p>
     * @example
     * 0-circle,1-polygon,2-line
     * @name yunos.fenceclient.FenceDetailInfo#type
     * @type {number}
     * @public
     * @since 5
     */
    public type: number;
    /**
     * <p>When fence is of circle type, radius is circle radius in meters</p>
     * @name yunos.fenceclient.FenceDetailInfo#radius
     * @type {number}
     * @public
     * @since 5
     */
    public radius: number;
    /**
     * <p>Number of minutes. After a period of time,
     * the state of the fence changed from entering to resident</p>
     * @name yunos.fenceclient.FenceDetailInfo#stayMins
     * @type {number}
     * @public
     * @since 5
     */
    public stayMins: number;
    /**
     * <p>Category of fence, user define values</p>
     * @name yunos.fenceclient.FenceDetailInfo#category
     * @type {string}
     * @public
     * @since 5
     */
    public category: string;
    /**
     * <p>PoiId of fence, if the fence is created from a known POI</p>
     * @name yunos.fenceclient.FenceDetailInfo#poiId
     * @type {string}
     * @public
     * @since 5
     */
    public poiId: string;
    /**
     * <p>Address of fence.</p>
     * @name yunos.fenceclient.FenceDetailInfo#address
     * @type {string}
     * @public
     * @since 5
     */
    public address: string;
    /**
     * <p>Name of fence.</p>
     * @name yunos.fenceclient.FenceDetailInfo#name
     * @type {string}
     * @public
     * @since 5
     */
    public name: string;
    /**
     * <p>Province of fence.</p>
     * @name yunos.fenceclient.FenceDetailInfo#province
     * @type {string}
     * @public
     * @since 5
     */
    public province: string;
    /**
     * <p>Coordinates, when type=0, it is a single coordinate,
     * indicating the center of the circle;
     * when fenceType=1, it is a polygon boundary,
     * which needs to meet the polygon limit,
     * and the start point and end point are closed;
     * when fenceType=2, it is a line type and needs to meet the number of coordinates > 1</p>
     * @example
     * circle: "116.483972 40.012197", "longitude latitude"
     * polygon: "116.483972 40.012752,116.485030 40.012197,116.485033 40.012150,116.484227 40.011310,116.483346 40.011859,116.483348 40.011916,116.483854 40.012733,116.483972 40.012752"
     * line: "116.483972 40.012752,116.485030 40.012197,116.485033 40.012150"
     * @name yunos.fenceclient.FenceDetailInfo#coordinates
     * @type {string}
     * @public
     * @since 5
     */
    public coordinates: string;
    /**
     * <p>City of fence.</p>
     * @name yunos.fenceclient.FenceDetailInfo#city
     * @type {string}
     * @public
     * @since 5
     */
    public city: string;
    /**
     * <p>Business data, json format string,
     * will be transparently transmitted to the subscriber when the fence is notified</p>
     * @name yunos.fenceclient.FenceDetailInfo#data
     * @type {string}
     * @public
     * @since 5
     */
    public data: string;
}
export = FenceDetailInfo;
