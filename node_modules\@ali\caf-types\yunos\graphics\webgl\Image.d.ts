/**
 * Copyright (C) 2017 Alibaba Group Holding Limited. All Rights Reserved.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import Bitmap = require("yunos/graphics/Bitmap");
import EventEmitter = require("../../core/EventEmitter");
/**
 * <p>This class is used to build image for WebGLView.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.graphics.webgl
 * @public
 * @since 3
 * @hiddenOnPlatform auto
 */
declare class Image extends EventEmitter {
    private _src;
    private _onload;
    private _onerror;
    private _onabort;
    private _multimediaImage;
    private _complete;
    /**
     * <p>Create this Image.</p>
     * @public
     * @since 3
     *
     */
    public constructor();
    /**
     * <p>Destroy this Image.</p>
     * @public
     * @since 3
     *
     */
    public destroy(): void;
    /**
     * <p>Set the src value of the Image.</p>
     * <p>And then load the image file.</p>
     * <p>If the image is loaded successfully, it will call onload function.</p>
     * <p>If the image is loaded failly, it will call onerror function.</p>
     * @name yunos.graphics.webgl.Image#src
     * @type {string}
     * @throws {TypeError} If paramter is not startWith "file://", "page://" or "Image://Texture"
     * @public
     * @since 3
     */
    public src: string;
    /**
     * <p>Return the original width of the image.</p>
     * @name yunos.graphics.webgl.Image#naturalWidth
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly naturalWidth: number;
    /**
     * <p>Return the original width of the image.</p>
     * @name yunos.graphics.webgl.Image#width
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly width: number;
    /**
     * <p>Return the original height of the image.</p>
     * @name yunos.graphics.webgl.Image#naturalHeight
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly naturalHeight: number;
    /**
     * <p>Return the original height of the image.</p>
     * @name yunos.graphics.webgl.Image#height
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly height: number;
    /**
     * <p>The callback of the image successfully loaded.</p>
     * @name yunos.graphics.webgl.Image#onload
     * @type {function}
     * @throws {TypeError} If parameter is not a function.
     * @public
     * @since 3
     *
     */
    public onload: (...args: Object[]) => void;
    /**
     * <p>The callback of the image failly loaded.</p>
     * @name yunos.graphics.webgl.Image#onerror
     * @type {function}
     * @throws {TypeError} If parameter is not a function.
     * @public
     * @since 3
     *
     */
    public onerror: (...args: Object[]) => void;
    /**
     * <p>The callback of the image terminates loading.</p>
     * @name yunos.graphics.webgl.Image#onabort
     * @type {function}
     * @throws {TypeError} If parameter is not a function.
     * @public
     * @since 3
     *
     */
    public onabort: (...args: Object[]) => void;
    /**
     * <p>Return the status of the image loading.</p>
     * @name yunos.graphics.webgl.Image#complete
     * @type {boolean}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly complete: boolean;
    /**
     * <p>Get the Bitmap from Image.</p>
     * @returns {yunos.graphics.Bitmap} The Bitmap from Image.
     * @readonly
     * @public
     * @since 3
     *
     */
    public getBitmap(): Bitmap;
}
export = Image;
