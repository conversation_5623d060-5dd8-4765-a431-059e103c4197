import Bitmap = require("./Bitmap");
import YObject = require("../core/YObject");
interface ImageCoreParameter {
    get(key: string): number | string;
    set(key: string, value: string | number): void;
}
interface CartonBuffer {
    read(key: string): string | number | ArrayBuffer;
    write(key: string, value: string | number | ArrayBuffer): void;
}
interface saveCompleteCallback {
    (error: null | Error, result: null | Bitmap | string, width?: number, height?: number): void;
}
/**
 * A helper class to create QRCode from given string.
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 2
 */
declare class QRCode extends YObject {
    private _imageCore;
    private _fgColor;
    private _bgColor;
    private _level;
    private _size;
    private _version;
    private _fgColorStr;
    private _bgColorStr;
    /**
     * create QRCode instance
     * @public
     * @since 2
     */
    public constructor();
    /**
     * Destroy this instance
     * @public
     * @since 2
    */
    public destroy(): void;
    /**
     * Background color for QRCode.
     * @name yunos.graphics.QRCode#backgroundColor
     * @type {string}
     * @throws {TypeError} If parameter is not a valid color String.
     * @public
     * @since 2
     */
    public backgroundColor: string;
    /**
     * Foreground color for QRCode.
     * @name yunos.graphics.QRCode#foregroundColor
     * @type {string}
     * @throws {TypeError} If parameter is not a valid color String.
     * @public
     * @since 2
     */
    public foregroundColor: string;
    /**
     * Error correction level for QRCode.
     * @name yunos.graphics.QRCode#level
     * @type {yunos.graphics.QRCode.ErrorCorrectionLevel}
     * @throws {TypeError} If parameter is not a yunos.graphics.errorCorrectionLevel member.
     * @public
     * @since 2
     */
    public level: string;
    /**
     * Version of QRCode, the higher of the version, the greater capacity of QRcode Image.
     * Version is an Integer and value is between 1 and 40.
     * @name yunos.graphics.QRCode#version
     * @type {number}
     * @throws {TypeError} If parameter is not Integer or value is smaller than 1 or grater than 40.
     * @public
     * @since 2
     */
    public version: number;
    /**
     * Size for QRCode.
     * @name yunos.graphics.QRCode#size
     * @type {number}
     * @throws {TypeError} If parameter is not Integer or value is smaller than 1.
     * @public
     * @since 2
     */
    public size: number;
    /**
     * This callback is called back by QRCode when process and complete.
     * @callback yunos.graphics.QRCode~saveCompleteCallback
     * @param {Error} error - Error if native catched some exception when process image.
     * @param {string} path - path where QRCode imaege saved, null if has error.
     * @param {number} width - image width, null if has error.
     * @param {number} height - image height, null if has error.
     * @public
     * @since 2
     */
    /**
     * Create QRCode form given value string and save to specify file.
     * @param {string} value - value to create QRCode.
     * @param {string} path - path to save QRCode image.
     * @param {yunos.graphics.QRCode.OutputType} type - type of file created from value.
     * @param {yunos.graphics.QRCode~saveCompleteCallback} callback - Callback function.
     * @public
     * @since 2
    */
    public saveFile(value: string, path: string, type: string, callback: saveCompleteCallback): void;
    /**
     * This callback is called back by QRCode when process complete.
     * @callback yunos.graphics.QRCode~saveCompleteCallback
     * @param {Error} error - Error if native catched some exception when process image.
     * @param {yunos.graphics.Bitmap} bitmap - yunos.graphics.Bitmap instance include QRCode, null if has error.
     * @param {number} width - image width, null if has error.
     * @param {number} height - image height, null if has error.
     * @public
     * @since 2
     */
    /**
     * Create QRCode form given value string and save to specify file.
     * @param {string} value - value to create QRCode.
     * @param {yunos.graphics.QRCode~saveCompleteCallback} callback - Callback function.
     * @public
     * @since 2
    */
    public saveBitmap(value: string, callback: saveCompleteCallback): void;
    private callback(buffer: CartonBuffer, param: ImageCoreParameter, callback: saveCompleteCallback): void;
    private createParam(): ImageCoreParameter;
    /**
     * Enum for QRCode error correction level.
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly ErrorCorrectionLevel: {
        [key: string]: string;
    };
    /**
     * Enum for QRCode output type.
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly OutputType: {
        [key: string]: string;
    };
}
export = QRCode;
