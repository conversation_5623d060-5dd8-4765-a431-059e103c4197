import ImageFilter = require("./ImageFilter");
/**
 * <p>The GrayscaleImageFilter can converts the input image to grayscale.</p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class GrayscaleImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The value of amount defines the proportion of the conversion.
     * A value of 1 is completely grayscale. A value of 0 leaves the input unchanged.
     * Values between 0 and 1 are linear multipliers on the effect. </p>
     * @name yunos.graphics.filter.GrayscaleImageFilter#amount
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public amount: number;
}
export = GrayscaleImageFilter;
