import YObject = require("yunos/core/YObject");
/**
 * <p>The Base ImageFilter class. Developers should use the subclass.</p>
 *
 * @example
 * const Image = require("yunos/multimedia/Image");
 * const ImageEffect = require("yunos/graphics/ImageEffect");
 * const BlurImageFilter = require("yunos/graphics/filter/BlurImageFilter");
 * let R = require("yunos/content/resource/Resource").getInstance();
 * let ie = new ImageEffect();
 * let bif = new BlurImageFilter();
 * bif.amount = 10;
 * ie.add(bif);
 * let image = new Image(R.getImageSrc("images/alios.png"));
 * ie.processBitmap(image.getBitmap(), (err, bmp) => {
 *   iv.src = bmp;
 * });

 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class ImageFilter extends YObject {
    protected _args: number[];
    public constructor(type?: number);
    private static readonly FilterType: {
        UNKNOWN: 0;
        BLUR: 1;
        BRIGHTNESS: 2;
        CONTRAST: 3;
        DROP_SHADOW: 4;
        GRAYSCALE: 5;
        HUE_ROTATE: 6;
        INVERT: 7;
        OPACITY: 8;
        SATURATE: 9;
        SEPIA: 10;
    };
}
export = ImageFilter;
