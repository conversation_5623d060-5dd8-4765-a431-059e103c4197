import {Parameter} from "yunos/vision/ImageCoreType";

declare class ImageCoreNode {
    new(): ImageCoreNode;
    createNodes(type: string[]): void;
    init(): void;
    setProcessCallback(callback: object): void; // set callback
    getParameters(): Parameter; // return parameters
    process(source: ArrayBuffer|string): void;
    setParameters(param: Parameter): void;
    start(): void;
    stop(): void;
    uninit(): void;
}

export = ImageCoreNode;
