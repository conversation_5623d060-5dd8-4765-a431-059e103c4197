<partial>
    <property-set name="welcome">
        <id name="id_logo">
            <property name="src">{img(images/bg_video.png)}</property>
        </id>
        <id name="id_disclaimer_tips">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_disclaimer_link">
            <property name="color">{theme.color.Brand_1}</property>
        </id>
    </property-set>

    <property-set name="welcome_disclaimer_content">
        <property name="color">{theme.color.White_2}</property>
        <property name="fontSize">{sdp(24)}</property>
        <property name="fontWeight">{enum.TextView.FontWeight.Normal}</property>
        <property name="lineHeight">{sdp(36)}</property>
        <property name="lineHeightMode">{enum.TextView.LineHeightMode.FixedHeight}</property>
    </property-set>
</partial>
