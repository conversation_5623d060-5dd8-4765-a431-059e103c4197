/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
const {Visible, Hidden, None} = require("yunos/ui/view/View").Visibility;
import View = require("yunos/ui/view/View");
import GridView = require("yunos/ui/view/GridView");
import TextView = require("yunos/ui/view/TextView");
import Cursor = require("yunos/provider/Cursor");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import LoadingPage = require("extend/hdt/control/LoadingPageBM");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import ScrollBar = require("yunos/ui/widget/ScrollBar");
const RecognitionMode = VoiceCommand.RecognitionMode;
import VideoInfo = require("../../model/VideoInfo");
import LocalAdapter = require("./adapter/LocalAdapter");
const iLocalModel = require("../../model/LocalModel").getInstance();
const iVideoModel = require("../../model/VideoModel").getInstance();
const iUserTrackHelper = require("../../utils/UserTrackHelper").getInstance();
const iNetworkState = require("../../monitor/NetworkState").getInstance();
import log = require("../../utils/log");
import Consts = require("../../Consts");
const RoutePath = Consts.RoutePath;
import Utils = require("../../utils/Utils");
const TAG = "SearchMorePresenter";

const LoadingPageType = {
    LOADING: 0,
    ERROR: 1,
    EMPTY: 2,
    NOTHING: 3
};

interface IViews {
    navigationBar?: NavigationBar;
    loadingPage?: LoadingPage;
    emptyInfo?: TextView;
    empty?: View;
    localGrid?: GridView;
    scrollbar?: ScrollBar;
    keyword?: TextView;
    title?: TextView;
}

interface IGridItemView {
    data: {
        videoSize: number,
        category: Object,
        tags: Object,
        duration: Object
    };
}

class SearchMorePresenter extends Presenter {
    private _destroyed: boolean;
    private _from: string;
    private _keyword: string;
    private _views: IViews;
    private _hidden: boolean;
    private _navBackListener: () => void;
    private _retryBtnListener: () => void;
    private _highlightUrlChanged: (path: string, url: string, index: number) => void;

    onCreate() {
        log.I(TAG, "onCreate");
        if (!this.context.data || !(<{ keyword: boolean }> this.context.data).keyword) {
            Utils.showToast(iRes.getString("SEARCH_KEYWORD_EMPTY"));
            this.destroy();
            return;
        }
        this._destroyed = false;
        this._from = (<{ from: string }> this.context.data).from;
        this._keyword = (<{ keyword: string }> this.context.data).keyword;
        this.attachView("search_result");
        this._initListener();
    }

    /**
     * 初始化监听器，监听U盘、高亮的变化消息
     */
    _initListener() {
        this._highlightUrlChanged = (path, url, index) => {
            if (this._destroyed) {
                log.W(TAG, "_highlightUrlChanged, presenter is destroyed");
                return;
            }

            if (!path) {
                log.W(TAG, "_highlightUrlChanged, path is null");
                return;
            }

            if (!url) {
                log.W(TAG, "_highlightUrlChanged, url is null");
                return;
            }

            log.I(TAG, "_highlightUrlChanged", path, url, index);
            if (this._views.localGrid && this._views.localGrid.adapter) {
                this._views.localGrid.adapter.onDataChange();
            }
        };
        iVideoModel.on(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_SEARCH_RESULT);
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_SEARCH_RESULT);
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._startSearch();
        this._addVoiceCommands();
    }

    _setupViews(parentView: View) {
        this._views = {};
        this._views.navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = Visible;
        this._views.navigationBar.titleItem.visibility = Visible;
        this._views.navigationBar.preTitleItem.visibility = None;
        this._views.navigationBar.addEventListener("back", this._navBackListener = () => {
            log.I(TAG, "back button pressed!");
            this.context.router.back();
        });

        this._views.title = this._views.navigationBar.titleItem.title;
        this._views.keyword = <TextView> parentView.findViewById("id_search_keyword");
        this._views.localGrid = <GridView> parentView.findViewById("id_local_grid");
        this._views.scrollbar = <ScrollBar> parentView.findViewById("id_scrollbar");
        this._views.localGrid.horizontalScrollBar = this._views.scrollbar;

        this._views.empty = parentView.findViewById("id_empty");
        this._views.emptyInfo = <TextView> this._views.empty.findViewById("id_empty_info");
        this._views.loadingPage = <LoadingPage> parentView.findViewById("id_loading_page");
        this._views.loadingPage.addEventListener("RetryButtonReleased", this._retryBtnListener = () => {
            log.I(TAG, "retry button pressed!");
            if (iNetworkState.networkConnected) {
                this._startSearch();
            }
        });
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        log.I(TAG, "_addVoiceCommands");
        const cmdKeys = [
            "VOICECMD_BACK_1",
            "VOICECMD_BACK_2"
        ];

        Utils.registerVoiceCommand(this._views.navigationBar, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            log.I(TAG, "voice command", cmdKey, index);
            this.context.router.back();

            const iPageInstance = require("../index").getInstance();
            Utils.cancelSpeech(iPageInstance);
        });
    }

    /**
     * 搜索本地视频或在线视频
     */
    _startSearch() {
        log.D(TAG, "_startSearch", this._keyword);
        this._showLoadingPage(LoadingPageType.LOADING);
        this._keyword = this._keyword ? this._keyword.trim() : this._keyword;
        if (this._keyword && this._keyword.length > 0) {
            this._views.keyword.text = iRes.getString("SEARCH_KEYWORD", {
                word: this._keyword
            });
            this._views.title.text = iRes.getString("USB_TITLE");
            this._views.localGrid.visibility = Visible;
            this._views.scrollbar.visibility = Visible;
            this._searchLocalVideo(this._keyword);
        }
    }

    /**
     * 搜索本地视频
     */
    _searchLocalVideo(keyword: string) {
        log.D(TAG, "_searchLocalVideo", keyword);
        iLocalModel.searchVideo(keyword, 0, (error: Object, cursor: Cursor) => {
            if (error) {
                this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("ERR_QUERY"));
                this._views.localGrid.visibility = Hidden;
            } else {
                if (cursor && cursor.count > 0) {
                    this._showLoadingPage(LoadingPageType.NOTHING);
                    this._views.localGrid.visibility = Visible;
                    let adapter = <LocalAdapter> this._views.localGrid.adapter;
                    if (!adapter) {
                        adapter = new LocalAdapter(true);
                        adapter.registerVoiceSelectListener(this._selectHandler.bind(this));
                        this._views.localGrid.adapter = adapter;
                        this._views.localGrid.on("itemselect", this._selectHandler.bind(this));
                    }
                    adapter.cursor = cursor;
                } else {
                    this._showLoadingPage(LoadingPageType.EMPTY);
                    this._views.localGrid.visibility = Hidden;
                }
            }
        });
    }

    _selectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_selectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        let adapter = <LocalAdapter> this._views.localGrid.adapter;
        if (!adapter) {
            return;
        }

        let videoInfo = <VideoInfo>iLocalModel.getVideoInfo(adapter.cursor, position);
        if (!videoInfo) {
            return;
        }

        let paramObj = {
            video_size: videoInfo.videoSize,
            video_duration: videoInfo.duration
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SEARCH_RESULT, iUserTrackHelper.SEARCH_RESULT_PICK, paramObj);

        let launchData = {
            from: Consts.FromType.SEARCH_LOCAL,
            index: position,
            list: adapter
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _navigate(path: string, launchData?: Object, newConfig = {}) {
        if (path === RoutePath.BACK) {
            this.context.router.back();
        } else {
            if (!this._hidden) {
                if (!launchData) {
                    launchData = {};
                }
                this.context.router.navigate(path, launchData, newConfig);
            } else {
                log.W(TAG, "_navigate negtive", path);
            }
        }
    }

    /**
     * 显示loading、查询错误、无网络，无数据等信息
     */
    _showLoadingPage(type: number, info?: string) {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_showLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_showLoadingPage", type);
        if (type === LoadingPageType.LOADING) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.LoadingMode;
            this._views.loadingPage.errorTitleVisible = false;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = false;
            this._views.loadingPage.retryButtonVisible = false;
            this._views.loadingPage.loadingText = iRes.getString("LOADING");
        } else if (type === LoadingPageType.ERROR) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.ErrorMode;
            this._views.loadingPage.errorTitleVisible = true;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = true;
            this._views.loadingPage.retryButtonVisible = true;
            this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
            this._views.loadingPage.errorTitle = iRes.getString("NETWORK_ERROR");
        } else if (type === LoadingPageType.EMPTY) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = Visible;
            if (info) {
                this._views.emptyInfo.text = info;
            }
        } else if (type === LoadingPageType.NOTHING) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = None;
        }
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }

        if (this._views.navigationBar) {
            Utils.removeVoiceCommand(this._views.navigationBar);
        }
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._views) {
            if (this._views.localGrid) {
                this._views.localGrid.removeAllListeners("itemselect");
                let adapter = <LocalAdapter> this._views.localGrid.adapter;
                if (adapter) {
                    adapter.registerVoiceSelectListener(null);
                    adapter.destroy();
                }
            }

            if (this._views.navigationBar) {
                this._views.navigationBar.removeEventListener("back", this._navBackListener);
                this._navBackListener = null;
            }

            if (this._views.loadingPage) {
                this._views.loadingPage.removeEventListener("RetryButtonReleased", this._retryBtnListener);
                this._retryBtnListener = null;
            }

            this._views = null;
        }

        iVideoModel.off(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
        log.I(TAG, "_removeAllListeners done");
    }

    _removeTimeout(timeout: NodeJS.Timer) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._removeVoiceCommands();
        this._removeAllListeners();
    }
}

export = SearchMorePresenter;
