import View = require("../view/View");
import CompositeView = require("../view/CompositeView");
import ColorOverlay = require("../../graphics/shader/ColorOverlay");
/**
 * Draw ball with canvas.
 * @extends yunos.ui.view.CompositeView
 * @private
 */
declare class Ball extends CompositeView {
    private _shaderView;
    private _forceArr;
    private _radius;
    private _ballColor;
    private _size;
    private cp1;
    private cp2;
    private cp3;
    private cp4;
    private cp5;
    private cp6;
    private _centerShaderView;
    private _leftShaderView;
    private _rightShaderView;
    private _radiusShaderWidth;
    private _shaderHeight;
    private _leftShader;
    private _rightShader;
    private _centerShader;
    /**
     * Sets the parameters that implement the blocking effect
     * @type {number[]}
     * @private
     */
    private forceArr: number[];
    /**
     * Sets the ellipsoid length of the sphere.
     * @type {number}
     * @private
     */
    private size: number;
    /**
     * Gets ball's radius.
     * @type {number}
     * @readonly
     * @private
     */
    private readonly radius: number;
    /**
     * Sets ball's color.
     * @type {number}
     * @private
     */
    private ballColor: string;
    private readonly shader: CompositeView;
    private initBall(): void;
    private createShader(): ColorOverlay;
    private show(): void;
    private appendShader(view: View, shader: ColorOverlay): void;
    private initShader(): void;
    private updateShaderColor(): void;
    private createView(width: number, height: number): View;
    private removeBall(): void;
    private doLayout(): void;
    private cubicEaseOut(t: number, b: number, c: number, d: number): number;
    private updateShape(): void;
}
export = Ball;
