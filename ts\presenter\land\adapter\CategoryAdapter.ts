/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets

import BaseAdapter = require("yunos/ui/adapter/BaseAdapter");
import View = require("yunos/ui/view/View");
const {Visible, None} = require("yunos/ui/view/View").Visibility;
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import ListItemBM = require("extend/hdt/control/ListItemBM");
import {IVoiceEvent} from "../../../Types";
const log = require("../../../utils/log");
const Consts = require("../../../Consts");
const TAG = "CategoryAdapter";

interface ICategoryData {
    category: string;
}

interface ICategoryView extends ListItemBM {
    title: TextView;
    focusImage: ImageView;
    index: number;
}

class CategoryAdapter extends BaseAdapter {
    private _focusPosition: number;
    private _voiceSelectListener: (itemView: View, position: number, point: object, voice: boolean) => void;

    constructor() {
        super();
        log.I(TAG, "CategoryAdapter");
    }

    createItem(position: number, convertView: ICategoryView) {
        if (!convertView) {
            convertView = <ICategoryView> LayoutManager.loadSync("online_category_item");
            convertView.title = <TextView>convertView.findViewById("id_title");
            convertView.focusImage = <ImageView>convertView.findViewById("id_focus_image");

            if (Consts.SUPPORT_VOICE_CMD) {
                convertView.title.voiceEnabled = true;
                convertView.title.voiceSelectMode = View.VoiceSelectMode.Custom;
                const VoiceCommand = require("yunos/ui/voice/VoiceCommand");
                convertView.title.defaultVoiceCommand.recognitionQuality = VoiceCommand.RecognitionQuality.LOW;
                convertView.title.defaultVoiceCommand.keepAwake = false;
                convertView.title.on("voice", (e: IVoiceEvent) => {
                    let itemIndex = convertView.index;
                    log.I(TAG, "voice", position, itemIndex);
                    if (this._voiceSelectListener) {
                        this._voiceSelectListener(convertView, itemIndex, null, true);
                    }
                    e.endLocalTask();
                });
            }
        }

        let categoryData = <ICategoryData> this.data[position];
        if (categoryData) {
            convertView.title.text = categoryData.category ? categoryData.category : "";
        }

        if (this._focusPosition === position) {
            convertView.focusImage.visibility = Visible;
        } else {
            convertView.focusImage.visibility = None;
        }
        return convertView;
    }

    setFocusPosition(position: number) {
        this._focusPosition = position;
    }

    registerVoiceSelectListener(callback: (itemView: View, position: number, point: object, voice: boolean) => void) {
        this._voiceSelectListener = callback;
    }
}

export = CategoryAdapter;
