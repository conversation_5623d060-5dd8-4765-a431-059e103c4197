/// <reference types="yunos" />
import { INode } from "yunos/ui/util/TypeHelper";
import View = require("yunos/ui/view/View");
import Theme = require("yunos/ui/theme/Theme");
export declare function reflectObject(target: Object): ObjectReflectI;
export declare function getNode(view: View): INode;
export declare function getTheme(view: View): Theme;
export declare function getScreenPosition(view: View): {
    top: int;
    left: int;
};
export declare function getScreenBackground(windowPositon: number): string;
