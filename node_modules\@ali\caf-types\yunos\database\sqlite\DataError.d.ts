/**
 * <p>DataError class, which contains the error information.</p>
 * @extends Error
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class DataError extends Error {
    private _code;
    private _message;
    private _desc;
    /**
     * Create a DataError instance with given error information.
     *
     * @param {number} code - The error code
     * @param {string} message - The static error string
     * @param {string} description - The string that contains the more
     * detailed information of the error
     * @public
     * @since 2
     */
    public constructor(code: number, message: string, description: string);
    /**
     * Get the error code.
     * @name yunos.database.sqlite.DataError#number
     * @type {number}
     * @public
     * @since 2
     */
    public number: number;
    /**
     * Get the brief error information.
     * @name yunos.database.sqlite.DataError#message
     * @type {string}
     * @public
     * @since 2
     */
    /**
     * Get the detailed error information.
     * @name yunos.database.sqlite.DataError#description
     * @type {string}
     * @public
     * @since 2
     */
    public description: string;
    /**
     * Convert the DataError information to a string.
     *
     * @public
     * @since 2
     */
    public toString(): string;
}
export = DataError;
