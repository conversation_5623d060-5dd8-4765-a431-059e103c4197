import EventEmitter = require("yunos/core/EventEmitter");
import DBus = require("ubus");
declare class FaceIDProxy extends EventEmitter {
    protected _iface: DBus.Interface;
    private static instance;
    private _regRule;
    private _recRule;
    private _livenessRule;
    private _boundRule;
    public constructor();
    private addListeners(): void;
    private addRegisterListener(): void;
    private addRecognizeListener(): void;
    private addLivenessListener(): void;
    private addBoundListener(): void;
    private removeListeners(): void;
    private registerFace(id: string, subID: string | undefined): number;
    private recognizeFace(): number;
    private stopRegisterFace(): number;
    private stopRecognizeFace(): number;
    private resetRecognizedResult(): number;
    private deleteFaceID(id: string, subID: string, callback: (p: object) => void): void;
    private queryRecognizedResultSync(appId?: string): Object;
    private queryRegisteredFaceSync(id: string): {}[] | {
        [key: string]: Object;
    };
    public hasRegisteredFaceSync(): boolean;
    private detectFaceLiveness(livenessType: number): number;
    private stopDetectFaceLiveness(livenessType: number): void;
    public bindFace(id: string, appId: string): number;
    public stopBindFace(): number;
    public unBindFace(id: string): number;
    public isFaceBoundSync(id: string): boolean;
    public getAppId(lable: string): string;
    private enableFaceID(id: string): number;
    private disableFaceID(id: string): number;
    private isFaceIDEnabled(id: string): boolean;
    private queryAllRegisteredFaceSync(id: string): {}[] | {
        [key: string]: Object;
    };
    private configBioUt(config: string): void;
    public destroy(): void;
    private static releaseInstance(): void;
    private static getInstance(): FaceIDProxy;
}
export = FaceIDProxy;
