import CompositeView = require("./CompositeView");
/**
 * <p>Gif view for playing gifs on YunOS.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.view
 * @public
 * @since 3
 *
 */
declare class GifView extends CompositeView {
    private _imageView;
    private _native;
    private _currentIdx;
    private _src;
    private _bmps;
    private _async;
    /**
     * <p>Constructor that create a gif view.</p>
     * @public
     * @since 3
     *
     */
    public constructor(...args: Object[]);
    /**
     * <p>Destructor that destroy this gif view.</p>
     * @public
     * @since 3
     *
     */
    public destroy(): void;
    /**
     * <p>The gif's url, which indicates a local path currently.
     * @name yunos.ui.view.GifView#src
     * @type {string}
     * @throws {TypeError} If the value is not an uri.
     * @public
     * @since 3
     *
     */
    public src: string;
    /**
     * <p>The gif's asynchronous loading mode.
     * @name yunos.ui.view.GifView#async
     * @type {boolean}
     * @throws {TypeError} If the value is not a boolean.
     * @public
     * @since 3
     *
     */
    public async: boolean;
    private reset;
    private play;
    private showFrames;
    /**
     * <p>Return the width of gif.</p>
     * @return {number}
     * @public
     * @since 3
     *
     */
    public getWidth(): number;
    /**
     * <p>Return the height of gif.</p>
     * @return {number}
     * @public
     * @since 3
     *
     */
    public getHeight(): number;
    private getDelay;
    private getFrameCount;
    private changeToNativeAdd;
    private clearNativeBitmap;
}
export = GifView;
