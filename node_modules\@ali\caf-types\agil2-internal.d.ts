declare class CmdBufferClass {
    getEvId(id: number): number | string;
    getEcId(id: number): number | string;
    getCmdId(id: number | string): number | string;
    getHandlerName(id: number | string): number | string;

    cbTextLayout: number;
    Sig: { [key: string]: number };
    cbResourceLoader: number;
    Cmd: { [key: string]: number };
    Ev: { [key: string]: number };
    Ec: { [key: string]: number };
    nullHandle: number;
    [key: string]: Function | number | { [key: string]: number };

    allocHandle(): number;

    addReleaseCmd(v: number): void;

    releaseScratch(v: number): void;

    createScratch(): number;

    setCurrent(v: number): void;

    mergeScratch(v: number): void;

    flush(v?: boolean): void;

    finish(): void;

    addCmdSIIF(cmdId: number, handle: number, str: string, v1: number, v2: number, v3: number): void;

    addVarCmd(cmdId: number, handle: number, sig: number, ...args: Object[]): void;

    addCmdI(v1: number, v2: number, v3: number): void;

    addCmdZ(v1: number, v2: number): void;

    addCmdH(v1: number, v2: number, v3: number): void;

    addCmdF(v1: number, v2: number, v3: number): void;

    addCmdU(v1: number, v2: number, v3: number): void;

    addCmdS(v1: number, v2: number, v3: string): void;

    addCmdf(v1: number, v2: number, v3: number[]): void;

    addCmdFFFF(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;

    addCmdIIII(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;

    addCmdUUUU(v1: number, v2: number, v3: number, v4: number, v5: number, v6: number): void;

    addCmdUU(v1: number, v2: number, v3: number, v4: number): void;

    addCmdII(v1: number, v2: number, v3: number, v4: number): void;

    addCmdIS(v1: number, v2: number, v3: number, v4: string): void;

    addCmduff(v1: number, v2: number, v3: number[], v4: number[], v5: number[]): void;

    addCmdSi(v1: number, v2: number, v3: string, v4: number[]): void;

    addCmdi(v1: number, v2: number, v3: number[]): void;

    addCmdHi(v1: number, v2: number, v3: number, v4: number[]): void;

    addCmdUF(v1: number, v2: number, v3: number, v4?: number): void;

    addCmdSH(v1: number, v2: number, v3: string, v4?: number): void;

    addCmdIfi(cmd: number, handle: any, id: number, values: number[], opts: number[]): void;

    setEventsHandlers(EventHandler: Object, RequestHandler: Object, OutOfBandHandler: Object): void;

    addCmdSf(cmd: number, handle: number, url: string, arg3: number[]): void;

    addCmdFF(cmd: number, handle: number, x: number, y: number): void;

    addCmdIII(cmd: number, handle: number, arg2: number, iface: number, id: number): void;

    addCmdSU(cmd: number, handle: number, name: string, arg3: number): void;

    addCmdSF(cmd: number, handle: number, name: any, object: number): void;

    addCmdUUI(v1: number, v2: number, v3: number, v4: number, v5: number): void;
}

declare class AgilRoot {
    CmdBuffer: CmdBufferClass;
    PlatformBridge: {
        createInitialWindow(...arg: Array<Object>): Array<Object>;
        createWindow(...arg: Array<Object>): Array<Object>;
        destroyWindow(v: number): void;
    }
}

declare class Agil {
    static root(): AgilRoot;
}

export = Agil;
