<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_container"
    width="{config.HEADER_NAV_RIGHT_ITEM_WIDTH}"
    height="{config.HEADER_NAV_RIGHT_ITEM_HEIGHT}"
    layout="{layout.online_nav_right_item}"
    propertySetName="online_nav_right_item_normal">

    <ButtonBM
        id="id_usb_first"
        width="{config.ONLINE_HEADER_ITEM_WIDTH}"
        buttonType="{enum.ButtonBM.ButtonType.Ghost}"
        contentType="{enum.ButtonBM.ContentType.IconLeft}"
        colorType="{enum.ButtonBM.ColorType.Integrated}"
        text="{string.USB_DRIVE}"/>

    <ButtonBM
        id="id_usb_second"
        width="{config.ONLINE_HEADER_ITEM_WIDTH}"
        buttonType="{enum.ButtonBM.ButtonType.Ghost}"
        contentType="{enum.ButtonBM.ContentType.IconLeft}"
        colorType="{enum.ButtonBM.ColorType.Integrated}"
        text="{string.USB_DRIVE}"/>

    <ButtonBM
        id="id_dlna"
        width="{config.ONLINE_HEADER_ITEM_WIDTH}"
        buttonType="{enum.ButtonBM.ButtonType.Ghost}"
        contentType="{enum.ButtonBM.ContentType.IconLeft}"
        colorType="{enum.ButtonBM.ColorType.Integrated}"
        text="{string.DLNA}"/>

    <ButtonBM
        id="id_search"
        width="{config.ONLINE_HEADER_ITEM_WIDTH}"
        buttonType="{enum.ButtonBM.ButtonType.Ghost}"
        contentType="{enum.ButtonBM.ContentType.IconLeft}"
        colorType="{enum.ButtonBM.ColorType.Integrated}"
        text="{string.SEARCH_TEXT}"/>
</CompositeView>
