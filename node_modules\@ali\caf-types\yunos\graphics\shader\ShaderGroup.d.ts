import YObject = require("../../core/YObject");
import Shader = require("./Shader");
import View = require("yunos/ui/view/View");
import ShaderImpl = require("yunos/ui/rendering/agil2/shader/Shader");
/**
 * <p>ShaderGroup is a collection of shaders.</p>
 *
 * @example
 *
 * let sg = new ShaderGroup();
 * let s1 = new Shader();
 * let s2 = new Shader();
 * sg.add(s1);
 * sg.add(s2);
 * view.addShader(sg);
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics.shader
 * @public
 * @since 5
 *
 */
declare class ShaderGroup extends YObject {
    private _shaders: Shader[];
    private _width: number;
    private _height: number;
    private _source: View | Shader;
    private _attachedView: View;
    /**
     * Create a shader group.
     * @public
     * @since 5
     */
    public constructor();
    private initializeImpl(): void;
    private attachedView: View;
    /**
     * Add shader into this group.
     * @param {yunos.graphics.shader.Shader} shader - shader to add
     * @public
     * @since 5
     */
    public add(shader: Shader): void;
    /**
     * Remove shader from this group.
     * @param {yunos.graphics.shader.Shader} shader - shader to remove
     * @public
     * @since 5
     */
    public remove(shader: Shader): void;
    /**
     * Shader in this group
     * @readonly
     * @public
     * @since 5
     */
    public readonly shaders: Shader[];
    /**
     * Source for shader
     * @param {yunos.ui.view.View | yunos.graphics.shader.Shader} source
     * @private
     */
    private source: View | Shader;
    private getImpl(): ShaderImpl;
    /**
     * Set width for shader in this group
     */
    private width: number;
    /**
     * Set height for shader in this group
     */
    private height: number;
}
export = ShaderGroup;
