import YObject = require('../../core/YObject');
/**
 * Enum of mask filter blur style
 * @ignore
 */
export declare enum BlurStyle {
    /**
     * fuzzy inside and outside
     * @ignore
     */
    Normal = 0,
    /**
     * solid inside, fuzzy outside
     * @ignore
     */
    Solid = 1,
    /**
     * nothing inside, fuzzy outside
     * @ignore
     */
    Outer = 2,
    /**
     * fuzzy inside, nothing oputside
     * @ignore
     */
    Inner = 3,
    /**
     * @ignore
     */
    LastEnum = 3
}
/**
 * Wrapper class of SkMaskFilter that perform transformations on the mask before drawing it.
 * @example
 *  let filter = MaskFilter.CreateBlurMaskFilter(BlurStyle.Inner, 3);
 * @ignore
 */
export declare class MaskFilter extends YObject {
}
