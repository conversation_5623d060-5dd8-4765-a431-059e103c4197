import View = require("yunos/ui/view/View");
import CompositeView = require("yunos/ui/view/CompositeView");
import Bitmap = require("yunos/graphics/Bitmap");
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
import Gradient = require("yunos/graphics/Gradient");
interface IStyle {
    fontNormalSize: number;
    textNormalColor: string;
    textSelectColor: string;
    iconSize: number;
    iconSpacing: number;
    mainBackground: string;
    firstTabMarginStart: number;
    defaultHeight: number;
    defaultItemSpacing: number;
    defaultItemMinSpacing: number;
    defaultBorderRadius: number;
    defaultBorderWidth: number;
    defaultAnimViewCapinsets: number | number[];
    defaultAnimViewMarginRight: number;
    defaultAnimViewMarginBottom: number;
    textAnimationDuration: number;
    defaultAnimViewHeight: number;
    defaultAnimViewWidth: number;
    defaultAnimViewColor: string;
    defaultAnimViewBorderRadius: number;
    defaultAnimViewBorderWidth: number;
    defaultAnimViewBorderColor: string | Gradient;
    defaultAnimViewShadowOffsetX: number;
    defaultAnimViewShadowOffsetY: number;
    defaultAnimViewShadowRadius: number;
    defaultAnimViewShadowColor: string;
    defaultTextWeight: number;
    defaultCurrentTextWeight: number;
    tabItemPadding: number | number[];
}
declare class DynamicMutiSwitchBML extends CompositeView {
    private _fontNormalSize;
    private _textNormalColor;
    private _textSelectColor;
    private _currentIndex;
    private _oldIndex;
    private _iconSize;
    private _items;
    private _tapRecognizer;
    _iconAutoColor: boolean;
    private _defaultItemSpacing;
    private _defaultItemMinSpacing;
    private _iconSpacing;
    private _animView;
    private _animationGroup;
    private _leftAnimation;
    private _defaultAnimViewHeight;
    private _defaultAnimViewMarginRight;
    private _defaultAnimViewMarginBottom;
    private _defaultAnimViewColor;
    private _defaultAnimViewBorderRadius;
    private _defaultAnimViewCapinsets;
    private _defaultAnimViewBorderWidth;
    private _defaultAnimViewBorderColor;
    private _defaultAnimViewShadowOffsetX;
    private _defaultAnimViewShadowOffsetY;
    private _defaultAnimViewShadowRadius;
    private _defaultAnimViewShadowColor;
    private _defaultTextWeight;
    private _defaultCurrentTextWeight;
    private _mainBackground;
    private _defaultBorderRadius;
    private _defaultBorderWidth;
    private _textAnimationDuration;
    private _defaultHeight;
    private _tabItemPadding;
    private _firstTabMarginStart;
    readonly defaultStyleName: string;
    width: number;
    constructor(...args: Object[]);
    items: string[];
    addItem(text: string, icon?: string | Bitmap): void;
    getItem(index: number): CompositeView;
    currentIndex: number;
    readonly layoutSpacing: number;
    private setTabBarState;
    refresh(): void;
    removeItem(index: number): void;
    private addRealItem;
    createIconView(icon?: string | Bitmap): ImageView | TextView;
    private onTapItem;
    private _getPadding;
    protected applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    getAnimationView(): View;
    private _getAnimationGroup;
    private _updateAnimViewPosition;
    private _getAnimViewLeft;
    onMeasureWidth(): int;
    private _updateLeftAnimation;
    protected _getRealItemWidth(index: number): number;
    private _getRealAnimaViewWidth;
    protected doAnimation(currentIndex: number, oldIndex: number): void;
    private smoothToIndex;
    destroy(recursive?: boolean): void;
    private _getAppTextFont;
}
export = DynamicMutiSwitchBML;
