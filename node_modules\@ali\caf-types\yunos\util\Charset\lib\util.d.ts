/// <reference types="node" />
export declare function generateAscii(): string;
export declare function hasEncoding(encoding: string): boolean;
export declare function cached(source: (...args: Object[]) => Object, cache?: {
    [key: string]: Object;
}): (...arg: Object[]) => Object;
export declare const getCodec: (...arg: Object[]) => Object;
export declare function getMainCharsetName(encoding: string): string;
export declare function generateReferenceBuffer(chars: string): Buffer;
export declare function generateReferenceFromTable(table: number[], shouldReplace: boolean): {
    [key: number]: number;
};
export declare function isASCII(charCode: number): boolean;
export declare function decodeCharCode(charCode: number, ret: number[], idx: number): number;
export declare function decodeCharCodeBE(charCode: number, ret: number[], idx: number): number;
export declare function detectBOM(buf: Buffer, byteSize: number): string;
