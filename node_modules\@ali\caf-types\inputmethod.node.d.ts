/*
 * Copyright (C) 2019 Alibaba Group Holding Limited. All Rights Reserved.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare class addon {

    static registerListener(callback: (...args: any[]) => void) : void;
    static startInputMethodService(): void;
    static finishLayout(): void;
    static setIMEngineMode(mode: number): void;
    static setIMEngineStatus(cmd: number, mode: string): void;
    static sendKeyEvent(key: string): void;
    static forwardKeyEvent(key: string): void;
    static updatePageSize(size: number): void;
    static selectCandidate(index: number): void;
    static selectPreedit(index: number): void;
    static commitString(text: string, mode: number): void;
    static setInputMode(mode: string): void;
    static showCandidates(): void;
    static showPreedits(): void;
    static hideCandidates(): void;
    static hidePreedits(): void;
    static resetKeyboardIse(): void;
    static resetEngineIse(): void;
    static commitPreeditString(s: string): void;
    static syncEngineData(data: string): void;
    static exit(): void;
}

export = addon;