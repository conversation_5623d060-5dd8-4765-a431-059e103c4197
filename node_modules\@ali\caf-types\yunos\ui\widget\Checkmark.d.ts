import EventEmitter = require("../../core/EventEmitter");
import ImageView = require("../view/ImageView");
import CompoundButton = require("./CompoundButton");
import { MultiState } from "../util/TypeHelper";
import VoiceEvent = require("../event/VoiceEvent");
interface IStyle {
    size: number;
    animationSequence: string[];
    animationOffSequence: string[];
    animationRate: number;
    multistateAui: MultiState;
    frameMultistateRadiobutton: MultiState;
    markMultistateRadiobutton: MultiState;
    markMultistateCheckbox: MultiState;
    offMultistateCheckbox: MultiState;
    onMultistateCheckbox: MultiState;
}
/**
 * <p>This is a widget intended to replace radiobutton and checkbox in most of the scenarios.</p>
 * <p>Supported scenarios include: <br>
 * 1. Toggle true or false for one item. <br>
 * 2. Single choice selection. <br>
 * 3. Multiple choice selection.</p>
 * <p>In the scenarios 2 & 3, Checkmark must be used along with the CheckmarkGroup.</p>
 * <p>In order to create a CheckmarkGroup, do the following: <br>
 * 1. Create a group by <code>var g = new Checkmark.CheckmarkGroup</code><br>
 * 2. Set the selection mode of the group. Ex: <code>g.mode = Checkmark.CheckmarkGroup.Multiple</code><br>
 * 3. Assign the <code>group</code> property of all the checkmarks that are in a group to the newly created group.</p>
 * @example
 * // normal
 * const toggleDesc = this.createDescription("Toggle");
 * const toggleBar = this.createBar("选项");
 * toggleBar.on("tap", () => toggleBar.checkmark.toggle());
 *
 * @example
 * // single choice
 * const singleDesc = this.createDescription("单选");
 * const singleBars = [
         this.createBar("单选选项 A", "A", Checkmark.StyleName.RadioButton),
         this.createBar("单选选项 B", "B", Checkmark.StyleName.RadioButton),
         this.createBar("单选选项 C", "C", Checkmark.StyleName.RadioButton)
   ];
 * const singleGroup = new CheckmarkGroup();
 * singleGroup.mode = CheckmarkGroup.Mode.Single;
 * for (let bar of singleBars) {
      bar.checkmark.group = singleGroup;
      bar.on("tap", () => bar.checkmark.checked = true);
   }
 * singleGroup.on("groupcheckedchange", (id, checked) => {});
 *
 * @example
 * // multiple choice
 * const multipleDesc = this.createDescription("多选");
 * const multipleBars = [
         this.createBar("多选选项 A", "A", Checkmark.StyleName.CheckBox),
         this.createBar("多选选项 B", "B", Checkmark.StyleName.CheckBox),
         this.createBar("多选选项 C", "C", Checkmark.StyleName.CheckBox)
    ];
 * const multipleGroup = new CheckmarkGroup();
 * multipleGroup.mode = CheckmarkGroup.Mode.Multiple;
 * for (let bar of multipleBars) {
         bar.checkmark.group = multipleGroup;
         bar.on("tap", () => bar.checkmark.toggle());
    }
 * multipleGroup.on("groupcheckedchange", (id, checked) => {});
 *
 * @extends yunos.ui.widget.CompoundButton
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class Checkmark extends CompoundButton {
    private _isAutoTheme;
    private _defaultWidth;
    private _defaultHeight;
    private _defaultContainerSize;
    private _checkMarkOn;
    private _checkFrame;
    private _checkMark;
    private _frameMultistateRadiobtn;
    private _offMultistateCheckbox;
    private _markMultistateCheckbox;
    private _multistateAui;
    private _animationSequence;
    private _animationOffSequence;
    private _animRate;
    private _markMultistateRadiobtn;
    private _onMultistateCheckbox;
    private _selfGroup;
    private _selfGroupStateChangeListener;
    private _autoCommand;
    private _voiceHandler;
    /**
     * <p>Create a checkmark.</p>
     * <p>Available constructor options:<br>
     * - checked<br>
     * - checkColor</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroys this checkmark.</p>
     * @public
     * @since 1
     * @override
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>Defines adaptive enabled state of this view</p>
     * @return {boolean}
     * @override
     * @protected
     * @since 3
     */
    protected adaptiveEnabled(): boolean;
    /**
     * <p>return the layout path that will be load</p>
     * @return {string} the layout path, if your view name is YOURVIEW, default path is "YOURVIEW/YOURVIEW.xml"
     * @override
     * @protected
     * @since 3
     */
    protected adaptiveLayoutFilePath(): "CheckmarkAuto/CheckmarkAuto.xml" | "Checkmark/Checkmark.xml";
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.Checkmark#defaultStyleName
     * @type {string}
     * @default "Checkmark"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: "Checkmark" | "CheckmarkRadioButton" | "CheckmarkCheckBox";
    /**
     * <p>Width of the checkmark. Read-only.</p>
     * @name yunos.ui.widget.Checkmark#width
     * @type {number}
     * @public
     * @override
     * @readonly
     * @since 1
     */
    public width: number;
    /**
     * <p>Height of the checkmark. Read-only.</p>
     * @name yunos.ui.widget.Checkmark#height
     * @type {number}
     * @public
     * @override
     * @readonly
     * @since 1
     */
    public height: number;
    /**
     * Defines enabled state of this view, true if this checkmark is enabled, false otherwise.
     * @name yunos.ui.widget.Checkmark#enabled
     * @type {boolean}
     * @default true
     * @fires yunos.ui.widget.Checkmark#propertychange
     * @throws {TypeError} If type of parameter is not boolean.
     * @override
     * @public
     * @since 2
     *
     */
    public enabled: boolean;
    /**
     * <p>The id of the checkmark.</p>
     * <p>This is required in the case of listview.</p>
     * @name yunos.ui.widget.Checkmark#id
     * @type {string}
     * @public
     * @override
     * @since 1
     */
    public id: string;
    /**
     * The plain-text content that this text view is to display.
     * @name yunos.ui.view.TextView#text
     * @type {string}
     * @fires yunos.ui.view.TextView#textchange
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @override
     * @since 6
     */
    public text: string;
    /**
     * <p>voiceViewType.</p>
     * @override
     * @protected
     * @since 6
     */
    protected readonly voiceViewType: string;
    /**
     * <p>Defines voiceBinding of the Checkmark, if not empty to bind one view text.<br/>
     * The attributes currently supports controls bound to text component(Button,TextView and so on),After being bound, <br/>
     * the bound control cannot be registered and responded to if it has voice events.<br/>
     * In the following example, if voiceBinding is used, it responds to the events of the bound View.</p>
     * @example
     * //Use in code
     * //The bound component
     * var textView = new TextView();
     * textView.id = "textViewId";
     * textView.text = "Sound";
     * this.voiceBinding = textView.id;
     *
     * @example
     * //Use in xml
     * //The bound component
     * <TextView id="textViewId" text="Sound"></TextView>
     * <Checkmark id="checkmark" voiceBinding="textViewId"/>
     * @name yunos.ui.view.Checkmark#voiceBinding
     * @type {string}
     * @override
     * @public
     * @since 6
     */
    public voiceBinding: string;
    private _voiceEventHandler(e: VoiceEvent): void;
    private _bindVoiceEvent(handler: (...args: Object[]) => void): void;
    private _unbindVoiceEvent(handler: (...args: Object[]) => void): void;
    /**
     * <p>Determine the state of the checkmark. True to checked, false to uncheck.</p>
     * @name yunos.ui.widget.Checkmark#checked
     * @type {boolean}
     * @throws {TypeError} If this value is not boolean.
     * @public
     * @since 1
     */
    public checked: boolean;
    /**
     * <p>Sets the checked of Checkmark.</p>
     * @param {boolean} checked - Set the checked of Checkmark, true to checke the Checkmark, false to uncheck it.
     * @param {boolean} animated - True need animation to run, false need not animation.
     * @throws {TypeError} If type of checked is not boolean.
     * @throws {TypeError} If type of animated is not boolean.
     * @public
     * @since 2
     */
    public setChecked(checked: boolean, animated: boolean): void;
    /**
     * The group that this Checkmark belongs to. Defaults to null.
     * @name yunos.ui.widget.Checkmark#group
     * @type {yunos.ui.widget.Checkmark.CheckmarkGroup}
     * @throws {TypeError} If this value is not a CheckmarkGroup object or null.
     * @public
     * @since 1
     */
    public group: Object;
    /**
     * <p>Get the animation view.</p>
     * @return {yunos.ui.view.ImageView}
     * @protected
     * @override
     * @since 3
     */
    protected getAnimationView(): ImageView;
    /**
     * <p>Get the animation sequence of this view.</p>
     * @return {string[]} Image src
     * @protected
     * @override
     * @since 3
     */
    protected getAnimationSequence(): string[];
    /**
     * <p>Get the animation rate of playing animation.</p>
     * @return {number} animationRate
     * @protected
     * @override
     * @since 3
     */
    protected getFrameRate(): number;
    /**
     * <p>The animation start.</p>
     * @protected
     * @override
     * @since 3
     */
    protected onAnimationStart(): void;
    /**
     * <p>The animation complete.</p>
     * @protected
     * @override
     * @since 3
     */
    protected onAnimationComplete(): void;
    private updateMultistate(): void;
    /**
     * Toggle the state of the checkmark.
     * @public
     * @since 1
     */
    public toggle(): void;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    private init(): void;
    private destroyGroup(): void;
    /**
     * <p>Enum for style of the checkmark.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     */
    public static readonly StyleName: {
        /**
         * <p>Default style of Checkmark.</p>
         * @public
         * @since 3
         */
        Default: int;
        /**
         * <p>RadioButton style of Checkmark.</p>
         * @public
         * @since 3
         */
        RadioButton: int;
        /**
         * <p>CheckBox style of Checkmark.</p>
         * @public
         * @since 3
         */
        CheckBox: int;
    };
}
declare namespace Checkmark {
    /**
     * <p>Used to group checkmarks so that they follow the same rule(mode), either single choice or multiple choices are allowed.</p>
     * <p>CheckmarkGroup only groups the checkmarks functionally, it does not impose any constraint on their layouts.</p>
     * <p>The group fires the event "groupcheckedchange" each time the checked state of one of the checkmark in the group changes.</p>
     * @extends yunos.core.EventEmitter
     * @memberof yunos.ui.widget.Checkmark
     * @public
     * @since 1
     */
    class CheckmarkGroup extends EventEmitter {
        private _mode: number;
        private _checkmarks: string[];
        private _checkedIds: string[];
        private _protectFromCheckedChange: boolean;
        /**
         * <p>Create a CheckmarkGroup.</p>
         * @public
         * @since 1
         */
        public constructor();
        /**
         * <p>Destructor that destroy the CheckmarkGroup.</p>
         * @public
         * @since 1
         */
        public destroy(): void;
        /**
         * <p>Mode of this CheckmarkGroup, defines the behavior of this group.</p>
         * <p>If mode is changed when the group is not empty, all the checked state of the checkmarks in the group is set to false.</p>
         * @name yunos.ui.widget.Checkmark.CheckmarkGroup#mode
         * @type {yunos.ui.widget.Checkmark.CheckmarkGroup.Mode}
         * @throws {TypeError} If this value is not in CheckmarkGroup.Mode.
         * @public
         * @since 1
         */
        public mode: number;
        /**
         * <p>Stores ids of the checkmarks within this group.</p>
         * @name yunos.ui.widget.Checkmark.CheckmarkGroup#checkmarks
         * @type {String[]}
         * @readonly
         * @public
         * @since 1
         */
        public readonly checkmarks: string[];
        /**
         * <p>Stores the ids of the checked checkmarks within this group.</p>
         * @name yunos.ui.widget.Checkmark.CheckmarkGroup#checkedIds
         * @type {String[]}
         * @throws {TypeError} If this value is not an array of string.
         * @public
         * @since 1
         */
        public checkedIds: string[];
        /**
         * <p>Convenience method to know whether a checkmark is checked.</p>
         * @param {string} id - the id of the checkmark.
         * @returns {boolean}
         * @public
         * @since 1
         */
        public isChecked(id: string): boolean;
        private add(viewId: string): void;
        private remove(viewId: string): void;
        private setChecked(id: string, checked: boolean, animated: boolean): boolean;
        /**
         * <p>Enum for mode of the checkmark group.</p>
         * @enum {number}
         * @readonly
         * @public
         * @since 1
         */
        public static readonly Mode: {
            /**
             * <p>Only one checkmark in the group can be checked.</p>
             * @public
             * @since 1
             */
            Single: int;
            /**
             * <p>One or more checkmarks in the group can be checked.</p>
             * @public
             * @since 1
             */
            Multiple: int;
        };
    }
}
export = Checkmark;
