export interface LocationSatObj {
    svid: number;
    constellation: number;
    cn0: number;
    elevation: number;
    azimuth: number;
    eph: boolean;
    alm: boolean;
    fix: boolean;
}
export interface LocationObj {
    errorCode: number;
    time?: number;
    provider?: string;
    latitude?: number;
    longitude?: number;
    accuracy?: number;
    offsetlatitude?: number;
    offsetlongitude?: number;
    adCode?: string;
    cityCode?: string;
    country?: string;
    province?: string;
    city?: string;
    district?: string;
    road?: string;
    address?: string;
    poi?: string;
    area?: string;
    altitude?: number;
    speed?: number;
    bearing?: number;
    extras?: string;
}
export interface LocationRequestAddon {
    getInterval(): number;
    getFastestInterval(): number;
    setInterval(interval: number, fastestInterval?: number): void;
    getPriority(): number;
    setPriority(priority: number): void;
    getSmallestDisplacement(): number;
    setSmallestDisplacement(smallestDisplacement: number): void;
    getTimeout(): number;
    setTimeout(timeout: number): void;
    isNeedAddress(): boolean;
    setNeedAddress(isNeed: boolean): void;
}
export interface LocationAddon {
    LocationRequest(): LocationRequestAddon;
    requestLocationUpdates(request: LocationRequestAddon, callback: (location: Object) => void): boolean;
    removeLocationUpdates(callback: (location: Object) => void): void;
    getLastLocation(): Object;
    getLastLocationAsync(callback: (err: Error, info?: Object) => void): void;
    requestGeocode(address: string, callback: (err: Error, datas?: Object[]) => void): void;
    requestReverseGeocode(latitude: number, longitude: number, callback: (err: Error, datas?: Object[]) => void): void;
    addFence(domain: string, key: string, request: Object, pagelink: string, callback: (err: Error, fenceKey: string) => void): void;
    removeFence(domain: string, key: string, callback: (err: Error, fenceKey: string) => void): void;
    getCountry(callback: (err: Error, country?: Object) => void): void;
    addGnssStatusListener(callback: (statusevent: number, gnssstatus?: Object) => void): boolean;
    removeGnssStatusListener(callback: (statusevent: number, gnssstatus?: Object) => void): void;
    addNmeaListener(callback: (timestamp: number, nmea: string) => void): boolean;
    removeNmeaListener(callback: (timestamp: number, nmea: string) => void): void;
    sendExtraCommand(command: number): boolean;
}
