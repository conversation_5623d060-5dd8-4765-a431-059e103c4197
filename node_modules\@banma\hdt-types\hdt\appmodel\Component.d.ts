import CompositeView = require("yunos/ui/view/CompositeView");
import Model = require("yunos/appmodel/Model");
interface Events {
    [key: string]: {
        [key: string]: (...args: Object[]) => void;
    };
}
declare abstract class Component extends CompositeView {
    private _model;
    private _autoBindedEvents;
    abstract onCreate(options?: Object): void;
    abstract onDestroy(): void;
    constructor(...args: Object[]);
    readonly events: Events;
    loadLayout(model?: Model): void;
    model: Model;
    destroy(): void;
    private _bindEvents;
    private _unbindEvents;
}
export = Component;
