import ImageFilter = require("./ImageFilter");
/**
 * <p>The SepiaImageFilter can convert the input image to sepia. </p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class SepiaImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>The value of amount defines the proportion of the conversion.
     * A value of 1 is completely sepia. A value of 0 leaves the input unchanged.
     * Values between 0 and 1 are linear multipliers on the effect. </p>
     * @name yunos.graphics.filter.SepiaImageFilter#amount
     * @type {number}
     * @default 0
     * @public
     * @since 5
     */
    public amount: number;
}
export = SepiaImageFilter;
