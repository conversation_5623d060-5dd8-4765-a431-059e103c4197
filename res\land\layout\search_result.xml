<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_search"
    layout="{layout.search_result}"
    propertySetName="search_result">

    <NavigationBar id="id_nav"/>

    <TextView
        id="id_search_keyword"
        height="{config.SEARCH_BANNER_HEIGHT}"
        text="{string.SEARCH_KEYWORD}"
        propertySetName="extend/hdt/FontHeadline"/>

    <GridView
        id="id_local_grid"
        visibility="{enum.View.Visibility.None}"
        orientation="{enum.GridView.Orientation.Horizontal}"
        rows="{config.LOCAL_ROWS_NUM}"
        rowSpacing="{config.ITEM_SPACE}"
        columnSpacing="{config.ITEM_SPACE}"
        dividerHeight="{config.ITEM_SPACE}"
        scrollBarCustomized="true"
        horizontalFadingEdgeEnabled="true"
        focusable="false"/>

    <ScrollBar
        id="id_scrollbar"
        height="{config.SCROLL_BAR_SIZE}"
        autoHidden="true"/>

    <include
        id="id_empty"
        visibility="{enum.View.Visibility.None}"
        markup="empty"/>

    <LoadingPageBM
        id="id_loading_page"
        visibility="{enum.View.Visibility.None}"/>
</CompositeView>
