import Page = require("yunos/page/Page");
declare enum ScreenPosition {
    ScreenARight = 1,
    ScreenB = 2,
    ScreenC = 3
}
declare class BasePanelWindowPage extends Page {
    private _displayId;
    private _marginLeft;
    private _marginRight;
    private _marginTop;
    private _marginBottom;
    private resolutionWidths;
    private resolutionHeights;
    private _isCustomWindowBackground;
    private _isCustomAnimation;
    private __baseAnimations;
    readonly globalThemeReference: string;
    customPageAnimation: boolean;
    onCreate(): void;
    initPageHeader(): void;
    updateWindowBackground(): void;
    customWindowBackground: boolean;
    panelDisplayId: ScreenPosition;
    marginLeft: number;
    marginTop: number;
    marginRight: number;
    marginBottom: number;
    private updateWindowSize;
    protected getWindowConfig(): {
        opaque: boolean;
        type: int;
        left: int;
        top: int;
        width: number;
        height: number;
        requireAdjust: boolean;
    };
    private getScreenSize;
    destroy(recursive: boolean): void;
}
export = BasePanelWindowPage;
