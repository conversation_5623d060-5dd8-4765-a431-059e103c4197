import YObject = require("../../core/YObject");
import Bitmap = require("../Bitmap");
import Size = require("../Size");
import Color = require("../Color");
/**
 * <p>The Font class is used to load font and measure text contents.</p>
 *
 * @example
 *
 * const Font = require("yunos/graphics/text/Font");
 * var font = new Font("sens-serif", "18px", Font.Style.Italic, Font.Weight.Bold);
 *
 * var size = Font.measureText(font, "hello world!");
 * log.D("Font", "text size: ", size.width, " ", size.height);
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics.text
 * @public
 * @since 4
 *
 */
declare class Font extends YObject {
    private _fontFamily;
    private _fontSize;
    private _fontStyle;
    private _fontWeight;
    private _fontScale;
    /**
     * <p>Constructor that create a Font Object.</p>
     * @param {string} fontString - CSS style font string.
     * @throws {TypeError} If family or size is not string.
     * @public
     * @since 4
     *
     */
    /**
     * <p>Constructor that create a Font Object.</p>
     * @param {string} family - Family of font.
     * @param {string|number} size - Size of font.
     * @param {number} style - Style of font.
     * @param {number} weight - Weight of font.
     * @throws {TypeError} If family is not string or size is not string or number.
     * @public
     * @since 4
     *
     */
    public constructor(family: Object, size?: Object, style?: number, weight?: number);
    private readonly fontFamily: string;
    private readonly fontSize: number;
    private readonly fontStyle: number;
    private readonly fontWeight: number;
    private parseFontStr(value: string): void;
    private converToPx(value: string): number;
    /**
     * <p>Destroy the Font.</p>
     * @public
     * @since 4
     *
     */
    public destroy(): void;
    /**
     * <p>Create a clone of the Font.</p>
     * @return {yunos.graphics.text.Font} Return a clone of the Font object.
     * @override
     * @public
     * @since 4
     *
     */
    public clone(): Font;
    /**
     * @typedef {Object} yunos.graphics.text.Font~Metrics
     * @property {number} height The distance between two consecutive baselines.
     * @property {number} ascent The distance from baseline to the top of glyph.
     * @property {number} descent The distance from baseline to the bottom of glyph.
     * @property {number} leading The distance from the top of glyph to the top of boundingbox.
     * @property {number} unitsPerEM  The number of font units per EM square for this face.
     * @public
     * @since 4
     *
     */
    /**
     * <p>Get the metrics from Font.</p>
     * @return {yunos.graphics.text.Font~Metrics} Return the metrics of the font.
     * @public
     * @since 4
     *
     */
    public getMetrics(): {
        width: number;
        height?: number;
        lineCount?: number;
        ascent?: number;
        descent?: number;
    };
    /**
     * <p>Return CSS style string to description the font object.</p>
     * @return {string} Return a CSS style font description string.
     * @override
     * @public
     * @since 4
     *
     */
    public toString(): string;
    /**
     * <p>Measure text size using a font object.</p>
     * @param {yunos.graphics.text.Font} font - Font object.
     * @param {string} text - The text string of measured.
     * @param {number} [letterSpacing] - The spacing of letter.
     * @param {number} [wordSpacing] - The spacing of word.
     * @param {number} [layoutWidth] - The layout width limit.
     * @param {number} [wrapMode] - The line wrap mode.
     * @param {number} [lineSpacing] - The extra line spacing.
     * @return {yunos.graphics.Size} The size of text content.
     * @public
     * @since 4
     *
     */
    public static measureText(font: Font, text: string, letterSpacing?: number, wordSpacing?: number, layoutWidth?: number, wrapMode?: number, lineSpacing?: number): Size;
    /**
     * @typedef {Object} yunos.graphics.text.Font~GlyphInfo
     * @property {number} index The index of the glyph in the font file.
     * @property {number} code The UTF-16 code of the character.
     * @property {number} width The width of the glyph image's bounding box.
     * @property {number} height The height of the glyph image's bounding box.
     * @property {number} advanceX The advance width for the glyph.
     * @property {number} advanceY The advance height for the glyph.
     * @public
     * @since 4
     *
     */
    /**
     * <p>Load glyphs and return them which are stored in an array.</p>
     * @param {yunos.graphics.text.Font} font - Font object.
     * @param {string} text - The text string of loaded.
     * @return {yunos.graphics.text.Font~GlyphInfo[]} The GlyphInfo array.
     * @public
     * @since 4
     *
     */
    public static loadTextGlyphs(font: Font, text: string): Size;
    /**
     * <p>Generate a bitmap object from glyphs.</p>
     * @param {yunos.graphics.text.Font} font - Font object.
     * @param {yunos.graphics.text.Font~GlyphInfo[]} glyphs - The glyph info array.
     * @param {yunos.graphics.Color} [color] - The text color(default white).
     * @return {yunos.graphics.Bitmap} The Bitmap object generated.
     * @public
     * @since 4
     *
     */
    public static glyphsToBitmap(font: Font, glyphs: Array<object>, color?: Color): Bitmap;
    /**
     * <p>Enum for line wrap mode.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly WrapMode: {
        /**
         * Line wrap any where.
         * @public
         * @since 4
         *
         */
        WrapAnyWhere: int;
        /**
         * Line wrap by word.
         * @public
         * @since 4
         *
         */
        WrapWord: int;
    };
    /**
     * <p>Enum for font style.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly Style: {
        /**
         * Font style normal.
         * @public
         * @since 4
         *
         */
        Normal: int;
        /**
         * Font style italic.
         * @public
         * @since 4
         *
         */
        Italic: int;
    };
    /**
     * <p>Enum for font weight.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly Weight: {
        /**
         * Font weight light.
         * @public
         * @since 4
         *
         */
        Light: int;
        /**
         * Font weight normal.
         * @public
         * @since 4
         *
         */
        Normal: int;
        /**
         * Font weight demi bold.
         * @public
         * @since 4
         *
         */
        DemiBold: int;
        /**
         * Font weight bold.
         * @public
         * @since 4
         *
         */
        Bold: int;
        /**
         * Font weight black.
         * @public
         * @since 4
         *
         */
        Black: int;
    };
    private static readonly FontUnit;
}
export = Font;
