<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    width="{config.ONLINE_ITEM_WIDTH}"
    height="{config.ONLINE_ITEM_HEIGHT}"
    multiState="{config.ITEM_MULTISTATE}"
    layout="{layout.online_item}">

    <OnlineImageViewBM
        id="id_cover"
        width="{config.ONLINE_ITEM_WIDTH}"
        height="{config.ONLINE_ITEM_HEIGHT}"
        asynchronous="true"
        defaultSrc="{img(images/ic_preview_default.png)}"
        borderRadius="{config.BORDER_RADIUS}"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"/>

    <ImageView
        id="id_mask"
        src="{img(images/ic_online_item_mask.png)}"
        borderRadius="{config.BORDER_RADIUS}"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"/>

    <ImageView
        id="id_hot_icon"
        width="{config.ONLINE_ITEM_HOT_ICON_SIZE}"
        height="{config.ONLINE_ITEM_HOT_ICON_SIZE}"
        src="{img(images/ic_hot.png)}"
        scaleType="{enum.ImageView.ScaleType.Center}"/>

    <TextView
        id="id_hot_num"
        text=""
        width="{sdp(100)}"
        height="{sdp(28)}"
        propertySetName="extend/hdt/FontBody4"
        color="{theme.color.White_2}"/>

    <TextView
        id="id_size"
        text=""
        width="{sdp(64)}"
        height="{sdp(28)}"
        propertySetName="extend/hdt/FontBody4"
        color="{theme.color.White_2}"/>

    <TextView
        id="id_title"
        text=""
        width="{sdp(224)}"
        height="{sdp(36)}"
        propertySetName="extend/hdt/FontBody2"
        color="{theme.color.White_2}"
        elideMode="{enum.TextView.ElideMode.ElideRight}"/>

    <ImageView
        id="id_last_played"
        width="{config.ONLINE_ITEM_WIDTH}"
        height="{config.ONLINE_ITEM_HEIGHT}"
        src="{img(images/ic_online_item_focus.png)}"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"
        visibility="{enum.View.Visibility.None}"/>
</CompositeView>
