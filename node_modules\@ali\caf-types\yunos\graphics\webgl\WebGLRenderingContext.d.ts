import EventEmitter = require("../../core/EventEmitter");
import SurfaceView = require("../../ui/view/SurfaceView");
import ImageData = require("../ImageData");
import Bitmap = require("../Bitmap");
import Image = require("./Image");
/**
 * <p>The WebGLRenderingContext class provides the OpenGL ES2.0 redering context for drawing surface on the surfaceView.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.graphics.webgl
 * @public
 * @since 3
 *
 */
declare class WebGLRenderingContext extends EventEmitter {
    private native;
    private readonly _drawingBufferHeight;
    private readonly _drawingBufferWidth;
    private readonly _config;
    public DEPTH_BUFFER_BIT: number;
    public STENCIL_BUFFER_BIT: number;
    public COLOR_BUFFER_BIT: number;
    public POINTS: number;
    public LINES: number;
    public LINE_LOOP: number;
    public LINE_STRIP: number;
    public TRIANGLES: number;
    public TRIANGLE_STRIP: number;
    public TRIANGLE_FAN: number;
    public ZERO: number;
    public ONE: number;
    public SRC_COLOR: number;
    public ONE_MINUS_SRC_COLOR: number;
    public SRC_ALPHA: number;
    public ONE_MINUS_SRC_ALPHA: number;
    public DST_ALPHA: number;
    public ONE_MINUS_DST_ALPHA: number;
    public DST_COLOR: number;
    public ONE_MINUS_DST_COLOR: number;
    public SRC_ALPHA_SATURATE: number;
    public FUNC_ADD: number;
    public BLEND_EQUATION: number;
    public BLEND_EQUATION_RGB: number;
    public BLEND_EQUATION_ALPHA: number;
    public FUNC_SUBTRACT: number;
    public FUNC_REVERSE_SUBTRACT: number;
    public BLEND_DST_RGB: number;
    public BLEND_SRC_RGB: number;
    public BLEND_DST_ALPHA: number;
    public BLEND_SRC_ALPHA: number;
    public CONSTANT_COLOR: number;
    public ONE_MINUS_CONSTANT_COLOR: number;
    public CONSTANT_ALPHA: number;
    public ONE_MINUS_CONSTANT_ALPHA: number;
    public BLEND_COLOR: number;
    public ARRAY_BUFFER: number;
    public ELEMENT_ARRAY_BUFFER: number;
    public ARRAY_BUFFER_BINDING: number;
    public ELEMENT_ARRAY_BUFFER_BINDING: number;
    public STREAM_DRAW: number;
    public STATIC_DRAW: number;
    public DYNAMIC_DRAW: number;
    public BUFFER_SIZE: number;
    public BUFFER_USAGE: number;
    public CURRENT_VERTEX_ATTRIB: number;
    public FRONT: number;
    public BACK: number;
    public FRONT_AND_BACK: number;
    public CULL_FACE: number;
    public BLEND: number;
    public DITHER: number;
    public STENCIL_TEST: number;
    public DEPTH_TEST: number;
    public SCISSOR_TEST: number;
    public POLYGON_OFFSET_FILL: number;
    public SAMPLE_ALPHA_TO_COVERAGE: number;
    public SAMPLE_COVERAGE: number;
    public NO_ERROR: number;
    public INVALID_ENUM: number;
    public INVALID_VALUE: number;
    public INVALID_OPERATION: number;
    public OUT_OF_MEMORY: number;
    public CW: number;
    public CCW: number;
    public LINE_WIDTH: number;
    public ALIASED_POINT_SIZE_RANGE: number;
    public ALIASED_LINE_WIDTH_RANGE: number;
    public CULL_FACE_MODE: number;
    public FRONT_FACE: number;
    public DEPTH_RANGE: number;
    public DEPTH_WRITEMASK: number;
    public DEPTH_CLEAR_VALUE: number;
    public DEPTH_FUNC: number;
    public STENCIL_CLEAR_VALUE: number;
    public STENCIL_FUNC: number;
    public STENCIL_FAIL: number;
    public STENCIL_PASS_DEPTH_FAIL: number;
    public STENCIL_PASS_DEPTH_PASS: number;
    public STENCIL_REF: number;
    public STENCIL_VALUE_MASK: number;
    public STENCIL_WRITEMASK: number;
    public STENCIL_BACK_FUNC: number;
    public STENCIL_BACK_FAIL: number;
    public STENCIL_BACK_PASS_DEPTH_FAIL: number;
    public STENCIL_BACK_PASS_DEPTH_PASS: number;
    public STENCIL_BACK_REF: number;
    public STENCIL_BACK_VALUE_MASK: number;
    public STENCIL_BACK_WRITEMASK: number;
    public VIEWPORT: number;
    public SCISSOR_BOX: number;
    public COLOR_CLEAR_VALUE: number;
    public COLOR_WRITEMASK: number;
    public UNPACK_ALIGNMENT: number;
    public PACK_ALIGNMENT: number;
    public MAX_TEXTURE_SIZE: number;
    public MAX_VIEWPORT_DIMS: number;
    public SUBPIXEL_BITS: number;
    public RED_BITS: number;
    public GREEN_BITS: number;
    public BLUE_BITS: number;
    public ALPHA_BITS: number;
    public DEPTH_BITS: number;
    public STENCIL_BITS: number;
    public POLYGON_OFFSET_UNITS: number;
    public POLYGON_OFFSET_FACTOR: number;
    public TEXTURE_BINDING_2D: number;
    public TEXTURE_BINDING_EXTERNAL_OES: number;
    public SAMPLE_BUFFERS: number;
    public SAMPLES: number;
    public SAMPLE_COVERAGE_VALUE: number;
    public SAMPLE_COVERAGE_INVERT: number;
    public COMPRESSED_TEXTURE_FORMATS: number;
    public DONT_CARE: number;
    public FASTEST: number;
    public NICEST: number;
    public GENERATE_MIPMAP_HINT: number;
    public BYTE: number;
    public UNSIGNED_BYTE: number;
    public SHORT: number;
    public UNSIGNED_SHORT: number;
    public INT: number;
    public UNSIGNED_INT: number;
    public FLOAT: number;
    public DEPTH_COMPONENT: number;
    public ALPHA: number;
    public RGB: number;
    public RGBA: number;
    public LUMINANCE: number;
    public LUMINANCE_ALPHA: number;
    public UNSIGNED_SHORT_4_4_4_4: number;
    public UNSIGNED_SHORT_5_5_5_1: number;
    public UNSIGNED_SHORT_5_6_5: number;
    public FRAGMENT_SHADER: number;
    public VERTEX_SHADER: number;
    public MAX_VERTEX_ATTRIBS: number;
    public MAX_VERTEX_UNIFORM_VECTORS: number;
    public MAX_VARYING_VECTORS: number;
    public MAX_COMBINED_TEXTURE_IMAGE_UNITS: number;
    public MAX_VERTEX_TEXTURE_IMAGE_UNITS: number;
    public MAX_TEXTURE_IMAGE_UNITS: number;
    public MAX_FRAGMENT_UNIFORM_VECTORS: number;
    public SHADER_TYPE: number;
    public DELETE_STATUS: number;
    public LINK_STATUS: number;
    public VALIDATE_STATUS: number;
    public ATTACHED_SHADERS: number;
    public ACTIVE_UNIFORMS: number;
    public ACTIVE_ATTRIBUTES: number;
    public SHADING_LANGUAGE_VERSION: number;
    public CURRENT_PROGRAM: number;
    public NEVER: number;
    public LESS: number;
    public EQUAL: number;
    public LEQUAL: number;
    public GREATER: number;
    public NOTEQUAL: number;
    public GEQUAL: number;
    public ALWAYS: number;
    public KEEP: number;
    public REPLACE: number;
    public INCR: number;
    public DECR: number;
    public INVERT: number;
    public INCR_WRAP: number;
    public DECR_WRAP: number;
    public VENDOR: number;
    public RENDERER: number;
    public VERSION: number;
    public NEAREST: number;
    public LINEAR: number;
    public NEAREST_MIPMAP_NEAREST: number;
    public LINEAR_MIPMAP_NEAREST: number;
    public NEAREST_MIPMAP_LINEAR: number;
    public LINEAR_MIPMAP_LINEAR: number;
    public TEXTURE_MAG_FILTER: number;
    public TEXTURE_MIN_FILTER: number;
    public TEXTURE_WRAP_S: number;
    public TEXTURE_WRAP_T: number;
    public TEXTURE_2D: number;
    public TEXTURE: number;
    public TEXTURE_EXTERNAL_OES: number;
    public TEXTURE_CUBE_MAP: number;
    public TEXTURE_BINDING_CUBE_MAP: number;
    public TEXTURE_CUBE_MAP_POSITIVE_X: number;
    public TEXTURE_CUBE_MAP_NEGATIVE_X: number;
    public TEXTURE_CUBE_MAP_POSITIVE_Y: number;
    public TEXTURE_CUBE_MAP_NEGATIVE_Y: number;
    public TEXTURE_CUBE_MAP_POSITIVE_Z: number;
    public TEXTURE_CUBE_MAP_NEGATIVE_Z: number;
    public MAX_CUBE_MAP_TEXTURE_SIZE: number;
    public TEXTURE0: number;
    public TEXTURE1: number;
    public TEXTURE2: number;
    public TEXTURE3: number;
    public TEXTURE4: number;
    public TEXTURE5: number;
    public TEXTURE6: number;
    public TEXTURE7: number;
    public TEXTURE8: number;
    public TEXTURE9: number;
    public TEXTURE10: number;
    public TEXTURE11: number;
    public TEXTURE12: number;
    public TEXTURE13: number;
    public TEXTURE14: number;
    public TEXTURE15: number;
    public TEXTURE16: number;
    public TEXTURE17: number;
    public TEXTURE18: number;
    public TEXTURE19: number;
    public TEXTURE20: number;
    public TEXTURE21: number;
    public TEXTURE22: number;
    public TEXTURE23: number;
    public TEXTURE24: number;
    public TEXTURE25: number;
    public TEXTURE26: number;
    public TEXTURE27: number;
    public TEXTURE28: number;
    public TEXTURE29: number;
    public TEXTURE30: number;
    public TEXTURE31: number;
    public ACTIVE_TEXTURE: number;
    public REPEAT: number;
    public CLAMP_TO_EDGE: number;
    public MIRRORED_REPEAT: number;
    public FLOAT_VEC2: number;
    public FLOAT_VEC3: number;
    public FLOAT_VEC4: number;
    public INT_VEC2: number;
    public INT_VEC3: number;
    public INT_VEC4: number;
    public BOOL: number;
    public BOOL_VEC2: number;
    public BOOL_VEC3: number;
    public BOOL_VEC4: number;
    public FLOAT_MAT2: number;
    public FLOAT_MAT3: number;
    public FLOAT_MAT4: number;
    public SAMPLER_2D: number;
    public SAMPLER_CUBE: number;
    public VERTEX_ATTRIB_ARRAY_ENABLED: number;
    public VERTEX_ATTRIB_ARRAY_SIZE: number;
    public VERTEX_ATTRIB_ARRAY_STRIDE: number;
    public VERTEX_ATTRIB_ARRAY_TYPE: number;
    public VERTEX_ATTRIB_ARRAY_NORMALIZED: number;
    public VERTEX_ATTRIB_ARRAY_POINTER: number;
    public VERTEX_ATTRIB_ARRAY_BUFFER_BINDING: number;
    public IMPLEMENTATION_COLOR_READ_TYPE: number;
    public IMPLEMENTATION_COLOR_READ_FORMAT: number;
    public COMPILE_STATUS: number;
    public LOW_FLOAT: number;
    public MEDIUM_FLOAT: number;
    public HIGH_FLOAT: number;
    public LOW_INT: number;
    public MEDIUM_INT: number;
    public HIGH_INT: number;
    public FRAMEBUFFER: number;
    public RENDERBUFFER: number;
    public RGBA4: number;
    public RGB5_A1: number;
    public RGB565: number;
    public DEPTH_COMPONENT16: number;
    public STENCIL_INDEX8: number;
    public DEPTH_STENCIL: number;
    public RENDERBUFFER_WIDTH: number;
    public RENDERBUFFER_HEIGHT: number;
    public RENDERBUFFER_INTERNAL_FORMAT: number;
    public RENDERBUFFER_RED_SIZE: number;
    public RENDERBUFFER_GREEN_SIZE: number;
    public RENDERBUFFER_BLUE_SIZE: number;
    public RENDERBUFFER_ALPHA_SIZE: number;
    public RENDERBUFFER_DEPTH_SIZE: number;
    public RENDERBUFFER_STENCIL_SIZE: number;
    public FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE: number;
    public FRAMEBUFFER_ATTACHMENT_OBJECT_NAME: number;
    public FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL: number;
    public FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE: number;
    public COLOR_ATTACHMENT0: number;
    public DEPTH_ATTACHMENT: number;
    public STENCIL_ATTACHMENT: number;
    public DEPTH_STENCIL_ATTACHMENT: number;
    public NONE: number;
    public FRAMEBUFFER_COMPLETE: number;
    public FRAMEBUFFER_INCOMPLETE_ATTACHMENT: number;
    public FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT: number;
    public FRAMEBUFFER_INCOMPLETE_DIMENSIONS: number;
    public FRAMEBUFFER_UNSUPPORTED: number;
    public FRAMEBUFFER_BINDING: number;
    public RENDERBUFFER_BINDING: number;
    public MAX_RENDERBUFFER_SIZE: number;
    public INVALID_FRAMEBUFFER_OPERATION: number;
    public UNPACK_FLIP_Y_WEBGL: number;
    public UNPACK_PREMULTIPLY_ALPHA_WEBGL: number;
    public CONTEXT_LOST_WEBGL: number;
    public UNPACK_COLORSPACE_CONVERSION_WEBGL: number;
    public BROWSER_DEFAULT_WEBGL: number;
    public EGL_HEIGHT: number;
    public EGL_WIDTH: number;
    /**
     * <p>Creates a WebGLRenderingContext.</p>
     * @param {yunos.ui.view.SurfaceView}
     * @throws {TypeError} If the param is not instanceof SurfaceView.
     * @public
     * @since 3
     */
    /**
     * <p>Creates a WebGLRenderingContext.</p>
     * @param {yunos.ui.view.SurfaceView | number} surfaceView
     * @param {object | null} config
     * @param {string} type
     * @throws {TypeError} If the param is not instanceof SurfaceView.
     * @public
     * @since 4
     *
     */
    public constructor(surfaceView: SurfaceView | number, config?: object, type?: string);
    /**
     * <p>Destroy this webGLContext.</p>
     * @public
     * @since 3
     *
     */
    public destroy(): void;
    /**
     * <p>The read-only drawingBufferHeight property represents the actual height of the current drawing buffer.</p>
     * <p>It should match the height attribute of the WebGLView element associated with this context, but might differ if the  implementation is not able to provide the requesed height.</p>
     * @name yunos.graphics.webgl.WebGLRenderingContext#drawingBufferHeight
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly drawingBufferHeight: number;
    /**
     * <p>The readonly drawingBufferWidth property represents the actual width of the current drawing buffer.</p>
     * <p>It should match the width attribute of the WebGLView element associated with this context, but might differ if the implementation is not able to provide the requesed width.<p>
     * @name yunos.graphics.webgl.WebGLRenderingContext#drawingBufferWidth
     * @type {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly drawingBufferWidth: number;
    /**
     * <p>Scissor defines a rectangle, called the scissor box, in window coordinates. The first two arguments, x and y, specify the lower left corner of the box. width and height specify the width and height of the box.
     *
     * To enable and disable the scissor test, call enable and disable with argument SCISSOR_TEST. The test is initially disabled. While the test is enabled, only pixels that lie within the scissor box can be modified by drawing commands. Window coordinates have integer values at the shared corners of frame buffer pixels. scissor(0,0,1,1) allows modification of only the lower left pixel in the window, and scissor(0,0,0,0) doesn't allow modification of any pixels in the window.
     *
     * When the scissor test is disabled, it is as though the scissor box includes the entire window.
     *
     * </p>
     * @param {number} x - Specify the lower left corner of the scissor box. Initially (0, 0)
     * @param {number} y - Specify the lower left corner of the scissor box. Initially (0, 0)
     * @param {number} width - Specify the width and height of the scissor box. When a GL context is first attached to a window, width and height are set to the dimensions of that window.
     * @param {number} height - Specify the width and height of the scissor box. When a GL context is first attached to a window, width and height are set to the dimensions of that window.
     * @throws {INVALID_VALUE} If either width or height is a negative value;
     * @public
     * @since 3
     *
     */
    public scissor(x: number, y: number, width: number, height: number): void;
    /**
     * <p>viewport specifies the affine transformation of x and y from normalized device coordinates to window coordinates. Let x nd y nd be normalized device coordinates. Then the window coordinates x w y w are computed as follows:
     *
     * x w = x nd + 1 ⁢ width 2 + x
     * y w = y nd + 1 ⁢ height 2 + y
     * Viewport width and height are silently clamped to a range that depends on the implementation. To query this range, call getParameter with argument MAX_VIEWPORT_DIMS.</p>
     * @param {number} x - Specify the lower left corner of the viewport rectangle, in pixels. The initial value is (0,0).
     * @param {number} y - Specify the lower left corner of the viewport rectangle, in pixels. The initial value is (0,0).
     * @param {number} width - Specify the width and height of the viewport. When a GL context is first attached to a window, width and height are set to the dimensions of that window.
     * @param {number} height -Specify the width and height of the viewport. When a GL context is first attached to a window, width and height are set to the dimensions of that window.
     * @throws {INVALID_VALUE} If either width or height is a negative value;
     * @public
     * @since 3
     *
     */
    public viewport(x: number, y: number, width: number, height: number): void;
    /**
     * <p>activeTexture selects which texture unit subsequent texture state calls will affect. The number of texture units an implementation supports is implementation dependent, but must be at least 8.</p>
     * @param {number} texture - Specifies which texture unit to make active. The number of texture units is implementation dependent, but must be at least 8. texture must be one of TEXTUREi, where i ranges from 0 to (MAX_COMBINED_TEXTURE_IMAGE_UNITS - 1). The initial value is TEXTURE0.
     * @throws {INVALID_ENUM} If texture is not one of gl.TEXTUREI, where I is within the range from 0 to gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS - 1;
     * @public
     * @since 3
     *
     */
    public activeTexture(texture: number): void;
    /**
     * <p>The BLEND_COLOR may be used to calculate the source and destination blending factors. The color components are clamped to the range 0 1 before being stored. See blendFunc for a complete description of the blending operations. Initially the BLEND_COLOR is set to (0, 0, 0, 0).</p>
     * @param {number} red - specify the components of BLEND_COLOR
     * @param {number} green - specify the components of BLEND_COLOR
     * @param {number} blue - specify the components of BLEND_COLOR
     * @param {number} alpha - specify the components of BLEND_COLOR
     * @public
     * @since 3
     *
     */
    public blendColor(red: number, green: number, blue: number, alpha: number): void;
    /**
     * <p>The blend equations determine how a new pixel (the ''source'' color) is combined with a pixel already in the framebuffer (the ''destination'' color). This function sets both the RGB blend equation and the alpha blend equation to a single equation.
     *
     * These equations use the source and destination blend factors specified by either blendFunc or blendFuncSeparate. See blendFunc or blendFuncSeparate for a description of the various blend factors.
     *
     * In the equations that follow, source and destination color components are referred to as R s G s B s A s and R d G d B d A d , respectively. The result color is referred to as R r G r B r A r . The source and destination blend factors are denoted s R s G s B s A and d R d G d B d A , respectively. For these equations all color components are understood to have values in the range 0 1 .
     *
     * Mode    RGB Components    Alpha Component
     * FUNC_ADD
     * Rr = R s ⁢ s R + R d ⁢ d R
     * Gr = G s ⁢ s G + G d ⁢ d G
     * Br = B s ⁢ s B + B d ⁢ d B
     * Ar = A s ⁢ s A + A d ⁢ d A
     * FUNC_SUBTRACT
     * Rr = R s ⁢ s R - R d ⁢ d R
     * Gr = G s ⁢ s G - G d ⁢ d G
     * Br = B s ⁢ s B - B d ⁢ d B
     * Ar = A s ⁢ s A - A d ⁢ d A
     * FUNC_REVERSE_SUBTRACT
     * Rr = R d ⁢ d R - R s ⁢ s R
     * Gr = G d ⁢ d G - G s ⁢ s G
     * Br = B d ⁢ d B - B s ⁢ s B
     * Ar = A d ⁢ d A - A s ⁢ s A
     * The results of these equations are clamped to the range 0 1 .
     *
     * The FUNC_ADD equation is useful for antialiasing and transparency, among other things.
     *
     * Initially, both the RGB blend equation and the alpha blend equation are set to FUNC_ADD.</p>
     * @param {number} mode - specifies how source and destination colors are combined. It must be FUNC_ADD, FUNC_SUBTRACT, or FUNC_REVERSE_SUBTRACT.
     * @throws {Error} If mode is not one of the three possible values;
     * @public
     * @since 3
     *
     */
    public blendEquation(mode: number): void;
    /**
     * <p>The blend equations determines how a new pixel (the ''source'' color) is combined with a pixel already in the framebuffer (the ''destination'' color). This function specifies one blend equation for the RGB-color components and one blend equation for the alpha component.
     *
     * The blend equations use the source and destination blend factors specified by either blendFunc or blendFuncSeparate. See blendFunc or blendFuncSeparate for a description of the various blend factors.
     *
     * In the equations that follow, source and destination color components are referred to as R s G s B s A s and R d G d B d A d , respectively. The result color is referred to as R r G r B r A r . The source and destination blend factors are denoted s R s G s B s A and d R d G d B d A , respectively. For these equations all color components are understood to have values in the range 0 1 .
     *
     * Mode    RGB Components    Alpha Component
     * FUNC_ADD
     * Rr = R s ⁢ s R + R d ⁢ d R
     * Gr = G s ⁢ s G + G d ⁢ d G
     * Br = B s ⁢ s B + B d ⁢ d B
     * Ar = A s ⁢ s A + A d ⁢ d A
     * FUNC_SUBTRACT
     * Rr = R s ⁢ s R - R d ⁢ d R
     * Gr = G s ⁢ s G - G d ⁢ d G
     * Br = B s ⁢ s B - B d ⁢ d B
     * Ar = A s ⁢ s A - A d ⁢ d A
     * FUNC_REVERSE_SUBTRACT
     * Rr = R d ⁢ d R - R s ⁢ s R
     * Gr = G d ⁢ d G - G s ⁢ s G
     * Br = B d ⁢ d B - B s ⁢ s B
     * Ar = A d ⁢ d A - A s ⁢ s A
     * The results of these equations are clamped to the range 0 1 .
     *
     * The FUNC_ADD equation is useful for antialiasing and transparency, among other things.
     *
     * Initially, both the RGB blend equation and the alpha blend equation are set to FUNC_ADD.</p>
     * @param {number} modeRGB - specifies the RGB blend equation, how the red, green, and blue components of the source and destination colors are combined. It must be FUNC_ADD, FUNC_SUBTRACT, or FUNC_REVERSE_SUBTRACT.
     * @param {number} modeAlpha - specifies the alpha blend equation, how the alpha component of the source and destination colors are combined. It must be FUNC_ADD, FUNC_SUBTRACT, or FUNC_REVERSE_SUBTRACT.
     * @throws {Error} If modeRGB or modeAlpha is not one of the three possible values;
     * @public
     * @since 3
     *
     */
    public blendEquationSeparate(modeRGB: number, modeAlpha: number): void;
    /**
     * <p>Pixels can be drawn using a function that blends the incoming (source) RGBA values with the RGBA values that are already in the frame buffer (the destination values). Blending is initially disabled. Use enable and disable with argument BLEND to enable and disable blending.
     *
     * blendFunc defines the operation of blending when it is enabled. sfactor specifies which method is used to scale the source color components. dfactor specifies which method is used to scale the destination color components. The possible methods are described in the following table. Each method defines four scale factors, one each for red, green, blue, and alpha. In the table and in subsequent equations, source and destination color components are referred to as R s G s B s A s and R d G d B d A d . The color specified by blendColor is referred to as R c G c B c A c . They are understood to have integer values between 0 and k R k G k B k A , where
     *
     * k c = 2 m c - 1
     *
     * and m R m G m B m A is the number of red, green, blue, and alpha bitplanes.
     *
     * Source and destination scale factors are referred to as s R s G s B s A and d R d G d B d A . The scale factors described in the table, denoted f R f G f B f A , represent either source or destination factors. All scale factors have range 0 1 .
     *
     * Parameter    f R f G f B f A
     * ZERO    0 0 0 0
     * ONE    1 1 1 1
     * SRC_COLOR    R s k R G s k G B s k B A s k A
     * ONE_MINUS_SRC_COLOR    1 1 1 1 - R s k R G s k G B s k B A s k A
     * DST_COLOR    R d k R G d k G B d k B A d k A
     * ONE_MINUS_DST_COLOR    1 1 1 1 - R d k R G d k G B d k B A d k A
     * SRC_ALPHA    A s k A A s k A A s k A A s k A
     * ONE_MINUS_SRC_ALPHA    1 1 1 1 - A s k A A s k A A s k A A s k A
     * DST_ALPHA    A d k A A d k A A d k A A d k A
     * ONE_MINUS_DST_ALPHA    1 1 1 1 - A d k A A d k A A d k A A d k A
     * CONSTANT_COLOR    R c G c B c A c
     * ONE_MINUS_CONSTANT_COLOR    1 1 1 1 - R c G c B c A c
     * CONSTANT_ALPHA    A c A c A c A c
     * ONE_MINUS_CONSTANT_ALPHA    1 1 1 1 - A c A c A c A c
     * SRC_ALPHA_SATURATE    i i i 1
     * In the table,
     *
     * i  = min ⁡ A s k A - A d k A
     *
     * To determine the blended RGBA values of a pixel, the system uses one of the equations set by blendEquation or blendEquationSeparate.
     *
     * Blending arithmetic is not exactly specified, because blending operates with imprecise integer color values. However, a blend factor that should be equal to 1 is guaranteed not to modify its multiplicand, and a blend factor equal to 0 reduces its multiplicand to 0.</p>
     * @param {number} sfactor - Specifies how the red, green, blue, and alpha source blending factors are computed. The following symbolic constants are accepted: ZERO, ONE, SRC_COLOR, ONE_MINUS_SRC_COLOR, DST_COLOR, ONE_MINUS_DST_COLOR, SRC_ALPHA, ONE_MINUS_SRC_ALPHA, DST_ALPHA, ONE_MINUS_DST_ALPHA, CONSTANT_COLOR, ONE_MINUS_CONSTANT_COLOR, CONSTANT_ALPHA, ONE_MINUS_CONSTANT_ALPHA, and SRC_ALPHA_SATURATE. The initial value is ONE.
     * @param {number} dfactor - Specifies how the red, green, blue, and alpha destination blending factors are computed. The following symbolic constants are accepted: ZERO, ONE, SRC_COLOR, ONE_MINUS_SRC_COLOR, DST_COLOR, ONE_MINUS_DST_COLOR, SRC_ALPHA, ONE_MINUS_SRC_ALPHA, DST_ALPHA, ONE_MINUS_DST_ALPHA. CONSTANT_COLOR, ONE_MINUS_CONSTANT_COLOR, CONSTANT_ALPHA, and ONE_MINUS_CONSTANT_ALPHA. The initial value is ZERO.
     * @throws {Error} If sfactor or dfactor is not one of the listed possible values;
     * @throws {Error} If a constant color and a constant alpha value are used together as source and destination factors
     * @public
     * @since 3
     *
     */
    public blendFunc(sfactor: number, dfactor: number): void;
    /**
     * <p>Pixels can be drawn using a function that blends the incoming (source) RGBA values with the RGBA values that are already in the frame buffer (the destination values). Blending is initially disabled. Use enable and disable with argument BLEND to enable and disable blending.
     *
     * blendFuncSeparate defines the operation of blending when it is enabled. srcRGB specifies which method is used to scale the source RGB-color components. dstRGB specifies which method is used to scale the destination RGB-color components. Likewise, srcAlpha specifies which method is used to scale the source alpha color component, and dstAlpha specifies which method is used to scale the destination alpha component. The possible methods are described in the following table. Each method defines four scale factors, one each for red, green, blue, and alpha.
     *
     * In the table and in subsequent equations, source and destination color components are referred to as R s G s B s A s and R d G d B d A d . The color specified by blendColor is referred to as R c G c B c A c . They are understood to have integer values between 0 and k R k G k B k A , where
     *
     * k c = 2 m c - 1
     *
     * and m R m G m B m A is the number of red, green, blue, and alpha bitplanes.
     *
     * Source and destination scale factors are referred to as s R s G s B s A and d R d G d B d A . All scale factors have range 0 1 .
     *
     * Parameter    RGB Factor    Alpha Factor
     * ZERO    0 0 0    0
     * ONE    1 1 1    1
     * SRC_COLOR    R s k R G s k G B s k B    A s k A
     * ONE_MINUS_SRC_COLOR    1 1 1 1 - R s k R G s k G B s k B    1 - A s k A
     * DST_COLOR    R d k R G d k G B d k B    A d k A
     * ONE_MINUS_DST_COLOR    1 1 1 - R d k R G d k G B d k B    1 - A d k A
     * SRC_ALPHA    A s k A A s k A A s k A    A s k A
     * ONE_MINUS_SRC_ALPHA    1 1 1 - A s k A A s k A A s k A    1 - A s k A
     * DST_ALPHA    A d k A A d k A A d k A    A d k A
     * ONE_MINUS_DST_ALPHA    1 1 1 - A d k A A d k A A d k A    1 - A d k A
     * CONSTANT_COLOR    R c G c B c    A c
     * ONE_MINUS_CONSTANT_COLOR    1 1 1 - R c G c B c    1 - A c
     * CONSTANT_ALPHA    A c A c A c    A c
     * ONE_MINUS_CONSTANT_ALPHA    1 1 1 - A c A c A c    1 - A c
     * SRC_ALPHA_SATURATE    i i i    1
     * In the table,
     *
     * i = min ⁡ A s 1 - A d
     *
     * To determine the blended RGBA values of a pixel, the system uses one of the equations set by blendEquation or blendEquationSeparate.
     *
     * Despite the apparent precision of the above equations, blending arithmetic is not exactly specified, because blending operates with imprecise integer color values. However, a blend factor that should be equal to 1 is guaranteed not to modify its multiplicand, and a blend factor equal to 0 reduces its multiplicand to 0.</p>
     * @param {number} srcRGB - Specifies how the red, green, and blue blending factors are computed. The following symbolic constants are accepted: ZERO, ONE, SRC_COLOR, ONE_MINUS_SRC_COLOR, DST_COLOR, ONE_MINUS_DST_COLOR, SRC_ALPHA, ONE_MINUS_SRC_ALPHA, DST_ALPHA, ONE_MINUS_DST_ALPHA, CONSTANT_COLOR, ONE_MINUS_CONSTANT_COLOR, CONSTANT_ALPHA, ONE_MINUS_CONSTANT_ALPHA, and SRC_ALPHA_SATURATE. The initial value is ONE.
     * @param {number} dstRGB - Specifies how the red, green, and blue destination blending factors are computed. The following symbolic constants are accepted: ZERO, ONE, SRC_COLOR, ONE_MINUS_SRC_COLOR, DST_COLOR, ONE_MINUS_DST_COLOR, SRC_ALPHA, ONE_MINUS_SRC_ALPHA, DST_ALPHA, ONE_MINUS_DST_ALPHA. CONSTANT_COLOR, ONE_MINUS_CONSTANT_COLOR, CONSTANT_ALPHA, and ONE_MINUS_CONSTANT_ALPHA. The initial value is ZERO.
     * @param {number} srcAlpha - Specified how the alpha source blending factor is computed. The same symbolic constants are accepted as for srcRGB. The initial value is ONE.
     * @param {number} dstAlpha - Specified how the alpha destination blending factor is computed. The same symbolic constants are accepted as for dstRGB. The initial value is ZERO.
     * @throws {Error} If srcRGB, dstRGB, srcAlpha, or dstAlpha is not one of the listed possible values;
     * @throws {Error} If a constant color and a constant alpha value are used together as source (srcRGB) and destination (dstRGB) factors
     * @public
     * @since 3
     *
     */
    public blendFuncSeparate(srcRGB: number, dstRGB: number, srcAlpha: number, dstAlpha: number): void;
    /**
     * <p>clearColor specifies the red, green, blue, and alpha values used by clear to clear the color buffers. Values specified by clearColor are clamped to the range 0 1 .</p>
     * @param {number} red - Specify the red, green, blue, and alpha values used when the color buffers are cleared. The initial values are all 0.
     * @param {number} green - Specify the red, green, blue, and alpha values used when the color buffers are cleared. The initial values are all 0.
     * @param {number} blue - Specify the red, green, blue, and alpha values used when the color buffers are cleared. The initial values are all 0.
     * @param {number} alpha - Specify the red, green, blue, and alpha values used when the color buffers are cleared. The initial values are all 0.
     * @public
     * @since 3
     *
     */
    public clearColor(red: number, green: number, blue: number, alpha: number): void;
    /**
     * <p>clearDepth specifies the depth value used by clear to clear the depth buffer. Values specified by clearDepthf are clamped to the range 0 1 .</p>
     * @param {number} depth - Specifies the depth value used when the depth buffer is cleared. The initial value is 1.
     * @public
     * @since 3
     *
     */
    public clearDepth(depth: number): void;
    /**
     * <p>clearStencil specifies the index used by clear to clear the stencil buffer. s is masked with 2 m - 1 , where m is the number of bits in the stencil buffer.</p>
     * @param {number} s - Specifies the index used when the stencil buffer is cleared. The initial value is 0.
     * @public
     * @since 3
     *
     */
    public clearStencil(s: number): void;
    /**
     * <p>colorMask specifies whether the individual color components in the frame buffer can or cannot be written. If red is FALSE, for example, no change is made to the red component of any pixel in any of the color buffers, regardless of the drawing operation attempted.
     *
     * Changes to individual bits of components cannot be controlled. Rather, changes are either enabled or disabled for entire color components.</p>
     * @param {number} red - Specify whether red, green, blue, and alpha can or cannot be written into the frame buffer. The initial values are all TRUE, indicating that the color components can be written.
     * @param {number} green - Specify whether red, green, blue, and alpha can or cannot be written into the frame buffer. The initial values are all TRUE, indicating that the color components can be written.
     * @param {number} blue - Specify whether red, green, blue, and alpha can or cannot be written into the frame buffer. The initial values are all TRUE, indicating that the color components can be written.
     * @param {number} alpha - Specify whether red, green, blue, and alpha can or cannot be written into the frame buffer. The initial values are all TRUE, indicating that the color components can be written.
     * @public
     * @since 3
     *
     */
    public colorMask(red: number, green: number, blue: number, alpha: number): void;
    /**
     * <p>cullFace specifies whether front- or back-facing polygons are culled (as specified by mode) when polygon culling is enabled. Polygon culling is initially disabled. To enable and disable polygon culling, call the enable and disable commands with the argument CULL_FACE.
     *
     * frontFace specifies which of the clockwise and counterclockwise polygons are front-facing and back-facing. See frontFace.</p>
     * @param {number} mode - Specifies whether front- or back-facing polygons are candidates for culling. Symbolic constants FRONT, BACK, and FRONT_AND_BACK are accepted. The initial value is BACK.
     * @public
     * @since 3
     *
     */
    public cullFace(mode: number): void;
    /**
     * <p>depthFunc specifies the function used to compare each incoming pixel depth value with the depth value present in the depth buffer. The comparison is performed only if depth testing is enabled. (See enable and disable of DEPTH_TEST.)
     *
     * func specifies the conditions under which the pixel will be drawn. The comparison functions are as follows:
     *
     * NEVER
     * Never passes.
     *
     * LESS
     * Passes if the incoming depth value is less than the stored depth value.
     *
     * EQUAL
     * Passes if the incoming depth value is equal to the stored depth value.
     *
     * LEQUAL
     * Passes if the incoming depth value is less than or equal to the stored depth value.
     *
     * GREATER
     * Passes if the incoming depth value is greater than the stored depth value.
     *
     * NOTEQUAL
     * Passes if the incoming depth value is not equal to the stored depth value.
     *
     * GEQUAL
     * Passes if the incoming depth value is greater than or equal to the stored depth value.
     *
     * ALWAYS
     * Always passes.
     *
     * The initial value of func is LESS. Initially, depth testing is disabled. If depth testing is disabled or no depth buffer exists, it is as if the depth test always passes.</p>
     * @param {number} func - Specifies the depth comparison function. Symbolic constants NEVER, LESS, EQUAL, LEQUAL, GREATER, NOTEQUAL, GEQUAL, and ALWAYS are accepted. The initial value is LESS.
     * @public
     * @since 3
     *
     */
    public depthFunc(func: number): void;
    /**
     * <p>depthMask specifies whether the depth buffer is enabled for writing. If flag is FALSE, depth buffer writing is disabled. Otherwise, it is enabled. Initially, depth buffer writing is enabled.</p>
     * @param {boolean} flag - Specifies whether the depth buffer is enabled for writing. If flag is FALSE, depth buffer writing is disabled. Otherwise, it is enabled. Initially, depth buffer writing is enabled.
     * @public
     * @since 3
     *
     */
    public depthMask(flag: boolean): void;
    /**
     * <p>After clipping and division by w, depth coordinates range from -1 to 1, corresponding to the near and far clipping planes. depthRangef specifies a linear mapping of the normalized depth coordinates in this range to window depth coordinates. Regardless of the actual depth buffer implementation, window coordinate depth values are treated as though they range from 0 through 1 (like color components). Thus, the values accepted by depthRangef are both clamped to this range before they are accepted.
     *
     * The setting of (0,1) maps the near plane to 0 and the far plane to 1. With this mapping, the depth buffer range is fully utilized.</p>
     * @param {number} zNear - Specifies the mapping of the near clipping plane to window coordinates. The initial value is 0.
     * @param {number} zFar - Specifies the mapping of the far clipping plane to window coordinates. The initial value is 1.
     * @public
     * @since 3
     *
     */
    public depthRange(zNear: number, zFar: number): void;
    /**
     * <p>enable and disable enable and disable various capabilities. Use isEnabled or get to determine the current setting of any capability. The initial value for each capability with the exception of DITHER is FALSE. The initial value for DITHER is TRUE.
     *
     * Both enable and disable take a single argument, cap, which can assume one of the following values:
     *
     * BLEND
     * If enabled, blend the computed fragment color values with the values in the color buffers. See blendFunc.
     *
     * CULL_FACE
     * If enabled, cull polygons based on their winding in window coordinates. See cullFace.
     *
     * DEPTH_TEST
     * If enabled, do depth comparisons and update the depth buffer. Note that even if the depth buffer exists and the depth mask is non-zero, the depth buffer is not updated if the depth test is disabled. See depthFunc and depthRangef.
     *
     * DITHER
     * If enabled, dither color components or indices before they are written to the color buffer.
     *
     * POLYGON_OFFSET_FILL
     * If enabled, an offset is added to depth values of a polygon's fragments produced by rasterization. See polygonOffset.
     *
     * SAMPLE_ALPHA_TO_COVERAGE
     * If enabled, compute a temporary coverage value where each bit is determined by the alpha value at the corresponding sample location. The temporary coverage value is then ANDed with the fragment coverage value.
     *
     * SAMPLE_COVERAGE
     * If enabled, the fragment's coverage is ANDed with the temporary coverage value. If SAMPLE_COVERAGE_INVERT is set to TRUE, invert the coverage value. See sampleCoverage.
     *
     * SCISSOR_TEST
     * If enabled, discard fragments that are outside the scissor rectangle. See scissor.
     *
     * STENCIL_TEST
     * If enabled, do stencil testing and update the stencil buffer. See stencilFunc and stencilOp.</p>
     * @param {number} cap - Specifies a symbolic constant indicating a GL capability.
     * @public
     * @since 3
     *
     */
    public disable(cap: number): void;
    /**
     * <p>enable and disable enable and disable various capabilities. Use isEnabled or get to determine the current setting of any capability. The initial value for each capability with the exception of DITHER is FALSE. The initial value for DITHER is TRUE.
     *
     * Both enable and disable take a single argument, cap, which can assume one of the following values:
     *
     * BLEND
     * If enabled, blend the computed fragment color values with the values in the color buffers. See blendFunc.
     *
     * CULL_FACE
     * If enabled, cull polygons based on their winding in window coordinates. See cullFace.
     *
     * DEPTH_TEST
     * If enabled, do depth comparisons and update the depth buffer. Note that even if the depth buffer exists and the depth mask is non-zero, the depth buffer is not updated if the depth test is disabled. See depthFunc and depthRangef.
     *
     * DITHER
     * If enabled, dither color components or indices before they are written to the color buffer.
     *
     * POLYGON_OFFSET_FILL
     * If enabled, an offset is added to depth values of a polygon's fragments produced by rasterization. See polygonOffset.
     *
     * SAMPLE_ALPHA_TO_COVERAGE
     * If enabled, compute a temporary coverage value where each bit is determined by the alpha value at the corresponding sample location. The temporary coverage value is then ANDed with the fragment coverage value.
     *
     * SAMPLE_COVERAGE
     * If enabled, the fragment's coverage is ANDed with the temporary coverage value. If SAMPLE_COVERAGE_INVERT is set to TRUE, invert the coverage value. See sampleCoverage.
     *
     * SCISSOR_TEST
     * If enabled, discard fragments that are outside the scissor rectangle. See scissor.
     *
     * STENCIL_TEST
     * If enabled, do stencil testing and update the stencil buffer. See stencilFunc and stencilOp.</p>
     * @param {number} cap - Specifies a symbolic constant indicating a GL capability.
     * @public
     * @since 3
     *
     */
    public enable(cap: number): void;
    /**
     * <p>In a scene composed entirely of opaque closed surfaces, back-facing polygons are never visible. Eliminating these invisible polygons has the obvious benefit of speeding up the rendering of the image. To enable and disable elimination of back-facing polygons, call enable and disable with argument CULL_FACE.
     *
     * The projection of a polygon to window coordinates is said to have clockwise winding if an imaginary object following the path from its first vertex, its second vertex, and so on, to its last vertex, and finally back to its first vertex, moves in a clockwise direction about the interior of the polygon. The polygon's winding is said to be counterclockwise if the imaginary object following the same path moves in a counterclockwise direction about the interior of the polygon. frontFace specifies whether polygons with clockwise winding in window coordinates, or counterclockwise winding in window coordinates, are taken to be front-facing. Passing CCW to mode selects counterclockwise polygons as front-facing; CW selects clockwise polygons as front-facing. By default, counterclockwise polygons are taken to be front-facing.<p>
     * @param {number} mode - Specifies the orientation of front-facing polygons. CW and CCW are accepted. The initial value is CCW.
     * @public
     * @since 3
     *
     */
    public frontFace(mode: number): void;
    /**
     * <p>getString returns a pointer to a static string describing some aspect of the current GL connection. name can be one of the following:
     *
     * VENDOR
     * Returns the company responsible for this GL implementation. This name does not change from release to release.
     *
     * RENDERER
     * Returns the name of the renderer. This name is typically specific to a particular configuration of a hardware platform. It does not change from release to release.
     *
     * VERSION
     * Returns a version or release number of the form OpenGL<space>ES<space><version number><space><vendor-specific information>.
     *
     * SHADING_LANGUAGE_VERSION
     * Returns a version or release number for the shading language of the form OpenGL<space>ES<space>GLSL<space>ES<space><version number><space><vendor-specific information>.
     *
     * EXTENSIONS
     * Returns a space-separated list of supported extensions to GL.
     *
     * Because the GL does not include queries for the performance characteristics of an implementation, some applications are written to recognize known platforms and modify their GL usage based on known performance characteristics of these platforms. Strings VENDOR and RENDERER together uniquely specify a platform. They do not change from release to release and should be used by platform-recognition algorithms.
     *
     * Some applications want to make use of features that are not part of the standard GL. These features may be implemented as extensions to the standard GL. The EXTENSIONS string is a space-separated list of supported GL extensions. (Extension names never contain a space character.)
     *
     * All strings are null-terminated.</p>
     * @param {number} pname - Specifies a symbolic constant, one of VENDOR, RENDERER, VERSION, SHADING_LANGUAGE_VERSION, or EXTENSIONS.
     * @return {any}
     * @public
     * @since 3
     *
     */
    public getParameter(pname: number): Object;
    /**
     * <p>getError returns the value of the error flag. Each detectable error is assigned a numeric code and symbolic name. When an error occurs, the error flag is set to the appropriate error code value. No other errors are recorded until getError is called, the error code is returned, and the flag is reset to NO_ERROR. If a call to getError returns NO_ERROR, there has been no detectable error since the last call to getError, or since the GL was initialized.
     *
     * To allow for distributed implementations, there may be several error flags. If any single error flag has recorded an error, the value of that flag is returned and that flag is reset to NO_ERROR when getError is called. If more than one flag has recorded an error, getError returns and clears an arbitrary error flag value. Thus, getError should always be called in a loop, until it returns NO_ERROR, if all error flags are to be reset.
     *
     * Initially, all error flags are set to NO_ERROR.
     *
     * The following errors are currently defined:
     *
     * NO_ERROR
     * No error has been recorded. The value of this symbolic constant is guaranteed to be 0.
     *
     * INVALID_ENUM
     * An unacceptable value is specified for an enumerated argument. The offending command is ignored and has no other side effect than to set the error flag.
     *
     * INVALID_VALUE
     * A numeric argument is out of range. The offending command is ignored and has no other side effect than to set the error flag.
     *
     * INVALID_OPERATION
     * The specified operation is not allowed in the current state. The offending command is ignored and has no other side effect than to set the error flag.
     *
     * INVALID_FRAMEBUFFER_OPERATION
     * The command is trying to render to or read from the framebuffer while the currently bound framebuffer is not framebuffer complete (i.e. the return value from checkFramebufferStatus is not FRAMEBUFFER_COMPLETE). The offending command is ignored and has no other side effect than to set the error flag.
     *
     * OUT_OF_MEMORY
     * There is not enough memory left to execute the command. The state of the GL is undefined, except for the state of the error flags, after this error is recorded.
     *
     * When an error flag is set, results of a GL operation are undefined only if OUT_OF_MEMORY has occurred. In all other cases, the command generating the error is ignored and has no effect on the GL state or frame buffer contents. If the generating command returns a value, it returns 0.</p>
     * @return {number} Error Code
     * @public
     * @since 3
     *
     */
    public getError(): number;
    /**
     * <p>Certain aspects of GL behavior, when there is room for interpretation, can be controlled with hints. A hint is specified with two arguments. target is a symbolic constant indicating the behavior to be controlled, and mode is another symbolic constant indicating the desired behavior. The initial value for each target is DONT_CARE. mode can be one of the following:
     *
     * FASTEST
     * The most efficient option should be chosen.
     *
     * NICEST
     * The most correct, or highest quality, option should be chosen.
     *
     * DONT_CARE
     * No preference.
     *
     * Though the implementation aspects that can be hinted are well defined, the interpretation of the hints depends on the implementation. The hint aspects that can be specified with target, along with suggested semantics, are as follows:
     *
     * GENERATE_MIPMAP_HINT
     * Indicates the quality of filtering when generating mipmap images with generateMipmap.</p>
     * @param {number} target - Specifies a symbolic constant indicating the behavior to be controlled. GENERATE_MIPMAP_HINT is accepted.
     * @param {number} mode - Specifies a symbolic constant indicating the desired behavior. FASTEST, NICEST, and DONT_CARE are accepted.
     * @public
     * @since 3
     *
     */
    public hint(target: number, mode: number): void;
    /**
     * <p>isEnabled returns TRUE if cap is an enabled capability and returns FALSE otherwise. Initially all capabilities except DITHER are disabled; DITHER is initially enabled.
     *
     * The following capabilities are accepted for cap:
     *
     * Constant    See
     * BLEND    blendFunc
     * CULL_FACE    cullFace
     * DEPTH_TEST    depthFunc, depthRangef
     * DITHER    enable
     * POLYGON_OFFSET_FILL    polygonOffset
     * SAMPLE_ALPHA_TO_COVERAGE    sampleCoverage
     * SAMPLE_COVERAGE    sampleCoverage
     * SCISSOR_TEST    scissor
     * STENCIL_TEST    stencilFunc, stencilOp
     * </p>
     * @param {number} cap - Specifies a symbolic constant indicating a GL capability.
     * @return {boolean} - A GLboolean indicating if the capability cap is enabled (true), or not (false).
     * @public
     * @since 3
     *
     */
    public isEnabled(cap: number): boolean;
    /**
     * <p>lineWidth specifies the rasterized width of lines.
     *
     * The actual width is determined by rounding the supplied width to the nearest integer. (If the rounding results in the value 0, it is as if the line width were 1.) If Δ x >= Δ y , i pixels are filled in each column that is rasterized, where i is the rounded value of width. Otherwise, i pixels are filled in each row that is rasterized.
     *
     * There is a range of supported line widths. Only width 1 is guaranteed to be supported; others depend on the implementation. To query the range of supported widths, call get with argument ALIASED_LINE_WIDTH_RANGE.</p>
     * @param {number} width - Specifies the width of rasterized lines. The initial value is 1.
     * @public
     * @since 3
     *
     */
    public lineWidth(width: number): void;
    /**
     * <p>pixelStorei sets pixel storage modes that affect the operation of subsequent readPixels as well as the unpacking of texture patterns (see texImage2D and texSubImage2D).
     *
     * pname is a symbolic constant indicating the parameter to be set, and param is the new value. One storage parameter affects how pixel data is returned to client memory:
     *
     * PACK_ALIGNMENT
     * Specifies the alignment requirements for the start of each pixel row in memory. The allowable values are 1 (byte-alignment), 2 (rows aligned to even-numbered bytes), 4 (word-alignment), and 8 (rows start on double-word boundaries).
     *
     * The other storage parameter affects how pixel data is read from client memory:
     *
     * UNPACK_ALIGNMENT
     * Specifies the alignment requirements for the start of each pixel row in memory. The allowable values are 1 (byte-alignment), 2 (rows aligned to even-numbered bytes), 4 (word-alignment), and 8 (rows start on double-word boundaries).
     *
     * The following table gives the type, initial value, and range of valid values for each storage parameter that can be set with pixelStorei.
     *
     * pname    Type    Initial Value    Valid Range
     * PACK_ALIGNMENT    integer    4    1, 2, 4, or 8
     * UNPACK_ALIGNMENT    integer    4    1, 2, 4, or 8
     * Boolean parameters are set to false if param is 0 and true otherwise.</p>
     * @param {number} pname - Specifies the symbolic name of the parameter to be set. One value affects the packing of pixel data into memory: PACK_ALIGNMENT. The other affects the unpacking of pixel data from memory: UNPACK_ALIGNMENT.
     * @param {number} param - Specifies the value that pname is set to.
     * @public
     * @since 3
     *
     */
    public pixelStorei(pname: number, param: number): void;
    /**
     * <p>When POLYGON_OFFSET_FILL is enabled, each fragment's depth value will be offset after it is interpolated from the depth values of the appropriate vertices. The value of the offset is factor × DZ + r × units , where DZ is a measurement of the change in depth relative to the screen area of the polygon, and r is the smallest value that is guaranteed to produce a resolvable offset for a given implementation. The offset is added before the depth test is performed and before the value is written into the depth buffer.
     *
     * polygonOffset is useful for rendering hidden-line images, for applying decals to surfaces, and for rendering solids with highlighted edges.</p>
     * @param {number} factor - Specifies a scale factor that is used to create a variable depth offset for each polygon. The initial value is 0.
     * @param {number} units - Is multiplied by an implementation-specific value to create a constant depth offset. The initial value is 0.
     * @public
     * @since 3
     *
     */
    public polygonOffset(factor: number, units: number): void;
    /**
     * <p>Multisampling samples a pixel multiple times at various implementation-dependent subpixel locations to generate antialiasing effects. Multisampling transparently antialiases points, lines, and polygons if it is enabled.
     *
     * value is used in constructing a temporary mask used in determining which samples will be used in resolving the final fragment color. This mask is bitwise-anded with the coverage mask generated from the multisampling computation. If the invert flag is set, the temporary mask is inverted (all bits flipped) and then the bitwise-and is computed.
     *
     * If an implementation does not have any multisample buffers available, or multisampling is disabled, rasterization occurs with only a single sample computing a pixel's final RGB color.
     *
     * Provided an implementation supports multisample buffers, and multisampling is enabled, then a pixel's final color is generated by combining several samples per pixel. Each sample contains color, depth, and stencil information, allowing those operations to be performed on each sample.</p>
     * @param {number} value - Specify a single floating-point sample coverage value. The value is clamped to the range 0 1 . The initial value is 1.0.
     * @param {boolean} invert - Specify a single boolean value representing if the coverage masks should be inverted. TRUE and FALSE are accepted. The initial value is FALSE.
     * @public
     * @since 3
     *
     */
    public sampleCoverage(value: number, invert: boolean): void;
    /**
     * <p>Stenciling, like depth-buffering, enables and disables drawing on a per-pixel basis. Stencil planes are first drawn into using GL drawing primitives, then geometry and images are rendered using the stencil planes to mask out portions of the screen. Stenciling is typically used in multipass rendering algorithms to achieve special effects, such as decals, outlining, and constructive solid geometry rendering.
     *
     * The stencil test conditionally eliminates a pixel based on the outcome of a comparison between the reference value and the value in the stencil buffer. To enable and disable the test, call enable and disable with argument STENCIL_TEST. To specify actions based on the outcome of the stencil test, call stencilOp or stencilOpSeparate.
     *
     * There can be two separate sets of func, ref, and mask parameters; one affects back-facing polygons, and the other affects front-facing polygons as well as other non-polygon primitives. stencilFunc sets both front and back stencil state to the same values. Use stencilFuncSeparate to set front and back stencil state to different values.
     *
     * func is a symbolic constant that determines the stencil comparison function. It accepts one of eight values, shown in the following list. ref is an integer reference value that is used in the stencil comparison. It is clamped to the range 0 2 n - 1 , where n is the number of bitplanes in the stencil buffer. mask is bitwise ANDed with both the reference value and the stored stencil value, with the ANDed values participating in the comparison.
     *
     * If stencil represents the value stored in the corresponding stencil buffer location, the following list shows the effect of each comparison function that can be specified by func. Only if the comparison succeeds is the pixel passed through to the next stage in the rasterization process (see stencilOp). All tests treat stencil values as unsigned integers in the range 0 2 n - 1 , where n is the number of bitplanes in the stencil buffer.
     *
     * The following values are accepted by func:
     *
     * NEVER
     * Always fails.
     *
     * LESS
     * Passes if ( ref & mask ) < ( stencil & mask ).
     *
     * LEQUAL
     * Passes if ( ref & mask ) <= ( stencil & mask ).
     *
     * GREATER
     * Passes if ( ref & mask ) > ( stencil & mask ).
     *
     * GEQUAL
     * Passes if ( ref & mask ) >= ( stencil & mask ).
     *
     * EQUAL
     * Passes if ( ref & mask ) = ( stencil & mask ).
     *
     * NOTEQUAL
     * Passes if ( ref & mask ) != ( stencil & mask ).
     *
     * ALWAYS
     * Always passes.</p>
     * @param {number} func - Specifies the test function. Eight symbolic constants are valid: NEVER, LESS, LEQUAL, GREATER, GEQUAL, EQUAL, NOTEQUAL, and ALWAYS. The initial value is ALWAYS.
     * @param {number} ref - Specifies the reference value for the stencil test. ref is clamped to the range 0 2 n - 1 , where n is the number of bitplanes in the stencil buffer. The initial value is 0.
     * @param {number} mask - Specifies a mask that is ANDed with both the reference value and the stored stencil value when the test is done. The initial value is all 1's.
     * @public
     * @since 3
     *
     */
    public stencilFunc(func: number, ref: number, mask: number): void;
    /**
     * <p>Stenciling, like depth-buffering, enables and disables drawing on a per-pixel basis. You draw into the stencil planes using GL drawing primitives, then render geometry and images, using the stencil planes to mask out portions of the screen. Stenciling is typically used in multipass rendering algorithms to achieve special effects, such as decals, outlining, and constructive solid geometry rendering.
     *
     * The stencil test conditionally eliminates a pixel based on the outcome of a comparison between the reference value and the value in the stencil buffer. To enable and disable the test, call enable and disable with argument STENCIL_TEST. To specify actions based on the outcome of the stencil test, call stencilOp or stencilOpSeparate.
     *
     * There can be two separate sets of func, ref, and mask parameters; one affects back-facing polygons, and the other affects front-facing polygons as well as other non-polygon primitives. stencilFunc sets both front and back stencil state to the same values, as if stencilFuncSeparate were called with face set to FRONT_AND_BACK.
     *
     * func is a symbolic constant that determines the stencil comparison function. It accepts one of eight values, shown in the following list. ref is an integer reference value that is used in the stencil comparison. It is clamped to the range 0 2 n - 1 , where n is the number of bitplanes in the stencil buffer. mask is bitwise ANDed with both the reference value and the stored stencil value, with the ANDed values participating in the comparison.
     *
     * If stencil represents the value stored in the corresponding stencil buffer location, the following list shows the effect of each comparison function that can be specified by func. Only if the comparison succeeds is the pixel passed through to the next stage in the rasterization process (see stencilOp). All tests treat stencil values as unsigned integers in the range 0 2 n - 1 , where n is the number of bitplanes in the stencil buffer.
     *
     * The following values are accepted by func:
     *
     * NEVER
     * Always fails.
     *
     * LESS
     * Passes if ( ref & mask ) < ( stencil & mask ).
     *
     * LEQUAL
     * Passes if ( ref & mask ) <= ( stencil & mask ).
     *
     * GREATER
     * Passes if ( ref & mask ) > ( stencil & mask ).
     *
     * GEQUAL
     * Passes if ( ref & mask ) >= ( stencil & mask ).
     *
     * EQUAL
     * Passes if ( ref & mask ) = ( stencil & mask ).
     *
     * NOTEQUAL
     * Passes if ( ref & mask ) != ( stencil & mask ).
     *
     * ALWAYS
     * Always passes.</p>
     * @param {number} face - Specifies whether front and/or back stencil state is updated. Three symbolic constants are valid: FRONT, BACK, and FRONT_AND_BACK.
     * @param {number} func - Specifies the test function. Eight symbolic constants are valid: NEVER, LESS, LEQUAL, GREATER, GEQUAL, EQUAL, NOTEQUAL, and ALWAYS. The initial value is ALWAYS.
     * @param {number} ref - Specifies the reference value for the stencil test. ref is clamped to the range 0 2 n - 1 , where n is the number of bitplanes in the stencil buffer. The initial value is 0.
     * @param {number} mask - Specifies a mask that is ANDed with both the reference value and the stored stencil value when the test is done. The initial value is all 1's.
     * @public
     * @since 3
     *
     */
    public stencilFuncSeparate(face: number, func: number, ref: number, mask: number): void;
    /**
     * <p>stencilMask controls the writing of individual bits in the stencil planes. The least significant n bits of mask, where n is the number of bits in the stencil buffer, specify a mask. Where a 1 appears in the mask, it's possible to write to the corresponding bit in the stencil buffer. Where a 0 appears, the corresponding bit is write-protected. Initially, all bits are enabled for writing.
     *
     * There can be two separate mask writemasks; one affects back-facing polygons, and the other affects front-facing polygons as well as other non-polygon primitives. stencilMask sets both front and back stencil writemasks to the same values. Use stencilMaskSeparate to set front and back stencil writemasks to different values.</p>
     * @param {number} mask - Specifies a bit mask to enable and disable writing of individual bits in the stencil planes. Initially, the mask is all 1's.
     * @public
     * @since 3
     *
     */
    public stencilMask(mask: number): void;
    /**
     * <p>stencilMaskSeparate controls the writing of individual bits in the stencil planes. The least significant n bits of mask, where n is the number of bits in the stencil buffer, specify a mask. Where a 1 appears in the mask, it's possible to write to the corresponding bit in the stencil buffer. Where a 0 appears, the corresponding bit is write-protected. Initially, all bits are enabled for writing.
     *
     * There can be two separate mask writemasks; one affects back-facing polygons, and the other affects front-facing polygons as well as other non-polygon primitives. stencilMask sets both front and back stencil writemasks to the same values, as if stencilMaskSeparate were called with face set to FRONT_AND_BACK.</p>
     * @param {number} face - Specifies whether the front and/or back stencil writemask is updated. Three symbolic constants are valid: FRONT, BACK, and FRONT_AND_BACK.
     * @param {number} mask - Specifies a bit mask to enable and disable writing of individual bits in the stencil planes. Initially, the mask is all 1's.
     * @public
     * @since 3
     *
     */
    public stencilMaskSeparate(face: number, mask: number): void;
    /**
     * <p>Stenciling, like depth-buffering, enables and disables drawing on a per-pixel basis. You draw into the stencil planes using GL drawing primitives, then render geometry and images, using the stencil planes to mask out portions of the screen. Stenciling is typically used in multipass rendering algorithms to achieve special effects, such as decals, outlining, and constructive solid geometry rendering.
     *
     * The stencil test conditionally eliminates a pixel based on the outcome of a comparison between the value in the stencil buffer and a reference value. To enable and disable the test, call enable and disable with argument STENCIL_TEST; to control it, call stencilFunc or stencilFuncSeparate.
     *
     * There can be two separate sets of sfail, dpfail, and dppass parameters; one affects back-facing polygons, and the other affects front-facing polygons as well as other non-polygon primitives. stencilOp sets both front and back stencil state to the same values. Use stencilOpSeparate to set front and back stencil state to different values.
     *
     * stencilOp takes three arguments that indicate what happens to the stored stencil value while stenciling is enabled. If the stencil test fails, no change is made to the pixel's color or depth buffers, and sfail specifies what happens to the stencil buffer contents. The following eight actions are possible.
     *
     * KEEP
     * Keeps the current value.
     *
     * ZERO
     * Sets the stencil buffer value to 0.
     *
     * REPLACE
     * Sets the stencil buffer value to ref, as specified by stencilFunc.
     *
     * INCR
     * Increments the current stencil buffer value. Clamps to the maximum representable unsigned value.
     *
     * INCR_WRAP
     * Increments the current stencil buffer value. Wraps stencil buffer value to zero when incrementing the maximum representable unsigned value.
     *
     * DECR
     * Decrements the current stencil buffer value. Clamps to 0.
     *
     * DECR_WRAP
     * Decrements the current stencil buffer value. Wraps stencil buffer value to the maximum representable unsigned value when decrementing a stencil buffer value of zero.
     *
     * INVERT
     * Bitwise inverts the current stencil buffer value.
     *
     * Stencil buffer values are treated as unsigned integers. When incremented and decremented, values are clamped to 0 and 2 n - 1 , where n is the value returned by querying STENCIL_BITS.
     *
     * The other two arguments to stencilOp specify stencil buffer actions that depend on whether subsequent depth buffer tests succeed (dppass) or fail (dpfail) (see depthFunc). The actions are specified using the same eight symbolic constants as sfail. Note that dpfail is ignored when there is no depth buffer, or when the depth buffer is not enabled. In these cases, sfail and dppass specify stencil action when the stencil test fails and passes, respectively.</p>
     * @param {number} fail - Specifies the action to take when the stencil test fails. Eight symbolic constants are accepted: KEEP, ZERO, REPLACE, INCR, INCR_WRAP, DECR, DECR_WRAP, and INVERT. The initial value is KEEP.
     * @param {number} zfail - Specifies the stencil action when the stencil test passes, but the depth test fails. dpfail accepts the same symbolic constants as sfail. The initial value is KEEP.
     * @param {number} zpass - Specifies the stencil action when both the stencil test and the depth test pass, or when the stencil test passes and either there is no depth buffer or depth testing is not enabled. dppass accepts the same symbolic constants as sfail. The initial value is KEEP.
     * @public
     * @since 3
     *
     */
    public stencilOp(fail: number, zfail: number, zpass: number): void;
    /**
     * <p>Stenciling, like depth-buffering, enables and disables drawing on a per-pixel basis. You draw into the stencil planes using GL drawing primitives, then render geometry and images, using the stencil planes to mask out portions of the screen. Stenciling is typically used in multipass rendering algorithms to achieve special effects, such as decals, outlining, and constructive solid geometry rendering.
     *
     * The stencil test conditionally eliminates a pixel based on the outcome of a comparison between the value in the stencil buffer and a reference value. To enable and disable the test, call enable and disable with argument STENCIL_TEST; to control it, call stencilFunc or stencilFuncSeparate.
     *
     * There can be two separate sets of sfail, dpfail, and dppass parameters; one affects back-facing polygons, and the other affects front-facing polygons as well as other non-polygon primitives. stencilOp sets both front and back stencil state to the same values, as if stencilOpSeparate were called with face set to FRONT_AND_BACK.
     *
     * stencilOpSeparate takes three arguments that indicate what happens to the stored stencil value while stenciling is enabled. If the stencil test fails, no change is made to the pixel's color or depth buffers, and sfail specifies what happens to the stencil buffer contents. The following eight actions are possible.
     *
     * KEEP
     * Keeps the current value.
     *
     * ZERO
     * Sets the stencil buffer value to 0.
     *
     * REPLACE
     * Sets the stencil buffer value to ref, as specified by stencilFunc.
     *
     * INCR
     * Increments the current stencil buffer value. Clamps to the maximum representable unsigned value.
     *
     * INCR_WRAP
     * Increments the current stencil buffer value. Wraps stencil buffer value to zero when incrementing the maximum representable unsigned value.
     *
     * DECR
     * Decrements the current stencil buffer value. Clamps to 0.
     *
     * DECR_WRAP
     * Decrements the current stencil buffer value. Wraps stencil buffer value to the maximum representable unsigned value when decrementing a stencil buffer value of zero.
     *
     * INVERT
     * Bitwise inverts the current stencil buffer value.
     *
     * Stencil buffer values are treated as unsigned integers. When incremented and decremented, values are clamped to 0 and 2 n - 1 , where n is the value returned by querying STENCIL_BITS.
     *
     * The other two arguments to stencilOpSeparate specify stencil buffer actions that depend on whether subsequent depth buffer tests succeed (dppass) or fail (dpfail) (see depthFunc). The actions are specified using the same eight symbolic constants as sfail. Note that dpfail is ignored when there is no depth buffer, or when the depth buffer is not enabled. In these cases, sfail and dppass specify stencil action when the stencil test fails and passes, respectively.</p>
     * @param {number} face - Specifies whether front and/or back stencil state is updated. Three symbolic constants are valid: FRONT, BACK, and FRONT_AND_BACK.
     * @param {number} fail - Specifies the action to take when the stencil test fails. Eight symbolic constants are accepted: KEEP, ZERO, REPLACE, INCR, INCR_WRAP, DECR, DECR_WRAP, and INVERT. The initial value is KEEP.
     * @param {number} zfail - Specifies the stencil action when the stencil test passes, but the depth test fails. dpfail accepts the same symbolic constants as sfail. The initial value is KEEP.
     * @param {number} zpass - Specifies the stencil action when both the stencil test and the depth test pass, or when the stencil test passes and either there is no depth buffer or depth testing is not enabled. dppass accepts the same symbolic constants as sfail. The initial value is KEEP.
     * @public
     * @since 3
     *
     */
    public stencilOpSeparate(face: number, fail: number, zfail: number, zpass: number): void;
    /**
     * <p>The attachShader method will attach either a fragment or vertext shader to a webGLProgram.</p>
     * @param {Object} program - A WebGLProgram object.
     * @param {Object} shader - A fragment or vertex WebGLShader object.
     * @public
     * @since 3
     *
     */
    public attachShader(program: object, shader: object): void;
    /**
     * <p>The bindAttribLocation method will binds a generitic vertex index to an attribute variable.</p>
     * @param {Object} program - A WebGLProgram object to bind.
     * @param {number} index - Specifies the index of the generic vertex to bind.
     * @param {string} name - Specifies the name of the variable to bind to the generic vertex index. This name cannot start with "webgl_" or "_webgl_", as these are reserved for use by WebGL.
     * @public
     * @since 3
     *
     */
    public bindAttribLocation(program: object, index: number, name: string): void;
    /**
     * <p>The compileShadeer method will compile a GLSL shader into binary data so that it can be used by a WebGLProgram.</p>
     * @param {Object} shader - Specifies a fragment or vertex WebGLShader.
     * @public
     * @since 3
     *
     */
    public compileShader(shader: object): void;
    /**
     * <p>The createProgram method will creates and initializes a webGLProgram Object.</p>
     * @return {Object} A WebGLProgram Object that is a combination of two compiled WebGLShaders consisting of a vertex shader and a fragment shader (both written in GLSL). These are then linked into a usable program.
     * @public
     * @since 3
     *
     */
    public createProgram(): object;
    /**
     * <p>The createShader method will creates and initializes a webGLShader Object.</p>
     * @param {yunos.webgl.WebGLRenderingContext.VERTEX_SHADER | yunos.webgl.WebGLRenderingContext.FRAGMENT_SHADER} - Either yunos.webgl.WebGLRenderingContext.VERTEX_SHADER or yunos.webgl.WebGLRenderingContext.FRAGMENT_SHADER.
     * @return {Object} Return a WebGLShader object.
     * @public
     * @since 3
     *
     */
    public createShader(type: number): object;
    /**
     * <p>The deleteProgram method will delete a given WebGLProgram Object.</p>
     * <p>This method has no effect if the program has already been deleted.</p>
     * @param {Object} program - Specifies a WebGLProgram object to delete.
     * @public
     * @since 3
     *
     */
    public deleteProgram(program: object): void;
    /**
     * <p>The deleteShader method will deletes a given WebGLShader object.</p>
     * <p>This method has no effect if the shader has already been deleted.</p>
     * @param {Object} shader - Specifies a WebGLShader object to delete.
     * @public
     * @since 3
     *
     */
    public deleteShader(shader: object): void;
    /**
     * <p>The detachShader method will detaches a previously attached WebGLShader from a WebGLProgram.</p>
     * @param {Object} program - Specifies a WebGLProgram object.
     * @param {Object} shader - Specifies a fragment or vertex WebGLShader object.
     * @public
     * @since 3
     *
     */
    public detachShader(program: object, shader: object): void;
    /**
     * <p>The getAttachedShaders method will returns a list of WebGLShader objects attached to a WebGLProgram.</p>
     * @param {Object} program - Specifies a WebGLProgram object to get attached shaders for.
     * @return {Object[]} Return An Array of WebGLShader objects that are attached to the given WebGLProgram.
     * @public
     * @since 3
     *
     */
    public getAttachedShaders(program: object): object[];
    /**
     * <p>The getProgramParameter method will return informations about the given program.</p>
     * <p>The second parameter's possible values:
     * <p>yunos.webgl.WebGLRenderingContext.DELETE_STATUS: Returns a GLboolean indicating whether or not the program is flagged for deletion.</p>
     * <p>yunos.webgl.WebGLRenderingContext.LINK_STATUS: Returns a GLboolean indicating whether or not the last link operation was successful.</p>
     * <p>yunos.webgl.WebGLRenderingContext.VALIDATE_STATUS: Returns a GLboolean indicating whether or not the last validation operation was successful.</p>
     * <p>yunos.webgl.WebGLRenderingContext.ATTACHED_SHADERS: Returns a GLint indicating the number of attached shaders to a program.</p>
     * <p>yunos.webgl.WebGLRenderingContext.ACTIVE_ATTRIBUTES: Returns a GLint indicating the number of active attribute variables to a program.</p>
     * <p>yunos.webgl.WebGLRenderingContext.ACTIVE_UNIFORMS: Returns a GLint indicating the number of active uniform variables to a program.</p>
     * @param {Object} program - Specifies a WebGLProgram to get parameter information from.
     * @param {yunos.webgl.WebGLRenderingContext.DELETE_STATUS | yunos.webgl.WebGLRenderingContext.LINK_STATUS | yunos.webgl.WebGLRenderingContext.VALIDATE_STATUS | yunos.webgl.WebGLRenderingContext.ATTACHED_SHADERS | yunos.webgl.WebGLRenderingContext.ACTIVE_ATTRIBUTES | yunos.webgl.WebGLRenderingContext.ACTIVE_UNIFORMS} pname Specifies the information to query.
     * @return {number}
     * @public
     * @since 3
     *
     */
    public getProgramParameter(program: object, pname: number): number;
    /**
     * <p>The getProgramInfoLog method return the information log for the specified WebGLProgram object.</p>
     * <p>It contains errors that occured during failed linking or validation of WebGLProgram objects.</p>
     * @param {Object} program - Specifies a WebGLProgram to query.
     * @return {string} Specifies that contains diagnostic messages, warning messages, and other information about the last linking or validation operation. When a WebGLProgram object is initially created, its information log will be a string of length 0.
     * @public
     * @since 3
     *
     */
    public getProgramInfoLog(program: object): string;
    /**
     * <p>The getShaderParameter method will return information about the given shader.</p>
     * <p>The second parameter's possible values:</p>
     * <p>yunos.webgl.WebGLRenderingContext.DELETE_STATUS: Returns a GLboolean indicating whether or not the shader is flagged for deletion.</p>
     * <p>yunos.webgl.WebGLRenderingContext.COMPILE_STATUS: Returns a GLboolean indicating whether or not the last shader compilation was successful.</p>
     * <p>yunos.webgl.WebGLRenderingContext.SHADER_TYPE: Returns a GLenum indicating whether the shader is a vertex shader (gl.VERTEX_SHADER) or fragment shader (gl.FRAGMENT_SHADER) object.</p>
     * @param {Object} shader - Specifies a WebGLShader to get parameter information from.
     * @param {yunos.webgl.WebGLRenderingContext.DELETE_STATUS | yunos.webgl.WebGLRenderingContext.COMPILE_STATUS | yunos.webgl.WebGLRenderingContext.SHADER_TYPE} pname - Specifies the information to query.
     * @return {number} Returns the requested shader information (as specified with panme).
     * @public
     * @since 3
     *
     */
    public getShaderParameter(shader: object, pname: number): number;
    /**
     * <p>The getShaderPrecisionFormat() method will return a new WebGLShaderPrecisionFormat object describing the range and precision for the specified shader numeric object.</p>
     * @param {Object} shaderType - Specifies either a FRAGMENT_SHADER object or a VERTEX_SHADER object.
     * @param {number} precisionType - A precision type value. Either LOW_FLOAT, MEDIUM_FLOAT, HIGH_FLOAT, LOW_INT, MEDIUM_INT, or HIGH_INT
     * @return {Object | null} Return a WebGLShaderPrecisionFormat object or null, if an error occurs.
     * @public
     * @since 3
     *
     */
    public getShaderPrecisionFormat(shaderType: number, precisionType: number): object | null;
    /**
     * <p>The getShaderInfoLog return the information log for the specified WebGLShader Object.</p>
     * <p>It contains warnings, debugging and compile information.</p>
     * @param {Object} shader - Specifies a WebGLProgram object to query.
     * @return {string} Specifies that contains diagnostic messages, warning messages, and other information about the last compile operation. When a WebGLShader object is initially created, its information log will be a string of length 0.
     * @public
     * @since 3
     *
     */
    public getShaderInfoLog(shader: object): string;
    /**
     * <p>The getShaderSource method will return the source code of a WebGLShader as a string.</p>
     * @param {Object} shader - Specifies a WebGLProgram object to get the source code from.
     * @return {string} Return a string containing the source code of the shader.
     * @public
     * @since 3
     *
     */
    public getShaderSource(shader: object): string;
    /**
     * <p>The isProgram method will return true if the passed WebGLProgram is valid, false otherwise.</p>
     * @param {Object} program - Specifies a WebGLProgram to check.
     * @return {boolean} Return a boolean indicating whether or not the program is valid.
     * @public
     * @since 3
     *
     */
    public isProgram(program: object): boolean;
    /**
     * <p>The isShader method will return true if the passed WebGLShader is valid, false otherwise.</p>
     * @param {Object} shader - Specifies a WebGLShader to check.
     * @return {boolean} Return a boolean indicating whether or not the shader is valid.
     * @public
     * @since 3
     *
     */
    public isShader(shader: object): boolean;
    /**
     * <p>The linkProgram method will link a given WebGLProgram to the attached vertex and fragment shaders.</p>
     * @param {Object} program - Specifies a WebGLProgram to link.
     * @public
     * @since 3
     *
     */
    public linkProgram(program: object): void;
    /**
     * <p>The shaderSource method will set the source code of a WebGLShader.</p>
     * @param {Object} shader - Specifies a WebGLShader object in which to set the source code.
     * @param {string} source - Specifies a string containing the GLSL source code to get.
     * @public
     * @since 3
     *
     */
    public shaderSource(shader: object, source: string): void;
    /**
     * <p>The use program method will set the specified WebGLProgram as part of the current rendering state.</p>
     * @param {Object} program - Specifies a WebGLProgram object to use.
     * @public
     * @since 3
     *
     */
    public useProgram(program: object): void;
    /**
     * <p>The validateProgram method will validate a WebGLProgram.</p>
     * <p>It checks if it is successfully linked and if it can be used in the current WebGL state.</p>
     * @param {Object} program - Specifies a WebGLProgram object to validate.
     * @public
     * @since 3
     *
     */
    public validateProgram(program: object): void;
    /**
     * <p>disableVertexAttribArray disables the generic vertex attribute array specified by index. disableVertexAttribArray disables the generic vertex attribute array specified by index. By default, all client-side capabilities are disabled, including all generic vertex attribute arrays. If enabled, the values in the generic vertex attribute array will be accessed and used for rendering when calls are made to vertex array commands such as drawArrays or drawElements.</p>
     * @param {number} index - Specifies the index of the generic vertex attribute to be enabled or disabled.
     * @public
     * @since 3
     *
     */
    public disableVertexAttribArray(index: number): void;
    /**
     * <p>enableVertexAttribArray enables the generic vertex attribute array specified by index. disableVertexAttribArray disables the generic vertex attribute array specified by index. By default, all client-side capabilities are disabled, including all generic vertex attribute arrays. If enabled, the values in the generic vertex attribute array will be accessed and used for rendering when calls are made to vertex array commands such as drawArrays or drawElements.</p>
     * @param {number} index - Specifies the index of the generic vertex attribute to be enabled or disabled.
     * @public
     * @since 3
     *
     */
    public enableVertexAttribArray(index: number): void;
    /**
     * <p>getActiveAttrib returns information about an active attribute variable in the program object specified by program. The number of active attributes can be obtained by calling getProgramiv with the value ACTIVE_ATTRIBUTES. A value of 0 for index selects the first active attribute variable. Permissible values for index range from 0 to the number of active attribute variables minus 1.
     *
     * Attribute variables have arbitrary names and obtain their values through numbered generic vertex attributes. An attribute variable is considered active if it is determined during the link operation that it may be accessed during program execution. Therefore, program should have previously been the target of a call to linkProgram, but it is not necessary for it to have been linked successfully.
     *
     * The size of the character buffer required to store the longest attribute variable name in program can be obtained by calling getProgramiv with the value ACTIVE_ATTRIBUTE_MAX_LENGTH. This value should be used to allocate a buffer of sufficient size to store the returned attribute name. The size of this character buffer is passed in bufSize, and a pointer to this character buffer is passed in name.
     *
     * getActiveAttrib returns the name of the attribute variable indicated by index, storing it in the character buffer specified by name. The string returned will be null terminated. The actual number of characters written into this buffer is returned in length, and this count does not include the null termination character. If the length of the returned string is not required, a value of NULL can be passed in the length argument.
     *
     * The type argument will return a pointer to the attribute variable's data type. The symbolic constants FLOAT, FLOAT_VEC2, FLOAT_VEC3, FLOAT_VEC4, FLOAT_MAT2, FLOAT_MAT3, or FLOAT_MAT4 may be returned. The size argument will return the size of the attribute, in units of the type returned in type.
     *
     * This function will return as much information as it can about the specified active attribute variable. If no information is available, length will be 0, and name will be an empty string. This situation could occur if this function is called after a link operation that failed. If an error occurs, the return values length, size, type, and name will be unmodified.</p>
     * @param {yunos.graphics.WebglContext.WebGLProgram} program - Specifies the program object to be queried.
     * @param {number} index -Specifies the index of the attribute variable to be queried.
     * @return {Object} A WebGLActiveInfo object.
     * @public
     * @since 3
     *
     */
    public getActiveAttrib(program: object, index: number): {
        name: string;
        size: number;
        type: number;
    };
    /**
     * <p>getActiveUniform returns information about an active uniform variable in the program object specified by program. The number of active uniform variables can be obtained by calling getProgramiv with the value ACTIVE_UNIFORMS. A value of 0 for index selects the first active uniform variable. Permissible values for index range from 0 to the number of active uniform variables minus 1.
     *
     * Shaders may use either built-in uniform variables, user-defined uniform variables, or both. Built-in uniform variables have a prefix of "gl_" and reference existing OpenGL state or values derived from such state (e.g., gl_DepthRange). User-defined uniform variables have arbitrary names and obtain their values from the application through calls to uniform. A uniform variable (either built-in or user-defined) is considered active if it is determined during the link operation that it may be accessed during program execution. Therefore, program should have previously been the target of a call to linkProgram, but it is not necessary for it to have been linked successfully.
     *
     * The size of the character buffer required to store the longest uniform variable name in program can be obtained by calling getProgramiv with the value ACTIVE_UNIFORM_MAX_LENGTH. This value should be used to allocate a buffer of sufficient size to store the returned uniform variable name. The size of this character buffer is passed in bufSize, and a pointer to this character buffer is passed in name.
     *
     * getActiveUniform returns the name of the uniform variable indicated by index, storing it in the character buffer specified by name. The string returned will be null terminated. The actual number of characters written into this buffer is returned in length, and this count does not include the null termination character. If the length of the returned string is not required, a value of NULL can be passed in the length argument.
     *
     * The type argument will return a pointer to the uniform variable's data type. The symbolic constants FLOAT, FLOAT_VEC2, FLOAT_VEC3, FLOAT_VEC4, INT, INT_VEC2, INT_VEC3, INT_VEC4, BOOL, BOOL_VEC2, BOOL_VEC3, BOOL_VEC4, FLOAT_MAT2, FLOAT_MAT3, FLOAT_MAT4, SAMPLER_2D, or SAMPLER_CUBE may be returned.
     *
     * If one or more elements of an array are active, the name of the array is returned in name, the type is returned in type, and the size parameter returns the highest array element index used, plus one, as determined by the compiler and/or linker. Only one active uniform variable will be reported for a uniform array.
     *
     * Uniform variables that are declared as structures or arrays of structures will not be returned directly by this function. Instead, each of these uniform variables will be reduced to its fundamental components containing the "." and "[]" operators such that each of the names is valid as an argument to getUniformLocation. Each of these reduced uniform variables is counted as one active uniform variable and is assigned an index. A valid name cannot be a structure, an array of structures, or a subcomponent of a vector or matrix.
     *
     * The size of the uniform variable will be returned in size. Uniform variables other than arrays will have a size of 1. Structures and arrays of structures will be reduced as described earlier, such that each of the names returned will be a data type in the earlier list. If this reduction results in an array, the size returned will be as described for uniform arrays; otherwise, the size returned will be 1.
     *
     * The list of active uniform variables may include both built-in uniform variables (which begin with the prefix "gl_") as well as user-defined uniform variable names.
     *
     * This function will return as much information as it can about the specified active uniform variable. If no information is available, length will be 0, and name will be an empty string. This situation could occur if this function is called after a link operation that failed. If an error occurs, the return values length, size, type, and name will be unmodified.</p>
     * @param {yunos.graphics.WebglContext.WebGLProgram} program - Specifies the program object to be queried.
     * @param {number} index -Specifies the index of the attribute variable to be queried.
     * @return {Object} A WebGLActiveInfo object.
     * @public
     * @since 3
     *
     */
    public getActiveUniform(program: object, index: number): {
        name: string;
        size: number;
        type: number;
    };
    /**
     * <p>getAttribLocation queries the previously linked program object specified by program for the attribute variable specified by name and returns the index of the generic vertex attribute that is bound to that attribute variable. If name is a matrix attribute variable, the index of the first column of the matrix is returned. If the named attribute variable is not an active attribute in the specified program object or if name starts with the reserved prefix "gl_", a value of -1 is returned.
     *
     * The association between an attribute variable name and a generic attribute index can be specified at any time by calling bindAttribLocation. Attribute bindings do not go into effect until linkProgram is called. After a program object has been linked successfully, the index values for attribute variables remain fixed until the next link command occurs. The attribute values can only be queried after a link if the link was successful. getAttribLocation returns the binding that actually went into effect the last time linkProgram was called for the specified program object. Attribute bindings that have been specified since the last link operation are not returned by getAttribLocation.</p>
     * @param {object} program - Specifies the program object to be queried.
     * @param {string} name - Points to a null terminated string containing the name of the attribute variable whose location is to be queried.
     * @return {number} A GLint number indicating the location of the variable name if found. Returns -1 otherwise.
     * @public
     * @since 3
     *
     */
    public getAttribLocation(program: object, name: string): number;
    /**
     * <p>getUniform returns in params the value(s) of the specified uniform variable. The type of the uniform variable specified by location determines the number of values returned. If the uniform variable is defined in the shader as a boolean, int, or float, a single value will be returned. If it is defined as a vec2, ivec2, or bvec2, two values will be returned. If it is defined as a vec3, ivec3, or bvec3, three values will be returned, and so on. To query values stored in uniform variables declared as arrays, call getUniform for each element of the array. To query values stored in uniform variables declared as structures, call getUniform for each field in the structure. The values for uniform variables declared as a matrix will be returned in column major order.
     *
     * The locations assigned to uniform variables are not known until the program object is linked. After linking has occurred, the command getUniformLocation can be used to obtain the location of a uniform variable. This location value can then be passed to getUniform in order to query the current value of the uniform variable. After a program object has been linked successfully, the index values for uniform variables remain fixed until the next link command occurs. The uniform variable values can only be queried after a link if the link was successful.</p>
     * @param {object} program - Specifies the program object to be queried.
     * @param {object} location - Specifies the location of the uniform variable to be queried.
     * @return {any}
     * @public
     * @since 3
     *
     */
    public getUniform(program: object, location: object): object;
    /**
     * <p>getUniformLocation returns an integer that represents the location of a specific uniform variable within a program object. name must be a null terminated string that contains no white space. name must be an active uniform variable name in program that is not a structure, an array of structures, or a subcomponent of a vector or a matrix. This function returns -1 if name does not correspond to an active uniform variable in program or if name starts with the reserved prefix "gl_".
     *
     * Uniform variables that are structures or arrays of structures may be queried by calling getUniformLocation for each field within the structure. The array element operator "[]" and the structure field operator "." may be used in name in order to select elements within an array or fields within a structure. The result of using these operators is not allowed to be another structure, an array of structures, or a subcomponent of a vector or a matrix. Except if the last part of name indicates a uniform variable array, the location of the first element of an array can be retrieved by using the name of the array, or by using the name appended by "[0]".
     *
     * The actual locations assigned to uniform variables are not known until the program object is linked successfully. After linking has occurred, the command getUniformLocation can be used to obtain the location of a uniform variable. This location value can then be passed to uniform to set the value of the uniform variable or to getUniform in order to query the current value of the uniform variable. After a program object has been linked successfully, the index values for uniform variables remain fixed until the next link command occurs. Uniform variable locations and values can only be queried after a link if the link was successful.</p>
     * @param {yunos.graphics.WebglContext.WebGLProgram} program - Specifies the program object to be queried.
     * @param {string} name - Points to a null terminated string containing the name of the uniform variable whose location is to be queried.
     * @return {Object} A WebGLUniformLocation object indicating the location of the variable name if found. Returns null otherwise.
     * @public
     * @since 3
     *
     */
    public getUniformLocation(program: object, name: string): object;
    /**
     * <p>getVertexAttrib returns in params the value of a generic vertex attribute parameter. The generic vertex attribute to be queried is specified by index, and the parameter to be queried is specified by pname.
     *
     * The accepted parameter names are as follows:
     *
     * VERTEX_ATTRIB_ARRAY_BUFFER_BINDING
     * params returns a single value, the name of the buffer object currently bound to the binding point corresponding to generic vertex attribute array index. If no buffer object is bound, 0 is returned. The initial value is 0.
     *
     * VERTEX_ATTRIB_ARRAY_ENABLED
     * params returns a single value that is non-zero (true) if the vertex attribute array for index is enabled and 0 (false) if it is disabled. The initial value is FALSE.
     *
     * VERTEX_ATTRIB_ARRAY_SIZE
     * params returns a single value, the size of the vertex attribute array for index. The size is the number of values for each element of the vertex attribute array, and it will be 1, 2, 3, or 4. The initial value is 4.
     *
     * VERTEX_ATTRIB_ARRAY_STRIDE
     * params returns a single value, the array stride for (number of bytes between successive elements in) the vertex attribute array for index. A value of 0 indicates that the array elements are stored sequentially in memory. The initial value is 0.
     *
     * VERTEX_ATTRIB_ARRAY_TYPE
     * params returns a single value, a symbolic constant indicating the array type for the vertex attribute array for index. Possible values are BYTE, UNSIGNED_BYTE, SHORT, UNSIGNED_SHORT, FIXED, and FLOAT. The initial value is FLOAT.
     *
     * VERTEX_ATTRIB_ARRAY_NORMALIZED
     * params returns a single value that is non-zero (true) if fixed-point data types for the vertex attribute array indicated by index are normalized when they are converted to floating point, and 0 (false) otherwise. The initial value is FALSE.
     *
     * CURRENT_VERTEX_ATTRIB
     * params returns four values that represent the current value for the generic vertex attribute specified by index. The initial value is (0,0,0,1).
     *
     * All of the parameters except CURRENT_VERTEX_ATTRIB represent client-side state.</p>
     * @param {number} index - Specifies the generic vertex attribute parameter to be queried.
     * @param {number} pname - Specifies the symbolic name of the vertex attribute parameter to be queried. Accepted values are VERTEX_ATTRIB_ARRAY_BUFFER_BINDING, VERTEX_ATTRIB_ARRAY_ENABLED, VERTEX_ATTRIB_ARRAY_SIZE, VERTEX_ATTRIB_ARRAY_STRIDE, VERTEX_ATTRIB_ARRAY_TYPE, VERTEX_ATTRIB_ARRAY_NORMALIZED, or CURRENT_VERTEX_ATTRIB.
     * @return {any} Returns the requested vertex attribute information (as specified with pname).
     * @public
     * @since 3
     *
     */
    public getVertexAttrib(index: number, pname: number): Object;
    /**
     * <p>The WebGLRenderingContext.getVertexAttribOffset() method of the WebGL API returns the address of a specified vertex attribute.</p>
     * @param {number} index - A GLuint specifying the index of the vertex attribute.
     * @param {number} pname - A GLenum which must be gl.VERTEX_ATTRIB_ARRAY_POINTER.
     * @return {number} A GLintptr indicating the address of the vertex attribute.
     * @public
     * @since 3
     *
     */
    public getVertexAttribOffset(index: number, pname: number): number;
    /**
     * <p>uniform modifies the value of a uniform variable or a uniform variable array. The location of the uniform variable to be modified is specified by location, which should be a value returned by getUniformLocation. uniform operates on the program object that was made part of current state by calling useProgram.
     *
     * The commands uniform{1|2|3|4}{f|i} are used to change the value of the uniform variable specified by location using the values passed as arguments. The number specified in the command should match the number of components in the data type of the specified uniform variable (e.g., 1 for float, int, bool; 2 for vec2, ivec2, bvec2, etc.). The suffix f indicates that floating-point values are being passed; the suffix i indicates that integer values are being passed, and this type should also match the data type of the specified uniform variable. The i variants of this function should be used to provide values for uniform variables defined as int, ivec2, ivec3, ivec4, or arrays of these. The f variants should be used to provide values for uniform variables of type float, vec2, vec3, vec4, or arrays of these. Either the i or the f variants may be used to provide values for uniform variables of type bool, bvec2, bvec3, bvec4, or arrays of these. The uniform variable will be set to false if the input value is 0 or 0.0f, and it will be set to true otherwise.
     *
     * All active uniform variables defined in a program object are initialized to 0 when the program object is linked successfully. They retain the values assigned to them by a call to uniform until the next successful link operation occurs on the program object, when they are once again initialized to 0.
     *
     * The commands uniform{1|2|3|4}{f|i}v can be used to modify a single uniform variable or a uniform variable array. These commands pass a count and a pointer to the values to be loaded into a uniform variable or a uniform variable array. A count of 1 should be used if modifying the value of a single uniform variable, and a count of 1 or greater can be used to modify an entire array or part of an array. When loading n elements starting at an arbitrary position m in a uniform variable array, elements m + n - 1 in the array will be replaced with the new values. If m + n - 1 is larger than the size of the uniform variable array, values for all array elements beyond the end of the array will be ignored. The number specified in the name of the command indicates the number of components for each element in value, and it should match the number of components in the data type of the specified uniform variable (e.g., 1 for float, int, bool; 2 for vec2, ivec2, bvec2, etc.). The data type specified in the name of the command must match the data type for the specified uniform variable as described previously for uniform{1|2|3|4}{f|i}.
     *
     * For uniform variable arrays, each element of the array is considered to be of the type indicated in the name of the command (e.g., uniform3f or uniform3fv can be used to load a uniform variable array of type vec3). The number of elements of the uniform variable array to be modified is specified by count
     *
     * The commands uniformMatrix{2|3|4}fv are used to modify a matrix or an array of matrices. The numbers in the command name are interpreted as the dimensionality of the matrix. The number 2 indicates a 2 × 2 matrix (i.e., 4 values), the number 3 indicates a 3 × 3 matrix (i.e., 9 values), and the number 4 indicates a 4 × 4 matrix (i.e., 16 values). Each matrix is assumed to be supplied in column major order. The count argument indicates the number of matrices to be passed. A count of 1 should be used if modifying the value of a single matrix, and a count greater than 1 can be used to modify an array of matrices.</p>
     * @param {Object} location - A WebGLUniformLocation object containing the location of the uniform attribute to modify.
     * @param {boolean} transpose - A GLboolean specifying whether to transpose the matrix. Must be false.
     * @param {number[]} v
     * @param {number} v0
     * @param {number} v1
     * @param {number} v2
     * @param {number} v3
     * @public
     * @since 3
     *
     */
    public uniform1f(location: object, x: number): void;
    /**
     * @param {object} location
     * @param {number} x
     * @param {number} y
     * @public
     * @since 3
     *
     */
    public uniform2f(location: object, x: number, y: number): void;
    /**
     * @param {object} location
     * @param {number} x
     * @param {number} y
     * @param {number} z
     * @public
     * @since 3
     *
     */
    public uniform3f(location: object, x: number, y: number, z: number): void;
    /**
     * @param {object} location
     * @param {number} x
     * @param {number} y
     * @param {number} z
     * @param {number} w
     * @public
     * @since 3
     *
     */
    public uniform4f(location: object, x: number, y: number, z: number, w: number): void;
    /**
     * @param {object} location
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniform1fv(location: object, v: Array<number> | Float32Array): void;
    /**
     * @param {object} location
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniform2fv(location: object, v: Array<number> | Float32Array): void;
    /**
     * @param {object} location
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniform3fv(location: object, v: Array<number> | Float32Array): void;
    /**
     * @param {object} location
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniform4fv(location: object, v: Array<number> | Float32Array): void;
    /**
     * @param {object} location
     * @param {number | boolean} v
     * @public
     * @since 3
     *
     */
    public uniform1i(location: object, x: number | boolean): void;
    /**
     * @param {object} location
     * @param {number} x
     * @param {number} y
     * @public
     * @since 3
     *
     */
    public uniform2i(location: object, x: number, y: number): void;
    /**
     * @param {object} location
     * @param {number} x
     * @param {number} y
     * @param {number} z
     * @public
     * @since 3
     *
     */
    public uniform3i(location: object, x: number, y: number, z: number): void;
    /**
     * @param {object} location
     * @param {number} x
     * @param {number} y
     * @param {number} z
     * @param {number} w
     * @public
     * @since 3
     *
     */
    public uniform4i(location: object, x: number, y: number, z: number, w: number): void;
    /**
     * @param {object} location
     * @param {Array<number> | Int32Array} v
     * @public
     * @since 3
     *
     */
    public uniform1iv(location: object, v: Array<number> | Int32Array): void;
    /**
     * @param {object} location
     * @param {Array<number> | Int32Array} v
     * @public
     * @since 3
     *
     */
    public uniform2iv(location: object, v: Array<number> | Int32Array): void;
    /**
     * @param {object} location
     * @param {Array<number> | Int32Array} v
     * @public
     * @since 3
     *
     */
    public uniform3iv(location: object, v: Array<number> | Int32Array): void;
    /**
     * @param {object} location
     * @param {Array<number> | Int32Array} v
     * @public
     * @since 3
     *
     */
    public uniform4iv(location: object, v: Array<number> | Int32Array): void;
    /**
     * @param {object} location
     * @param {boolean | number} transpose
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniformMatrix2fv(location: object, transpose: boolean | number, v: Array<number> | Float32Array): void;
    /**
     * @param {object} location
     * @param {boolean | number} transpose
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniformMatrix3fv(location: object, transpose: boolean | number, v: Array<number> | Float32Array): void;
    /**
     * @param {object} location
     * @param {boolean | number} transpose
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public uniformMatrix4fv(location: object, transpose: boolean | number, v: Array<number> | Float32Array): void;
    /**
     * <p>The vertexAttrib family of entry points allows an application to pass generic vertex attributes in numbered locations.
     *
     * Generic attributes are defined as four-component values that are organized into an array. The first entry of this array is numbered 0, and the size of the array is specified by the implementation-dependent symbolic constant MAX_VERTEX_ATTRIBS. Individual elements of this array can be modified with a vertexAttrib call that specifies the index of the element to be modified and a value for that element.
     *
     * These commands can be used to specify one, two, three, or all four components of the generic vertex attribute specified by index. A 1 in the name of the command indicates that only one value is passed, and it will be used to modify the first component of the generic vertex attribute. The second and third components will be set to 0, and the fourth component will be set to 1. Similarly, a 2 in the name of the command indicates that values are provided for the first two components, the third component will be set to 0, and the fourth component will be set to 1. A 3 in the name of the command indicates that values are provided for the first three components and the fourth component will be set to 1, whereas a 4 in the name indicates that values are provided for all four components.
     *
     * The letter f indicates that the arguments are of type float. When v is appended to the name, the commands can take a pointer to an array of floats.
     *
     * OpenGL ES Shading Language attribute variables are allowed to be of type mat2, mat3, or mat4. Attributes of these types may be loaded using the vertexAttrib entry points. Matrices must be loaded into successive generic attribute slots in column major order, with one column of the matrix in each generic attribute slot.
     *
     * A user-defined attribute variable declared in a vertex shader can be bound to a generic attribute index by calling bindAttribLocation. This allows an application to use descriptive variable names in a vertex shader. A subsequent change to the specified generic vertex attribute will be immediately reflected as a change to the corresponding attribute variable in the vertex shader.
     *
     * The binding between a generic vertex attribute index and a user-defined attribute variable in a vertex shader is part of the state of a program object, but the current value of the generic vertex attribute is not. The value of each generic vertex attribute is part of current state and it is maintained even if a different program object is used.
     *
     * An application may freely modify generic vertex attributes that are not bound to a named vertex shader attribute variable. These values are simply maintained as part of current state and will not be accessed by the vertex shader. If a generic vertex attribute bound to an attribute variable in a vertex shader is not updated while the vertex shader is executing, the vertex shader will repeatedly use the current value for the generic vertex attribute.
     *
     * </p>
     * @param {number} index - A GLuint specifying the position of the vertex attribute to be modified.
     * @param {number[]} v
     * @param {number} v0
     * @param {number} v1
     * @param {number} v2
     * @param {number} v3
     * @public
     * @since 3
     *
     */
    public vertexAttrib1f(index: number, x: number): void;
    /**
     * @param {number} index
     * @param {number} x
     * @param {number} y
     * @public
     * @since 3
     *
     */
    public vertexAttrib2f(index: number, x: number, y: number): void;
    /**
     * @param {number} index
     * @param {number} x
     * @param {number} y
     * @param {number} z
     * @public
     * @since 3
     *
     */
    public vertexAttrib3f(index: number, x: number, y: number, z: number): void;
    /**
     * @param {number} index
     * @param {number} x
     * @param {number} y
     * @param {number} z
     * @param {number} w
     * @public
     * @since 3
     *
     */
    public vertexAttrib4f(index: number, x: number, y: number, z: number, w: number): void;
    /**
     * @param {number} index
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public vertexAttrib1fv(index: number, v: Array<number> | Float32Array): void;
    /**
     * @param {number} index
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public vertexAttrib2fv(index: number, v: Array<number> | Float32Array): void;
    /**
     * @param {number} index
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public vertexAttrib3fv(index: number, v: Array<number> | Float32Array): void;
    /**
     * @param {number} index
     * @param {Array<number> | Float32Array} v
     * @public
     * @since 3
     *
     */
    public vertexAttrib4fv(index: number, v: Array<number> | Float32Array): void;
    /**
     * <p>vertexAttribPointer specifies the location and data format of the array of generic vertex attributes at index index to use when rendering. size specifies the number of components per attribute and must be 1, 2, 3, or 4. type specifies the data type of each component, and stride specifies the byte stride from one attribute to the next, allowing vertices and attributes to be packed into a single array or stored in separate arrays. If set to TRUE, normalized indicates that values stored in an integer format are to be mapped to the range [-1,1] (for signed values) or [0,1] (for unsigned values) when they are accessed and converted to floating point. Otherwise, values will be converted to floats directly without normalization.
     *
     * If a non-zero named buffer object is bound to the ARRAY_BUFFER target (see bindBuffer) while a generic vertex attribute array is specified, pointer is treated as a byte offset into the buffer object's data store. Also, the buffer object binding (ARRAY_BUFFER_BINDING) is saved as generic vertex attribute array client-side state (VERTEX_ATTRIB_ARRAY_BUFFER_BINDING) for index index.
     *
     * When a generic vertex attribute array is specified, size, type, normalized, stride, and pointer are saved as client-side state, in addition to the current vertex array buffer object binding.
     *
     * To enable and disable a generic vertex attribute array, call enableVertexAttribArray and disableVertexAttribArray with index. If enabled, the generic vertex attribute array is used when drawArrays or drawElements is called.</p>
     * @param {number} index - Specifies the index of the generic vertex attribute to be modified.
     * @param {number} size - Specifies the number of components per generic vertex attribute. Must be 1, 2, 3, or 4. The initial value is 4.
     * @param {number} type - Specifies the data type of each component in the array. Symbolic constants BYTE, UNSIGNED_BYTE, SHORT, UNSIGNED_SHORT, FIXED, or FLOAT are accepted. The initial value is FLOAT.
     * @param {number} normalized - Specifies whether fixed-point data values should be normalized (TRUE) or converted directly as fixed-point values (FALSE) when they are accessed.
     * @param {number} stride - Specifies the byte offset between consecutive generic vertex attributes. If stride is 0, the generic vertex attributes are understood to be tightly packed in the array. The initial value is 0.
     * @param {number} offset - Specifies a pointer to the first component of the first generic vertex attribute in the array. The initial value is 0.
     * @throws {INVALID_VALUE} A gl.INVALID_VALUE error is thrown if offset is negative.
     * @throws {INVALID_OPERATION} A gl.INVALID_OPERATION error is thrown if stride and offset are not multiples of the size of the data type.
     * @throws {INVALID_OPERATION} A gl.INVALID_OPERATION error is thrown if no WebGLBuffer is bound to the ARRAY_BUFFER target.
     * @public
     * @since 3
     *
     */
    public vertexAttribPointer(index: number, size: number, type: number, normalized: number, stride: number, offset: number): void;
    /**
     * <p>bindBuffer lets you create or use a named buffer object. Calling bindBuffer with target set to ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER and buffer set to the name of the new buffer object binds the buffer object name to the target. When a buffer object is bound to a target, the previous binding for that target is automatically broken.</p>
     *
     * <p>Buffer object names are unsigned integers. The value zero is reserved, but there is no default buffer object for each buffer object target. Instead, buffer set to zero effectively unbinds any buffer object previously bound, and restores client memory usage for that buffer object target. Buffer object names and the corresponding buffer object contents are local to the shared object space of the current GL rendering context.</p>
     *
     * <p>You may use genBuffers to generate a set of new buffer object names.</p>
     *
     * <p>The state of a buffer object immediately after it is first bound is a zero-sized memory buffer with STATIC_DRAW usage.</p>
     *
     * <p>While a non-zero buffer object name is bound, GL operations on the target to which it is bound affect the bound buffer object, and queries of the target to which it is bound return state from the bound buffer object. While buffer object name zero is bound, as in the initial state, attempts to modify or query state on the target to which it is bound generates an INVALID_OPERATION error.</p>
     *
     * <p>When vertex array pointer state is changed by a call to vertexAttribPointer, the current buffer object binding (ARRAY_BUFFER_BINDING) is copied into the corresponding client state for the vertex attrib array being changed, one of the indexed VERTEX_ATTRIB_ARRAY_BUFFER_BINDINGs. While a non-zero buffer object is bound to the ARRAY_BUFFER target, the vertex array pointer parameter that is traditionally interpreted as a pointer to client-side memory is instead interpreted as an offset within the buffer object measured in basic machine units.</p>
     *
     * <p>While a non-zero buffer object is bound to the ELEMENT_ARRAY_BUFFER target, the indices parameter of drawElements that is traditionally interpreted as a pointer to client-side memory is instead interpreted as an offset within the buffer object measured in basic machine units.</p>
     *
     * <p>A buffer object binding created with bindBuffer remains active until a different buffer object name is bound to the same target, or until the bound buffer object is deleted with deleteBuffers.</p>
     *
     * <p>Once created, a named buffer object may be re-bound to any target as often as needed. However, the GL implementation may make choices about how to optimize the storage of a buffer object based on its initial binding target.</p>
     * @param {number} target - Specifies the target to which the buffer object is bound. The symbolic constant must be ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER.
     * @param {Object} buffer - Specifies the name of a buffer object.
     * @throws {INVALID_OPERATION} Only one target can be bound to a given WebGLBuffer. An attempt to bind the buffer to another target will throw an INVALID_OPERATION error and the current buffer binding will remain the same.
     * @public
     * @since 3
     *
     */
    public bindBuffer(target: number, buffer: object): void;
    /**
     * <p>bufferData creates a new data store for the buffer object currently bound to target. Any pre-existing data store is deleted. The new data store is created with the specified size in bytes and usage. If data is not NULL, the data store is initialized with data from this pointer.</p>
     *
     * <p>usage is a hint to the GL implementation as to how a buffer object's data store will be accessed. This enables the GL implementation to make more intelligent decisions that may significantly impact buffer object performance. It does not, however, constrain the actual usage of the data store. usage can be broken down into two parts: first, the frequency of access (modification and usage), and second, the nature of that access. The frequency of access may be one of these:</p>
     *
     * <p>STREAM</p>
     * <p>The data store contents will be modified once and used at most a few times.</p>
     *
     * <p>STATIC</p>
     * <p>The data store contents will be modified once and used many times.</p>
     *
     * <p>DYNAMIC</p>
     * <p>The data store contents will be modified repeatedly and used many times.</p>
     *
     * <p>The nature of access must be:</p>
     *
     * <p>DRAW</p>
     * <p>The data store contents are modified by the application, and used as the source for GL drawing and image specification commands.</p>
     * @param {number} target - Specifies the target buffer object. The symbolic constant must be ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER.
     * @param {number | Array | ArrayBuffer} data - Specifies a pointer to data that will be copied into the data store for initialization, or NULL if no data is to be copied.
     * @param {number} usage - Specifies the expected usage pattern of the data store. The symbolic constant must be STREAM_DRAW, STATIC_DRAW, or DYNAMIC_DRAW.
     * @throws {OUT_OF_MEMORY} A gl.OUT_OF_MEMORY error is thrown if the context is unable to create a data store with the given size.
     * @throws {INVALID_VALUE} A gl.INVALID_VALUE error is thrown if size is negative.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target or usage are not one of the allowed enums.
     * @public
     * @since 3
     *
     */
    public bufferData(target: number, data: number | Array<number> | ArrayBuffer, usage: number): void;
    /**
     * <p>bufferSubData redefines some or all of the data store for the buffer object currently bound to target. Data starting at byte offset offset and extending for size bytes is copied to the data store from the memory pointed to by data. An error is thrown if offset and size together define a range beyond the bounds of the buffer object's data store.</p>
     * @param {number} target - Specifies the target buffer object. The symbolic constant must be ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER.
     * @param {number} offset - Specifies the offset into the buffer object's data store where data replacement will begin, measured in bytes.
     * @param {number | Array | ArrayBuffer} srcData - Specifies a pointer to the new data that will be copied into the data store.
     * @throws {INVALID_VALUE} A gl.INVALID_VALUE error is thrown if the data would be written past the end of the buffer or if data is null.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target is not one of the allowed enums.
     * @public
     * @since 3
     *
     */
    public bufferSubData(target: number, offset: number, srcData: number | Array<number> | ArrayBuffer): void;
    /**
     * <p> createBuffer method creates and initializes a WebGLBuffer storing data such as vertices or colors.</p>
     * @return {Object} A WebGLBuffer storing data such as vertices or colors.
     * @public
     * @since 3
     *
     */
    public createBuffer(): object;
    /**
     * <p>deleteBuffers deletes n buffer objects named by the elements of the array buffers. After a buffer object is deleted, it has no contents, and its name is free for reuse (for example by genBuffers). If a buffer object that is currently bound is deleted, the binding reverts to 0 (the absence of any buffer object, which reverts to client memory usage).</p>
     *
     * <p>deleteBuffers silently ignores 0's and names that do not correspond to existing buffer objects.</p>
     * @param {Object} buffer - Specifies an array of buffer objects to be deleted.
     * @public
     * @since 3
     *
     */
    public deleteBuffer(buffer: object): void;
    /**
     * <p>getBufferParameteriv returns in data a selected parameter of the buffer object specified by target.</p>
     *
     * <p>value names a specific buffer object parameter, as follows:</p>
     *
     * <p>BUFFER_SIZE</p>
     * <p>  params returns the size of the buffer object, measured in bytes. The initial value is 0.</p>
     *
     * <p>BUFFER_USAGE</p>
     * <p>  params returns the buffer object's usage pattern. The initial value is STATIC_DRAW.</p>
     * @param {number} target - Specifies the target buffer object. The symbolic constant must be ARRAY_BUFFER or ELEMENT_ARRAY_BUFFER.
     * @param {number} pname - BUFFER_SIZE or BUFFER_USAGE
     * @return {number} Depends on the requested information (as specified with pname). Either a GLint or a GLenum.
     * @public
     * @since 3
     *
     */
    public getBufferParameter(target: number, pname: number): number;
    /**
     * <p>isBuffer returns TRUE if buffer is currently the name of a buffer object. If buffer is zero, or is a non-zero value that is not currently the name of a buffer object, or if an error occurs, isBuffer returns FALSE.</p>
     *
     * <p>A name returned by genBuffers, but not yet associated with a buffer object by calling bindBuffer, is not the name of a buffer object.</p>
     * @param {Object} buffer - Specifies a value that may be the name of a buffer object.
     * @return {boolean} A GLboolean indicating whether or not the buffer is valid.
     * @public
     * @since 3
     *
     */
    public isBuffer(buffer: object): boolean;
    /**
     * <p>bindFramebuffer lets you create or use a named framebuffer object. Calling bindFramebuffer with target set to FRAMEBUFFER and framebuffer set to the name of the new framebuffer object binds the framebuffer object name. When a framebuffer object is bound, the previous binding is automatically broken.</p>
     *
     * <p>Framebuffer object names are unsigned integers. The value zero is reserved to represent the default framebuffer provided by the windowing system. Framebuffer object names and the corresponding framebuffer object contents are local to the shared object space of the current GL rendering context.</p>
     *
     * <p>You may use genFramebuffers to generate a set of new framebuffer object names.</p>
     *
     * <p>The state of a framebuffer object immediately after it is first bound is three attachment points (COLOR_ATTACHMENT0, DEPTH_ATTACHMENT, and STENCIL_ATTACHMENT) each with NONE as the object type.</p>
     *
     * <p>While a non-zero framebuffer object name is bound, GL operations on target FRAMEBUFFER affect the bound framebuffer object, and queries of target FRAMEBUFFER or of framebuffer details such as DEPTH_BITS return state from the bound framebuffer object. While framebuffer object name zero is bound, as in the initial state, attempts to modify or query state on target FRAMEBUFFER generates an INVALID_OPERATION error.</p>
     *
     * <p>While a non-zero framebuffer object name is bound, all rendering to the framebuffer (with drawArrays and drawElements) and reading from the framebuffer (with readPixels, copyTexImage2D, or copyTexSubImage2D) use the images attached to the application-created framebuffer object rather than the default window-system-provided framebuffer.</p>
     *
     * <p>Application created framebuffer objects (i.e. those with a non-zero name) differ from the default window-system-provided framebuffer in a few important ways. First, they have modifiable attachment points for a color buffer, a depth buffer, and a stencil buffer to which framebuffer attachable images may be attached and detached. Second, the size and format of the attached images are controlled entirely within the GL and are not affected by window-system events, such as pixel format selection, window resizes, and display mode changes. Third, when rendering to or reading from an application created framebuffer object, the pixel ownership test always succeeds (i.e. they own all their pixels). Fourth, there are no visible color buffer bitplanes, only a single "off-screen" color image attachment, so there is no sense of front and back buffers or swapping. Finally, there is no multisample buffer, so the value of the implementation-dependent state variables SAMPLES and SAMPLE_BUFFERS are both zero for application created framebuffer objects.</p>
     *
     * <p>A framebuffer object binding created with bindFramebuffer remains active until a different framebuffer object name is bound, or until the bound framebuffer object is deleted with deleteFramebuffers.</p>
     * @param {number} target - Specifies the target to which the framebuffer object is bound. The symbolic constant must be FRAMEBUFFER.
     * @param {Object} framebuffer - Specifies the name of a framebuffer object.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target is not gl.FRAMEBUFFER, gl.DRAW_FRAMEBUFFER, or gl.READ_FRAMEBUFFER.
     * @public
     * @since 3
     *
     */
    public bindFramebuffer(target: number, framebuffer: object): void;
    /**
     * <p>checkFramebufferStatus returns a symbolic constant that identifies whether or not the currently bound framebuffer is framebuffer complete, and if not, which of the rules of framebuffer completeness is violated.</p>
     *
     * <p>If the framebuffer is complete, then FRAMEBUFFER_COMPLETE is returned. If the framebuffer is not complete, the return values are as follows:</p>
     *
     * <p>FRAMEBUFFER_INCOMPLETE_ATTACHMENT</p>
     * <p>  Not all framebuffer attachment points are framebuffer attachment complete. This means that at least one attachment point with a renderbuffer or texture attached has its attached object no longer in existence or has an attached image with a width or height of zero, or the color attachment point has a non-color-renderable image attached, or the depth attachment point has a non-depth-renderable image attached, or the stencil attachment point has a non-stencil-renderable image attached.</p>
     *
     * <p>Color-renderable formats include RGBA4, RGB5_A1, and RGB565. DEPTH_COMPONENT16 is the only depth-renderable format. STENCIL_INDEX8 is the only stencil-renderable format.</p>
     *
     * <p>FRAMEBUFFER_INCOMPLETE_DIMENSIONS</p>
     * <p>  Not all attached images have the same width and height.</p>
     *
     * <p>FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT</p>
     * <p>  No images are attached to the framebuffer.</p>
     *
     * <p>FRAMEBUFFER_UNSUPPORTED</p>
     * <p>  The combination of internal formats of the attached images violates an implementation-dependent set of restrictions.</p>
     *
     * <p>If the currently bound framebuffer is not framebuffer complete, then it is an error to attempt to use the framebuffer for writing or reading. This means that rendering commands (clear, drawArrays, and drawElements) as well as commands that read the framebuffer (readPixels, copyTexImage2D, and copyTexSubImage2D) will generate the error INVALID_FRAMEBUFFER_OPERATION if called while the framebuffer is not framebuffer complete.</p>
     * @param {number} target - Specifies the target framebuffer object. The symbolic constant must be FRAMEBUFFER.
     * @return {number} A GLenum indicating the completeness status of the framebuffer or 0 if an error occurs.
     * @public
     * @since 3
     *
     */
    public checkFramebufferStatus(framebuffer: number): number;
    /**
     * <p>genFramebuffers returns n framebuffer object names in framebuffers. There is no guarantee that the names form a contiguous set of integers; however, it is guaranteed that none of the returned names was in use immediately before the call to genFramebuffers.</p>
     *
     * <p>Framebuffer object names returned by a call to genFramebuffers are not returned by subsequent calls, unless they are first deleted with deleteFramebuffers.</p>
     *
     * <p>No framebuffer objects are associated with the returned framebuffer object names until they are first bound by calling bindFramebuffer.</p>
     * @return {Object} A WebGLFramebuffer object create by context.
     * @public
     * @since 3
     *
     */
    public createFramebuffer(): object;
    /**
     * <p>deleteFramebuffers deletes n framebuffer objects named by the elements of the array framebuffers. After a framebuffer object is deleted, it has no attachments, and its name is free for reuse (for example by genFramebuffers). If a framebuffer object that is currently bound is deleted, the binding reverts to 0 (the window-system-provided framebuffer).</p>
     *
     * <p>deleteFramebuffers silently ignores 0's and names that do not correspond to existing framebuffer objects.</p>
     * @param {Object} framebuffer - Specifies an array of framebuffer objects to be deleted.
     * @public
     * @since 3
     *
     */
    public deleteFramebuffer(framebuffer: object): void;
    /**
     * <p>framebufferRenderbuffer attaches the renderbuffer specified by renderbuffer as one of the logical buffers of the currently bound framebuffer object. attachment specifies whether the renderbuffer should be attached to the framebuffer object's color, depth, or stencil buffer. A renderbuffer may not be attached to the default framebuffer object name 0.</p>
     *
     * <p>If renderbuffer is not 0, the value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE for the specified attachment point is set to RENDERBUFFER and the value of FRAMEBUFFER_ATTACHMENT_OBJECT_NAME is set to renderbuffer. FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL and FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE are set to the default values 0 and TEXTURE_CUBE_MAP_POSITIVE_X, respectively. Any previous attachment to the attachment logical buffer of the currently bound framebuffer object is broken.</p>
     *
     * <p>If renderbuffer is 0, the current image, if any, attached to the attachment logical buffer of the currently bound framebuffer object is detached. The value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE is set to NONE. The value of FRAMEBUFFER_ATTACHMENT_OBJECT_NAME is set to 0. FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL and FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE are set to the default values 0 and TEXTURE_CUBE_MAP_POSITIVE_X, respectively.</p>
     * @param {number} target - Specifies the framebuffer target. The symbolic constant must be FRAMEBUFFER.
     * @param {number} attachment - Specifies the attachment point to which renderbuffer should be attached. Must be one of the following symbolic constants: COLOR_ATTACHMENT0, DEPTH_ATTACHMENT, or STENCIL_ATTACHMENT.
     * @param {number} renderbuffertarget - Specifies the renderbuffer target. The symbolic constant must be RENDERBUFFER.
     * @param {Object} renderbuffer - Specifies the renderbuffer object that is to be attached.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target is not gl.FRAMEBUFFER, gl.DRAW_FRAMEBUFFER, or gl.READ_FRAMEBUFFER.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if renderbuffertarget is not gl.RENDERBUFFER.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if attachment is not one of the allowed enums.
     * @public
     * @since 3
     *
     */
    public framebufferRenderbuffer(target: number, attachment: number, renderbuffertarget: number, renderbuffer: object): void;
    /**
     * <p>framebufferTexture2D attaches the texture image specified by texture and level as one of the logical buffers of the currently bound framebuffer object. attachment specifies whether the texture image should be attached to the framebuffer object's color, depth, or stencil buffer. A texture image may not be attached to the default framebuffer object name 0.</p>
     *
     * <p>If texture is not 0, the value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE for the specified attachment point is set to TEXTURE, the value of FRAMEBUFFER_ATTACHMENT_OBJECT_NAME is set to texture, and the value of FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL is set to level. If texture is a cube map texture, the value of FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE is set to textarget; otherwise it is set to the default value TEXTURE_CUBE_MAP_POSITIVE_X. Any previous attachment to the attachment logical buffer of the currently bound framebuffer object is broken.</p>
     *
     * <p>If texture is 0, the current image, if any, attached to the attachment logical buffer of the currently bound framebuffer object is detached. The value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE is set to NONE. The value of FRAMEBUFFER_ATTACHMENT_OBJECT_NAME is set to 0. FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL and FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE are set to the default values 0 and TEXTURE_CUBE_MAP_POSITIVE_X, respectively.</p>
     * @param {number} target - Specifies the framebuffer target. The symbolic constant must be FRAMEBUFFER.
     * @param {number} attachment - Specifies the attachment point to which an image from texture should be attached. Must be one of the following symbolic constants: COLOR_ATTACHMENT0, DEPTH_ATTACHMENT, or STENCIL_ATTACHMENT.
     * @param {number} textarget - Specifies the texture target. Must be one of the following symbolic constants: TEXTURE_2D, TEXTURE_CUBE_MAP_POSITIVE_X, TEXTURE_CUBE_MAP_NEGATIVE_X, TEXTURE_CUBE_MAP_POSITIVE_Y, TEXTURE_CUBE_MAP_NEGATIVE_Y, TEXTURE_CUBE_MAP_POSITIVE_Z, or TEXTURE_CUBE_MAP_NEGATIVE_Z.
     * @param {Object} texture - Specifies the texture object whose image is to be attached.
     * @param {number} level - Specifies the mipmap level of the texture image to be attached, which must be 0.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target is not gl.FRAMEBUFFER.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if attachment is not one of the accepted attachment points.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if textarget is not one of the accepted texture targets.
     * @throws {INVALID_VALUE} A gl.INVALID_VALUE error is thrown if level is not 0.
     * @throws {INVALID_OPERATION} A gl.INVALID_OPERATION error is thrown if texture isn't 0 or the name of an existing texture object.
     * @public
     * @since 3
     *
     */
    public framebufferTexture2D(target: number, attachment: number, textarget: number, texture: object, level: number): void;
    /**
     * <p>getFramebufferAttachmentParameteriv returns in params a selected attachment parameter of the attachpoint point attachment of the currently bound framebuffer object.</p>
     *
     * <p>pname names a specific framebuffer object attachment parameter, as follows:</p>
     *
     * <p>FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE</p>
     * <p>params returns the type of object which contains the attached image, either RENDERBUFFER, TEXTURE, or if no image is attached, NONE. The initial value is NONE.</p>
     *
     * <p>FRAMEBUFFER_ATTACHMENT_OBJECT_NAME</p>
     * <p>If the value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE is RENDERBUFFER, params returns the name of the renderbuffer object which contains the attached image. If the value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE is TEXTURE, params returns the name of the texture object which contains the attached image. The initial value is zero.</p>
     *
     * <p>FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL</p>
     * <p>If the value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE is TEXTURE, params returns the mipmap level of the texture object which contains the attached image. The initial value is zero.</p>
     *
     * <p>FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE</p>
     * <p>If the value of FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE is TEXTURE and FRAMEBUFFER_ATTACHMENT_OBJECT_NAME is the name of a cube-map texture, params returns the cube map face of the cube-map texture object which contains the attached image. If the attached image is from a texture object but not a cube-map, params returns 0. The initial value is TEXTURE_CUBE_MAP_POSITIVE_X.</p>
     * @param {number} target - Specifies the target framebuffer object. The symbolic constant must be FRAMEBUFFER.
     * @param {number} attachment - Specifies the symbolic name of a framebuffer object attachment point. Accepted values are COLOR_ATTACHMENT0, DEPTH_ATTACHMENT, and STENCIL_ATTACHMENT.
     * @param {number} pname - Specifies the symbolic name of a framebuffer object attachment parameter. Accepted values are FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE, FRAMEBUFFER_ATTACHMENT_OBJECT_NAME, FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL, and FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE.
     * @return {number|Object} Either a GLint, a GLenum, a WebGLRenderbuffer, or a WebGLTexture.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target is not gl.FRAMEBUFFER, gl.DRAW_FRAMEBUFFER, gl.READ_FRAMEBUFFER or if attachment is not one of the accepted attachment points.
     * @public
     * @since 3
     *
     */
    public getFramebufferAttachmentParameter(target: number, attachment: number, pname: number): number | object;
    /**
     * <p>isFramebuffer returns TRUE if framebuffer is currently the name of a framebuffer object. If framebuffer is zero, or is a non-zero value that is not currently the name of a framebuffer object, or if an error occurs, isFramebuffer returns FALSE.</p>
     *
     * A name returned by genFramebuffers, but not yet associated with a framebuffer object by calling bindFramebuffer, is not the name of a framebuffer object.</p>
     * @param {Object} framebuffer - Specifies a value that may be the name of a framebuffer object.
     * @return {boolean} true if the passed WebGLFramebuffer is valid and false otherwise.
     * @public
     * @since 3
     *
     */
    public isFramebuffer(framebuffer: object): boolean;
    /**
     * <p>readPixels and readnPixels return pixel data from the frame buffer, starting with the pixel whose lower left corner is at location (x, y), into client memory starting at location data. Several parameters control the processing of the pixel data before it is placed into client memory. These parameters are set with pixelStore. This reference page describes the effects on readPixels and readnPixels of most, but not all of the parameters specified by these three commands.</p>
     * <p>If a non-zero named buffer object is bound to the PIXEL_PACK_BUFFER target (see bindBuffer) while a block of pixels is requested, data is treated as a byte offset into the buffer object's data store rather than a pointer to client memory.</p>
     * <p>readPixels and readnPixels return values from each pixel with lower left corner at (x+i,y+j)x+iy+j for 0<=i<width0<=i<width and 0<=j<height0<=j<height. This pixel is said to be the iith pixel in the jjth row. Pixels are returned in row order from the lowest to the highest row, left to right in each row.</p>
     * <p>format specifies the format for the returned pixel values; accepted values are:</p>
     * <p>STENCIL_INDEX</p>
     * <p>  Stencil values are read from the stencil buffer.</p>
     * <p>DEPTH_COMPONENT</p>
     * <p>  Depth values are read from the depth buffer. Each component is converted to floating point such that the minimum depth value maps to 0 and the maximum value maps to 1. Each component is clamped to the range [0,1]01.</p>
     * <p>DEPTH_STENCIL</p>
     * <p>  Values are taken from both the depth and stencil buffers. The type parameter must be UNSIGNED_INT_24_8 or FLOAT_32_UNSIGNED_INT_24_8_REV.</p>
     * <p>RED, GREEN, BLUE, RGB, BGR, RGBA, BGRA</p>
     * <p>  Color values are taken from the color buffer.</p>
     * <p>Finally, the indices or components are converted to the proper format, as specified by type. If format is STENCIL_INDEX and type is not FLOAT, each index is masked with the mask value given in the following table. If type is FLOAT, then each integer index is converted to single-precision floating-point format.</p>
     * <p>If format is RED, GREEN, BLUE, RGB, BGR, RGBA, or BGRA and type is not FLOAT, each component is multiplied by the multiplier shown in the following table. If type is FLOAT, then each component is passed as is (or converted to the client's single-precision floating-point format if it is different from the one used by the GL).</p>
     * @param {number} x - A GLint specifying the first horizontal pixel that is read from the lower left corner of a rectangular block of pixels.
     * @param {number} y - A GLint specifying the first vertical pixel that is read from the lower left corner of a rectangular block of pixels.
     * @param {number} width - A GLsizei specifying the width of the rectangle.
     * @param {number} height - A GLsizei specifying the height of the rectangle.
     * @param {number} format - A GLenum specifying the format of the pixel data
     * @param {number} type - A GLenum specifying the data type of the pixel data
     * @param {Float32Array|Uint8Array} pixels - An ArrayBufferView object to read data into. The array type must match the type of the type parameter.
     * @throws {INVALID_ENUM} If format or type is not an accepted value.
     * @throws {INVALID_OPERATION} If type is gl.UNSIGNED_SHORT_5_6_5 and format is not gl.RGB. or type is gl.UNSIGNED_SHORT_4_4_4_4 and format is not gl.RGBA. or type does not match the typed array type of pixels.
     * @throws {INVALID_FRAMEBUFFER_OPERATION} If the currently bound framebuffer is not framebuffer complete.
     * @public
     * @since 3
     *
     */
    public readPixels(x: number, y: number, width: number, height: number, format: number, type: number, pixels: Float32Array | Uint8Array): void;
    /**
     * <p>A renderbuffer is a data storage object containing a single image of a renderable internal format. A renderbuffer's image may be attached to a framebuffer object to use as a destination for rendering and as a source for reading.</p>
     *
     * <p>bindRenderbuffer lets you create or use a named renderbuffer object. Calling bindRenderbuffer with target set to RENDERBUFFER and renderbuffer set to the name of the new renderbuffer object binds the renderbuffer object name. When a renderbuffer object is bound, the previous binding is automatically broken.</p>
     *
     * <p>Renderbuffer object names are unsigned integers. The value zero is reserved, but there is no default renderbuffer object. Instead, renderbuffer set to zero effectively unbinds any renderbuffer object previously bound. Renderbuffer object names and the corresponding renderbuffer object contents are local to the shared object space of the current GL rendering context.</p>
     *
     * <p>You may use genRenderbuffers to generate a set of new renderbuffer object names.</p>
     *
     * <p>The state of a renderbuffer object immediately after it is first bound is a zero-sized memory buffer with format RGBA4 and zero-sized red, green, blue, alpha, depth, and stencil pixel depths.</p>
     *
     * <p>While a non-zero renderbuffer object name is bound, GL operations on target RENDERBUFFER affect the bound renderbuffer object, and queries of target RENDERBUFFER return state from the bound renderbuffer object. While renderbuffer object name zero is bound, as in the initial state, attempts to modify or query state on target RENDERBUFFER generates an INVALID_OPERATION error.</p>
     *
     * <p>A renderbuffer object binding created with bindRenderbuffer remains active until a different renderbuffer object name is bound, or until the bound renderbuffer object is deleted with deleteRenderbuffers.</p>
     * @param {number} target - Specifies the target to which the renderbuffer object is bound. The symbolic constant must be RENDERBUFFER.
     * @param {Object} renderbuffer - Specifies the name of a renderbuffer object.
     * @throws {INVALID_ENUM} A gl.INVALID_ENUM error is thrown if target is not gl.RENDERBUFFER.
     * @public
     * @since 3
     *
     */
    public bindRenderbuffer(target: number, renderbuffer: object): void;
    /**
     * <p>genRenderbuffers returns n renderbuffer object names in renderbuffers. There is no guarantee that the names form a contiguous set of integers; however, it is guaranteed that none of the returned names was in use immediately before the call to genRenderbuffers.</p>
     *
     * <p>Renderbuffer object names returned by a call to genRenderbuffers are not returned by subsequent calls, unless they are first deleted with deleteRenderbuffers.</p>
     *
     * <p>No renderbuffer objects are associated with the returned renderbuffer object names until they are first bound by calling bindRenderbuffer.</p>
     * @return {Object} renderBuffer created by context
     * @public
     * @since 3
     *
     */
    public createRenderbuffer(): object;
    /**
     * <p>deleteRenderbuffers deletes n renderbuffer objects named by the elements of the array renderbuffers. After a renderbuffer object is deleted, it has no contents, and its name is free for reuse (for example by genRenderbuffers).</p>
     *
     * <p>If a renderbuffer object that is currently bound is deleted, the binding reverts to 0 (the absence of any renderbuffer object). Additionally, special care must be taken when deleting a renderbuffer object if the image of the renderbuffer is attached to a framebuffer object. In this case, if the deleted renderbuffer object is attached to the currently bound framebuffer object, it is automatically detached. However, attachments to any other framebuffer objects are the responsibility of the application.</p>
     *
     * <p>deleteRenderbuffers silently ignores 0's and names that do not correspond to existing renderbuffer objects.</p>
     * @param {Object} renderbuffer - Specifies an array of renderbuffer objects to be deleted.
     * @public
     * @since 3
     *
     */
    public deleteRenderbuffer(renderbuffer: object): void;
    /**
     * <p>getRenderbufferParameteriv returns in params a selected parameter of the currently bound renderbuffer object.</p>
     *
     * <p>pname names a specific renderbuffer object parameter, as follows:</p>
     *
     * <p>RENDERBUFFER_WIDTH</p>
     * <p>  params returns the width in pixels of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_HEIGHT</p>
     * <p>  params returns the height in pixels of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_INTERNAL_FORMAT</p>
     * <p>  params returns the internal format of the image of the currently bound renderbuffer. The initial value is RGBA4.</p>
     *
     * <p>RENDERBUFFER_RED_SIZE</p>
     * <p>  params returns the resolution in bits for the red component of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_GREEN_SIZE</p>
     * <p>  params returns the resolution in bits for the green component of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_BLUE_SIZE</p>
     * <p>  params returns the resolution in bits for the blue component of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_ALPHA_SIZE</p>
     * <p>  params returns the resolution in bits for the alpha component of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_DEPTH_SIZE</p>
     * <p>  params returns the resolution in bits for the depth component of the image of the currently bound renderbuffer. The initial value is 0.</p>
     *
     * <p>RENDERBUFFER_STENCIL_SIZE</p>
     * <p>  params returns the resolution in bits for the stencil component of the image of the currently bound renderbuffer. The initial value is 0.</p>
     * @param {number} target - Specifies the target framebuffer object. The symbolic constant must be FRAMEBUFFER.
     * @param {number} pname - Specifies the symbolic name of a framebuffer object attachment parameter. Accepted values are FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE, FRAMEBUFFER_ATTACHMENT_OBJECT_NAME, FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL, and FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE.
     * @return {number} Depends on the requested information (as specified with pname). Either a GLint or a GLenum.
     * @public
     * @since 3
     *
     */
    public getRenderbufferParameter(target: number, pname: number): number;
    /**
     * <p>isRenderbuffer returns TRUE if renderbuffer is currently the name of a renderbuffer object. If renderbuffer is zero, or is a non-zero value that is not currently the name of a renderbuffer object, or if an error occurs, isRenderbuffer returns FALSE.</p>
     *
     * <p>A name returned by genRenderbuffers, but not yet associated with a renderbuffer object by calling bindRenderbuffer, is not the name of a renderbuffer object.</p>
     * @param {Object} renderbuffer - Specifies a value that may be the name of a framebuffer object.
     * @return {boolean} true if the passed object is valid and false otherwise.
     * @public
     * @since 3
     *
     */
    public isRenderbuffer(renderbuffer: object): boolean;
    /**
     * <p>renderbufferStorage establishes the data storage, format, and dimensions of a renderbuffer object's image. Any existing data store for the renderbuffer is deleted and the contents of the new data store are undefined.</p>
     *
     * <p>An implementation may vary its allocation of internal component resolution based on any renderbufferStorage parameter (except target), but the allocation and chosen internal format must not be a function of any other state and cannot be changed once they are established. The actual resolution in bits of each component of the allocated image can be queried with getRenderbufferParameteriv.</p>
     * @param {number} target - Specifies the renderbuffer target. The symbolic constant must be RENDERBUFFER.
     * @param {number} internalformat - Specifies the color-renderable, depth-renderable, or stencil-renderable format of the renderbuffer. Must be one of the following symbolic constants: RGBA4, RGB565, RGB5_A1, DEPTH_COMPONENT16, or STENCIL_INDEX8.
     * @param {number} width - Specifies the width of the renderbuffer in pixels.
     * @param {number} height -Specifies the height of the renderbuffer in pixels.
     * @public
     * @since 3
     *
     */
    public renderbufferStorage(target: number, internalformat: number, width: number, height: number): void;
    /**
     * <p>bindTexture lets you create or use a named texture. Calling bindTexture with target set to TEXTURE_2D or TEXTURE_CUBE_MAP and texture set to the name of the new texture binds the texture name to the target of the current active texture unit. When a texture is bound to a target, the previous binding for that target is automatically broken.</p>
     *
     * <p>Texture names are unsigned integers. The value zero is reserved to represent the default texture for each texture target. Texture names and the corresponding texture contents are local to the shared object space of the current GL rendering context.</p>
     *
     * <p>You may use genTextures to generate a set of new texture names.</p>
     *
     * <p>When a texture is first bound, it assumes the specified target: A texture first bound to TEXTURE_2D becomes a two-dimensional texture and a texture first bound to TEXTURE_CUBE_MAP becomes a cube-mapped texture. The state of a two-dimensional texture immediately after it is first bound is equivalent to the state of the default TEXTURE_2D at GL initialization, and similarly for cube-mapped textures.</p>
     *
     * <p>While a texture is bound, GL operations on the target to which it is bound affect the bound texture, and queries of the target to which it is bound return state from the bound texture. In effect, the texture targets become aliases for the textures currently bound to them, and the texture name zero refers to the default textures that were bound to them at initialization.</p>
     *
     * <p>A texture binding created with bindTexture remains active until a different texture is bound to the same target, or until the bound texture is deleted with deleteTextures.</p>
     *
     * <p>Once created, a named texture may be re-bound to its same original target as often as needed. It is usually much faster to use bindTexture to bind an existing named texture to one of the texture targets than it is to reload the texture image using texImage2D.</p>
     * @param {number} target - A GLenum specifying the binding point (target). Possible values: TEXTURE_2D, TEXTURE_CUBE_MAP, TEXTURE_EXTERNAL_OES
     * @param {Object} texture - A WebGLTexture object to bind.
     * @throws {INVALID_ENUM} If target is not gl.TEXTURE_2D, gl.TEXTURE_CUBE_MAP, gl.TEXTURE_EXTERNAL_OES, gl.TEXTURE_3D, or gl.TEXTURE_2D_ARRAY.
     * @public
     * @since 3
     *
     */
    public bindTexture(target: number, texture: object): void;
    /**
     * <p>Specifies a two-dimensional texture image in a compressed format.</p>
     *
     * <p>compressedTexImage2D defines a two-dimensional texture image or cube-map texture image using compressed image data from client memory. </p>
     *
     * <p>The texture image is decoded according to the extension specification defining the specified internalformat.</p>
     *
     * <p>OpenGL ES defines no specific compressed texture formats, but does provide a mechanism to obtain symbolic constants for such formats provided by extensions.</p>
     *
     * <p>The number of compressed texture formats supported can be obtained by querying the value of NUM_COMPRESSED_TEXTURE_FORMATS.</p>
     *
     * <p>The list of specific compressed texture formats supported can be obtained by querying the value of COMPRESSED_TEXTURE_FORMATS</p>
     * @param {number} target - A GLenum specifying the binding point (target) of the active texture.
     * @param {number} level - A GLint specifying the level of detail. Level 0 is the base image level and level n is the nth mipmap reduction level.
     * @param {number} internalformat - A GLenum specifying the compressed image format. Compressed image formats must be enabled by WebGL extensions before using this method.
     * @param {number} width - A GLsizei specifying the width of the texture.
     * @param {number} height - A GLsizei specifying the height of the texture.
     * @param {number} border - A GLint specifying the width of the border. Must be 0.
     * @param {Uint32Array|Uint8Array|ArrayBufferView} pixels - A ArrayBufferView that be used as a data store for the compressed image data in memory.
     * @public
     * @since 3
     *
     */
    public compressedTexImage2D(target: number, level: number, internalformat: number, width: number, height: number, border: number, pixels: Uint32Array | Uint8Array): void;
    /**
     * <p> specify a two-dimensional texture subimage in a compressed format</p>
     *
     * <p>Texturing allows elements of an image array to be read by shaders.</p>
     *
     * <p>compressedTexSubImage2D and compressedTextureSubImage2D redefine a contiguous subregion of an existing two-dimensional texture image.</p>
     *
     * <p>The texels referenced by data replace the portion of the existing texture array with x indices xoffset and xoffset+width−1xoffset+width-1, and the y indices yoffset and yoffset+height−1yoffset+height-1, inclusive.</p>
     *
     * <p>This region may not include any texels outside the range of the texture array as it was originally specified.</p>
     *
     * <p>It is not an error to specify a subtexture with width of 0, but such a specification has no effect.</p>
     *
     * <p>internalformat must be a known compressed image format (such as RGTC) or an extension-specified compressed-texture format.</p>
     *
     * <p>The format of the compressed texture image is selected by the GL implementation that compressed it (see texImage2D) and should be queried at the time the texture was compressed with getTexLevelParameter.</p>
     *
     * <p>If a non-zero named buffer object is bound to the PIXEL_UNPACK_BUFFER target (see bindBuffer) while a texture image is specified, data is treated as a byte offset into the buffer object's data store.</p>
     * @param {number} target - A GLenum specifying the binding point (target) of the active texture
     * @param {number} level - A GLint specifying the level of detail. Level 0 is the base image level and level n is the nth mipmap reduction level.
     * @param {number} xoffset - A GLint specifying the horizontal offset within the compressed texture image.
     * @param {number} yoffset - A GLint specifying the vertical offset within the compressed texture image.
     * @param {number} width - A GLsizei specifying the width of the compressed texture.
     * @param {number} height - A GLsizei specifying the height of the compressed texture.
     * @param {number} format - A GLenum specifying the compressed image format. Compressed image formats must be enabled by WebGL extensions before using this method.
     * @param {Uint32Array|Uint8Array|ArrayBufferView} pixels - A ArrayBufferView that be used as a data store for the compressed image data in memory.
     * @public
     * @since 3
     *
     */
    public compressedTexSubImage2D(target: number, level: number, xoffset: number, yoffset: number, width: number, height: number, format: number, pixels: Uint32Array | Uint8Array): void;
    /**
     * <p>Texturing maps a portion of a specified texture image onto each graphical primitive for which texturing is active. Texturing is active when the current fragment shader or vertex shader makes use of built-in texture lookup functions.</p>
     *
     * <p>copyTexImage2D defines a two-dimensional texture image or cube-map texture image with pixels from the current framebuffer (rather than from client memory, as is the case for texImage2D).</p>
     *
     * <p>The screen-aligned pixel rectangle with lower left corner at (x, y) and with a width of width and a height of height defines the texture array at the mipmap level specified by level. internalformat specifies the internal format of the texture array.</p>
     *
     * <p>The pixels in the rectangle are processed exactly as if readPixels had been called with format set to RGBA, but the process stops just after conversion of RGBA values. Subsequent processing is identical to that described for texImage2D, beginning with the clamping of the R, G, B, and A values to the range 0 1 and then conversion to the texture's internal format for storage in the texel array.</p>
     *
     * <p>The components required for internalformat must be a subset of those present in the framebuffer's format. For example, a RGBA framebuffer can be used to supply components for any internalformat. However, a RGB framebuffer can only be used to supply components for RGB or LUMINANCE base internal format textures, not ALPHA, LUMINANCE_ALPHA, or RGBA textures.</p>
     *
     * <p>Pixel ordering is such that lower x and y screen coordinates correspond to lower s and t texture coordinates.</p>
     *
     * <p>If any of the pixels within the specified rectangle are outside the framebuffer associated with the current rendering context, then the values obtained for those pixels are undefined.</p>
     * @param {number} target - Specifies the target texture of the active texture unit. Must be TEXTURE_2D, TEXTURE_CUBE_MAP_POSITIVE_X, TEXTURE_CUBE_MAP_NEGATIVE_X, TEXTURE_CUBE_MAP_POSITIVE_Y, TEXTURE_CUBE_MAP_NEGATIVE_Y, TEXTURE_CUBE_MAP_POSITIVE_Z, or TEXTURE_CUBE_MAP_NEGATIVE_Z.
     * @param {number} level - Specifies the level-of-detail number. Level 0 is the base image level. Level n is the nth mipmap reduction image.
     * @param {number} internalformat - Specifies the internal format of the texture. Must be one of the following symbolic constants: ALPHA, LUMINANCE, LUMINANCE_ALPHA, RGB, or RGBA.
     * @param {number} x - Specify the window coordinates of the lower left corner of the rectangular region of pixels to be copied.
     * @param {number} y - Specify the window coordinates of the lower left corner of the rectangular region of pixels to be copied.
     * @param {number} width - Specifies the width of the texture image. All implementations support 2D texture images that are at least 64 texels wide and cube-mapped texture images that are at least 16 texels wide.
     * @param {number} height - Specifies the height of the texture image. All implementations support 2D texture images that are at least 64 texels high and cube-mapped texture images that are at least 16 texels high.
     * @param {number} border - Specifies the width of the border. Must be 0.
     * @public
     * @since 3
     *
     */
    public copyTexImage2D(target: number, level: number, internalformat: number, x: number, y: number, width: number, height: number, border: number): void;
    /**
     * <p>Texturing maps a portion of a specified texture image onto each graphical primitive for which texturing is active. Texturing is active when the current fragment shader or vertex shader makes use of built-in texture lookup functions.</p>
     *
     * <p>copyTexSubImage2D replaces a rectangular portion of a two-dimensional texture image or cube-map texture image with pixels from the current framebuffer (rather than from client memory, as is the case for texSubImage2D).</p>
     *
     * <p>The screen-aligned pixel rectangle with lower left corner at x y and with width width and height height replaces the portion of the texture array with x indices xoffset through xoffset + width - 1 , inclusive, and y indices yoffset through yoffset + height - 1 , inclusive, at the mipmap level specified by level.</p>
     *
     * <p>The pixels in the rectangle are processed exactly as if readPixels had been called with format set to RGBA, but the process stops just after conversion of RGBA values. Subsequent processing is identical to that described for texSubImage2D, beginning with the clamping of the R, G, B, and A values to the range 0 1 and then conversion to the texture's internal format for storage in the texel array.</p>
     *
     * <p>The destination rectangle in the texture array may not include any texels outside the texture array as it was originally specified. It is not an error to specify a subtexture with zero width or height, but such a specification has no effect.</p>
     *
     * <p>If any of the pixels within the specified rectangle are outside the framebuffer associated with the current rendering context, then the values obtained for those pixels are undefined.</p>
     *
     * <p>No change is made to the internalformat, width, or height parameters of the specified texture array or to texel values outside the specified subregion.</p>
     * @param {number} target - Specifies the target texture of the active texture unit. Must be TEXTURE_2D, TEXTURE_CUBE_MAP_POSITIVE_X, TEXTURE_CUBE_MAP_NEGATIVE_X, TEXTURE_CUBE_MAP_POSITIVE_Y, TEXTURE_CUBE_MAP_NEGATIVE_Y, TEXTURE_CUBE_MAP_POSITIVE_Z, or TEXTURE_CUBE_MAP_NEGATIVE_Z.
     * @param {number} level - Specifies the level-of-detail number. Level 0 is the base image level. Level n is the nth mipmap reduction image.
     * @param {number} xoffset - Specifies a texel offset in the x direction within the texture array.
     * @param {number} yoffset - Specifies a texel offset in the y direction within the texture array.
     * @param {number} x - Specify the window coordinates of the lower left corner of the rectangular region of pixels to be copied.
     * @param {number} y - Specify the window coordinates of the lower left corner of the rectangular region of pixels to be copied.
     * @param {number} width - Specifies the width of the texture subimage.
     * @param {number} height - Specifies the height of the texture subimage.
     * @public
     * @since 3
     *
     */
    public copyTexSubImage2D(target: number, level: number, xoffset: number, yoffset: number, x: number, y: number, width: number, height: number): void;
    /**
     * <p>genTextures returns n texture names in textures. There is no guarantee that the names form a contiguous set of integers; however, it is guaranteed that none of the returned names was in use immediately before the call to genTextures.</p>
     *
     * <p>The generated textures have no dimensionality; they assume the dimensionality of the texture target to which they are first bound (see bindTexture).</p>
     *
     * <p>Texture names returned by a call to genTextures are not returned by subsequent calls, unless they are first deleted with deleteTextures.</p>
     * @return {Object} texture created by context
     * @public
     * @since 3
     *
     */
    public createTexture(): object;
    /**
     * <p>deleteTextures deletes n textures named by the elements of the array textures. After a texture is deleted, it has no contents or dimensionality, and its name is free for reuse (for example by genTextures). If a texture that is currently bound is deleted, the binding reverts to 0 (the default texture).</p>
     *
     * <p>deleteTextures silently ignores 0's and names that do not correspond to existing textures.</p>
     * @param {Object} texture - Specifies an array of texture objects to be deleted.
     * @public
     * @since 3
     *
     */
    public deleteTexture(texture: object): void;
    /**
     * <p>generateMipmap computes a complete set of mipmap arrays derived from the zero level array. Array levels up to and including the 1x1 dimension texture image are replaced with the derived arrays, regardless of previous contents. The zero level texture image is left unchanged.</p>
     *
     * <p>The internal formats of the derived mipmap arrays all match those of the zero level texture image. The dimensions of the derived arrays are computed by halving the width and height of the zero level texture image, then in turn halving the dimensions of each array level until the 1x1 dimension texture image is reached.</p>
     *
     * <p>The contents of the derived arrays are computed by repeated filtered reduction of the zero level array. No particular filter algorithm is required, though a box filter is recommended. hint may be called to express a preference for speed or quality of filtering.</p>
     * @param {number} target - Specifies the texture target of the active texture unit to which the texture object is bound whose mipmaps will be generated. Must be one of the following symbolic constants: TEXTURE_2D or TEXTURE_CUBE_MAP.
     * @public
     * @since 3
     *
     */
    public generateMipmap(target: number): void;
    /**
     * <p>getTexParameter returns in params the value of the texture parameter specified as pname. target defines the target texture of the active texture unit, either TEXTURE_2D or TEXTURE_CUBE_MAP, to specify two-dimensional or cube-mapped texturing. pname accepts the same symbols as texParameter, with the same interpretations:</p>
     *
     * <p>TEXTURE_MAG_FILTER</p>
     * <p>Returns the single-valued texture magnification filter, a symbolic constant. The initial value is LINEAR.</p>
     *
     * <p>TEXTURE_MIN_FILTER</p>
     * <p>Returns the single-valued texture minification filter, a symbolic constant. The initial value is NEAREST_MIPMAP_LINEAR.</p>
     *
     * <p>TEXTURE_WRAP_S</p>
     * <p>Returns the single-valued wrapping function for texture coordinate s, a symbolic constant. The initial value is REPEAT.</p>
     *
     * <p>TEXTURE_WRAP_T</p>
     * <p>Returns the single-valued wrapping function for texture coordinate t, a symbolic constant. The initial value is REPEAT.</p>
     * @param {number} target - Specifies the symbolic name of the target texture of the active texture unit. TEXTURE_2D and TEXTURE_CUBE_MAP are accepted.
     * @param {number} pname - Specifies the symbolic name of a texture parameter. TEXTURE_MAG_FILTER, TEXTURE_MIN_FILTER, TEXTURE_WRAP_S, and TEXTURE_WRAP_T are accepted.
     * @return {number|boolean} Returns the requested texture information (as specified with pname). If an error occurs, null is returned.
     * @public
     * @since 3
     *
     */
    public getTexParameter(target: number, pname: number): number | boolean;
    /**
     * <p>isTexture returns TRUE if texture is currently the name of a texture. If texture is zero, or is a non-zero value that is not currently the name of a texture, or if an error occurs, isTexture returns FALSE.</p>
     *
     * <p>A name returned by genTextures, but not yet associated with a texture by calling bindTexture, is not the name of a texture.</p>
     * @param {Object} texture - Specifies a value that may be the name of a texture object.
     * @return {boolean} true if the passed object is valid and false otherwise.
     * @public
     * @since 3
     *
     */
    public isTexture(texture: object): boolean;
    /**
     * <p>Texturing maps a portion of a specified texture image onto each graphical primitive for which texturing is active. Texturing is active when the current fragment shader or vertex shader makes use of built-in texture lookup functions.</p>
     *
     * <p>To define texture images, call texImage2D. The arguments describe the parameters of the texture image, such as height, width, level-of-detail number (see texParameter), and format. The last three arguments describe how the image is represented in memory.</p>
     *
     * <p>Data is read from data as a sequence of unsigned bytes or shorts, depending on type. When type is UNSIGNED_BYTE, each of the bytes is interpreted as one color component. When type is one of UNSIGNED_SHORT_5_6_5, UNSIGNED_SHORT_4_4_4_4, or UNSIGNED_SHORT_5_5_5_1, each unsigned short value is interpreted as containing all the components for a single texel, with the color components arranged according to format. Color components are treated as groups of one, two, three, or four values, again based on format. Groups of components are referred to as texels.</p>
     *
     * <p>width × height texels are read from memory, starting at location data. By default, these texels are taken from adjacent memory locations, except that after all width texels are read, the read pointer is advanced to the next four-byte boundary. The four-byte row alignment is specified by pixelStorei with argument UNPACK_ALIGNMENT, and it can be set to one, two, four, or eight bytes.</p>
     *
     * <p>The first element corresponds to the lower left corner of the texture image. Subsequent elements progress left-to-right through the remaining texels in the lowest row of the texture image, and then in successively higher rows of the texture image. The final element corresponds to the upper right corner of the texture image.</p>
     *
     * <p>format determines the composition of each element in data. It can assume one of these symbolic values:</p>
     *
     * <p>ALPHA</p>
     * <p>Each element is a single alpha component. The GL converts it to floating point and assembles it into an RGBA element by attaching 0 for red, green, and blue. Each component is then clamped to the range [0,1].</p>
     *
     * <p>RGB</p>
     * <p>Each element is an RGB triple. The GL converts it to floating point and assembles it into an RGBA element by attaching 1 for alpha. Each component is then clamped to the range [0,1].</p>
     *
     * <p>RGBA</p>
     * <p>Each element contains all four components. The GL converts it to floating point, then each component is clamped to the range [0,1].</p>
     *
     * <p>LUMINANCE</p>
     * <p>Each element is a single luminance value. The GL converts it to floating point, then assembles it into an RGBA element by replicating the luminance value three times for red, green, and blue and attaching 1 for alpha. Each component is then clamped to the range [0,1].</p>
     *
     * <p>LUMINANCE_ALPHA</p>
     * <p>Each element is a luminance/alpha pair. The GL converts it to floating point, then assembles it into an RGBA element by replicating the luminance value three times for red, green, and blue. Each component is then clamped to the range [0,1].</p>
     *
     * <p>Color components are converted to floating point based on the type. When type is UNSIGNED_BYTE, each component is divided by 2 8 - 1 . When type is UNSIGNED_SHORT_5_6_5, UNSIGNED_SHORT_4_4_4_4, or UNSIGNED_SHORT_5_5_5_1, each component is divided by 2 N - 1 , where N is the number of bits in the bitfield.</p>
     * other parameters:
     *         texImage2D(target, level, internalformat, format, type, ImageData? pixels)
     *         texImage2D(target, level, internalformat, format, type, Image? pixels)
     *         texImage2D(target, level, internalformat, format, type, Video? pixels)
     *         texImage2D(target, level, internalformat, format, type, Bitmap? pixels)
     * @param {number} target - Specifies the target texture of the active texture unit. Must be TEXTURE_2D, TEXTURE_EXTERNAL_OES(video only), TEXTURE_CUBE_MAP_POSITIVE_X, TEXTURE_CUBE_MAP_NEGATIVE_X, TEXTURE_CUBE_MAP_POSITIVE_Y, TEXTURE_CUBE_MAP_NEGATIVE_Y, TEXTURE_CUBE_MAP_POSITIVE_Z, or TEXTURE_CUBE_MAP_NEGATIVE_Z.
     * @param {number} level - Specifies the level-of-detail number. Level 0 is the base image level. Level n is the nth mipmap reduction image.
     * @param {number} internalformat - Specifies the internal format of the texture. Must be one of the following symbolic constants: ALPHA, LUMINANCE, LUMINANCE_ALPHA, RGB, RGBA.
     * @param {number} width - Specifies the width of the texture image. All implementations support 2D texture images that are at least 64 texels wide and cube-mapped texture images that are at least 16 texels wide.
     * @param {number} height - Specifies the height of the texture image All implementations support 2D texture images that are at least 64 texels high and cube-mapped texture images that are at least 16 texels high.
     * @param {number} border - Specifies the width of the border. Must be 0.
     * @param {number} format - Specifies the format of the texel data. Must match internalformat. The following symbolic values are accepted: ALPHA, RGB, RGBA, LUMINANCE, and LUMINANCE_ALPHA.
     * @param {number} type - Specifies the data type of the texel data. The following symbolic values are accepted: UNSIGNED_BYTE, UNSIGNED_SHORT_5_6_5, UNSIGNED_SHORT_4_4_4_4, and UNSIGNED_SHORT_5_5_5_1.
     * @param {ArrayBufferView|Uint8Array|Uint32Array} pixels - Specifies a pointer to the image data in memory.
     * @public
     * @since 3
     *
     */
    public texImage2D(target: number, level: number, internalformat: number, width: number, height: number, border: number | ImageData | Image | Bitmap, format?: number, type?: number, pixels?: ArrayBufferView | Uint32Array | Uint8Array): void;
    /**
     * <p>Texturing maps a portion of a specified texture image onto each graphical primitive for which texturing is active. Texturing is active when the current fragment shader or vertex shader makes use of built-in texture lookup functions.</p>
     *
     * <p>texSubImage2D redefines a contiguous subregion of an existing two-dimensional texture image. The texels referenced by data replace the portion of the existing texture array with x indices xoffset and xoffset + width - 1 , inclusive, and y indices yoffset and yoffset + height - 1 , inclusive. This region may not include any texels outside the range of the texture array as it was originally specified. It is not an error to specify a subtexture with zero width or height, but such a specification has no effect.</p>
     * @param {number} target - Specifies the target texture of the active texture unit. Must be TEXTURE_2D, TEXTURE_CUBE_MAP_POSITIVE_X, TEXTURE_CUBE_MAP_NEGATIVE_X, TEXTURE_CUBE_MAP_POSITIVE_Y, TEXTURE_CUBE_MAP_NEGATIVE_Y, TEXTURE_CUBE_MAP_POSITIVE_Z, or TEXTURE_CUBE_MAP_NEGATIVE_Z.
     * @param {number} level - Specifies the level-of-detail number. Level 0 is the base image level. Level n is the nth mipmap reduction image.
     * @param {number} xoffset - Specifies a texel offset in the x direction within the texture array.
     * @param {number} yoffset - Specifies a texel offset in the y direction within the texture array.
     * @param {number} width - Specifies the width of the texture subimage.
     * @param {number} height - Specifies the height of the texture subimage.
     * @param {number} format - Specifies the format of the pixel data. The following symbolic values are accepted: ALPHA, RGB, RGBA, LUMINANCE, and LUMINANCE_ALPHA.
     * @param {number} type - Specifies the data type of the pixel data. The following symbolic values are accepted: UNSIGNED_BYTE, UNSIGNED_SHORT_5_6_5, UNSIGNED_SHORT_4_4_4_4, and UNSIGNED_SHORT_5_5_5_1.
     * @param {ArrayBufferView|Uint8Array|Uint32Array} pixels - Specifies a pointer to the image data in memory.
     * @public
     * @since 3
     *
     */
    public texSubImage2D(target: number, level: number, xoffset: number, yoffset: number, width: number, height: number, format: number, type: number, pixels: ArrayBufferView | Uint8Array | Uint32Array): void;
    /**
     * <p>Texture mapping is a technique that applies an image onto an object's surface as if the image were a decal or cellophane shrink-wrap. The image is created in texture space, with an (s, t) coordinate system. A texture is a two-dimensional or cube-mapped image and a set of parameters that determine how samples are derived from the image.</p>
     *
     * <p>texParameter assigns the value or values in params to the texture parameter specified as pname. target defines the target texture of the active texture unit, either TEXTURE_2D or TEXTURE_CUBE_MAP. The following symbols are accepted in pname:</p>
     *
     * <p>TEXTURE_MIN_FILTER</p>
     * <p>The texture minifying function is used whenever the pixel being textured maps to an area greater than one texture element. There are six defined minifying functions. Two of them use the nearest one or nearest four texture elements to compute the texture value. The other four use mipmaps.</p>
     *
     * <p>A mipmap is an ordered set of arrays representing the same image at progressively lower resolutions. If the texture has dimensions w × h , there are floor ⁡ log 2 ⁡ max ⁡ w h + 1 mipmap levels. The first mipmap level is the original texture, with dimensions w × h . Each subsequent mipmap level has dimensions max ⁡ 1 floor ⁡ w 2 i × max ⁡ 1 floor ⁡ h 2 i , where i is the mipmap level, until the final mipmap is reached, which has dimension 1 × 1 .</p>
     *
     * <p>To define the mipmap levels, call texImage2D, compressedTexImage2D, or copyTexImage2D with the level argument indicating the order of the mipmaps. Level 0 is the original texture; level floor ⁡ log 2 ⁡ max ⁡ w h is the final 1 × 1 mipmap.</p>
     *
     * <p>params supplies a function for minifying the texture as one of the following:</p>
     *
     * <p>NEAREST</p>
     * <p>Returns the value of the texture element that is nearest (in Manhattan distance) to the center of the pixel being textured.</p>
     *
     * <p>LINEAR</p>
     * <p>Returns the weighted average of the four texture elements that are closest to the center of the pixel being textured.</p>
     *
     * <p>NEAREST_MIPMAP_NEAREST</p>
     * <p>Chooses the mipmap that most closely matches the size of the pixel being textured and uses the NEAREST criterion (the texture element nearest to the center of the pixel) to produce a texture value.</p>
     *
     * <p>LINEAR_MIPMAP_NEAREST</p>
     * <p>Chooses the mipmap that most closely matches the size of the pixel being textured and uses the LINEAR criterion (a weighted average of the four texture elements that are closest to the center of the pixel) to produce a texture value.</p>
     *
     * <p>NEAREST_MIPMAP_LINEAR</p>
     * <p>Chooses the two mipmaps that most closely match the size of the pixel being textured and uses the NEAREST criterion (the texture element nearest to the center of the pixel) to produce a texture value from each mipmap. The final texture value is a weighted average of those two values.</p>
     *
     * <p>LINEAR_MIPMAP_LINEAR</p>
     * <p>Chooses the two mipmaps that most closely match the size of the pixel being textured and uses the LINEAR criterion (a weighted average of the four texture elements that are closest to the center of the pixel) to produce a texture value from each mipmap. The final texture value is a weighted average of those two values.</p>
     *
     * <p>As more texture elements are sampled in the minification process, fewer aliasing artifacts will be apparent. While the NEAREST and LINEAR minification functions can be faster than the other four, they sample only one or four texture elements to determine the texture value of the pixel being rendered and can produce moire patterns or ragged transitions. The initial value of TEXTURE_MIN_FILTER is NEAREST_MIPMAP_LINEAR.</p>
     *
     * <p>TEXTURE_MAG_FILTER</p>
     * <p>The texture magnification function is used when the pixel being textured maps to an area less than or equal to one texture element. It sets the texture magnification function to either NEAREST or LINEAR (see below). NEAREST is generally faster than LINEAR, but it can produce textured images with sharper edges because the transition between texture elements is not as smooth. The initial value of TEXTURE_MAG_FILTER is LINEAR.</p>
     *
     * <p>NEAREST</p>
     * <p>Returns the value of the texture element that is nearest (in Manhattan distance) to the center of the pixel being textured.</p>
     *
     * <p>LINEAR</p>
     * <p>Returns the weighted average of the four texture elements that are closest to the center of the pixel being textured.</p>
     *
     * <p>TEXTURE_WRAP_S</p>
     * <p>Sets the wrap parameter for texture coordinate s to either CLAMP_TO_EDGE, MIRRORED_REPEAT, or REPEAT. CLAMP_TO_EDGE causes s coordinates to be clamped to the range 1 2N 1 - 1 2N , where N is the size of the texture in the direction of clamping. REPEAT causes the integer part of the s coordinate to be ignored; the GL uses only the fractional part, thereby creating a repeating pattern. MIRRORED_REPEAT causes the s coordinate to be set to the fractional part of the texture coordinate if the integer part of s is even; if the integer part of s is odd, then the s texture coordinate is set to 1 - frac ⁡ s , where frac ⁡ s represents the fractional part of s. Initially, TEXTURE_WRAP_S is set to REPEAT.</p>
     *
     * <p>TEXTURE_WRAP_T</p>
     * <p>Sets the wrap parameter for texture coordinate t to either CLAMP_TO_EDGE, MIRRORED_REPEAT, or REPEAT. See the discussion under TEXTURE_WRAP_S. Initially, TEXTURE_WRAP_T is set to REPEAT.</p>
     * @param {number} target - Specifies the target texture of the active texture unit, which must be either TEXTURE_2D or TEXTURE_CUBE_MAP.
     * @param {number} pname - Specifies the symbolic name of a single-valued texture parameter. pname can be one of the following: TEXTURE_MIN_FILTER, TEXTURE_MAG_FILTER, TEXTURE_WRAP_S, or TEXTURE_WRAP_T.
     * @param {number} param - Specifies the value of pname.
     * @public
     * @since 3
     *
     */
    public texParameterf(target: number, pname: number, param: number): void;
    /**
     * <p>Texture mapping is a technique that applies an image onto an object's surface as if the image were a decal or cellophane shrink-wrap. The image is created in texture space, with an (s, t) coordinate system. A texture is a two-dimensional or cube-mapped image and a set of parameters that determine how samples are derived from the image.</p>
     *
     * <p>texParameter assigns the value or values in params to the texture parameter specified as pname. target defines the target texture of the active texture unit, either TEXTURE_2D or TEXTURE_CUBE_MAP. The following symbols are accepted in pname:</p>
     *
     * <p>TEXTURE_MIN_FILTER</p>
     * <p>The texture minifying function is used whenever the pixel being textured maps to an area greater than one texture element. There are six defined minifying functions. Two of them use the nearest one or nearest four texture elements to compute the texture value. The other four use mipmaps.</p>
     *
     * <p>A mipmap is an ordered set of arrays representing the same image at progressively lower resolutions. If the texture has dimensions w × h , there are floor ⁡ log 2 ⁡ max ⁡ w h + 1 mipmap levels. The first mipmap level is the original texture, with dimensions w × h . Each subsequent mipmap level has dimensions max ⁡ 1 floor ⁡ w 2 i × max ⁡ 1 floor ⁡ h 2 i , where i is the mipmap level, until the final mipmap is reached, which has dimension 1 × 1 .</p>
     *
     * <p>To define the mipmap levels, call texImage2D, compressedTexImage2D, or copyTexImage2D with the level argument indicating the order of the mipmaps. Level 0 is the original texture; level floor ⁡ log 2 ⁡ max ⁡ w h is the final 1 × 1 mipmap.</p>
     *
     * <p>params supplies a function for minifying the texture as one of the following:</p>
     *
     * <p>NEAREST</p>
     * <p>Returns the value of the texture element that is nearest (in Manhattan distance) to the center of the pixel being textured.</p>
     *
     * <p>LINEAR</p>
     * <p>Returns the weighted average of the four texture elements that are closest to the center of the pixel being textured.</p>
     *
     * <p>NEAREST_MIPMAP_NEAREST</p>
     * <p>Chooses the mipmap that most closely matches the size of the pixel being textured and uses the NEAREST criterion (the texture element nearest to the center of the pixel) to produce a texture value.</p>
     *
     * <p>LINEAR_MIPMAP_NEAREST</p>
     * <p>Chooses the mipmap that most closely matches the size of the pixel being textured and uses the LINEAR criterion (a weighted average of the four texture elements that are closest to the center of the pixel) to produce a texture value.</p>
     *
     * <p>NEAREST_MIPMAP_LINEAR</p>
     * <p>Chooses the two mipmaps that most closely match the size of the pixel being textured and uses the NEAREST criterion (the texture element nearest to the center of the pixel) to produce a texture value from each mipmap. The final texture value is a weighted average of those two values.</p>
     *
     * <p>LINEAR_MIPMAP_LINEAR</p>
     * <p>Chooses the two mipmaps that most closely match the size of the pixel being textured and uses the LINEAR criterion (a weighted average of the four texture elements that are closest to the center of the pixel) to produce a texture value from each mipmap. The final texture value is a weighted average of those two values.</p>
     *
     * <p>As more texture elements are sampled in the minification process, fewer aliasing artifacts will be apparent. While the NEAREST and LINEAR minification functions can be faster than the other four, they sample only one or four texture elements to determine the texture value of the pixel being rendered and can produce moire patterns or ragged transitions. The initial value of TEXTURE_MIN_FILTER is NEAREST_MIPMAP_LINEAR.</p>
     *
     * <p>TEXTURE_MAG_FILTER</p>
     * <p>The texture magnification function is used when the pixel being textured maps to an area less than or equal to one texture element. It sets the texture magnification function to either NEAREST or LINEAR (see below). NEAREST is generally faster than LINEAR, but it can produce textured images with sharper edges because the transition between texture elements is not as smooth. The initial value of TEXTURE_MAG_FILTER is LINEAR.</p>
     *
     * <p>NEAREST</p>
     * <p>Returns the value of the texture element that is nearest (in Manhattan distance) to the center of the pixel being textured.</p>
     *
     * <p>LINEAR</p>
     * <p>Returns the weighted average of the four texture elements that are closest to the center of the pixel being textured.</p>
     *
     * <p>TEXTURE_WRAP_S</p>
     * <p>Sets the wrap parameter for texture coordinate s to either CLAMP_TO_EDGE, MIRRORED_REPEAT, or REPEAT. CLAMP_TO_EDGE causes s coordinates to be clamped to the range 1 2N 1 - 1 2N , where N is the size of the texture in the direction of clamping. REPEAT causes the integer part of the s coordinate to be ignored; the GL uses only the fractional part, thereby creating a repeating pattern. MIRRORED_REPEAT causes the s coordinate to be set to the fractional part of the texture coordinate if the integer part of s is even; if the integer part of s is odd, then the s texture coordinate is set to 1 - frac ⁡ s , where frac ⁡ s represents the fractional part of s. Initially, TEXTURE_WRAP_S is set to REPEAT.</p>
     *
     * <p>TEXTURE_WRAP_T</p>
     * <p>Sets the wrap parameter for texture coordinate t to either CLAMP_TO_EDGE, MIRRORED_REPEAT, or REPEAT. See the discussion under TEXTURE_WRAP_S. Initially, TEXTURE_WRAP_T is set to REPEAT.</p>
     * @param {number} target - Specifies the target texture of the active texture unit, which must be either TEXTURE_2D or TEXTURE_CUBE_MAP.
     * @param {number} pname - Specifies the symbolic name of a single-valued texture parameter. pname can be one of the following: TEXTURE_MIN_FILTER, TEXTURE_MAG_FILTER, TEXTURE_WRAP_S, or TEXTURE_WRAP_T.
     * @param {number} param - Specifies the value of pname.
     * @public
     * @since 3
     *
     */
    public texParameteri(target: number, pname: number, param: number): void;
    /**
     * <p> clear sets the bitplane area of the window to values previously selected by clearColor, clearIndex, clearDepth, clearStencil, and clearAccum. Multiple color buffers can be cleared simultaneously by selecting more than one buffer at a time using drawBuffer.</p>
     *
     * <p>The pixel ownership test, the scissor test, dithering, and the buffer writemasks affect the operation of clear. The scissor box bounds the cleared region. Alpha function, blend function, logical operation, stenciling, texture mapping, and depth-buffering are ignored by clear.</p>
     *
     * <p>clear takes a single argument that is the bitwise OR of several values indicating which buffer is to be cleared.</p>
     *
     * <p>The values are as follows:</p>
     *
     * <p>  COLOR_BUFFER_BIT</p>
     *
     * <p>     Indicates the buffers currently enabled for color writing.</p>
     *
     * <p> DEPTH_BUFFER_BIT</p>
     *
     * <p>     Indicates the depth buffer.</p>
     *
     * <p> ACCUM_BUFFER_BIT</p>
     *
     * <p>     Indicates the accumulation buffer.</p>
     *
     * <p> STENCIL_BUFFER_BIT</p>
     *
     * <p>     Indicates the stencil buffer.</p>
     *
     * <p> The value to which each buffer is cleared depends on the setting of the clear value for that buffer.</p>
     * @param {number} mask - A GLbitfield bitwise OR mask that indicates the buffers to be cleared. Possible values are:
     *  COLOR_BUFFER_BIT, DEPTH_BUFFER_BIT, STENCIL_BUFFER_BIT
     * @throws {INVALID_ENUM} If mask is not one of the listed possible values.
     * @public
     * @since 3
     *
     */
    public clear(mask: number): void;
    /**
     * <p>drawArrays specifies multiple geometric primitives with very few subroutine calls. Instead of calling a GL procedure to pass each individual vertex, normal, texture coordinate, edge flag, or color, you can prespecify separate arrays of vertices, normals, and colors and use them to construct a sequence of primitives with a single call to drawArrays.</p>
     * <p>When drawArrays is called, it uses count sequential elements from each enabled array to construct a sequence of geometric primitives, beginning with element first. mode specifies what kind of primitives are constructed and how the array elements construct those primitives.</p>
     * <p>Vertex attributes that are modified by drawArrays have an unspecified value after drawArrays returns. Attributes that aren't modified remain well defined.</p>
     * @param {number} mode - A GLenum specifying the type primitive to render.
     * @param {number} first - A GLint specifying the starting index in the array of vector points.
     * @param {number} count - A GLsizei specifying the number of indices to be rendered.
     * @throws {INVALID_ENUM} If mode is not one of the accepted values.
     * @throws {INVALID_VALUE} If first or count are negative.
     * @throws {INVALID_OPERATION} If gl.CURRENT_PROGRAM is null.
     * @public
     * @since 3
     *
     */
    public drawArrays(mode: number, first: number, count: number): void;
    /**
     * <p>drawElements specifies multiple geometric primitives with very few subroutine calls. Instead of calling a GL function to pass each individual vertex, normal, texture coordinate, edge flag, or color, you can prespecify separate arrays of vertices, normals, and so on, and use them to construct a sequence of primitives with a single call to drawElements.</p>
     * <p>When drawElements is called, it uses count sequential elements from an enabled array, starting at indices to construct a sequence of geometric primitives. mode specifies what kind of primitives are constructed and how the array elements construct these primitives. If more than one array is enabled, each is used.</p>
     * <p>Vertex attributes that are modified by drawElements have an unspecified value after drawElements returns. Attributes that aren't modified maintain their previous values.</p>
     * @param {number} mode - A GLenum specifying the type primitive to render.
     * @param {number} count - A GLsizei specifying the number of elements to be rendered.
     * @param {number} type - A GLenum specifying the type of the values in the element array buffer.
     * @param {number} offset - A GLintptr specifying an offset in the element array buffer. Must be a valid multiple of the size of the given type.
     * @throws {INVALID_ENUM} If mode is not one of the accepted values.
     * @throws {INVALID_OPERATION} If offset is not a valid multiple of the size of the given type.
     * @throws {INVALID_VALUE} If count is negative.
     * @public
     * @since 3
     *
     */
    public drawElements(mode: number, count: number, type: number, offset: number): void;
    /**
     * <p>finish does not return until the effects of all previously called GL commands are complete. Such effects include all changes to GL state, all changes to connection state, and all changes to the frame buffer contents.</p>
     * @public
     * @since 3
     *
     */
    public finish(): void;
    /**
     * <p>Different GL implementations buffer commands in several different locations, including network buffers and the graphics accelerator itself. flush empties all of these buffers, causing all issued commands to be executed as quickly as they are accepted by the actual rendering engine. Though this execution may not be completed in any particular time period, it does complete in finite time.</p>
     * <p>Because any GL program might be executed over a network, or on an accelerator that buffers commands, all programs should call flush whenever they count on having all of their previously issued commands completed. For example, call flush before waiting for user input that depends on the generated image.</p>
     * @public
     * @since 3
     *
     */
    public flush(): void;
    /**
     * <p> post color buffer to the view.</p>
     * @public
     * @since 3
     *
     */
    public swapBuffer(): void;
    /**
     * <p>The WebGLRenderingContext.getExtension() method enables a WebGL extension.</p>
     * @return {object}
     * @param {String} name
     * @public
     * @since 3
     *
     */
    public getExtension(name: string): object;
    /**
     * <p>The WebGLRenderingContext.getSupportedExtensions() method returns a list of all the supported WebGL extensions.</p>
     * @return {Array} An Array of strings with all the supported WebGL extensions.
     * @public
     * @since 3
     *
     */
    public getSupportedExtensions(): Array<object>;
    /**
     * <p>The WebGLContextAttributes object that contains the actual context parameters. Might return null, if the context is lost.</p>
     * @return {Object} contain this context Config
     * @public
     * @since 3
     *
     */
    public getContextAttributes(): object;
    /**
     * <p> returns a Boolean indicating whether or not the WebGL context has been lost.</p>
     * @return {Boolean} whether or not the WebGL context has been lost
     * @public
     * @since 3
     *
     */
    public isContextLost(): boolean;
    private makeCurrent(): void;
    private registrationEvent;
    private initConstants;
}
export = WebGLRenderingContext;
