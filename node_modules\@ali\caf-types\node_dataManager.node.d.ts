import FileSystemError = require("yunos/cloudfs/FileSystemError");

declare namespace dataManager {
    interface statFs {
        bsize: number;
        bfree: number;
        frsize: number;
        blocks: number;
    }
}

declare class dataManager {
    static statFs(path: string) : dataManager.statFs;
    cleanPrivateData(uri: string, type: number, callback: (result: number) => void) : void;
    cleanPublicData(type: number) : number;
    getAssetDir(uri: string) : string;
    getPrivateDataDir(uri: string, type: number) : string;
    getPrivateDataSize(uri: string, type: number, callback: (err: number, result: number) => void): void;
    getPublicDataDir(type?: number) : string;
    static dupFileDescriptor(x: Object): number;
    /**
     * @friend
     * @draft
     */
    static getAbsolutePath(v: string): string;
    static checkSecurity(uri : string) : boolean;
    resetUserId() : void;
}

export = dataManager;

