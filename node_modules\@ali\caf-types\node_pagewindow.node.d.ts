declare namespace PageWindow {
    interface Resolution {
        densityFactor: number;
        densityScaleFactor: number;
        width: number;
        height: number;
        xdpi: number;
        ydpi: number;
        bitsPerPixel: number;
        refreshRate: number;
    }
}

declare class PageWindow {
    static getResolution(v: number): PageWindow.Resolution;

    static getDisplayIds(): number[];

    static on(v1: string, v2: Function): void;

    static requestVsync(v: number): void;
}

export = PageWindow;
