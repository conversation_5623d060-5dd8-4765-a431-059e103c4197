/// <reference types="node" />
import Dialog = require("./Dialog");
import Loading = require("./Loading");
import Bitmap = require("../../graphics/Bitmap");
interface IStyle {
    [index: string]: Object;
    minWidth: number;
    maxWidth: number;
    minHeight: number;
    maxHeight: number;
    paddings: number[];
    capInsets: number[];
    sizeStyle: number;
    fontSize: string;
    textColor: string;
    spacing: number;
    iconPosition: number;
    isAutoStyle: boolean;
    closeIconSrc: string | Bitmap | Buffer;
    closeIconWidth: number;
    closeIconHeight: number;
    closeIconMarginLeft: number;
    closeIconMarginTop: number;
    closeIconEnable: boolean;
}
/**
 * <p>A simple dialog containing a loading widget and description textView.</p>
 * @extends yunos.ui.widget.Dialog
 * @memberof yunos.ui.widget
 * @public
 * @since 2
 */
declare class LoadingDialog extends Dialog {
    private _container;
    private _closeIcon;
    private _loading;
    private _message;
    private _paddings;
    private _sizeStyle;
    private _fontSize;
    private _textColor;
    private _defaultIconPosition;
    private _iconPosition;
    private _spacing;
    private _closeIconSrc;
    private _closeIconWidth;
    private _closeIconHeight;
    private _closeIconMarginLeft;
    private _closeIconMarginTop;
    private _closeIconEnable;
    /**
     * <p>Create a LoadingDialog.</p>
     * @public
     * @since 2
     */
    /**
     * <p>Destructor that destroy this LoadingDialog.</p>
     * @param {boolean} recursive - destroy the children in the LoadingDialog if the value is true.
     * @public
     * @override
     * @since 2
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds title of the dialog.</p>
     * @name yunos.ui.widget.LoadingDialog#title
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 1
     */
    public title: string;
    /**
     * <p>This property holds message content of the dialog.</p>
     * @name yunos.ui.widget.LoadingDialog#message
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @override
     * @since 2
     */
    public message: string;
    /**
     * Gets the Loading in dialog.
     * @name yunos.ui.widget.LoadingDialog#loading
     * @type {yunos.ui.widget.Loading}
     * @readonly
     * @public
     * @since 6
     */
    public readonly loading: Loading;
    /**
     * <p>Indicates the loading icon's placement relative to loading text.</p>
     * @name yunos.ui.widget.LoadingDialog#iconPosition
     * @type {yunos.ui.widget.LoadingDialog.IconPosition}
     * @throws {TypeError} If this value is not in IconPosition.
     * @public
     * @since 4
     *
     */
    public iconPosition: number;
    /**
     * <p>Set the spacing distance between loading icon and loading text.</p>
     * @name yunos.ui.widget.LoadingDialog#iconSpacing
     * @type {number}
     * @public
     * @since 4
     *
     */
    public iconSpacing: number;
    /**
     * <p>Apply theme style for this loading dialog.</p>
     * @method applyStyle
     * @protected
     * @override
     * @since 2
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Update theme style for theme config changing.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.LoadingDialog#defaultStyleName
     * @type {string}
     * @default "LoadingDialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Enum for Icon Position relative to loading text.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly IconPosition: {
        /**
         * <p>Loading icon positioned at the left side of loading text.</p>
         * @public
         * @since 4
         *
         */
        Left: int;
        /**
         * <p>Loading icon positioned at the up side of loading text. </p>
         * @public
         * @since 4
         *
         */
        Top: int;
        /**
         * <p>Loading icon positioned at the right side of loading text.</p>
         * @public
         * @since 4
         *
         */
        Right: int;
        /**
         * <p>Loading icon positioned at the bottom side of loading text</p>
         * @public
         * @since 4
         *
         */
        Bottom: int;
    };
}
export = LoadingDialog;
