import { IPullRefreshView } from "../control/Types";
declare class PullRefreshViewHelper {
    private pullRefreshView;
    private _headerDistanceChangeCallBack;
    private _footerChangeCallBack;
    private _headerChangeCallBack;
    private _completeCallBack;
    constructor(pullRefreshView: IPullRefreshView);
    init(): void;
    initHeaderSpriteSrc(): void;
    private setHeaderSpriteViewParamsAndDistance;
    private initHeaderView;
    private initFooterView;
    private initEvents;
    private playHeaderAnimation;
    private getHeaderSpriteView;
    initFooterImageViewSrc(): void;
    private setFooterViewParamsAndDistance;
    private getFooterImageView;
    private getPullView;
    private getRotateAnimation;
    destory(): void;
}
export = PullRefreshViewHelper;
