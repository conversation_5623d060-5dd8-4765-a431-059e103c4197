import EventEmitter = require("../../core/EventEmitter");
import View = require("../view/View");
/**
 * <p>This class descript voice action using in view support voice.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.ui.voice
 * @public
 * @since 3
 */
declare class VoiceCommand extends EventEmitter {
    private _category;
    private _requestVoiceState;
    private _localTaskEnd;
    private _enabledWhenCoverd;
    private _activeMode;
    private _recognitionQuality;
    private _recognitionMode;
    private _customCommands;
    private _action;
    private _customDmCommands;
    private _sceneId;
    private _keepEffective;
    private _keepFullTimeActive;
    public static globalSceneId: string;
    private _autoFeedback;
    private _hitAnimation;
    private _voiceTitle;
    private _voiceSubTitle;
    private _bindingVoice;
    private static setGlobalSceneId(id: string): void;
    private _id;
    public constructor();
    private addToMarkupParent(parent: View): void;
    /**
     * Defines action of this command.
     * @name yunos.ui.voice.VoiceCommand#action
     * @type {string}
     * @public
     * @since 3
     *
     */
    public action: string;
    /**
     * Defines category of this command using in different scene as music or map.
     * @name yunos.ui.voice.VoiceCommand#category
     * @type {string}
     * @public
     * @since 4
     *
     */
    public category: string;
    /**
     * Using in CustomCommand action, save the key words needs fire the command.
     * @name yunos.ui.voice.VoiceCommand#customCommands
     * @type {string[]}
     * @public
     * @since 3
     *
     */
    public customCommands: string[];
    private customDmCommands: Object[];
    /**
     * auto command voiceTitle.
     * @name yunos.ui.voice.VoiceCommand#voiceTitle
     * @type {string[]}
     * @protected
     * @since 6
     *
     */
    protected voiceTitle: string[];
    /**
     * auto command voiceSubTitle.
     * @name yunos.ui.voice.VoiceCommand#voiceSubTitle
     * @type {string}
     * @protected
     * @since 6
     *
     */
    protected voiceSubTitle: string;
    /**
     * auto command voiceBinding.
     * @name yunos.ui.voice.VoiceCommand#voiceBinding
     * @type {string}
     * @protected
     * @since 6
     *
     */
    protected voiceBinding: string;
    private enabledWhenCovered: boolean;
    /**
     * Using in CustomCommand action, set the recognition mode amount ASR or QuickWord or Both.
     * @name yunos.ui.voice.VoiceCommand#recognitionMode
     * @type {string}
     * @public
     * @since 3
     *
     */
    public recognitionMode: number;
    /**
     * Using in CustomCommand action, set the recognition Quality amount Low or Middle or High,
     * In Low mode action matching with low matching rate.
     * In Middle mode matching rate must larger than 80%.
     * In High mode matching rate must 100%.
     * @name yunos.ui.voice.VoiceCommand#recognitionQuality
     * @type {string}
     * @public
     * @since 3
     *
     */
    public recognitionQuality: string;
    private activeMode: number;
    private sceneId: string;
    private localTaskEnd: boolean;
    private requestVoiceState: string;
    /**
     * Set state after the voice command is processed.
     * @name yunos.ui.voice.VoiceCommand#interactorState
     * @type {yunos.ui.voice.VoiceCommand.InteractorState}
     * @default yunos.ui.voice.VoiceCommand.InteractorState.Processing
     * @public
     * @since 4
     *
     */
    public interactorState: string;
    private keepEffective: boolean;
    /**
     * Set voice command keep valid state in quickword mode.
     * @name yunos.ui.voice.VoiceCommand#keepAwake
     * @type {boolean}
     * @public
     * @since 4
     *
     */
    public keepAwake: boolean;
    /**
     * In QuickWord mode the mode will active after awake.
     * @name yunos.ui.voice.VoiceCommand#keepFullTimeActive
     * @type {boolean}
     * @public
     * @since 5
     *
     */
    public keepFullTimeActive: boolean;
    /**
     * Automatic feedback after hit.
     * @name yunos.ui.voice.VoiceCommand#autoFeedback
     * @type {boolean}
     * @public
     * @since 6
     *
     */
    public autoFeedback: boolean;
    /**
     * hit Animation.
     * @name yunos.ui.voice.VoiceCommand#hitAnimation
     * @type {boolean}
     * @public
     * @since 6
     *
     */
    public hitAnimation: boolean;
    /**
     * Defines id of this command.
     * @name yunos.ui.voice.VoiceCommand#id
     * @type {string}
     * @friend
     *
     */
    id: string;
    /**
     * Create voice command with input action;
     * @public
     * @since 3
     *
     */
    public static create(action: string): VoiceCommand;
    private static createSelectCommand(): VoiceCommand;
    private static createPreviousCommand(): VoiceCommand;
    private static createNextCommand(): VoiceCommand;
    private static createSelectPageCommand(): VoiceCommand;
    private static createCustomCommand(): VoiceCommand;
    private static createLastPageCommand(): VoiceCommand;
    private static createPreviousItemCommand(): VoiceCommand;
    private static createNextItemCommand(): VoiceCommand;
    private static createCustomDmCommand(): VoiceCommand;
    private static createAutoCommand(): VoiceCommand;
    /**
     * Voice command actions;
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     *
     *
     */
    public static readonly ActionCommands: {
        /**
         * Change to previous page;
         * @public
         * @since 4
         *
         *
         */
        PreviousPage: string;
        Previous: string;
        /**
         * Change to next page;
         * @public
         * @since 4
         *
         *
         */
        NextPage: string;
        Next: string;
        /**
         * Change to last page;
         * @public
         * @since 4
         *
         *
         */
        LastPage: string;
        Wake: string;
        /**
         * Exit current page;
         * @public
         * @since 5
         */
        Exit: string;
        /**
         * Select a page;
         * @public
         * @since 4
         *
         *
         */
        SelectPage: string;
        Custom: string;
        /**
         * Select an item;
         * @public
         * @since 4
         *
         *
         */
        SelectItem: string;
        Select: string;
        CancelOrder: string;
        /**
         * Select previous item;
         * @public
         * @since 4
         *
         *
         */
        PreviousItem: string;
        /**
         * Select next item;
         * @public
         * @since 4
         *
         *
         */
        NextItem: string;
        CustomDm: string;
        LastItem: string;
        ScrollBy: string;
        ScrollTo: string;
        /**
         * auto widget;
         * @public
         * @since 6
         */
        Auto: string;
    };
    private static readonly Tips: {
        readonly Previous: string;
        readonly Next: string;
        readonly Wake: string;
        readonly Exit: string;
        readonly SelectPage: string;
        readonly CancelOrder: string;
        readonly Select: string;
        readonly LastPage: string;
        readonly PreviousItem: string;
        readonly NextItem: string;
    };
    /**
     * Defined recognition mode;
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public static readonly RecognitionMode: {
        /**
         * VoiceCommand RecognitionMode default ASR.
         * @public
         * @since 3
         *
         */
        ASR: int;
        /**
         * VoiceCommand RecognitionMode in QuickWord no need wakeup voice.
         * @public
         * @since 3
         *
         */
        QuickWord: int;
        /**
         * VoiceCommand fire in both mode.
         * @public
         * @since 3
         *
         */
        Both: int;
    };
    /**
     * Defined RecognitionQuality mode;
     * @enum {string}
     * @readonly
     * @public
     * @since 3
     *
     */
    public static readonly RecognitionQuality: {
        /**
         * VoiceCommand RecognitionQuality HIGH mode.
         * @public
         * @since 3
         *
         */
        High: string;
        HIGH: string;
        /**
         * VoiceCommand RecognitionQuality MIDDLE mode.
         * @public
         * @since 3
         *
         */
        Middle: string;
        MIDDLE: string;
        /**
         * VoiceCommand RecognitionQuality LOW mode.
         * @public
         * @since 3
         *
         */
        Low: string;
        LOW: string;
    };
    private static readonly ActiveMode: {
        Active: int;
        Inactive: int;
    };
    private static readonly VoiceState: {
        Processing: string;
        KWS: string;
        Auto: string;
    };
    /**
     * Defined InteractorState state mode;
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly InteractorState: {
        /**
         * InteractorState in Processing mode.
         * @public
         * @since 4
         *
         */
        Processing: string;
        /**
         * InteractorState in Listening mode.
         * @public
         * @since 4
         *
         */
        Listening: string;
        /**
         * InteractorState in Idle mode.
         * @public
         * @since 4
         *
         */
        Idle: string;
    };
}
export = VoiceCommand;
