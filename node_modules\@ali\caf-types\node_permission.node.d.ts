/*
 * Copyright (C) 2019 Alibaba Group Holding Limited. All Rights Reserved.
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare class PermissionNode {
    checkPermission(iface: Object, perm: string, msg: Object): boolean;
    inquirePermission(perm: string, callback: (err: number, auth: boolean, data?: Object) => void): void;
    changePermission(domain: string, perm: string, permOption: number, userId: number): void;
    getPermissionStatus(domain: string, perm: string, userId: number): number;
    getPermStatusByUid(uid: number, perm: string): number;
    getDomainListByPerm(perm: string, callback: (err: number, res: object[]) => void): void;
    getDomainListByUidSync(uid: number): string[];
    getDomainListByUid(uid: number, callback: (err: number, res: string) => void): void;
    getPermListByDomainSync(domain: string, userId: number): object[];
    getPermListByDomain(domain: string, userId: number, callback: (err: number, res: object[]) => void): void;
    getAppList(userId: number, callback: (err: number, res: string) => void): void;
    getUidByDomain(domian: string, userId: number): number;
    loadPermLog(uid: number, perm: string, callback: (err: number, res: string) => void): void;
    isSystemApp(uri: string): boolean;
    isSystemAppByUid(uid: number): boolean;
    isRemoveable(uri: string): boolean;
    isRemoveableByUid(uid: number): boolean;
    isPrivilegedApp(uri: string): boolean;
    isPrivilegedAppByUid(uid: number): boolean;
    checkSelfPrivacyPermissions(permList: string[]): string;
    changePermissions(domain: string, permList: string[], permOption: number, userId: number, flag: number): void;
    getPrivacyDisplayNames(permList: string[]): string[];
    checkDomainPrivacyPermissions(domain: string, userId: number, permList: string[]): string;
    reauthDomainPermissions(domainList: string[], callback: (err: number, res: boolean) => void): void;
    getReauthDomainPermissionsStatus(): boolean;
}

export = PermissionNode;
