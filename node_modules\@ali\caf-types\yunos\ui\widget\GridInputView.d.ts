/// <reference types="node" />
import CompositeView = require("../view/CompositeView");
import TextView = require("../view/TextView");
import Button = require("./Button");
import { Options } from "yunos/ui/util/TypeHelper";
import Bitmap = require("yunos/graphics/Bitmap");
import Gradient = require("yunos/graphics/Gradient");
interface IStyle {
    defaultHeight: number;
    defaultSpacing: number;
    defaultSubViewWidth: number;
    defaultBorderWidth: number;
    defaultBorderRadius: number | number[];
    defaultBorderColor: string;
    defaultFontSize: number;
    defaultFontColor: string;
    defaultSelectItemPadding: number;
    defaultItemCustomBorder: string;
    defaultItemDropdown: string | Bitmap | Buffer;
    defaultItemSize: number;
    defaultInputItemCount: number;
    defaultPlaceholderFontSize: string | number;
    defaultPlaceholderColor: string;
    defaultWindowBackground: string | number | Bitmap | Gradient;
    defaultWindowBorderColor: string | number | Bitmap | Gradient;
    defaultKeyboardBtnFontSize: string | number;
    defaultKeyboardBtnFontColor: string;
    defaultKeyboardBtnSelectedColor: string | number | Bitmap | Gradient;
    defaultHKeyboardSpacing: number;
    defaultVKeyboardSpacing: number;
    defaultHKeyboardBtnHeight: number;
    defaultVKeyboardBtnHeight: number;
    defaultHKeyboardVerticalMargin: number;
    defaultVKeyboardVerticalMargin: number;
    defaultHKeyboardHorizontalMargin: number;
    defaultVKeyboardHorizontalMargin: number;
    defaultHKeyboardColumns: number;
    defaultVKeyboardColumns: number;
    defaultHKeyboardHeightPercent: number;
    defaultVKeyboardHeightPercent: number;
}
/**
 * Each character is displayed by an textview in GridInputView.
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 3
 *
 */
declare class GridInputView extends CompositeView {
    private _screen;
    private _autoHideKeyBoard;
    private _autoToUpperCase;
    private _viewArr;
    private _inputItemCount;
    private _selectBtns;
    private _defaultSubViewWidth;
    private _defaultSpacing;
    private _defaultHeight;
    private _defaultBorderWidth;
    private _defaultBorderRadius;
    private _defaultBorderColor;
    private _defaultFontSize;
    private _defaultFontColor;
    private _defaultItemSize;
    private _windowTopLine;
    private _defaultWindowBorderColor;
    private _customWindow;
    private _inputView;
    private _keyboardView;
    private _defaultWindowBackground;
    private _selectOptions;
    private _defaultItemDropdown;
    private _defaultSelectItemPadding;
    private _defaultHKeyboardBtnHeight;
    private _defaultVKeyboardBtnHeight;
    private _defaultHKeyboardSpacing;
    private _defaultHKeyboardColumns;
    private _defaultVKeyboardColumns;
    private _defaultVKeyboardSpacing;
    private _defaultHKeyboardVerticalMargin;
    private _defaultHKeyboardHorizontalMargin;
    private _defaultVKeyboardVerticalMargin;
    private _defaultVKeyboardHorizontalMargin;
    private _defaultKeyboardBtnFontColor;
    private _defaultKeyboardBtnFontSize;
    private _defaultKeyboardBtnSelectedColor;
    private _defaultHKeyboardHeightPercent;
    private _defaultVKeyboardHeightPercent;
    private _defaultItemCustomBorder;
    private _defaultPlaceholderFontSize;
    private _defaultPlaceholderColor;
    private _defaultInputItemCount;
    private _showSoftKeyboard(): void;
    private _hideSoftKeyboard(): void;
    private _onSelectButtonTextChange(): void;
    private _onTextChange(text: string): void;
    private _onItemTap(view: TextView): void;
    private setItemCustomBackground(index: number, background: string, placeholderText: string): void;
    private _refreshItemCustomView(view: TextView): void;
    private removeItemCustomBackground(index: number): void;
    private _resetItemView(view: TextView): void;
    /**
     * The text content that this grid input view(all items) is to display.
     * @name yunos.ui.widget.GridInputView#text
     * @type {string}
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @since 3
     *
     */
    public text: string;
    /**
     * The text content that this grid input view(exclude select items) is to display.
     * @name yunos.ui.widget.GridInputView#InputText
     * @type {string}
     * @throws {TypeError} If type of parameter is not string.
     * @override
     * @public
     * @since 3
     *
     */
    public getInputText(): string;
    private setInputText(value: string): void;
    /**
     * The text content that this grid input view(only select Items) is to display.
     * @name yunos.ui.widget.GridInputView#selectButtonText
     * @type {string}
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @since 3
     *
     */
    public getSelectButtonText(index: number): string;
    private setSelectButtonText(index: number, value: string): void;
    /**
     * @private
     */
    private autoHideKeyBoard: boolean;
    /**
     * @private
     */
    private autoToUpperCase: boolean;
    /**
     * <p>Defines the input keyboard style. Default input method only support None, HiddenText, PreferNumbers and EmailCharactersOnly, others using default style.</p>
     * @name yunos.ui.widget.GridInputView#inputType
     * @type {yunos.ui.widget.GridInputView.InputType}
     * @throws {TypeError} If this value is not in GridInputView.InputType.
     * @public
     * @since 3
     *
     */
    public inputType: number;
    /**
     * <p>Defines the inputMethodReturnKeyType of GridInputView.</p>
     * <p>This will allow to utilize the native return keys provided by Input Methond App, to indicate what kind of action pressing it will result in.</p>
     * @name yunos.ui.widget.GridInputView#inputMethodReturnKeyType
     * @type {yunos.ui.widget.GridInputView.ReturnKeyType}
     * @public
     * @since 3
     *
     */
    public inputMethodReturnKeyType: number;
    private _hideWindow(): void;
    private _showWindow(itemView: TextView): void;
    private _initCustomWindow(): void;
    private _isHorizontal(): boolean;
    private _updateItemViewText(args: Options): void;
    private _isSelectBtn(itemIndex: number): boolean;
    private _getSelectBtnData(itemIndex: number): Button;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.GridInputView#defaultStyleName
     * @type {string}
     * @default "GridInputView"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Enum for Key type.</p>
     * <p>The soft keyboard type.</p>
     * <p>Allow eight types of mode such as ReturnKeyDefault, ReturnKeyEnter, ReturnKeyDone, ReturnKeyGo,
     * ReturnKeySend, ReturnKeySearch, ReturnKeyNext and ReturnKeyPrevious.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     */
    public static readonly ReturnKeyType: {
        ReturnKeyDefault: int;
        ReturnKeyEnter: int;
        ReturnKeyDone: int;
        ReturnKeyGo: int;
        ReturnKeySend: int;
        ReturnKeySearch: int;
        ReturnKeyNext: int;
        ReturnKeyPrevious: int;
        ReturnKeyJoin: int;
    };
    /**
     * Enum for the soft input keyboard type.
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     */
    public static readonly InputType: {
        None: int;
        HiddenText: int;
        SensitiveData: int;
        NoAutoUppercase: int;
        PreferNumbers: int;
        PreferUppercase: int;
        PreferLowercase: int;
        NoPredictiveText: int;
        FormattedNumbersOnly: int;
        UppercaseOnly: int;
        LowercaseOnly: int;
        DialableCharactersOnly: int;
        EmailCharactersOnly: int;
        UrlCharactersOnly: int;
        NotShowOnFocus: int;
    };
}
export = GridInputView;
