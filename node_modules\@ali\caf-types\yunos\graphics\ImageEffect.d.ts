import YObject = require("../core/YObject");
import Bitmap = require("yunos/graphics/Bitmap");
import ImageFilter = require("yunos/graphics/filter/ImageFilter");
/**
 * <p>The ImageEffect class applies graphical effects like blur or color shift to a bitmap.</p>
 *
 * @example
 * const Image = require("yunos/multimedia/Image");
 * const ImageEffect = require("yunos/graphics/ImageEffect");
 * const BlurImageFilter = require("yunos/graphics/filter/BlurImageFilter");
 * let R = require("yunos/content/resource/Resource").getInstance();
 * let ie = new ImageEffect();
 * let bif = new BlurImageFilter();
 * bif.amount = 10;
 * ie.add(bif);
 * let image = new Image(R.getImageSrc("images/alios.png"));
 * ie.processBitmap(image.getBitmap(), (err, bmp) => {
 *   iv.src = bmp;
 * });
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 5
 */
declare class ImageEffect extends YObject {
    private _filters;
    private _node;
    public constructor();
    /**
     * Remove filter into this effect.
     * @param {yunos.graphics.filter.ImageFilter} filter - filter to remove
     * @public
     * @since 5
     */
    public remove(filter: ImageFilter): void;
    /**
     * Add filter into this effect.
     * @param {yunos.graphics.filter.ImageFilter} filter - filter to add
     * @public
     * @since 5
     */
    public add(filter: ImageFilter): void;
    /**
     * Return the filters array in this effect.
     * @readonly
     * @public
     * @since 5
     */
    public readonly filters: ImageFilter[];
    /**
     * Use the filters to render the bitmap.
     * @param {yunos.graphics.Bitmap} bitmap - The bitmap to process
     * @public
     * @since 5
     */
    public processBitmapSync(bitmap: Bitmap): Bitmap;
    /**
     * <p>This function is used for the {@link processBitmap} method.</p>
     * @callback yunos.graphics.ImageEffect~loadCallback
     * @param {Error|null} err - The error object which contains details when the invocation is failed.
     * @param {yunos.graphics.Bitmap} [bitmap] - The processed bitmap.
     * @public
     * @since 5
     */
    /**
     * Asynchronous process the bitmap. See the {@link processBitmapSync} method.
     * @param {yunos.graphics.Bitmap} bitmap - The bitmap to process
     * @param {yunos.graphics.ImageEffect~loadCallback} callback - The callback will be invoked when process completed.
     * @public
     * @since 5
     */
    public processBitmap(bitmap: Bitmap, callback: (error: Error, bitmap?: Bitmap) => void): void;
}
export = ImageEffect;
