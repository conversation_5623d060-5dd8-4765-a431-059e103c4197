import YObject = require("yunos/core/YObject");
import DataError = require("yunos/database/sqlite/DataError");
import { SQLiteTransactionAddon } from "node_sql_caf.node";
/**
 * <p>The function which indicates the result of transaction operation.</p>
 * @callback yunos.database.sqlite.SQLiteTransaction~transactionCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the result
 * @public
 * @since 2
 */
/**
 * <p>Transaction class, which represents a instance of SQLite transaction.</p>
 * <p>A transaction instance can be got via SQLiteDatabase.createTransaction()</p>
 *
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class SQLiteTransaction extends YObject {
    private _transaction: SQLiteTransactionAddon;
    /**
     * Create a SQLiteTransaction instance with given transactoin addon object.
     *
     * @param {Object} transaction - The transaction addon object
     * @private
     * @hiddenOnPlatform auto
     */
    public constructor(transaction: SQLiteTransactionAddon);
    /**
     * <p>Async interface to begin a transaction</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let isExclusive = true;
     *            let transaction = dbInstance.createTransaction(isExclusive);
     *            transaction.begin(function(error) {
     *                if (error === null) {
     *                    // transaction has began
     *                }
     *            });
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteTransaction~transactionCallback} beginCallback -
     * Callback function to handle the begin result
     * @throws {yunos.database.sqlite.DataError} If the given callback is invalid
     * @public
     * @since 2
     */
    public begin(beginCallback: (err: DataError) => void): void;
    /**
     * <p>Async interface to commit a transaction</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let isExclusive = true;
     *            let transaction = dbInstance.createTransaction(isExclusive);
     *            transaction.begin(function(error) {
     *                if (error === null) {
     *                    let statement = dbInstance.createDeleteStatement("testTable",
     *                        null, null, transaction);
     *                    statement.executeCommand(function(err) {
     *                        if (err === null) {
     *                            transaction.commit(function(commitErr) {
     *                                if (commitErr === null) {
     *                                    // transactoin is committed
     *                                }
     *                            });
     *                        }
     *                    });
     *                }
     *            });
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteTransaction~transactionCallback} commitCallback - Callback
     * function to handle the commit result
     * @throws {yunos.database.sqlite.DataError} If the given callback is invalid
     * @public
     * @since 2
     */
    public commit(commitCallback: (err: DataError) => void): void;
    /**
     * <p>Async interface to rollback a transaction</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let isExclusive = true;
     *            let transaction = dbInstance.createTransaction(isExclusive);
     *            transaction.begin(function(error) {
     *                if (error === null) {
     *                    let statement = dbInstance.createDeleteStatement("testTable",
     *                        null, null, transaction);
     *                    statement.executeCommand(function(err) {
     *                        if (err === null) {
     *                            // SQL has been executed successfully
     *                            transaction.commit(function(commitErr) {
     *                                if (commitErr === null) {
     *                                    // transactoin is committed
     *                                }
     *                            });
     *                        } else {
     *                            // SQL has been executed with error
     *                            transaction.rollback(function(rollbackErr) {
     *                                if (rollbackErr === null) {
     *                                    // transactoin is rollback
     *                                }
     *                            });
     *                        }
     *                    });
     *                }
     *            });
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteTransaction~transactionCallback} rollbackCallback - Callback
     * function to handle the rollback result
     * @throws {yunos.database.sqlite.DataError} If the given callback is invalid
     * @public
     * @since 2
     */
    public rollback(rollbackCallback: (err: DataError) => void): void;
}
export = SQLiteTransaction;
