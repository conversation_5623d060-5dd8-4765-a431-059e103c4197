{"domain": {"name": "video.alios.cn", "default_locale": "zh-CN", "permission": {"use_permission": ["CREATE_OVERLAY_WINDOW.permission.yunos.com", "READ_EXTERNAL_STORAGE.permission.yunos.com", "ACCESS_CACHE_FILESYSTEM.permission.yunos.com", "WRITE_EXTERNAL_STORAGE.permission.yunos.com", "DEVICE_POWER.permission.yunos.com", "MEDIA_SERVICE.permission.yunos.com", "NETWORK_BIND_SERVICE.permission.yunos.com", "NETWORK_POLICY_MANAGEMENT.permission.yunos.com", "WIFI.permission.yunos.com", "MOBILE.permission.yunos.com", "NETWORK_ACCESS.permission.yunos.com", "NETWORK.permission.yunos.com", "PLAY_TTS.permission.yunos.com", "STOP_TTS.permission.yunos.com", "CHANGE_VOICE_STATE.permission.yunos.com", "WAKE_LOCK.permission.yunos.com", "VEHICLE_GET_WARNING.permission.alios.cn", "VEHICLE_GET_NORMAL.permission.alios.cn", "VEHICLE_GET_AC.permission.alios.cn", "THIRD_ACCOUNT_AUTH.permission.alios.cn", "GET_TTS_TIP.permission.yunos.com", "READ_ACCOUNT.permission.yunos.com", "ACCESS_FM_RADIO.permission.yunos.com", "WRITE_MEDIA_STORAGE.permission.yunos.com", "COSMO_SDK.permission.yunos.com", "MODIFY_AUDIO_SETTINGS.permission.yunos.com", "SYSTEMSETTING.permission.yunos.com", "MAP.permission.yunos.com", "CAR_PROPERTY_SYS.permission.alios.cn"], "define_permission": [{"name": "VIDEO_DLNA.permission.yunos.com", "type": "normal", "description": "sync playback information permission"}], "access_rules": [{"type": "lifecycle-api", "target": ["sendlink"], "allow": ["systemui.alios.cn"], "deny": ["*"]}]}, "version_code": **********, "version": "v1.1.**********", "sharedUserId": "app_video", "extension": {"static_compile": true, "extendPackages": ["extend/hdt/control"]}, "use_sdk": 1, "icon": "res/default/images/ic_launcher_video_normal.png", "title": "__VAR_VIDEO_TITLE__"}, "pages": [{"extension": {"engine": "agil"}, "content_path": "src/index.js", "uri": "page://video.alios.cn/video", "splash": "page://video.alios.cn/asset/splash_app.xml", "main": true, "icon": "res/default/images/ic_launcher_video_normal.png", "title": "__VAR_VIDEO_TITLE__"}]}