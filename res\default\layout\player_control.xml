<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_player_control"
    background="{theme.color.Black_1}"
    layout="{layout.player_control}">

    <CompositeView id="id_decorate_container"/>

    <LoadingBM
        id="id_loading"
        sizeStyle="{enum.LoadingBM.SizeStyle.L}"/>

    <include
        id="id_network"
        markup="error"
        visibility="{enum.View.Visibility.None}"/>

    <CompositeView
        id="id_header"
        height="{config.HEADER_HEIGHT}"
        background="{color.PLAYER_CTRLBAR_BG_COLOR}"
        visibility="{enum.View.Visibility.None}"
        layout="{layout.player_header}">
        <NavigationBar id="id_nav"/>
    </CompositeView>

    <CompositeView
        id="id_ctrlbar"
        height="{config.PLAYER_CTRL_BAR_HEIGHT}"
        visibility="{enum.View.Visibility.None}"
        layout="{layout.ctrlbar_container}">
        <View
            id="id_background"
            height="{config.PLAYER_CTRL_BAR_BACKGROUND_HEIGHT}"
            background="{color.PLAYER_CTRLBAR_BG_COLOR}"/>
        <ImageView
            id="id_prev"
            width="{config.PLAYER_CTRL_BAR_ICON_SIZE}"
            height="{config.PLAYER_CTRL_BAR_ICON_SIZE}"
            multiState="{config.CTRLBAR_PREV_BTN_MULTISTATE}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
        <ImageView
            id="id_play_pause"
            width="{config.PLAYER_CTRL_BAR_ICON_SIZE}"
            height="{config.PLAYER_CTRL_BAR_ICON_SIZE}"
            multiState="{config.CTRLBAR_PAUSE_BTN_MULTISTATE}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
        <ImageView
            id="id_next"
            width="{config.PLAYER_CTRL_BAR_ICON_SIZE}"
            height="{config.PLAYER_CTRL_BAR_ICON_SIZE}"
            multiState="{config.CTRLBAR_NEXT_BTN_MULTISTATE}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
        <CompositeView
            id="id_seek_bar"
            height="{config.PLAYER_SEEK_BAR_HEIGHT}"
            layout="{layout.seek_bar}"/>
        <TextView
            id="id_elapsed_time"
            width="{config.PLAYER_TIME_TEXT_WIDTH}"
            propertySetName="extend/hdt/FontBody4"
            color="{theme.color.White_2}"
            align="{enum.TextView.Align.Right}"/>
        <TextView
            id="id_total_time"
            width="{config.PLAYER_TIME_TEXT_WIDTH}"
            propertySetName="extend/hdt/FontBody4"
            color="{theme.color.White_2}"
            align="{enum.TextView.Align.Left}"/>
    </CompositeView>
</CompositeView>
