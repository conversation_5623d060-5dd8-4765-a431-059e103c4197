import CompositeView = require("../view/CompositeView");
import TextView = require("../view/TextView");
import View = require("../view/View");
import NumberPicker = require("./NumberPicker");
/**
 * <p>This class is a widget for selecting a date. The date can be selected by a<br>
 * year, month, and day.</p>
 * @example
 * const datePicker = new DatePicker();
 * datePicker.minDate = "1987-11-05";
 * datePicker.maxDate = "2018-11-05";
 * datePicker.addEventListener("datechanged", (property, oldValue, value) => {
 *     console.log(`设置的时间为${value}`);
 * });
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class DatePicker extends CompositeView {
    private _layoutTimer;
    private _updateTimer;
    private _yearView;
    private _monthView;
    private _dayView;
    private _yearTextView;
    private _monthTextView;
    private _dayTextView;
    private _defaultStartYear;
    private _defaultEndYear;
    private _minDate;
    private _maxDate;
    private _maxDateYear;
    private _maxDateMonth;
    private _maxDateDay;
    private _minDateYear;
    private _minDateMonth;
    private _minDateDay;
    private _currentDate;
    private _dividerTextColor;
    private _dividerTextSize;
    private _dividerOffsetLeft;
    private _dividerYearText;
    private _dividerMonthText;
    private _dividerDayText;
    private _horizontalPadding;
    private _verticalPadding;
    private _count;
    private _dividerTextVisible;
    private _defaultYearFormat;
    private _defaultMonthFormat;
    private _defaultDayFormat;
    private _defaultYearAlign;
    private _defaultMonthAlign;
    private _defaultDayAlign;
    private _currentPickerWidth;
    private _currentPickerHeight;
    private _defaultWidth;
    private _defaultHeight;
    /**
     * Create a datePicker.
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this DatePicker.</p>
     * @param {boolean} recursive - destroy the children in the DatePicker if the value is true.
     * @public
     * @override
     * @since 2
     *
     */
    /**
     * Destroy this datePicker.
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * Gets the NumberPicker used to select year.
     * @name yunos.ui.widget.DatePicker#yearPicker
     * @type {yunos.ui.widget.NumberPicker}
     * @readonly
     * @public
     * @since 6
     */
    public readonly yearPicker: NumberPicker;
    /**
     * Gets the NumberPicker used to select month.
     * @name yunos.ui.widget.DatePicker#monthPicker
     * @type {yunos.ui.widget.NumberPicker}
     * @readonly
     * @public
     * @since 6
     */
    public readonly monthPicker: NumberPicker;
    /**
     * Gets the NumberPicker used to select day.
     * @name yunos.ui.widget.DatePicker#dayPicker
     * @type {yunos.ui.widget.NumberPicker}
     * @readonly
     * @public
     * @since 6
     */
    public readonly dayPicker: NumberPicker;
    /**
     * This property holds the count of line in the picker.
     * @name yunos.ui.widget.DatePicker#count
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not great than or equal to 3.
     * @throws {RangeError} If parameter is not odd number.
     * @default 3
     * @public
     * @since 3
     *
     */
    public count: number;
    /**
     * Sets the minimal date supported by this DatePicker in milliseconds.
     * @name yunos.ui.widget.DatePicker#minDate
     * @type {number|string}
     * @throws {TypeError} If parameter can not change to Date.
     * @public
     * @since 1
     */
    public minDate: Object;
    /**
     * Sets the maximal date supported by this DatePicker in milliseconds.
     * @name yunos.ui.widget.DatePicker#maxDate
     * @type {number|string}
     * @throws {TypeError} If parameter can not change to Date.
     * @public
     * @since 1
     */
    public maxDate: Object;
    /**
     * This property holds the color of the divider text.
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @public
     * @since 3
     *
     */
    public dividerTextColor: string;
    /**
     * This property holds the fontSize of the divider text.
     * @name yunos.ui.widget.DatePicker#dividerTextSize
     * @type {number|string}
     * @public
     * @since 1
     */
    public dividerTextSize: number;
    /**
     * This property holds the divider text's offset to the left.
     * @name yunos.ui.widget.DatePicker#dividerOffsetLeft
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public dividerOffsetLeft: number;
    /**
     * Gets the picker year.
     * @name yunos.ui.widget.DatePicker#year
     * @type {number}
     * @readonly
     * @public
     * @since 1
     */
    public readonly year: number;
    /**
     * Gets the picker month.
     * @name yunos.ui.widget.DatePicker#month
     * @type {number}
     * @readonly
     * @public
     * @since 1
     */
    public readonly month: number;
    /**
     * Gets the picker day.
     * @name yunos.ui.widget.DatePicker#day
     * @type {number}
     * @readonly
     * @public
     * @since 1
     */
    public readonly day: number;
    /**
     * <p>This property holds the current choice Date. the value is in milliseconds.</p>
     * <p>the default value is systems Date.</p>
     * @name yunos.ui.widget.DatePicker#currentDate
     * @type {number|string}
     * @throws {TypeError} If parameter can not change to Date.
     * @public
     * @since 1
     */
    public currentDate: Object;
    /**
     * <p>This property holds the padding of horizontal direction, the value is count on the width.</p>
     * @name yunos.ui.widget.DatePicker#horizontalPadding
     * @type {number}
     * @throws {TypeError} If type of parameter is not a number.
     * @private
     */
    private horizontalPadding: number;
    /**
     * <p>This property holds the padding of vertical direction, the value is count on the height.</p>
     * @name yunos.ui.widget.DatePicker#verticalPadding
     * @type {number}
     * @throws {TypeError} If type of parameter is not a number.
     * @private
     */
    private verticalPadding: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.DatePicker#defaultStyleName
     * @type {string}
     * @default "DatePicker"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Get the default style name of number picker.</p>
     * <p>Override this getter to reset NumberPicker's style.</p>
     * @name yunos.ui.widget.DatePicker#pickerStyleName
     * @type {string}
     * @default ""
     * @readonly
     * @public
     * @since 6
     *
     */
    public readonly pickerStyleName: string;
    private createPicker(): NumberPicker;
    private setPickerSize(): void;
    private createDividerText(text: string): TextView;
    private updateDividerTextSize(textView: TextView, size: number): void;
    private updateAsyncPicker(): void;
    private updatePicker(): void;
    private formatDisplayContent(picker: NumberPicker, format: string): void;
    private doLayout(): void;
    private updateDividerTextPosition(picker: NumberPicker, dividerTextView: View): void;
    private getPickerInfo(picker: NumberPicker): {
        leftPoint: number;
        textWidth: number;
        textHeight: number;
    };
    private clearLayoutTimer(timerFunc: Object): void;
    private onYearValueChanged(oldVal: number, value: number): void;
    private onMonthValueChanged(oldVal: number, value: number): void;
    private onDayValueChanged(oldVal: number, value: number): void;
    private updateYearControl(): void;
    private updateMonthControl(): void;
    private updateDayControl(): void;
    private getDayByDate(year: number, month: number): number;
    /**
     * <p>Apply theme style for DatePicker.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 5
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
}
export = DatePicker;
