export declare class CertificateNode {
	getIssuerDN(x509: object, dn: object): void;
	getSubjectDN(x509: object, dn: object): void;
	getNotBefore(x509: object): number;
	getNotAfter(x509: object): number;
	getSerialNumber(x509: object): Buffer;
	getEncoded(x509: object): Buffer;
	destroy(x509: object): Buffer;
	verify(x509: object, key: string): boolean;
	checkValidity(x509: object, date: Date): boolean;
	getPublicKey(x509: object): string;
	generateCertificate(data: Buffer): object;
}

export declare class CertificateStoreNode {
	installCertificate(x509: object): boolean;
	containsAlias(alias: string, includeSystemDeleted: boolean): boolean;
	aliases(): Array<string>;
	allSystemAliases(): Array<string>;
	allUserAliases(): Array<string>;
	deleteCertificate(alias: string): boolean;
	getCertificateAlias(certificate: object, includeSystemDeleted: boolean): string;
	findIssuer(certificate: object): object;
	getTrustAnchor(certificate: object): object;
	getCertificateChain(leaf: object): Array<object>;
	getCertificate(alias: string, includeSystemDeleted: boolean): object;
}

export declare class KeyStoreNode {
	list(prefix: string, uid: number): Array<string>;
	get(name: string, uid: number): Buffer;
	isEmpty(credential: boolean): boolean;
	sign(name: string, data: Buffer): Buffer;
	reset(credential: boolean): void;
}
