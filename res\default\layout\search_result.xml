<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_search"
    layout="{layout.search_result}"
    propertySetName="search_result">

    <NavigationBar id="id_nav"/>

    <TextView
        id="id_search_keyword"
        height="{config.SEARCH_BANNER_HEIGHT}"
        text="{string.SEARCH_KEYWORD}"
        propertySetName="extend/hdt/FontHeadline"/>

    <ListView
        id="id_disk_list"
        visibility="{enum.View.Visibility.None}"
        orientation="{enum.ListView.Orientation.Vertical}"
        verticalFadingEdgeEnabled="true"
        focusable="false"/>

    <GridView
        id="id_online_list"
        orientation="{enum.GridView.Orientation.Vertical}"
        columns="{config.ONLINE_COLUMNS_NUM}"
        rowSpacing="{config.ITEM_SPACE}"
        columnSpacing="{config.ITEM_SPACE}"
        visibility="{enum.View.Visibility.None}"
        pulDownDistance="{config.PULLDOWN_DISTANCE}"
        verticalFadingEdgeEnabled="true"
        focusable="false"/>

    <include
        id="id_empty"
        visibility="{enum.View.Visibility.None}"
        markup="empty"/>

    <LoadingPageBM
        id="id_loading_page"
        visibility="{enum.View.Visibility.None}"/>
</CompositeView>
