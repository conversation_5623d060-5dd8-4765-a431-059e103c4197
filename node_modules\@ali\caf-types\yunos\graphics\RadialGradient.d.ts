import Gradient = require("./Gradient");
/**
 * <p>Create a RadialGradient which draw a radial gradient given by the coordinates of the two circles represented by the parameters.</p>
 *
 * @example
 *
 * class MyView extends View {
 *   onDraw(ctx) { // override
 *     let radialGradient = ctx.createRadialGradient(150, 150, 150, 150, 150, 0);
 *     // let radialGradient = new RadialGradient(150, 150, 150, 150, 150, 0);
 *     radialGradient.addColorStop(0, "white");
 *     radialGradient.addColorStop(1, "green");
 *     ctx.fillStyle = radialGradient;
 *     ctx.fillRect(0, 0, 300, 300);
 *   }
 * }
 *
 * @extends yunos.graphics.Gradient
 * @memberof yunos.graphics
 * @public
 * @since 3
 *
 */
declare class RadialGradient extends Gradient {
    private x0: number;
    private y0: number;
    private r0: number;
    private x1: number;
    private y1: number;
    private r1: number;
    /**
     * <p>Create a radial gradient.</p>
     * @param {number} x0 - The x axis of the coordinate of the start circle.
     * @param {number} y0 - The y axis of the coordinate of the start circle.
     * @param {number} r0 - The radius of the start circle.
     * @param {number} x1 - The x axis of the coordinate of the end circle.
     * @param {number} y1 - The y axis of the coordinate of the end circle.
     * @param {number} r1 - The radius of the end circle.
     * @public
     * @since 3
     *
     */
    public constructor(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number);
}
export = RadialGradient;
