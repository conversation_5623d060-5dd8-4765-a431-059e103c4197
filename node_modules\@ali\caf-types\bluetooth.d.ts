import EventEmitter = require("yunos/core/EventEmitter");

declare namespace btNS {
    type FunctionCallbackInfo = (msg: { eventName: string, data: Object }) => void;
}

declare class bluetooth {
    events: EventEmitter;

    /** main */
    registerCallback(callback: btNS.FunctionCallbackInfo): void;
    enableEvent(event: string): void;
    disableEvent(event: string): void;

    connect(address: string, profile: string): number;
    disconnect(address: string, profile: string): number;
    getConnectedDevices(profile: string): Object[];
    setPriority(address: string, profile: string, priority: number): boolean;
    getPriority(address: string, profile: string): number;
    setPermission(address: string, type: number, value: number): boolean;
    getPermission(address: string, type: number): number;

    /** PAN */
    panActivate(): number;
    panDeactivate(): number;
    panIsActivated(): boolean;

    /** Adapter */
    getAdapterAddress(): string;
    getAdapterUuids(): string[];
    setAdapterName(name: string): number;
    getAdapterName(): string;
    setAdapterEnabled(): void;
    setAdapterEnabledSync(isEnabled: boolean): number;
    isAdapterEnabled(): boolean;
    getAdapterState(): number;
    pokeAdapter(): void;
    shutdown(): void;

    setAdapterVisible(isVisible: boolean): number;
    isAdapterVisible(): boolean;
    setAdapterConnectable(isConnectable: boolean): number;
    isAdapterConnectable(): boolean;

    startScan(): number;
    stopScan(): number;
    isScanning(): boolean;

    pairDevice(address: string): number;
    cancelDevicePairing(address: string): number;
    unpairDevice(address: string): number;
    getPairedDevices(): Object[];

    setPasskey(address: string, variant: number, confirm: boolean, passcode: number): number;
    setPincode(address: string, pincode: string): number;

    setDeviceAlias(address: string, alias: string): number;
    getDeviceInfoApi(address: string): Object;

    /** OPP */
    opcInit(): number;
    sendFiles(address: string, files: string[]): number;
    sendFds(address: string, fileArray: string[], fdArray: number[]): number;
    stopSendingFiles(): number;

    obexServerInit(path: string): number;
    confirmReceivingFiles(acceptOrNot: boolean, filename: string): number;
    stopReceivingFiles(transferId: number): number;

    /** headset */
    headsetIsAudioConnected(): boolean;
    headsetConnectAudio(): number;
    headsetDisconnectAudio(): number;
    headsetNotifyPhoneState(state: Object): boolean;
    headsetNotifyDeviceState(state: Object): boolean;
    headsetNotifyClccResponse(state: Object): boolean;
    headsetNotifyScoVolume(state: Object): boolean;

    /** Socket */
    socketListen(serviceName: string, uuid: string, result: Object): void;
    socketStopListen(uuid: string, result: Object): void;
    socketConnect(address: string, uuid: string, result: Object): void;

    /** GATT Client*/
    gattStartScan(scanFilters: Object[], settings: Object): boolean;
    gattStopScan(): boolean;
    gattConnect(address: string): boolean;
    gattDisconnect(address: string): boolean;
    gattDiscoverServices(address: string): boolean;
    gattGetServices(address: string): Object[];
    gattReadRssi(address: string): boolean;

    gattReadCharacteristic(address: string, id: Object): boolean;
    gattWriteCharacteristic(address: string, id: Object, buffer: Buffer): boolean;
    gattSetCharacteristicNotify(address: string, id: Object, enabled: boolean): boolean;

    gattWriteDescriptor(address: string, id: Object, buffer: Buffer): boolean;
    gattReadDescriptor(address: string, id: Object): boolean;

    /** GATT Server */
    createAdvertisement(advertiseData: Object[], advertiseSetttings: Object): number;
    destroyAdvertisement(client_if: number): number;
    startAdvertising(client_if: number): number;
    stopAdvertising(client_if: number): number;

    /** PBAP Client */
    getPhonebookSize(address: string,phonebook: number): number;
    pullPhonebook(address: string,phonebook:number, offset: number, max_count: number): number;
    pbapConnect(address: string): number;
    pbapDisconnect(address: string): number;

    /** AVP */
    avrcpSetFocusState(address: string, state: number): number;
    avrcpCommand(address: string, command: number): number;
    avrcpGetTrackInfo(address: string): Object;
    avrcpSetVolume(address: string, volume: number): number;
    avrcpSetProperty(address: string, type: number, value: number): number;
    avrcpGetProperty(address: string, type: number): number;
    avrcpBrowseGetNowPlayingList(address: string): number;
    avrcpBrowsePlayItem(address: string, itemIndex: number):number;
    avrcpCoverartGetImage(address: string, handle: string, type: string, flag: boolean):number;
    a2dpSinkSuspend(address: string):number;
    a2dpSinkResume(address: string):number;
    a2dpSourceSuspend(address: string):number;
    a2dpSourceResume(address: string):number;
    enableAvpEvent(eventName: string): void;
    disableAvpEvent(eventName: string): void;

    /** HFP Client */
    hfpGetCapabilities(address: string): Object;
    hfpGetCurrentEvents(address: string): Object;
    hfpClientConnectSco(address: string): number;
    hfpClientDisconnectSco(address: string): number;
    hfpClientStartVR(address: string): number;
    hfpClientStopVR(address: string): number;
    hfpClientVolumeControl(address: string,type: number, volume: number): boolean;
    hfpClientDialNo(address: string,number: string, memory: number): boolean;
    hfpClientSendTones(address: string,tones: number): boolean;
    hfpClientAnswerCall(address: string): boolean;
    hfpClientHungupCall(address: string): boolean;
    hfpClientGetCurrentCalls(address: string): boolean;
    hfpClientGetLastNumber(address: string): boolean;
    hfpClientRetrieveSubscriberInfo(address: string): boolean;
    hfpClientIsAudioConnected(address: string): boolean;
    hfpClientReleaseAndAccept(address: string): boolean;
    hfpClientCallSwap(address: string): boolean;
    hfpClientReleaseAllCall(address: string): boolean;
    hfpClientJoinCall(address: string): boolean;
    hfpClientReleasePrivateMode(address: string,idx: number): boolean;
    hfpClientReleaseSpecialCall(address: string,idx: number): boolean;
    hfpClientSendAtcmd(address: string,cmd: string): boolean;
    hfpClientGetAudioState(address: string): number;
    hfpClientSetCallMultiZone(zoneId: number,sessionName: string): boolean;

    /** MAP Client */
    mapSendMessage(number: string, content: string): number;
    mapConnect(address: string): number;
    mapDisconnect(address: string): number;
}
export = bluetooth;