import YObject = require("yunos/core/YObject");
declare class TunerServiceAdapter extends YObject {
    private _iface;
    private bus;
    private service;
    private _permission;
    constructor(permission?: string);
    registerSetThreshold(cb: (fm_level: number, usn: number, wam: number, fm_fof: number, am_level: number, am_fof: number) => Promise<string>): void;
    registerGetThreshold(cb: () => Promise<string>): void;
    registerAutoStore(cb: () => Promise<string>): void;
    registerGetFilterTime(cb: () => Promise<string>): void;
    registerSetFilterTime(cb: (distortTime: number, vialdTime: number) => Promise<string>): void;
    emitSigFMAMList(fmamlist: string): boolean;
    destroy(): void;
}
export = TunerServiceAdapter;
