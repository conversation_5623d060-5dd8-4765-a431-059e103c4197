<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    width="{sdp(780)}"
    height="{config.HEADER_HEIGHT}"
    layout="{layout.search_header}"
    propertySetName="search">
    <CompositeView
        id="id_search_container"
        height="{sdp(80)}"
        layout="{layout.search_input}"
        propertySetName="extend/hdt/InputBox">
        <ImageView
            id="id_search_icon"/>
        <TextField
            id="id_search_input"
            maxLength="25"
            clearable="true"
            placeholder="{string.SEARCH_HINT}"
            inputMethodReturnKeyType="{enum.TextField.ReturnKeyType.ReturnKeySearch}"/>
        <ImageView
            id="id_search_close"/>
    </CompositeView>
</CompositeView>
