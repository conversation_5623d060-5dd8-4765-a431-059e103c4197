import ShaderGroup = require("./ShaderGroup");
import Size = require("../Size");
import Rectangle = require("../Rectangle");
/**
 * <p>This class provide the gaussian blur shader support for user interface interface components.</p>
 * @extends yunos.graphics.shader.ShaderGroup
 * @memberof yunos.graphics.shader
 * @public
 * @since 2
 */
declare class GaussianBlur extends ShaderGroup {
    private verticalBlur;
    private horizontalBlur;
    private _textureSizeDefined;
    private _saturation;
    private _brightness;
    private _contrast;
    private _radius;
    private _maxRadius;
    private _samples;
    private _deviation;
    private _transparentBorder;
    private _textureSize;
    private _scale;
    private _sourceRect;
    /**
     * Defines how much the source saturation is increased or decreased.
     * The decrease of the saturation is linear, but the increase is applied with a non-linear curve to allow very high saturation adjustment at the high end of the value range.
     * @name yunos.graphics.shader.GaussianBlur#saturation
     * @type {number}
     * @default 0
     * @throws {TypeError} If contrast is not a number.
     * @public
     * @since 2
     */
    public saturation: number;
    /**
     * Defines how much the source brightness is increased or decreased.
     * The value ranges from -1.0 to 1.0. By default, the property is set to 0.0 (no change).
     * @name yunos.graphics.shader.GaussianBlur#brightness
     * @type {number}
     * @default 0
     * @throws {TypeError} If brightness is not a number.
     * @public
     * @since 2
     */
    public brightness: number;
    /**
     * Defines how much the source contrast is increased or decreased.
     * The decrease of the contrast is linear, but the increase is applied with a non-linear curve to allow very high contrast adjustment at the high end of the value range.
     * @name yunos.graphics.shader.GaussianBlur#contrast
     * @type {number}
     * @default 0
     * @throws {TypeError} If contrast is not a number.
     * @public
     * @since 2
     */
    public contrast: number;
    /**
     * This property defines the distance of the neighboring pixels which affect the blurring of an individual pixel. A larger radius increases the blur effect.
     * @name yunos.graphics.shader.GaussianBlur#radius
     * @type {number}
     * @default 0
     * @throws {TypeError} If raduis is not a number.
     * @public
     * @since 2
     */
    public radius: number;
    /**
     * This property defines how many samples are taken per pixel when blur calculation is done. Larger value produces better quality, but is slower to render.
     * Ideally, this value should be twice as large as the highest required radius value plus 1, for example, if the radius is animated between 0.0 and 4.0, samples should be set to 9.
     * @name yunos.graphics.shader.GaussianBlur#samples
     * @type {number}
     * @default 18 for highRes screen and 9 for low Res screen
     * @throws {TypeError} If raduis is not a number.
     * @private
     */
    private samples: number;
    /**
     * This property is a parameter to the gaussian function that is used when calculating neighboring pixel weights for the blurring. A larger deviation causes image to appear more blurry, but it also reduces the quality of the blur. A very large deviation value causes the effect to look a bit similar to what, for exmple, a box blur algorithm produces. A too small deviation values makes the effect insignificant for the pixels near the radius.
     * @name yunos.graphics.shader.GaussianBlur#deviation
     * this mothed is deprecated
     * @type {number}
     * @default 3
     * @public
     * @since 2
     */
    public deviation: number;
    /**
     * This property defines the blur behavior near the edges of the item, where the pixel blurring is affected by the pixels outside the source edges.
     * @name yunos.graphics.shader.GaussianBlur#transparentBorder
     * @type {boolean}
     * @default false
     * @public
     * @since 2
     */
    public transparentBorder: boolean;
    /**
     * Defines the textureSize
     * default textureSize is equal to sourceItem's size
     * @name yunos.graphics.shader.GaussianBlur#textureSize
     * @type {yunos.graphics.Size}
     * @throws {TypeError} textureSize if not a valid yunos.graphics.Size
     * @public
     * @since 2
     * @deprecated 3
     *
     */
    public textureSize: Size;
    /**
     * Defines the scale for source
     * @name yunos.graphics.shader.GaussianBlur#scale
     * @type {number}
     * @throws {TypeError} textureSize if not a number
     * @private
     */
    private scale: number;
    /**
     * Defines the sourceRect
     * default sourceRect is equal to sourceItem's rect
     * @name yunos.graphics.shader.GaussianBlur#sourceRect
     * @type {yunos.graphics.Rectangle}
     * @public
     * @since 2
     * @deprecated 3
     *
     */
    public sourceRect: Rectangle;
}
export = GaussianBlur;
