/// <reference types="yunos" />
declare class Openapi {
    readonly appKey: string;
    readonly appSecretToken: string;
    readonly timeout: number;
    constructor(appKey: string, appSecretToken: string, timeout?: number);
    get(method: string, version: string, params: ObjectReflectI, businessType?: string): Promise<string>;
    post(method: string, version: string, params: ObjectReflectI, businessType?: string): Promise<string>;
    private _doRequest;
    private _resolveParams;
    private _sign;
}
export = Openapi;
