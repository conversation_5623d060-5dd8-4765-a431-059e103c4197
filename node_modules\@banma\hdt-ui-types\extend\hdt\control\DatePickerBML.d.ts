import DatePicker = require("yunos/ui/widget/DatePicker");
interface IStyle {
    width: number;
    height: number;
    horizontalPadding: number;
    verticalPadding: number;
    styleName: string;
}
declare class DatePickerBML extends DatePicker {
    private __styleName;
    readonly defaultStyleName: string;
    protected applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
}
export = DatePickerBML;
