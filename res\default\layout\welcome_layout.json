{"welcome": {"type": "RelativeLayout", "params": {"id_nav": {"align": {"left": "parent", "top": "parent", "right": "parent"}}, "id_logo": {"align": {"left": "parent", "top": {"target": "id_nav", "side": "bottom"}, "right": "parent"}, "margin": {"top": "{sdp(188)}"}}, "id_btn": {"align": {"left": "parent", "bottom": {"target": "parent", "side": "bottom"}}, "margin": {"left": "{config.WELCOME_BTN_MARGIN}", "bottom": "{config.WELCOME_BTN_MARGIN}"}}, "id_disclaimer": {"align": {"center": "parent", "bottom": {"target": "id_btn", "side": "top"}}, "margin": {"bottom": "{sdp(32)}"}}}}, "welcome_disclaimer": {"type": "RelativeLayout", "params": {"id_disclaimer_checkbox": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(12)}"}}, "id_disclaimer_tips": {"align": {"left": {"target": "id_disclaimer_checkbox", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(12)}"}}, "id_disclaimer_link": {"align": {"left": {"target": "id_disclaimer_tips", "side": "right"}, "middle": "parent"}}}}}