import UBus = require("ubus");
import YObject = require("../core/YObject");
/**
 * <p>Util provides some convenient methods.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.contextagent
 * @friend
 */
declare class Util extends YObject {
    static _pendingSignal: {
        uri: string;
        data: Object;
    }[];
    static _signalConnected: boolean;
    /**
     * <p>Send reply message</p>
     * @param {Object} replyMessage - reply message
     * @friend
     */
    static sendReplyMessage(replyMessage: Object): void;
    /**
     * <p>force connect to context agent signal interface</p>
     * @param {boolean} forceLink - force to connect context agent signal interface immediately.
     * @friend
     */
    static connect2SignalService(forceLink: boolean): void;
    /**
     * <p>Send signal to context agent</p>
     * @param {Object} signal - signal
     * @param {string} signal.serviceName - service name
     * @param {string} signal.signalName - signal name
     * @param {*} signal.data - data for signal
     * @friend
     */
    static sendSignal(signal: {
        serviceName: string;
        signalName: string;
        data: Object;
    }): void;
    static _sendPendingSignals(): void;
    /**
     * @hiddenOnPlatform auto
     */
    static getIfaceForSignal(): UBus.Interface;
    /**
     * @hiddenOnPlatform auto
     */
    static getIfaceForJarvisInternal(): UBus.Interface;
    /**
     * @hiddenOnPlatform auto
     */
    static _getIface(busName: string, busPath: string, busInterface: string, nameForCache: string, initCallback?: (iface: UBus.Interface) => void): UBus.Interface;
}
export = Util;
