import EventEmitter = require("yunos/core/EventEmitter");
import Page = require("yunos/page/Page");
import SmilePay = require("./SmilePay");
/**
 * <p>BiometricManager aims to provide biometric capbility and maintain the biometric <br>
 *  information.<br>
 *  For devices with cameras, BiometricManager warms up the camera device and scanning <br>
 *  for valid faces, provides face registration and authentication capabilities.</p>
 *  @example
 *  let BiometricManager = require("yunos/device/biometrics/BiometricManager");
 *  let biometricManager = BiometricManager.getInstance(context);
 * @extends yunos.core.EventEmitter
 * @memberof yunos.device.biometrics
 * @public
 * @since 4
 * @hiddenOnPlatform auto
 *
 */
declare class BiometricManager extends EventEmitter {
    private _context;
    private _xSrvEventHandle;
    private _xService;
    private _xSrvConnListener;
    private _smilePay;
    private _faceIDService;
    private static instance;
    /**
     * Create instance of BiometricManager.
     * @public
     * @param {yunos.cloudfs.Context} context - The context of app page.
     * @since 4
     *
     */
    public constructor(context: Page);
    private _registerFace(id: string): void;
    /**
     * <p>This call warms up the camera device and starts scanning for valid faces.<br>
     * It terminates when "stop" API is called or when "destroy" API is called.<br>
     * Once appearing face is detected, register the specific face with given face id.<br>
     * Register listener with event named "onFaceRegistered" to receive the result asynchronously.</p>
     * @example Face registration example:
     * let biometricManager = BiometricManager.getInstance(context);
     * biometricManager.registerFace(id);
     * manager.on("onFaceRegistered", (result) => {
     *  });
     * @param {string} id - the face id.
     * @public
     * @since 4
     *
     */
    public registerFace(id: string): void;
    private _authenticateFace(): void;
    /**
    * <p>This call warms up the camera device and starts scanning for valid faces.<br>
    * It terminates when "stop" API is called or when "destroy" API is called.<br>
    * Once face detected, search the face feature and find out the registration information.<br>
    * Register listener with event named "onFaceRecognized" to receive the result asynchronously.</p>
    * @example Face authentication example:
    * let biometricManager = BiometricManager.getInstance(context);
    * biometricManager.authenticateFace();
    * manager.on("onFaceAuthenticated", (result) => {
    *  });
    * @public
    * @since 4
    *
    */
    public authenticateFace(): void;
    /**
     * <p>The callback is called by queryBiometricInfo method.</p>
     * @callback yunos.device.biometrics.BiometricManager~queryCallback
     * @param {Object} info - JSON formated object which contains biometric information.<br>
     * There are properties [id] [face] [voice] in the object now.
     * @private
     */
    /**
     * <p>Query registered biometric infomation.</p>
     * @param {yunos.device.biometrics.BiometricManager~queryCallback} callback - <br>
     * The callback would be called when biometric infomation is searched.
     * @public
     * @since 4
     *
     */
    public queryBiometricInfo(callback: Function): void;
    /**
     * <p>The callback is called by deleteBiometricInfo method.</p>
     * @callback yunos.device.biometrics.BiometricManager~deleteCallback
     * @param {Object} error - JSON formated object which contains error info.<br>
     * The object is null when the invocation is succeeded.<br>
     * There are properties [code] [message] in the object now.
     * @private
     */
    /**
     * <p>Delete registered biometric infomation according to given parameters.</p>
     * @param {Object} info - the JSON formated biometric infomation.
     * @param {string} token - the SMS verification result.
     * @param {yunos.device.biometrics.BiometricManager~deleteCallback} callback - <br>
     * The callback would be called when biometric infomation is deleted.
     * @public
     * @since 4
     *
     */
    public deleteBiometricInfo(info: Object, token: string, callback: Function): void;
    /**
     * <p>The callback is called by deleteBiometricInfo method.</p>
     * @callback yunos.device.biometrics.BiometricManager~uploadCallback
     * @param {Object} error - JSON formated object which contains error info.<br>
     * The object is null when the invocation is succeeded.<br>
     * @private
     */
    /**
     * <p>Update biometric infomation on cloud and locally.</p>
     * @param {Object} info - the JSON formated biometric infomation.
     * @param {string} token - the SMS verification result.
     * @param {yunos.device.biometrics.BiometricManager~uploadCallback} callback - <br>
     * The callback would be called when biometric infomation is uploaded.
     * @public
     * @since 4
     *
     */
    public uploadBiometricInfo(info: Object, token: string, callback: Function): void;
    /**
     * <p>The callback is called by generateBiometricID method.</p>
     * @callback yunos.device.biometrics.BiometricManager~generateCallback
     * @param {string} id - the unique biometric ID.
     * @private
     */
    /**
     * <p>Generate biometric ID.</p>
     * @param {yunos.device.biometrics.BiometricManager~generateCallback} callback - <br>
     * The callback would be called when biometric ID is generated.
     * @public
     * @since 4
     *
     */
    public generateBiometricID(callback: Function): void;
    /**
     * <p>The callback is called by getFaceValidity method.</p>
     * @callback yunos.device.biometrics.BiometricManager~validityCallback
     * @param {boolean} validity - the validity of face recognition hardware.
     * @private
     */
    /**
     * <p>Get the validity of face recognition hardware.</p>
     * @param {yunos.device.biometrics.BiometricManager~validityCallback} callback - <br>
     * The callback would be called when obtained face recognition hardware validity.
     * @public
     * @since 4
     *
     */
    public getFaceValidity(callback: Function): void;
    /**
     * <p>The callback is called by getVersion method.</p>
     * @callback yunos.device.biometrics.BiometricManager~versionCallback
     * @param {string} version - the version of biometric service.
     * @private
     */
    /**
     * <p>Get the version of biometric service.</p>
     * @param {yunos.device.biometrics.BiometricManager~versionCallback} callback - <br>
     * The callback would be called when the version of biometric service is obtained.
     * @public
     * @since 4
     *
     */
    public getVersion(callback: Function): void;
    private _initListener(): void;
    private _stop(): void;
    /**
     * <p>Stop the biometric service.</p>
     * @public
     * @since 4
     *
     */
    public stop(): void;
    /**
     * <p>Destroy the biometric service.</p>
     * @public
     * @since 4
     *
     */
    public destroy(): void;
    public readonly smilePay: SmilePay;
    private registerFaceIDEvents(): void;
    /**
     * <p>This call scanning for valid faces.<br>
     * @example Face registration example:
     * let biometricManager = BiometricManager.getInstance(context);
     * biometricManager.on("faceRegistered", (result) => {
     *  });
     * biometricManager.registerFaceEx(id);
     * @param {string} id - the user id.
     * @param {string | undefined} subID - the sub id.
     *
     */
    public registerFaceEx(id: string, subID?: string): number;
    /**
     * <p>Stop face registe.</p>
     */
    public stopRegisterFace(): number;
    /**
     * @example Face registration example:
     * let biometricManager = BiometricManager.getInstance(context);
     * biometricManager.on("faceRecognized", (result) => {});
     * biometricManager.recognizeFace(id);
     */
    public recognizeFace(): number;
    /**
     * <p>Stop face recognition.</p>
     * Close camera and remove callbacks.
     */
    public stopRecognizeFace(): number;
    public detectFaceLiveness(livenessType: number): number;
    public stopDetectFaceLiveness(livenessType: number): void;
    /**
     * This method will be called when delete face ID.
     * @callback yunos.device.biometrics.BiometricManager~deleteFaceIDCallback
     * @param {Object} result - result
     * @example
     * let callback = function(result) {
     *     if (result && result.result == BiometricManager.FaceResult.SUCCESS) {
     *         // delete face ID success
     *     }
     * };
     */
    /**
     * <p>delete face ID.</p>
     * @param {string} id - the user id.
     * @param {string} subID - the sub id.
     * @param {yunos.device.biometrics.BiometricManager~deleteFaceIDCallback} callback -
     * Callback of delete face ID
     */
    public deleteFaceID(id: string, subID: string, callback: (p: object) => void): void;
    /**
     * <p>Determine if the face was registered, for current login user.</p>
     * @return {boolean} if current user has registered face, return true; else, return false
     */
    public hasRegisteredFaceSync(): boolean;
    /**
     * <p>Query registered face ID synchronously.</p>
     * @param {string} id - the user id.
     */
    public queryRegisteredFaceSync(id?: string): {}[] | {
        [key: string]: Object;
    };
    /**
     * <p>Reset cached recognized result synchronously.</p>
     */
    public queryRecognizedFaceSync(appId?: string): Object;
    /**
     * <p>Reset cached recognized result.</p>
     */
    public resetRecognizedFace(): number;
    /**
     * @param id
     * @param label
     */
    public bindFace(id: string, appId: string): number;
    public stopBindFace(): number;
    public unBindFace(id: string): number;
    public isFaceBoundSync(id: string): boolean;
    /**
     *  <p>Enable face for the id.</p>
     */
    public enableFaceID(id: string): number;
    /**
     *  <p>Disable face for the id.</p>
     */
    public disableFaceID(id: string): number;
    /**
     *  <p>Check if face id is enabled. </p>
     */
    public isFaceIDEnabled(id: string): boolean;
    /**
     * <p>Query all registered face ID synchronously, including disabled face ids.</p>
     */
    public queryAllRegisteredFaceSync(id: string): {}[] | {
        [key: string]: Object;
    };
    /**
     * <p>set ut config.</p>
     */
    public configBioUt(config: string): void;
    /**
     * <p>Enum for face registration and authentication result.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly FaceResult: {
        /**
         * face registration or authentication success, when get this value.
         * @public
         * @since 4
         */
        SUCCESS: int;
        /**
         * face registration or authentication got unknown error, when get this value.
         * @public
         * @since 4
         */
        ERROR: int;
        /**
         * face registration information failed to save, when get this value.
         * @public
         * @since 4
         */
        REGISTER_SAVE_FAIL: int;
        /**
         * find existed face id in TKS, when get this value.
         * @public
         * @since 4
         */
        ID_EXISTED: int;
        /**
         * delete non-exist face id in TKS, when get this value.
         * @public
         * @since 4
         */
        ID_NOT_EXIST: int;
        /**
         * liveness detection failed, when get this value.
         * @public
         * @since 4
         */
        LIVE_DETECT_FAIL: int;
    };
    public static readonly FaceLivenessType: {
        FACE_LIVENESS_NOTHING: int;
        FACE_LIVENESS_BLINK: int;
        FACE_LIVENESS_NOD: int;
        FACE_LIVENESS_HEADSHAKE: int;
        FACE_LIVENESS_MOUTH: int;
    };
    /**
     * <p>Enum for face registration and authentication result.</p>
     */
    public static readonly RecognizeMode: {
        /**
         * face registration or authentication got unknown error, when get this value.
         */
        DEFAULT: int;
        /**
         * face registration or authentication success, when get this value.
         */
        AUTO: int;
    };
    public static releaseInstance(): void;
    public static getInstance(context: Page): BiometricManager;
}
export = BiometricManager;
