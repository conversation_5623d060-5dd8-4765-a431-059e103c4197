import YObject = require("yunos/core/YObject");
declare class SystemServiceAdapter extends YObject {
    private _iface;
    private bus;
    private service;
    private _permission;
    constructor(permission?: string);
    registerSetSettings(cb: (name: string, value: string) => Promise<string>): void;
    registerGetSettings(cb: (name: string) => Promise<string>): void;
    registerGetURLAddress(cb: (name: string) => Promise<string>): void;
    registerGetConfigs(cb: (name: string) => Promise<string>): void;
    registerGetConfigsVer(cb: () => Promise<string>): void;
    registerSetBrightnessMode(cb: (brightness_mode: string) => Promise<string>): void;
    registerSetAutoBrightnessValueMode(cb: (brightness_value_mode: number) => Promise<string>): void;
    registerSetBrightnessValue(cb: (brightness_level: number, distinguish: string) => Promise<string>): void;
    registerGetBrightnessValue(cb: () => Promise<string>): void;
    registerGetAutoBrightnessValueMode(cb: () => Promise<string>): void;
    registerGetDayNightMode(cb: () => Promise<string>): void;
    registerSetAutoTimeStatus(cb: (gps_mode: number) => Promise<string>): void;
    registerGetAutoTimeStatus(cb: () => Promise<string>): void;
    registerSetDate(cb: (year: number, month: number, days: number) => Promise<string>): void;
    registerSetTime(cb: (hour: number, minute: number) => Promise<string>): void;
    registerGetGPSTimeFromVIP(cb: () => Promise<string>): void;
    registerGetUtcTime(cb: () => Promise<string>): void;
    registerSetTimeZone(cb: (time_zone: number) => Promise<string>): void;
    registerGetTimeZone(cb: () => Promise<string>): void;
    registerSetDateFormat(cb: (date_format: number) => Promise<string>): void;
    registerGetDateFormat(cb: () => Promise<string>): void;
    registerSetTimeFormat(cb: (time_format: number) => Promise<string>): void;
    registerGetTimeFormat(cb: () => Promise<string>): void;
    registerSetSystemLanguage(cb: (system_language: number) => Promise<string>): void;
    registerGetSystemLanguage(cb: () => Promise<string>): void;
    registerGetVehicleInfo(cb: () => Promise<string>): void;
    registerGetMemoryInfo(cb: () => Promise<string>): void;
    registerSetAppNetwork(cb: (network_type: string, network_mode: number) => Promise<string>): void;
    registerGetAppNetwork(cb: (select_mode: string) => Promise<string>): void;
    registerGetVehicleType(cb: () => Promise<string>): void;
    registerGetVehicleBirthday(cb: () => Promise<string>): void;
    registerGetApproxRemainingDataAmount(cb: (data_type: number, threshold: number) => Promise<string>): void;
    registerGetTotalDataAmount(cb: (data_type: number) => Promise<string>): void;
    registerSetDimnessMode(cb: (data_type: string) => Promise<string>): void;
    registerGetDimnessMode(cb: () => Promise<string>): void;
    registerGetURLAddressByType(cb: (name: string) => Promise<string>): void;
    registerGetBrightnessLevel(cb: () => Promise<string>): void;
    emitRecoveryFinished(res_msg: string): boolean;
    emitRecoveryStart(res_msg: string): boolean;
    emitLightSensorValueChanged(lightsensor_value: string): boolean;
    emitCallAlertStatusChanged(settings_status: string): boolean;
    emitSmartAudioSettingChanged(settings_status: string): boolean;
    emitSmartAudioAutoUpdateSettingChanged(settings_status: string): boolean;
    emitNotificationAlertStatusChanged(settings_status: string): boolean;
    emitSMSAlertStatusChanged(settings_status: string): boolean;
    emitVehicleWarningAlertStatusChanged(settings_status: string): boolean;
    emitSteeringWheelKeyStsChanged(settings_status: string): boolean;
    emitMapScaleAutoSettingChanged(settings_status: string): boolean;
    emitSmartAirConditionSettingChanged(settings_status: string): boolean;
    emitAutoInstallStatusChanged(settings_status: string): boolean;
    emitKeyVolumeStatusChanged(settings_status: string): boolean;
    emitBrightnessValueChanged(brightness_status: string): boolean;
    emitDayNightModeChanged(brightness_status: string): boolean;
    emitAutoBrightnessValueModeChanged(brightness_value_mode: string): boolean;
    emitNoScreenTouch(notouch_time: string): boolean;
    emitAutoTimeStatusChanged(gps_mode: string): boolean;
    emitDateChangeByUser(date: string): boolean;
    emitTimeChangeByUser(time: string): boolean;
    emitTimeZoneChanged(time_zone: string): boolean;
    emitDateFormatChanged(date_format: string): boolean;
    emitTimeFormatChanged(time_format: string): boolean;
    emitSystemLanguageChanged(system_language: string): boolean;
    emitVehicleinfoChanged(system_info: string): boolean;
    emitAppNetworkChanged(network_status: string): boolean;
    emitUtcTimeChanged(utc_time: string): boolean;
    emitLowDataAmountChanged(approx_remaining: string): boolean;
    emitEffectiveDataAmountChanged(remaining: string): boolean;
    emitDimnessModeChanged(dimness_mode: string): boolean;
    emitURLAddressUpdated(resMsg: string): boolean;
    emitZebraCalIsNotFull(resMsg: string): boolean;
    emitAtsOnOffSettingChanged(resMsg: string): boolean;
    emitrkeytypechanged(resMsg: string): boolean;
    emitlkeytypechanged(resMsg: string): boolean;
    destroy(): void;
}
export = SystemServiceAdapter;
