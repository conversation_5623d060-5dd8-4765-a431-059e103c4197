import Shader = require("./Shader");
/**
 * <p>This class provide the colorize shader support for user interface interface components.</p>
 * @extends yunos.graphics.shader.Shader
 * @memberof yunos.graphics.shader
 * @public
 * @since 2
 */
declare class Colorize extends Shader {
    private _hue;
    private _saturation;
    private _lightness;
    /**
     * Defines how much the source hue is increased or decreased.
     * The value ranges from 0 to 360. By default, the property is set to 180 (no change).
     * @name yunos.graphics.shader.Colorize#hue
     * @default 0
     * @throws {TypeError} If hue is not a number.
     * @type {number}
     * @public
     * @since 2
     */
    public hue: number;
    /**
     * Defines how much the source saturation is increased or decreased.
     * The value ranges from 0 to 1. By default, the property is set to 0.5 (no change).
     * @name yunos.graphics.shader.Colorize#saturation
     * @default 0
     * @throws {TypeError} If saturation is not a number.
     * @type {number}
     * @public
     * @since 2
     */
    public saturation: number;
    /**
     * Defines how much the source lightness is increased or decreased.
     * The value ranges from 0 to 1. By default, the property is set to 0.5 (no change).
     * @name yunos.graphics.shader.Colorize#lightness
     * @default 0
     * @throws {TypeError} If lightness is not a number.
     * @type {number}
     * @public
     * @since 2
     */
    public lightness: number;
}
export = Colorize;
