/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use sound";

import log = require("./utils/log");
const TAG = "Features";

interface IFeatures {
    [name: string]: boolean;
}

interface IFeature {
    has(key: string): boolean;
}

let Features: IFeatures = {};
let supportUsb: boolean;
let supportDlna: boolean;

function isSupportUsb() {
    if (supportUsb === undefined) {
        const Feature = <IFeature> require("yunos/util/Feature");
        if (Feature.has("YUNOS_SYSCAP_USB_VIDEO")) {
            supportUsb = true;
        } else {
            supportUsb = false;
        }
    }
    log.D(TAG, "isSupportUsb", supportUsb);
    return supportUsb;
}

function isSupportDlna() {
    if (supportDlna === undefined) {
        const Feature = <IFeature> require("yunos/util/Feature");
        if (Feature.has("YUNOS_SYSCAP_DLNA")) {
            supportDlna = true;
        } else {
            supportDlna = false;
        }
    }
    log.D(TAG, "isSupportDlna", supportDlna);
    return supportDlna;
}

Features.SUPPORT_USB = isSupportUsb();
Features.SUPPORT_DLNA = isSupportDlna();
export = Features;
