import View = require("yunos/ui/view/View");
import EventEmitter = require("yunos/core/EventEmitter");
import MotionGestureEvent = require("yunos/ui/event/MotionGestureEvent");
import { MotionEvent, TipDuration, ArrowPosition, MotionReferrer } from "../control/Types";
declare class MotionAgent extends EventEmitter {
    private options;
    private _tipInstance;
    private _expectedMotionEvents;
    constructor(options: {
        anchorView: View;
        motionList: {
            type: MotionEvent;
            text: string;
        }[];
        referrer: MotionReferrer;
        dialogLevel?: number;
        tipDuration?: TipDuration;
        arrowPosition?: ArrowPosition;
        tipOffsetX?: number;
        tipOffsetY?: number;
        tipWidth?: number;
    });
    tipDuration: TipDuration;
    arrowPosition: ArrowPosition;
    tipOffsetX: number;
    tipOffsetY: number;
    tipWidth: number;
    dialogLevel: number;
    excute(): void;
    private _prepareTip;
    private _initMotionEvents;
    private _clearMotionGestures;
    protected onNodEvent(ev: MotionGestureEvent): void;
    protected onShakeEvent(ev: MotionGestureEvent): void;
    protected onHoldEvent(ev: MotionGestureEvent): void;
    protected onHoldEndEvent(ev: MotionGestureEvent): void;
    destroy(): void;
}
export = MotionAgent;
