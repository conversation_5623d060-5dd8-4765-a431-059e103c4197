/**
 * <p>The root class of the YunOS cloud app framework(CAF) class hierarchy.
 * All CAF class inherit either directly or indirectly from this class.</p>
 * @memberof yunos.core
 * @public
 * @since 1
 */
declare class YObject {
    protected __hasdestroyed: boolean;
    private _guid;
    /**
     * <p>Release the instance of YObject.</p>
     * @example
     * class Data extends YObject {
     *     constructor(title, content) {
     *         this.title = title;
     *         this.content = content;
     *     }
     *
     *     destroy() {
     *         this.title = null;
     *         this.content = null;
     *         super.destroy();
     *     }
     * }
     *
     * var data = new Data("YunOS", "A great computer Operation System");
     * data.destroy();
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * Whether the YObject has been destroyed.
     * @name yunos.core.YObject#destroyed
     * @type {boolean}
     * @readonly
     * @public
     * @since 2
     */
    public readonly destroyed: boolean;
    /**
     * <p>Compare if two objects are equals to each other.</p>
     * <p>You can overwrite this method to implement object comparison.</p>
     * @example
     * class URL extends YObject {
     *     equals(value) {
     *        return value.urlString === this.urlString;
     *     }
     * }
     * @param {yunos.core.YObject} value - another object to compare
     * @return {boolean} Compare results.
     * @public
     * @since 1
     */
    public equals(value: YObject): boolean;
    /**
     * <p>Clone an object to another with the same property value.</p>
     * <p>You can overwrite this method to implement while cloning object.</p>
     * @example
     * class People extends YObject {
     *     clone() {
     *         var obj = super.clone();
     *         obj.name = this.name;
     *         obj.age = this.age;
     *         return obj;
     *     }
     * }
     * @return {yunos.core.YObject} cloned object
     * @public
     * @since 1
     */
    public clone(): YObject;
    /**
     * <p>Returns a string containing a concise, human-readable description of this object.</p>
     * <p>Subclasses are encouraged to override this method and provide an implementation that
     * takes into account the object's type and data.</p>
     * @return {string} the human-readable description of this object.
     * @public
     * @since 1
     */
    public toString(): string;
}
export = YObject;
