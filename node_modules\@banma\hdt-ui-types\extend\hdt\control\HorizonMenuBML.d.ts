import CompositeView = require("yunos/ui/view/CompositeView");
import Bitmap = require("yunos/graphics/Bitmap");
interface IStyle {
    fontNormalSize: number;
    fontSelectSize: number;
    textNormalColor: string;
    textSelectColor: string;
}
declare class HorizonMenuBML extends CompositeView {
    private _fontNormalSize;
    private _fontSelectSize;
    private _textNormalColor;
    private _textSelectColor;
    private _currentIndex;
    private _items;
    private _inAnimation;
    private _timeoutCallback;
    private __timer;
    private rectangle;
    private _tapRecognizer;
    readonly defaultStyleName: string;
    constructor(...args: Object[]);
    items: string[];
    addItem(text: string, icon?: string | Bitmap): void;
    getItem(index: number): CompositeView;
    currentIndex: number;
    private smoothToIndex;
    private setTextViewState;
    removeItem(index: number): void;
    private addRealItem;
    private updateChildLayout;
    private getTransition;
    private onTapItem;
    protected applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    destroy(recursive?: boolean): void;
}
export = HorizonMenuBML;
