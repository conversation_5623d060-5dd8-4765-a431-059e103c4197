<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    clipBound="false"
    layout="{layout.online_category_container}"
    propertySetName="online">

    <GridView
        id="id_category_list"
        columns="{config.ONLINE_CATEGORY_COLUMNS_NUM}"
        rowSpacing="{config.ITEM_SPACE}"
        columnSpacing="{config.ITEM_SPACE}"
        orientation="{enum.GridView.Orientation.Vertical}"
        focusable="false"
        scrollBarCustomized="true"
        verticalFadingEdgeEnabled="true"/>

    <ScrollBar
        id="id_scrollbar"
        width="{config.SCROLL_BAR_SIZE}"
        autoHidden="false"/>

    <LoadingBM
        id="id_loading"
        sizeStyle="{enum.LoadingBM.SizeStyle.L}"/>

    <CompositeView
        id="id_network"
        visibility="{enum.View.Visibility.None}"
        layout="{layout.online_category_network_error}">
        <TextView
            id="id_error_info"
            text="{string.NETWORK_ERROR}"
            propertySetName="extend/hdt/FontTitle2"/>
        <ButtonBM
            id="id_btn_retry"
            text="{string.RELOAD}"
            width="{sdp(240)}"
            height="{sdp(64)}"
            colorType="{enum.ButtonBM.ColorType.Secondary}"
            buttonType="{enum.ButtonBM.ButtonType.Ghost}"/>
    </CompositeView>
</CompositeView>
