import CompositeView = require("yunos/ui/view/CompositeView");
import { ButtonLColorType, ButtonLType } from "./Types";
declare enum DisplayMode {
    LoadingMode = 0,
    ErrorMode = 1
}
interface IStyle {
    loadingAnimWidth: number;
    loadingAnimHeight: number;
    loadingAnimOffsetYPct: number;
    loadingAnimPaddingTop: number;
    loadingTextPropertySet: string;
    loadingTextColor: string;
    loadingTextMarginTop: number;
    loadingTextLineHeight: number;
    loadingTextContent: string;
    errorImageTop: number;
    errorImageSrc: string;
    errorTitlePropertySet: string;
    errorTitleColor: string;
    errorTitleMarginTop: number;
    errorTitleContent: string;
    errorTextPropertySet: string;
    errorTextColor: string;
    errorTextMarginTop: number;
    errorTextMaxWidth: number;
    errorTextContent: string;
    retryButtonWidth: number;
    retryButtonType: ButtonLType;
    retryButtonColorType: ButtonLColorType;
    retryButtonText: string;
    retryButtonMarginTop: number;
    loadingStyleName: string;
}
declare class LoadingPageBML extends CompositeView {
    static readonly DisplayMode: typeof DisplayMode;
    private _errorTitleVisible;
    private _errorTextVisible;
    private _errorImageVisible;
    private _retryButtonVisible;
    private _displayMode;
    private loadingContainer;
    private loadingView;
    private loadingTextView;
    private errorImageView;
    private errorTitleView;
    private errorTextView;
    private retryButton;
    private _loadingStyleName;
    private _loadingAnimWidth;
    private _loadingAnimHeight;
    private _loadingAnimPaddingTop;
    private _loadingTextPropertySet;
    private _loadingTextColor;
    private _loadingTextMarginTop;
    private _loadingTextContent;
    private _errorImageTop;
    private _errorImageSrc;
    private _errorTitlePropertySet;
    private _errorTitleColor;
    private _errorTitleMarginTop;
    private _errorTitleContent;
    private _errorTextPropertySet;
    private _errorTextColor;
    private _errorTextMarginTop;
    private _errorTextMaxWidth;
    private _errorTextContent;
    private _retryButtonWidth;
    private _retryButtonType;
    private _retryButtonColorType;
    private _retryButtonText;
    private _retryButtonMarginTop;
    readonly defaultStyleName: string;
    constructor();
    updateloadingStyle(): void;
    private _initLoadingViews;
    private getLoadingContainer;
    private _initErrorViews;
    private _validDisplayMode;
    private getLoadingView;
    private getLoadingTextView;
    errorText: string;
    errorImgSrc: string;
    displayMode: DisplayMode;
    loadingText: string;
    errorTitle: string;
    retryButtonText: string;
    retryButtonVisible: boolean;
    errorTitleVisible: boolean;
    _relayoutErrorMode(): void;
    errorTextVisible: boolean;
    errorImageVisible: boolean;
    private getErrorImageView;
    private getErrorTitleView;
    private getErrorTextView;
    private getRetryButton;
    private _initLayout;
    protected applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    private prepareForShowing;
    private prepareForClosing;
    destroy(recursive?: boolean): void;
}
export = LoadingPageBML;
