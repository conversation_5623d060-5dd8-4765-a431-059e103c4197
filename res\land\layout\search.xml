<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_search"
    layout="{layout.search}"
    propertySetName="search">

    <NavigationBar id="id_nav"/>

    <CompositeView
        id="id_search_history"
        layout="{layout.search_history}">
        <TextView
            id="id_sh_title"
            height="{config.SEARCH_BANNER_HEIGHT}"
            text="{string.SEARCH_HISTORY_TITLE}"
            propertySetName="extend/hdt/FontHeadline"/>
        <CompositeView id="id_sh_content"/>
    </CompositeView>

    <CompositeView
        id="id_result_container"
        visibility="{enum.View.Visibility.Hidden}"
        layout="{layout.result_container}">
        <CompositeView
            id="id_local_container"
            visibility="{enum.View.Visibility.Hidden}"
            layout="{layout.search_local_result}">
            <CompositeView
                id="id_local_banner"
                height="{config.SEARCH_BANNER_HEIGHT}"
                layout="{layout.search_result_banner}">
                <TextView
                    id="id_local_title"
                    text="{string.USB_TITLE}"
                    propertySetName="extend/hdt/FontHeadline"/>
                <CompositeView
                    id="id_local_more"
                    height="{config.SEARCH_MORE_HEIGHT}"
                    visibility="{enum.View.Visibility.Hidden}"
                    layout="{layout.search_local_more}">
                    <TextView
                        id="id_local_more_tips"
                        text="{string.SEARCH_MORE}"
                        propertySetName="extend/hdt/FontBody4"/>
                    <ImageView
                        id="id_local_more_icon"
                        scaleType="{enum.ImageView.ScaleType.Center}"/>
                </CompositeView>
            </CompositeView>
            <GridView
                id="id_local_grid"
                orientation="{enum.GridView.Orientation.Horizontal}"
                rows="{config.LOCAL_ROWS_NUM}"
                rowSpacing="{config.ITEM_SPACE}"
                columnSpacing="{config.ITEM_SPACE}"
                horizontalFadingEdgeEnabled="true"
                focusable="false"/>
        </CompositeView>
        <CompositeView
            id="id_online_container"
            visibility="{enum.View.Visibility.None}"
            layout="{layout.search_online_result}">
            <TextView
                id="id_online_title"
                height="{config.SEARCH_BANNER_HEIGHT}"
                verticalAlign="{enum.TextView.VerticalAlign.Middle}"
                text="{string.ONLINE_TITLE}"
                propertySetName="extend/hdt/FontHeadline"/>
            <ListView
                id="id_online_list"
                orientation="{enum.ListView.Orientation.Horizontal}"
                spacing="{config.ITEM_SPACE}"
                scrollBarCustomized="true"
                horizontalFadingEdgeEnabled="true"
                focusable="false"/>
        </CompositeView>
        <ScrollBar
            id="id_local_scrollbar"
            height="{config.SCROLL_BAR_SIZE}"
            autoHidden="true"/>
        <ScrollBar
            id="id_online_scrollbar"
            height="{config.SCROLL_BAR_SIZE}"
            autoHidden="true"/>
    </CompositeView>

    <LoadingPageBM
        id="id_loading_page"
        visibility="{enum.View.Visibility.None}"/>

    <include
        id="id_empty"
        markup="empty"
        visibility="{enum.View.Visibility.None}"/>
</CompositeView>
