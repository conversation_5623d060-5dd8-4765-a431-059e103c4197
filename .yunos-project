{"uuid": "WmBvyr3RD", "version": "1.0", "lastDevice": "", "product": "EP33L", "keyStoreType": "dev", "keyStorePath": "", "keyStoreAlias": "", "keyStorePassword": "", "jsni": false, "projectType": "caf", "supportSystemType": "ivi", "cafApiLevel": "6", "script": {"runPreScript": false, "preScript": "# Before running the page in project, you may have pre-step to do, such as:\n# mkdir -p /tmp/test && cd /tmp/test\n# ...", "preScriptTimeout": 5000, "runPostScript": false, "postScript": "# After running the page in project, you may have post-step to do, such as:\n# rm -rf /tmp/test\n# ...", "postScriptTimeout": 5000}, "page": {}}