import YObject = require("../core/YObject");
/**
 * Range is a mathematical model representing a range with location, length.
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 2
 */
declare class Range extends YObject {
    private _location;
    private _length;
    private _isNotFound;
    /**
     * Constructor for Range.
     * @param {number} [location = 0] - location for the range
     * @param {number} [length = 0] - length for the range
     * @public
     * @since 2
     */
    public constructor(location?: number, length?: number);
    /**
     * Creates a not found range.
     * @return {yunos.util.Range} the generated range that is marked as not found
     * @public
     * @since 2
     */
    public static createNotFound(): Range;
    /**
     * Creates an infinite range.
     * @return {yunos.util.Range} the generated range that is marked as infinite
     * @public
     * @since 2
     */
    public static createInfinite(): Range;
    /**
     * Location represents the start point of the range.
     * @name yunos.util.Range#location
     * @type {number}
     * @public
     * @since 2
     */
    public location: number;
    /**
     * Length of the range. Must be a positive number.
     * @name yunos.util.Range#length
     * @type {number}
     * @throws {TypeError} If the param is not a non-negative number.
     * @public
     * @since 2
     */
    public length: number;
    /**
     * Check if the range is not found.
     * @return {boolean} boolean result indicates whether the range is not found
     * @public
     * @since 2
     */
    public isNotFound(): boolean;
    /**
     * Set whether the range is not found or not.
     * If you do not pass any arguments, the range will be set as not found.
     * @param {boolean} [value = true] - the value that determines whether the range should be not found
     * @public
     * @since 2
     */
    public setNotFound(value?: boolean): void;
    /**
     * Check if the range is infinite.
     * If you specify the length of a range to be +Infinity, the range will be a infinite range.
     * @return {boolean} boolean result indicates whether the range is infinite
     * @public
     * @since 2
     */
    public isInfinite(): boolean;
    /**
     * Make a copy of the Range instance.
     * @return {yunos.util.Range}
     * @public
     * @since 2
     */
    public clone(): Range;
}
export = Range;
