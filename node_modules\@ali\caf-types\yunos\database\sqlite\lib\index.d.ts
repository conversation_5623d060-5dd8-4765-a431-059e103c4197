import { ContentValuesAddon } from "node_sql_caf.node";
import { CursorWindowAddon } from "node_sql_caf.node";
import SQLiteConstants = require("yunos/database/sqlite/lib/SQLiteConstants");
import SQLiteCursor = require("yunos/database/sqlite/lib/SQLiteCursor");
import SQLiteReturnCode = require("yunos/database/sqlite/lib/SQLiteReturnCode");
import SQLiteSync = require("yunos/database/sqlite/lib/SQLiteSync");
declare class SQLiteLib {
    /**
     * @private
     * @hiddenOnPlatform auto
     */
    private static readonly ContentValues: typeof ContentValuesAddon;
    /**
     * @private
     * @hiddenOnPlatform auto
     */
    private static readonly CursorWindow: typeof CursorWindowAddon;
    private static readonly SQLiteConstants: typeof SQLiteConstants;
    private static readonly SQLiteCursor: typeof SQLiteCursor;
    private static readonly SQLiteReturnCode: typeof SQLiteReturnCode;
    private static readonly SQLiteSync: typeof SQLiteSync;
}
export = SQLiteLib;
