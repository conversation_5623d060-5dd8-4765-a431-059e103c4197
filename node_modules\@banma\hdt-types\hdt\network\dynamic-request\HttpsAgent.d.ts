import { IDomainInfo, IRequestInfo } from "./interfaces";
declare class HttpsAgent {
    private urlPromByApi;
    private urlPromByServiceType;
    constructor(urlPromByApi: (href: string) => Promise<IDomainInfo>, urlPromByServiceType: (serviceType: string) => Promise<IDomainInfo>);
    post(options: IRequestInfo): Promise<string>;
    get(options: IRequestInfo): Promise<string>;
    convertUrl(options: IRequestInfo): Promise<string>;
    private convertData;
    private request;
}
export = HttpsAgent;
