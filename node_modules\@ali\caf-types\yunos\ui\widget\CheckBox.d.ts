import CompoundButton = require("./CompoundButton");
import ImageView = require("../view/ImageView");
import TouchEvent = require("../event/TouchEvent");
import Bitmap = require("../../graphics/Bitmap");
import Gradient = require("../../graphics/Gradient");
import { Options } from "../util/TypeHelper";
import VoiceEvent = require("../event/VoiceEvent");
/**
 * <p>CheckBox is a widget for multiple choice button which could define text.</p>
 * @example
 * // Default
 * const cbDefault = new CheckBox();
 * cbDefault.checked = true;
 * cbDefault.isLightMode = true;
 * cbDefault.animatable = true;
 * @extends yunos.ui.widget.CompoundButton
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class CheckBox extends CompoundButton {
    private _primary;
    private _isLightMode;
    private _originPrimary;
    private _animatable;
    private _x;
    private _y;
    private _radius;
    private _defaultHeight;
    private _lightStyle;
    private _normalStyle;
    private _animationDuration;
    private _defaultWidth;
    private _frontView;
    private _animChecked;
    private _backView;
    private _animationSequence;
    private __scale;
    private _autoCommand;
    private _voiceHandler;
    /**
     * <p>Create a CheckBox.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destroy this CheckBox.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds the checked of CheckBox, true to check the CheckBox, false to uncheck it.</p>
     * @name yunos.ui.widget.CheckBox#checked
     * @type {boolean}
     * @default false
     * @fires yunos.ui.view.View#checkedchanged
     * @throws {TypeError} If type of parameter is not boolean.
     * @public
     * @since 1
     */
    public checked: boolean;
    /**
     * <p>Allows to enable the light mode. In this mode the check box show white background.</p>
     * @name yunos.ui.widget.CheckBox#isLightMode
     * @type {boolean}
     * @default false
     * @fires yunos.ui.view.View#propertychange
     * @throws {TypeError} If type of parameter is not boolean.
     * @public
     * @since 1
     */
    public isLightMode: boolean;
    /**
     * <p>This property holds the width of the view.</p>
     * @name yunos.ui.widget.CheckBox#width
     * @type {number}
     * @readonly
     * @override
     * @public
     * @since 1
     */
    public width: number;
    /**
     * The plain-text content that this text view is to display.
     * @name yunos.ui.view.TextView#text
     * @type {string}
     * @fires yunos.ui.view.TextView#textchange
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @override
     * @since 6
     */
    public text: string;
    /**
     * <p>This property holds the height of the view.</p>
     * @name yunos.ui.widget.CheckBox#height
     * @type {number}
     * @readonly
     * @override
     * @public
     * @since 1
     */
    public height: number;
    /**
     * <p>This property holds the background of the view, Readonly.</p>
     * @name yunos.ui.widget.CheckBox#background
     * @type {string}
     * @override
     * @public
     * @since 1
     */
    public background: string | number | Bitmap | Gradient;
    /**
     * Defines enabled state of this view, true if this checkbox is enabled, false otherwise.
     * @name yunos.ui.widget.CheckBox#enabled
     * @type {boolean}
     * @default true
     * @fires yunos.ui.widget.CheckBox#propertychange
     * @throws {TypeError} If type of parameter is not boolean.
     * @override
     * @public
     * @since 2
     */
    public enabled: boolean;
    /**
     * <p>This property holds the animatable of CheckBox. True - need animation to run, false - need not run.</p>
     * @name yunos.ui.widget.CheckBox#animatable
     * @type {boolean}
     * @default false
     * @throws {TypeError} If type of parameter is not boolean.
     * @public
     * @since 2
     */
    public animatable: boolean;
    /**
     * <p>voiceViewType.</p>
     * @override
     * @protected
     * @since 6
     */
    protected readonly voiceViewType: string;
    /**
     * <p>Defines voiceBinding of the CheckBox, if not empty to bind one view text.<br/>
     * The attributes currently supports controls bound to text component(Button,TextView and so on),After being bound, <br/>
     * the bound control cannot be registered and responded to if it has voice events.<br/>
     * In the following example, if voiceBinding is used, it responds to the events of the bound View.</p>
     * @example
     * //Use in code
     * //The bound component
     * var textView = new TextView();
     * textView.id = "textViewId";
     * textView.text = "Sound";
     * this.voiceBinding = textView.id;
     *
     * @example
     * //Use in xml
     * //The bound component
     * <TextView id="textViewId" text="Sound"></TextView>
     * <CheckBox id="checkbox" voiceBinding="textViewId"/>
     * @name yunos.ui.view.CheckBox#voiceBinding
     * @type {string}
     * @override
     * @public
     * @since 6
     */
    public voiceBinding: string;
    private _voiceEventHandler(e: VoiceEvent): void;
    private _bindVoiceEvent(handler: (...args: Object[]) => void): void;
    private _unbindVoiceEvent(handler: (...args: Object[]) => void): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.CheckBox#defaultStyleName
     * @type {string}
     * @default "CheckBox"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Sets the checked of CheckBox.</p>
     * @param {boolean} checked - Set the checked of CheckBox, true to check the CheckBox, false to uncheck it.
     * @param {boolean} animated - True need animation to run, false need not animation.
     * @throws {TypeError} If type of checked is not boolean.
     * @throws {TypeError} If type of animated is not boolean.
     * @public
     * @since 2
     */
    public setChecked(checked: boolean, animated: boolean): void;
    /**
     * <p>Apply style.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    private getStyleObj(): Options;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    private resetStyle(): void;
    private onPropertyChange(property: string, oldValue?: Object, value?: Object): void;
    private handlePropertyAndState(): void;
    private resetUI(): void;
    /**
     * @protected
     * @param {yunos.ui.TouchEvent} ev input Event
     * @override
     */
    protected onTouchStart(ev?: TouchEvent): void;
    /**
     * @protected
     * @param {yunos.ui.TouchEvent} ev input Event
     * @override
     */
    protected onTouchEnd(ev?: TouchEvent): void;
    /**
     * <p>Get the animation view.</p>
     * @return {yunos.ui.view.ImageView}
     * @protected
     * @override
     * @since 3
     */
    protected getAnimationView(): ImageView;
    /**
     * <p>Get the animation sequence of this view.</p>
     * @return {string[]} Image src
     * @protected
     * @override
     * @since 3
     */
    protected getAnimationSequence(): string[];
    /**
     * <p>The animation start.</p>
     * @protected
     * @override
     * @since 3
     */
    protected onAnimationStart(): void;
    /**
     * <p>The animation complete.</p>
     * @protected
     * @override
     * @since 3
     */
    protected onAnimationComplete(): void;
}
export = CheckBox;
