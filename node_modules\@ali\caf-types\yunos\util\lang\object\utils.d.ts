/**
 * <p>Check if the given value is `null` or `undefined`.</p>
 * @param {*} value - the value to check.
 * @return {boolean} Returns `true` if `value` is `null` or `undefined`, else `fasle`;s
 * @private
 */
export declare function isNone(value: Object): boolean;
/**
 * <p>Checks if `value` is the [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types) of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String("")`)
 *
 * @param {*} value - The value to check.
 * @returns {boolean} Returns `true` if `value` is the type of object, else `false`.
 * @private
 */
export declare function isObject(value: Object): boolean;
/**
 * <p>pPerforms a [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero) comparison between two values to determine if they are equivalent.</p>
 * @param {*} value - The value to compare.
 * @param {*} other - The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * equal(object, object);
 * // => true
 *
 * equal(object, other);
 * // => false
 *
 * equal('a', 'a');
 * // => true
 *
 * equal('a', Object('a'));
 * // => false
 *
 * equal(NaN, NaN);
 * // => true
 * @private
 */
export declare function equal(value: Object, other: Object): boolean;
/**
 * <p>Checks if `value` is array-like. A value is considered array-like if it's<br/>
 * not a function and has a `value.length` that's an integer greater than or</br>
 * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.</p>
 * @param {*} value - The value to check.
 * @returns {boolean} Returns `true` if `value` is array-like, else `false`.
 * @example
 *
 * isArrayLike([1, 2, 3]);
 * // => true
 *
 * isArrayLike(document.body.children);
 * // => true
 *
 * isArrayLike('abc');
 * // => true
 *
 * isArrayLike(_.noop);
 * // => false
 * @private
 */
export declare function isArrayLike(value: Object): boolean;
/**
 * Checks if `value` is object-like. A value is object-like if it's not `null` and has a `typeof` result of "object".
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
 * @example
 *
 * isObjectLike({});
 * // => true
 *
 * isObjectLike([1, 2, 3]);
 * // => true
 *
 * isObjectLike(_.noop);
 * // => false
 *
 * isObjectLike(null);
 * // => false
 * @private
 */
export declare function isObjectLike(value: Object): boolean;
/**
 * This method is like `isArrayLike` except that it also checks if `value` is an object.
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array-like object, else `false`.
 * @example
 *
 * isArrayLikeObject([1, 2, 3]);
 * // => true
 *
 * isArrayLikeObject(document.body.children);
 * // => true
 *
 * isArrayLikeObject('abc');
 * // => false
 *
 * isArrayLikeObject(_.noop);
 * // => false
 * @private
 */
export declare function isArrayLikeObject(value: Object): boolean;
/**
 * <p>Checks if `value` is a valid array-like index.</p>
 * @param {*} value The value to check.
 * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.
 * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.
 * @private
 */
export declare function isIndex(value: Object, length?: number): boolean;
/**
 * <p>Checks if `value` has element of `NaN`.</p>
 * @param {Array} array - The array to check.
 * @returns {Array} Returns `true` if `value` has `NaN`, else `false`.
 * @private
 */
export declare function isIncludeNaN(array: Object[]): boolean;
