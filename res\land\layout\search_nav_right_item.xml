<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_right_item"
    width="{config.SEARCH_INPUT_WIDTH}"
    height="{config.SEARCH_INPUT_HEIGHT}"
    layout="{layout.search_header}"
    propertySetName="search">
    <CompositeView
        id="id_search_container"
        layout="{layout.search_input}"
        propertySetName="extend/hdt/InputBox">
        <ImageView
            id="id_search_icon"
            width="{sdp(72)}"
            height="{sdp(72)}"
            scaleType="{enum.ImageView.ScaleType.Center}"/>
        <TextField
            id="id_search_input"
            width="{config.SEARCH_TEXT_FIELD_WIDTH}"
            maxLength="25"
            clearable="true"
            placeholder="{string.SEARCH_HINT}"
            inputMethodReturnKeyType="{enum.TextField.ReturnKeyType.ReturnKeySearch}"/>
        <ImageView
            id="id_search_close"/>
    </CompositeView>
</CompositeView>
