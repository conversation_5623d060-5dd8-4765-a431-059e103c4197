import AlertDialog = require("./AlertDialog");
import GridView = require("../view/GridView");
import CompositeView = require("../view/CompositeView");
/**
 * FileDialog widget.
 * A dialog that display files under the folder path.
 * @extends yunos.ui.widget.AlertDialog
 * @memberOf yunos.ui.widget
 * @private
 */
declare class FileDialog extends AlertDialog {
    private _conditionView;
    private _initialPath;
    private _folderPath;
    private _directPath;
    private _styleType;
    private _posSelect;
    private _formatSelect;
    private saveField;
    private _gridView;
    private _fileLists;
    private _adapter;
    private _textArea;
    /**
     * @private
     */
    private folderPath: string;
    private updateFileDialog(): void;
    /**
     * Create the condition view for the dialog.
     * @return {yunos.ui.view.CompositeView} the condition view.
     * @protected
     */
    protected getConditionView(): CompositeView;
    private onSaveFieldTextChange(): void;
    private createAdapter(): void;
    /**
     * Create the content view for the file dialog.
     * Use a gridView as the custom view.
     * @override
     * @protected
     */
    /**
     * Create the content view for the file dialog.
     * Use a gridView as the custom view.
     * @returns {yunos.ui.view.CompositeView} the content view.
     * @override
     * @public
     * @since 6
     */
    public getContentView(path?: string): GridView;
    private getFileList(path?: string): {
        text: string;
        style: string;
    }[];
    private refresh(path: string): void;
    private up(path: string): void;
    /**
     * Initial path for Filedialog.
     * File dialog will be shown under this certain path each time it shows.
     * @name yunos.ui.widget.FileDialog#initialPath
     * @type {string}
     * @throws {TypeError} If it is not a string
     * @private
     */
    private initialPath: string;
    private init(): void;
    /**
     * Listener function for button tap event.
     * @override
     * @protected
     */
    protected onTap(index: number): void;
}
declare namespace FileDialog {
    /**
     * Enum for FileDialog mode type.
     * @enum {number}
     * @readonly
     * @private
     */
    enum StyleType {
        /** The fileDialog will only be used for display. */
        Open = 0,
        /** The fileDialog will be used for display and storage files */
        Save = 1
    }
}
export = FileDialog;
