import ImageFilter = require("./ImageFilter");
/**
 * <p>The ContrastImageFilter can adjusts the contrast of the input image. </p>
 *
 * @extends yunos.graphics.filter.ImageFilter
 * @memberof yunos.graphics.filter
 * @public
 * @since 5
 */
declare class ContrastImageFilter extends ImageFilter {
    public constructor();
    /**
     * <p>A value of 0 will create an image that is completely gray.
     * A value of 1 leaves the input unchanged. Values of an amount over 1 are allowed, providing results with more contrast.</p>
     * @name yunos.graphics.filter.ContrastImageFilter#amount
     * @type {number}
     * @default 1
     * @public
     * @since 5
     */
    public amount: number;
}
export = ContrastImageFilter;
