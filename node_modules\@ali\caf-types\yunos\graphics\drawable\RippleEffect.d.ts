import Context = require("../Context");
import CanvasDrawable = require("./CanvasDrawable");
/**
 * round shape spread from the point of touch
 * @extends yunos.graphics.drawable.CanvasDrawable
 * @memberof yunos.graphics.drawable
 * @public
 * @since 6
 */
declare class RippleEffect extends CanvasDrawable {
    private batch;
    private _rippleColor;
    private _maxOpacity;
    private _frameCount;
    private _touchHandler;
    protected onBindView(): void;
    private readonly maxRippleRadius;
    private readonly rippleSpeed;
    /**
     * the frame count of the ripple
     * @type {number} frame count value
     * @name yunos.graphics.drawable.RippleEffect#frameCount
     * @default 20
     * @public
     * @since 6
     */
    public frameCount: number;
    /**
     * the max opacity of the ripple
     * @type {number} max opacity value
     * @name yunos.graphics.drawable.RippleEffect#maxOpacity
     * @default 0.7
     * @public
     * @since 6
     */
    public maxOpacity: number;
    /**
     * the color of the ripple
     * @type {string|number} color value
     * @name yunos.graphics.drawable.RippleEffect#rippleColor
     * @public
     * @default "#ffffff"
     * @since 6
     */
    public rippleColor: string;
    private _getFillStyle;
    /**
     * drawing implementation
     *
     * @param {yunos.graphics.Context} ctx Canvas Context
     * @return {boolean}  true do draw next frame, false to stop.
     * @public
     * @since 6
     */
    public onDraw(ctx: Context): boolean;
}
export = RippleEffect;
