import YObject = require("../core/YObject");
/**
 * <p>Location based fence.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @see [FenceClient]{@link yunos.fenceclient.FenceClient}
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class FenceInfo extends YObject {
    private _fenceId;
    private _actions;
    private _type;
    private _radius;
    private _stayMins;
    private _category;
    private _poiId;
    private _address;
    private _name;
    private _province;
    private _coordinates;
    private _city;
    private _data;
    public constructor();
    /**
     * <p>Identify a fence in a fence group.</p>
     * @name yunos.fenceclient.FenceInfo#fenceId
     * @type {string}
     * @public
     * @since 5
     */
    public fenceId: string;
    /**
     * <p>Support detect actions</p>
     * @example
     * ["enter","exit","stay"]
     * @name yunos.fenceclient.FenceInfo#action
     * @type {string[]}
     * @public
     * @since 5
     */
    public actions: string[];
    /**
     * <p>Type of fence.</p>
     * @example
     * 0-circle,1-polygon,2-line
     * @name yunos.fenceclient.FenceInfo#type
     * @type {number}
     * @public
     * @since 5
     */
    public type: number;
    /**
     * <p>When fence is of circle type, radius is circle radius in meters</p>
     * @name yunos.fenceclient.FenceInfo#radius
     * @type {number}
     * @public
     * @since 5
     */
    public radius: number;
    /**
     * <p>Number of minutes. After a period of time,
     * the state of the fence changed from entering to resident</p>
     * @name yunos.fenceclient.FenceInfo#stayMins
     * @type {number}
     * @public
     * @since 5
     */
    public stayMins: number;
    /**
     * <p>Category of fence, user define values</p>
     * @name yunos.fenceclient.FenceInfo#category
     * @type {string}
     * @public
     * @since 5
     */
    public category: string;
    /**
     * <p>PoiId of fence, if the fence is created from a known POI</p>
     * @name yunos.fenceclient.FenceInfo#poiId
     * @type {string}
     * @public
     * @since 5
     */
    public poiId: string;
    /**
     * <p>Address of fence.</p>
     * @name yunos.fenceclient.FenceInfo#address
     * @type {string}
     * @public
     * @since 5
     */
    public address: string;
    /**
     * <p>Name of fence.</p>
     * @name yunos.fenceclient.FenceInfo#name
     * @type {string}
     * @public
     * @since 5
     */
    public name: string;
    /**
     * <p>Province of fence.</p>
     * @name yunos.fenceclient.FenceInfo#province
     * @type {string}
     * @public
     * @since 5
     */
    public province: string;
    /**
     * <p>Coordinates, when type=0, it is a single coordinate,
     * indicating the center of the circle;
     * when fenceType=1, it is a polygon boundary,
     * which needs to meet the polygon limit,
     * and the start point and end point are closed;
     * when fenceType=2, it is a line type and needs to meet the number of coordinates > 1</p>
     * @example
     * circle: "116.483972 40.012197", "longitude latitude"
     * polygon: "116.483972 40.012752,116.485030 40.012197,116.485033 40.012150,116.484227 40.011310,116.483346 40.011859,116.483348 40.011916,116.483854 40.012733,116.483972 40.012752"
     * line: "116.483972 40.012752,116.485030 40.012197,116.485033 40.012150"
     * @name yunos.fenceclient.FenceInfo#coordinates
     * @type {string}
     * @public
     * @since 5
     */
    public coordinates: string;
    /**
     * <p>City of fence.</p>
     * @name yunos.fenceclient.FenceInfo#city
     * @type {string}
     * @public
     * @since 5
     */
    public city: string;
    /**
     * <p>Business data, json format string,
     * will be transparently transmitted to the subscriber when the fence is notified</p>
     * @name yunos.fenceclient.FenceInfo#data
     * @type {string}
     * @public
     * @since 5
     */
    public data: string;
    /**
     * <p>Enum for fence type values.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly FenceType: {
        /**
         * Type of circle fence, which consists of a center coordinate and radius.
         * @public
         * @since 5
         */
        Circle: int;
        /**
         * Type of polygon fence, which composed of a set of closed coordinates,
         * the structure of the coordinates is "longitude1 latitude1,...,longitude1 latitude1".
         * @public
         * @since 5
         */
        Polygon: int;
        /**
         * Type of line fence, which consists of a set of linear coordinate sequences and a radius.
         * @public
         * @since 5
         */
        Line: int;
    };
    /**
     * <p>Enum for supported fence action types,
     * the fence can support one or more action type subscriptions, separated by commas</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly ActionType: {
        /**
         * Enter the fence action, triggered when entering the fence.
         * @public
         * @since 5
         */
        Enter: string;
        /**
         * Leave the fence action, triggered when leaving the fence.
         * @public
         * @since 5
         */
        Exit: string;
        /**
         * Stay in the fence action, triggered after staying in the fence for more than a certain time.
         * @public
         * @since 5
         */
        Stay: string;
    };
}
export = FenceInfo;
