import Matrix4 = require("../math/Matrix4");
import Vector3 = require("../math/Vector3");
/**
 * Abstract base class for projection.
 * This class should always be inherited when you build a new projection.
 * @memberof yunos.graphics.3d.projection
 * @abstract
 * @public
 * @since 5
 */
declare class Projection {
    protected _projectionMatrix: Matrix4;
    protected _projectionMatrixInverse: Matrix4;
    private _viewMatrix;
    private _viewMatrixInverse;
    private _viewEye;
    private _viewTarget;
    private _viewUp;
    protected _near: number;
    protected _far: number;
    /**
     * constructor that create a Projection
     * @public
     * @since 5
     */
    public constructor(near?: number, far?: number);
    /**
     * Destructor that destroy this Projection
     * @public
     * @since 5
     */
    public destroy(): void;
    /**
     * get the projection matrix
     * @name yunos.graphics.3d.projection.Projection#projectionMatrix
     * @type {yunos.graphics.3d.math.Matrix4}
     * @readonly
     * @public
     * @since 5
     */
    public readonly projectionMatrix: Matrix4;
    /**
     * get the inverse projection matrix
     * @name yunos.graphics.3d.projection.Projection#projectionMatrixInverse
     * @type {yunos.graphics.3d.math.Matrix4}
     * @readonly
     * @public
     * @since 5
     */
    public readonly projectionMatrixInverse: Matrix4;
    /**
     * return the view matrix
     * @name yunos.graphics.3d.projection.Projection#viewMatrix
     * @type {yunos.graphics.3d.math.Matrix4}
     * @readonly
     * @public
     * @since 5
     */
    public readonly viewMatrix: Matrix4;
    /**
     * return the inverse view matrix
     * @name yunos.graphics.3d.projection.Projection#viewMatrixInverse
     * @type {yunos.graphics.3d.math.Matrix4}
     * @readonly
     * @public
     * @since 5
     */
    public readonly viewMatrixInverse: Matrix4;
    /**
     * The observer's position
     * @name yunos.graphics.3d.projection.Projection#viewEye
     * @type {yunos.graphics.3d.math.Vector3}
     * @public
     * @since 5
     */
    public viewEye: Vector3;
    /**
     * The target's position
     * @name yunos.graphics.3d.projection.Projection#viewTarget
     * @type {yunos.graphics.3d.math.Vector3}
     * @public
     * @since 5
     */
    public viewTarget: Vector3;
    /**
     * Direction of the z-axis
     * @name yunos.graphics.3d.projection.Projection#viewUp
     * @type {yunos.graphics.3d.math.Vector3}
     * @default {yunos.graphics.3d.math.Vector3(0, 0, 1)}
     * @public
     * @since 5
     */
    public viewUp: Vector3;
    /**
     * projection frustum near plane
     * @name yunos.graphics.3d.projection.Projection#near
     * @type {number}
     * @public
     * @since 5
     */
    public near: number;
    /**
     * projection frustum far plane
     * @name yunos.graphics.3d.projection.Projection#far
     * @type {number}
     * @public
     * @since 5
     */
    public far: number;
    /**
     * Constructs a rotation matrix, looking from eye towards target oriented by the up vector.
     * @param {yunos.graphics.3d.math.Vector3} eye - the position of eye.
     * @param {yunos.graphics.3d.math.Vector3} target - the position of target.
     * @param {yunos.graphics.3d.math.Vector3} up - oriented by the up vector
     * @public
     * @since 5
     */
    public lookAt(eye: Vector3, target: Vector3, up: Vector3): void;
    /**
     * Create a new Projection with identical elements to this one.
     * @return {yunos.graphics.3d.projection.Projection} return the new Projection
     * @public
     * @since 5
     */
    public clone(): Projection;
    /**
     * Copy the elements of projection p into this projection
     * @param {yunos.graphics.3d.projection.Proejction} proejction - the projection to copy
     * @public
     * @since 5
     */
    public copy(projection: Projection): void;
    private updateViewMatrix;
    private updateProjectionMatrix(): void;
}
export = Projection;
