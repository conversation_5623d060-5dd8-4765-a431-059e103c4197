/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable disallowQuotedKeysInObjects
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

const iRes = require("yunos/content/resource/Resource").getInstance();
const iScreen = require("yunos/device/Screen").getInstance();
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import View = require("yunos/ui/view/View");
import TapRecognizer = require("yunos/ui/gesture/TapRecognizer");
import Toast = require("yunos/ui/widget/Toast");
import GestureRecognizer = require("yunos/ui/gesture/GestureRecognizer");
import Consts = require("../Consts");
import log = require("../utils/log");
import {IVoiceEvent} from "../Types";
const TAG = "Utils";
const XIAOYUN_HIDE_TIMEOUT = 0;
const NARROW_SCREEN_RESOLUTION = 21 / 9;

let showingToast: string = null;
let windowWidth: number;
let windowHeight: number;
let pageWidth: number;
let pageHeight: number;

interface ITapView extends View {
    _localProperties: {
        tapRecognizer?: GestureRecognizer,
        onTapListener?: (...args: Object[]) => void
    };

}

class Utils {

    /**
     * 播报tts
     */
    static sendXiaoYunSpoken(pageContext: Object, spokenText: string, onTTSCompleted: () => void) {
    }

    /**
     * 隐藏小云
     */
    static cancelSpeech(pageContext: Object, cancelSpeech = true) {
    }

    /**
     * 注册语音指令
     */
    static registerVoiceCommand(view: View, cmdKeys: string[], recognitionMode: number,
        onVoice: (arg0: string, arg1: number, arg3?: IVoiceEvent) => void, keepFullTimeActive?: boolean) {
        view.voiceEnabled = true;
        view.voiceSelectMode = View.VoiceSelectMode.Custom;
        let cmds: string[] = [];
        for (let i = 0; i < cmdKeys.length; i++) {
            let cmd = iRes.getString(cmdKeys[i]);
            cmds.push(cmd);
        }
        let voiceCommand = new VoiceCommand();
        voiceCommand.interactorState = VoiceCommand.InteractorState.Idle;
        voiceCommand.customCommands = cmds;
        (<{ sceneId: string }><object>voiceCommand).sceneId = Consts.SCENE_ID;
        if (recognitionMode !== undefined) {
            voiceCommand.recognitionMode = recognitionMode;
        }
        if (keepFullTimeActive) {
            voiceCommand.keepFullTimeActive = keepFullTimeActive;
        }
        view.addVoiceCommand(voiceCommand);
        view.on("voice", (option: IVoiceEvent) => {
            let index = cmds.indexOf(option._querywords);
            log.I(TAG, "VoiceCommand.onVoice view:", view.id, "cmd:",
                option._querywords, "index:", index);
            if (onVoice) {
                onVoice(cmdKeys[index], index, option);
            }
            option.endLocalTask();
        });
    }

    /**
     * 移除语音指令
     */
    static removeVoiceCommand(view: View, onvoice?: (arg0: string, arg1: number, arg3?: IVoiceEvent) => void) {
        view.voiceSelectMode = undefined;
        if (!onvoice) {
            view.removeAllListeners("voice");
        } else {
            view.removeListener("voice", onvoice);
        }
        view.removeAllVoiceCommands();
    }

    static convertViewCount(count: number) {
        if (!count || count < 0) {
            return "";
        }
        return (count / 10000).toFixed(1) + "W";
    }

    static convertSize(size: number) {
        if (!size || size < 0) {
            return "";
        }
        return (size / 1024 / 1024).toFixed(1) + "M";
    }

    static getDimen(key: Object) {
        let value = iRes.getConfig(key);
        if (typeof value === Consts.TYPE_STRING) {
            // {dp(10)}
            let puIndex = value.indexOf("{");
            let suIndex = value.indexOf("(", puIndex);
            let svIndex = value.indexOf(")", suIndex);
            if (puIndex < 0 || suIndex < 0 || svIndex < 0) {
                log.I(TAG, "getDimen failed, invalid value:", value);
                return 0;
            }
            let unitStr = value.substring(puIndex + 1, suIndex);
            let valueStr = value.substring(suIndex + 1, svIndex);
            if (unitStr === "dp") {
                return Number.parseFloat(valueStr) * iScreen.densityFactor;
            } else if (unitStr === "px") {
                return Number.parseFloat(valueStr);
            } else if (unitStr === "sdp") {
                return iScreen.getPixelBySDp(Number.parseFloat(valueStr));
            }
        } else if (typeof value === Consts.TYPE_NUMBER) {
            return value;
        }
        log.I(TAG, "getDimen failed, unsupported type:", value);
        return 0;
    }

    /**
     * 注册/移除view的tap手势
     */
    static setOnTapListener(view: View, listener: (...args: Object[]) => void) {
        if (!view) {
            log.E(TAG, "setTapListener failed! view invalid.");
            return;
        }
        let iView = <ITapView>view;
        if (listener) {
            if (iView._localProperties) {
                if (iView._localProperties.tapRecognizer) {
                    iView.removeGestureRecognizer(iView._localProperties.tapRecognizer);
                    iView._localProperties.tapRecognizer = undefined;
                }
                if (iView._localProperties.onTapListener) {
                    iView.removeEventListener("tap", iView._localProperties.onTapListener);
                    iView._localProperties.onTapListener = undefined;
                }
            } else {
                iView._localProperties = {};
            }
            iView._localProperties.tapRecognizer = new TapRecognizer();
            iView.addGestureRecognizer(iView._localProperties.tapRecognizer);
            iView.addEventListener("tap", listener);
            iView._localProperties.onTapListener = listener;
        } else {
            if (iView._localProperties) {
                if (iView._localProperties.onTapListener) {
                    iView.removeEventListener("tap", iView._localProperties.onTapListener);
                    iView._localProperties.onTapListener = undefined;
                }
                if (iView._localProperties.tapRecognizer) {
                    iView.removeGestureRecognizer(iView._localProperties.tapRecognizer);
                    iView._localProperties.tapRecognizer = undefined;
                }
            }
        }
    }

    static isNarrowScreen() {
        return iScreen.widthPixels / iScreen.heightPixels < NARROW_SCREEN_RESOLUTION;
    }

    static getScreenWidth() {
        return iScreen.widthPixels;
    }

    static getScreenHeight() {
        return iScreen.heightPixels;
    }

    static getWindowWidth() {
        if (windowWidth === undefined) {
            windowWidth = iScreen.widthPixels;
        }
        return windowWidth;
    }

    static setWindowWidth(windowW: number) {
        windowWidth = windowW;
    }

    static getWindowHeight() {
        if (windowHeight === undefined) {
            windowHeight = iScreen.heightPixels;
        }
        return windowHeight;
    }

    static setWindowHeight(windowH: number) {
        windowHeight = windowH;
    }

    static getPageWidth() {
        if (pageWidth === undefined) {
            pageWidth = iScreen.widthPixels;
        }
        return pageWidth;
    }

    static setPageWidth(pageW: number) {
        pageWidth = pageW;
    }

    static getPageHeight() {
        if (pageHeight === undefined) {
            pageHeight = iScreen.heightPixels;
        }
        return pageHeight;
    }

    static setPageHeight(pageH: number) {
        pageHeight = pageH;
    }

    static secondToTime(second: number, longTimeStyle: boolean) {
        if (second) {
            if (second > 0 && second / 1000 < 1) {
                return longTimeStyle ? "00:00:01" : "00:01";
            }
            let theTime = parseInt(parseInt(second + "") / 1000 + "");
            let theTime1 = 0;
            let theTime2 = 0;
            if (theTime >= 60) {
                theTime1 = parseInt(theTime / 60 + "");
                theTime = parseInt(theTime % 60 + "");
                if (theTime1 >= 60) {
                    theTime2 = parseInt(theTime1 / 60 + "");
                    theTime1 = parseInt(theTime1 % 60 + "");
                }
            }
            let seconds = parseInt(theTime + "") > 9 ? theTime.toString() : "0" + theTime;
            let mintues = "00";
            if (parseInt(theTime1 + "") > 0) {
                mintues = parseInt(theTime1 + "") > 9 ? theTime1.toString() : "0" + theTime1;
            }
            let hours = "00";
            if (parseInt(theTime2 + "") > 0 || longTimeStyle) {
                hours = parseInt(theTime2 + "") > 9 ? theTime2.toString() : "0" + theTime2;
                return hours + ":" + mintues + ":" + seconds;
            } else {
                return mintues + ":" + seconds;
            }
        } else {
            return longTimeStyle ? "00:00:00" : "00:00";
        }
    }

    static timeToSecond(time: string) {
        if (!time) {
            return 0;
        }

        let arr = time.split(":");
        let sum = 0;
        if (arr[0]) {
            sum += parseInt(arr[0]) * 3600;
        }
        if (arr[1]) {
            sum += parseInt(arr[1]) * 60;
        }
        if (arr[2]) {
            sum += parseInt(arr[2]);
        }
        return sum;
    }

    static getTsFormatDate(timeStamp: number) {
        let tsDate = new Date(timeStamp);
        let year = tsDate.getFullYear();
        let month = tsDate.getMonth() + 1;
        let date = tsDate.getDate();
        let hours = tsDate.getHours();
        let mintues = tsDate.getMinutes();

        let strMonth = ""
        if (month >= 1 && month <= 9) {
            strMonth = "0" + month;
        } else {
            strMonth = "" + month;
        }

        let strDate = "";
        if (date >= 0 && date <= 9) {
            strDate = "0" + date;
        } else {
            strDate = "" + date;
        }

        let strHours = "";
        if (hours >= 0 && hours <= 9) {
            strHours = "0" + hours;
        } else {
            strHours = "" + hours;
        }

        let strMintues = "";
        if (mintues >= 0 && mintues <= 9) {
            strMintues = "0" + mintues;
        } else {
            strMintues = "" + mintues;
        }

        let nowDate = new Date();
        if (nowDate.getFullYear() !== year) {
            return year + "-" + strMonth + "-" + strDate + " " + strHours + ":" + strMintues;
        } else {
            return strMonth + "-" + strDate + " " + strHours + ":" + strMintues;
        }
    }

    static showToast(text: string) {
        if (!text) {
            return;
        }
        if (text === showingToast) {
            return;
        }
        showingToast = text;
        try {
            let toast = new Toast();
            toast.on("close", () => {
                log.D(TAG, "close toast text =", showingToast);
                showingToast = null;
                if (toast) {
                    try {
                        toast.destroy();
                    } catch (e) {
                        log.I(TAG, "toast destroy failed", e);
                    }
                }
            });
            toast.text = text;
            toast.show();
        } catch (e) {
            log.I(TAG, "toast failed", e);
        }
    }

    static strToArray(str: string) {
        let arr: string[] = [];
        if (str) {
            str = str.replace(/\s+/g, " ");
            str = str.replace(/(^\s*)|(\s*$)/g, "");
            arr = str.split(" ");
            log.I(TAG, "strToArray", arr.length);
            for (let i = 0; i < arr.length; i++) {
                log.I(TAG, arr[i]);
            }
        }
        return arr;
    }

    static isOnlineVideo(url: string) {
        if (url && url.startsWith("http")) {
            return true;
        }
        return false;
    }

    static isGBKEncoding(buffer: string | number | Buffer) {
        let buff;
        if (buffer instanceof Buffer) {
            buff = buffer;
        } else if (typeof buffer === "string") {
            buff = Buffer.from(buffer);
        }
        let oneByte = 0X00; // binary 00000000
        let yesCount = 0;
        let noCount = 0;
        let c = 0;
        let c1 = 0;

        for (let i = 0; i < buff.length;) {
            c = buff[i];
            if (c >> 7 == oneByte) {
                i++;
                continue;
            } else if (c >= 0X81 && c <= 0XFE) {
                c1 = buff[i + 1];
                if (c1 >= 0X40 && c1 <= 0XFE) {
                    yesCount++;
                    i += 2;
                    continue;
                }
            }

            noCount++;
            i += 2;
        }

        log.I(TAG, "isGBKEncoding yes/no:", yesCount, noCount);
        let ret = 100 * yesCount / (yesCount + noCount);
        return ret > 90;
    }

    // judge the byte whether begin with binary 10
    static isUtf8SpecialByte(ch: number) {
        const SPECIAL_BYTE = 0X02; // binary 00000010
        if (ch >> 6 === SPECIAL_BYTE) {
            return 1;
        } else {
            return 0;
        }
    }

    static isUtf8Encoding(buffer: number[] | Buffer) {
        let buff = buffer;
        const ONE_BYTE = 0X00; // binary 00000000
        const TWO_BYTE = 0X06; // binary 00000110
        const THREE_BYTE = 0X0E; // binary 00001110
        const FORE_BYTE = 0X1E; // binary 00011110
        const FIVE_BYTE = 0X3E; // binary 00111110
        const SIX_BYTE = 0X7E; // binary 01111110

        let yesCount = 0;
        let noCount = 0;

        let c1 = 0;
        let c2 = 0;
        let c3 = 0;
        let c4 = 0;
        let c5 = 0;

        let c = 0;
        for (let i = 0; i < buff.length;) {
            c = buff[i];
            if (c >> 7 == ONE_BYTE) {
                i++;
                continue;
            } else if (c >> 5 === TWO_BYTE) {
                c1 = buff[i + 1];
                if (Utils.isUtf8SpecialByte(c1)) {
                    yesCount++;
                    i += 2;
                    continue;
                }
            } else if (c >> 4 === THREE_BYTE) {
                c1 = buff[i + 1];
                c2 = buff[i + 2];
                if (Utils.isUtf8SpecialByte(c1) &&
                    Utils.isUtf8SpecialByte(c2)) {
                    yesCount++;
                    i += 3;
                    continue;
                }
            } else if (c >> 3 === FORE_BYTE) {
                c1 = buff[i + 1];
                c2 = buff[i + 2];
                c3 = buff[i + 3];
                if (Utils.isUtf8SpecialByte(c1) &&
                    Utils.isUtf8SpecialByte(c2) &&
                    Utils.isUtf8SpecialByte(c3)) {
                    yesCount++;
                    i += 4;
                    continue;
                }
            } else if (c >> 2 === FIVE_BYTE) {
                c1 = buff[i + 1];
                c2 = buff[i + 2];
                c3 = buff[i + 3];
                c4 = buff[i + 4];
                if (Utils.isUtf8SpecialByte(c1) &&
                    Utils.isUtf8SpecialByte(c2) &&
                    Utils.isUtf8SpecialByte(c3) &&
                    Utils.isUtf8SpecialByte(c4)) {
                    yesCount++;
                    i += 5;
                    continue;
                }
            } else if (c >> 1 === SIX_BYTE) {
                c1 = buff[i + 1];
                c2 = buff[i + 2];
                c3 = buff[i + 3];
                c4 = buff[i + 4];
                c5 = buff[i + 5];
                if (Utils.isUtf8SpecialByte(c1) &&
                    Utils.isUtf8SpecialByte(c2) &&
                    Utils.isUtf8SpecialByte(c3) &&
                    Utils.isUtf8SpecialByte(c4) &&
                    Utils.isUtf8SpecialByte(c5)) {
                    yesCount++;
                    i += 6;
                    continue;
                }
            }

            noCount++;
            i++;
        }

        log.I(TAG, "isUtf8Encoding yes/no:", yesCount, noCount);
        let ret = 100 * yesCount / (yesCount + noCount);
        return ret > 90;
    }

    // decode ^ and M- notiation (Caret and M Hypen: CAMH)
    // utf8:M-dM-:M-^QM-iM-*M-^QM-eM-#M-+UM-gM-^[M-^X
    // GBK:M-?M-IM-<M-0; esc: M-\?
    // %^M-$M-#IV
    // return: -1, decode failed.
    //       : a Buffer object, decode successe.
    static decodeCAMHNotation(raw: Object[]) {
        let rawBuff = Buffer.from(raw);
        let outBuff = Buffer.alloc(rawBuff.length);
        let writePos = 0;
        let rp = 0;
        let len = rawBuff.length;
        let retValue: number | Buffer = 0;
        while (rp < len) {
            // log.I(TAG, "====pos:", rp, " value:", rawBuff[rp], rawBuff[rp + 1], rawBuff[rp + 2], rawBuff[rp + 3]);
            if (rawBuff[rp] === 77 && rawBuff[rp + 1] === 45 && rawBuff[rp + 2] === 94) { // M-^
                let ch = rawBuff[rp + 3];
                ch ^= 0x40;
                if (ch >= 32 && ch !== 0x7f) {
                    log.W(TAG, "decodeCAMHNotation failed. M-^ pos/value:", rp, ch, ", raw:", raw);
                    retValue = -1;
                    break;
                }
                ch += 128;
                outBuff.writeUInt8(ch, writePos);
                writePos++;
                rp += 4;
            } else if (rawBuff[rp] === 77 && rawBuff[rp + 1] === 45) { // M-
                let ch = rawBuff[rp + 2];
                let shift = 3;
                if (ch === 92) { // esc: M-\", M-\\, M-\<, M-\ , M-\', M-\>, M-$
                    ch = rawBuff[rp + 3];
                    shift = 4;
                    if (ch === 77 || ch === 94) { // M or ^ is after M-\
                        ch = rawBuff[rp + 2];
                        shift = 3;
                    }
                }

                ch -= 128;
                if (ch >= 0) {
                    log.W(TAG, "decodeCAMHNotation failed. M- pos/value:", rp, ch, ", raw:", raw);
                    retValue = -1;
                    break;
                }
                ch += 256;
                outBuff.writeUInt8(ch, writePos);
                writePos++;
                rp += shift;
            } else if (rawBuff[rp] === 94) { // ^
                let ch = rawBuff[rp + 1];
                ch ^= 0x40;
                if (ch >= 32 && ch !== 127) {
                    log.W(TAG, "decodeCAMHNotation failed. M-^ pos/value:", rp, ch, ", raw:", raw);
                    retValue = -1;
                    break;
                }
                outBuff.writeUInt8(ch, writePos);
                writePos++;
                rp += 2;
            } else {
                let ch = rawBuff[rp];
                if (ch < 32 || ch === 127 || ch > 128) {
                    log.W(TAG, "decodeCAMHNotation failed.  pos/value:", rp, ch, ", raw:", raw);
                    retValue = -1;
                    break;
                }
                if (ch === 92 && rawBuff[rp + 1] !== 77 && rawBuff[rp + 1] !== 94) {
                    ch = rawBuff[rp + 1];
                    outBuff.writeUInt8(ch, writePos);
                    writePos++;
                    rp += 2;
                } else {
                    outBuff.writeUInt8(ch, writePos);
                    writePos++;
                    rp++;
                }
            }
        }
        if (retValue !== -1) {
            // log.I(TAG, "==== outBuff:", outBuff.toString("hex"));
            retValue = outBuff.slice(0, writePos);
        }
        return retValue;
    }

    static decodeUdiskLabel(label: Object[]) {
        if (!label) {
            return iRes.getString("USB_TITLE");
        }
        let outBuff;
        let retValue = Utils.decodeCAMHNotation(label);
        if (retValue !== -1) {
            // log.I(TAG, "==== outBuff:", outBuff.toString("hex"));
            outBuff = retValue;
        } else {
            log.I(TAG, "decodeUdiskLabel decode failed.");
            outBuff = Buffer.from(label);
        }
        let encoding = "utf8";

        if (Utils.isUtf8Encoding(<Buffer>outBuff)) {
            log.I(TAG, "decodeCAMHNotation encoding:Utf8");
            encoding = "utf8";
        } else if (Utils.isGBKEncoding(outBuff)) {
            log.I(TAG, "decodeCAMHNotation encoding:GBK");
            encoding = "GBK";
        }
        const Charset = require("yunos/util/Charset");
        let outStr = "";
        try {
            outStr = Charset.decode(outBuff, encoding);
        } catch (e) {
            log.E(TAG, "Charset.decode failed. error:", e);
        }
        log.I(TAG, "decodeUdiskLabel outStr:", outStr);
        return outStr;
    }

    static sendLink(uri: string | Object, event: string | Object, data: Object) {
        log.D(TAG, "sendLink uri-event-data:", uri, event, data);
        const PageLink = require("yunos/page/PageLink");
        const Page = require("yunos/page/Page");
        let link = new PageLink();
        link.uri = uri || "";
        link.eventName = event || "";
        link.data = typeof data == "object" ? JSON.stringify(data) : data || "";
        return Page.getInstance().sendLink(link);
    }

    /**
     * 投屏的URL由于来源较多，格式不统一，主要是根据视频后缀处理
     */
    static extractUrl(url: string) {
        if (!url) {
            return url;
        }

        let commonParts = "";
        if (url.indexOf("youku.com") !== -1) {
            let index = url.indexOf("&type");
            if (index !== -1) {
                commonParts = url.substr(0, index);
            }
        }

        if (!commonParts) {
            let suffix = [".m3u8", ".mp4"];
            for (let i = 0; i < suffix.length; i++) {
                let index = url.indexOf(suffix[i]);
                if (index !== -1) {
                    commonParts = url.substr(0, index);
                    break;
                }
            }

            if (commonParts && commonParts.indexOf("qq.com") !== -1) {
                let index = url.lastIndexOf("/");
                if (index !== -1) {
                    commonParts = commonParts.substr(index + 1, commonParts.length);
                }

                index = commonParts.indexOf(".");
                if (index !== -1) {
                    commonParts = commonParts.substr(0, index);
                }
            }
        }

        if (!commonParts) {
            let index = url.lastIndexOf(".");
            if (index !== -1) {
                commonParts = url.substr(0, index);
            } else {
                commonParts = url;
            }
        }
        return commonParts;
    }
}

export = Utils;
