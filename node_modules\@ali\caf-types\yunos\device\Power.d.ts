declare module Power {
    /**
     * @friend
     */
    class IPowerRequest {
        send: () => void;
        revoke: () => void;
    }
}
/**
 * @friend
 */
declare class Power {
    static getInstance: () => Power;
    createPowerRequest: (type: number, brightness: number| string) => Power.IPowerRequest;
    static readonly RequestType: {
        OVERRIDE_BACKLIGHT: number;
        KEEP_SCREEN_FULL: number;
        KEEP_SCREEN_DIM: number;
        SCREEN_INSTANT_ON: number;
        SCREEN_DELAYED_OFF: number;
    }
    static readonly RequestFlag : {
        SCREEN_INSTANT_ON: number;
    }
    testScreenOn(): boolean;
}

export = Power;
