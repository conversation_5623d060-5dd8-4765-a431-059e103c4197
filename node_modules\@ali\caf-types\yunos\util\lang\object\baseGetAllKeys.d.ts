/**
 * The base implementation which uses `keysFunc` and `symbolsFunc` to get the enumerable property names and symbols of `object`.
 * @param {Object} object - The object to query.
 * @param {Function} keysFunc - The function to get the keys of `object`.
 * @param {Function} symbolsFunc - The function to get the symbols of `object`.
 * @returns {Array} Returns the array of property names and symbols.
 * @private
 */
export declare const baseGetAllKeys: (object: Object, keysFunc: (obj: Object) => Object[], symbolsFunc: (obj: Object) => Object[]) => Object[];
