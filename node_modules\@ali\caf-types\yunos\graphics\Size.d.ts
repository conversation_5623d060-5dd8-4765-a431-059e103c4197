import YObject = require("../core/YObject");
/**
 * <p>Size is class for describing width and height dimensions in pixels.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 2
 */
declare class Size extends YObject {
    private _width;
    private _height;
    /**
     * <p>Constructor that create a Size instance.</p>
     * @param {number} width - The width of the size, in pixels.
     * @param {number} height - The height of the size, in pixels.
     * @public
     * @since 2
     */
    public constructor(width: number, height: number);
    /**
     * <p>Get the width of the size (in pixels).</p>
     * @name yunos.graphics.Size#width
     * @type {number}
     * @public
     * @since 2
     */
    public width: number;
    /**
     * <p>Get the height of the size (in pixels).</p>
     * @name yunos.graphics.Size#height
     * @type {number}
     * @public
     * @since 2
     */
    public height: number;
    /**
     * <p>Check whether this size equals a specified size.</p>
     * @param {yunos.graphics.Size} size - the specified size instance.
     * @return {boolean} true means equal, otherwise false.
     * @override
     * @public
     * @since 2
     */
    public equals(size: YObject): boolean;
    /**
     * <p>Returns a human-readable Size string.</p>
     * @return {string} the size string.
     * @override
     * @public
     * @since 2
     */
    public toString(): string;
    private static parseSize(string: string): Size;
}
export = Size;
