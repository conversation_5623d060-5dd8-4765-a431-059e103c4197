
declare namespace MultimediaNode {
  export class NodeMMParam {
      constructor();
      destroy(): void;
      writeInt32(value: number): void;
      writeInt64(value: number): void;
      writeFloat(value: number): void;
      writeDouble(value: number): void;
      writeString(value: string): void;
      readInt32(): number;
      readInt64(): number;
      readString(): string;
      readDouble(): number;
      readFloat(): number;
  }

  export class NodeMediaBase {
      appendPluginsXml(xmlFile: string): void;
      createPipeline(libNameWithPath: string): void;
      destroy(): void;
  }

  export class NodeMediaMeta {
      setInt32(key: string, value: number): void;
      setInt64(key: string, value: number): void;
      setFloat(key: string, value: number): void;
      setDouble(key: string, value: number): void;
      setString(key: string, value: string): void;
      setFraction(key: string, numerator: number, denominator: number): void;
      setByte<PERSON>uffer(key: string, buffer: Buffer): void;
      setRect(key: string, left: number, top: number, right: number, bottom: number): void;
      getInt32(key: string): number;
      getInt64(key: string): number;
      getFloat(key: string): number;
      getDouble(key: string): number;
      getString(key: string): string;
      getFraction(key: string): Object;
      getRect(key: string): Object;
      readFromMsg(msg: Object): boolean;
      writeToMsg(msg: Object): boolean;
      readFromMMParam(nativeMMParam: NodeMMParam): void;
      destroy(): void;
      containsKey(key: string): boolean;
      dump(): void;
  }

  export class NodeMediaPlayer {
      constructor(v: number);
      setListener(v: Function): void;
      setURISource(uri: string, source?: Object): void;
      setFdSource(fd: number, offset: number, length: number): void;
      setSubtitleSource(uri: string): void;
      setDisplayName(name: string): void;
      setVideoDisplay(surfaceAddress: number): void;
      prepare(): void;
      reset(): void;
      setVolume(left: number, right: number): void;
      setMute(mute: boolean): void;
      getMute(): boolean;
      start(): void;
      stop(): void;
      pause(): void;
      seek(msec: number): void;
      isPlaying(): boolean;
      getVideoWidth(): number;
      getVideoHeight(): number;
      getCurrentPosition(): number;
      getDuration(): number;
      setAudioStreamType(type: number): void;
      getAudioStreamType(): number;
      setAudioZoneId(zoneId: number, sessionName: string): void;
      getAudioZoneId(): number;
      setLoop(loop: boolean): void;
      isLooping(): boolean;
      prepareAsync(): void;
      setParameter(mediaMeta: NodeMediaMeta): void;
      getParameter(): NodeMediaMeta;
      setPipeline(mediaBase: NodeMediaBase): void;
      enableExternalSubtitleSupport(enable: boolean): void;
      invoke(req: NodeMMParam): NodeMMParam;
      setPlayTrackMode(mode: number): void;
      startOffset(offset: number): void;
  }

  export class NodeTranscoder {
      constructor();
      setListener(v: Function): void;
      setURISource(uri: string, source?: Object): void;
      reset(): void;
      start(): void;
      read(size: number): void;
      stop(): void;
      pause(): void;
      prepareAsync(): void;
      setParameter(mediaMeta: NodeMediaMeta): void;
  }

  export class NodeMediaRecorder {
      constructor(v: number);
      setListener(v: Function): void;
      destroy(): void;
      setRecorderUsage(usage: number): void;
      setCamera(camera: Object): void;
      setVideoSource(uri: string, headers?: Object): void;
      setVideoSourceFormat(width: number, height: number, format: string): void;
      setAudioSource(uri: string, headers?: Object): void;
      setVideoEncoder(mime: string): void;
      setAudioEncoder(mime: string): void;
      setOutputFormat(mime: string): void;
      setOutputFile(path: string): void;
      setOutputFileFd(fd: string): void;
      setPreviewSurface(handle: Object): void;
      prepare(): void;
      reset(): void;
      start(): void;
      stop(): void;
      stopSync(): void;
      pause(): void;
      isRecording(): boolean;
      getVideoWidth(): number;
      getVideoHeight(): number;
      getCurrentPosition(): number;
      setParameter(meta: NodeMediaMeta): void;
      getParameter(): NodeMediaMeta;
      setMaxDuration(msec: number): void;
      setMaxFileSize(bytes: number): void;
      setPipeline(mediaBase: NodeMediaBase): void;
  }

  export interface NodePCMPlayer {
      new(): NodePCMPlayer;
      setListener(v: Function): void;
      play(uri: string, format: number, sampleRate: number, channelCount: number): void;
      pause(): void;
      resume(): void;
      stop(): void;
      release(): void;
  }

  export interface NodePCMRecorder {
      new(): NodePCMRecorder;
      setListener(v: Function): void;
      prepare(inputSource: number, format: number, sampleRate: number,
        channelsArray: number[]): void;
      start(): void;
      pause(): void;
      read(size: number): void;
      reset(): void;
  }

  export class NodeMediaMetaRetriever {
      setUriSource(uri: string, callback: Function): void;
      setFdSource(fd: Object, offset: number, length: number, callback: Function): void;
      extractMetadata(callback: Function): void;
      extractVideoFrame(timeMs: number, format?: number, callback?: Function): void;
      extractCover(callback: Function): void;
      reset(): void;
      static readonly Format: { RGB565: string };
  }

  export class NodeExif {
      constructor();
      open(uri: string): void;
      openFd(fd: number, offset: number, length: number): void;
      close(): void;
      getStringTag(tag: string): void;
      getIntTag(tag: string): number;
      getRationalTag(tag: number): number;
      static readonly TagType: string;
  }

}

export = MultimediaNode;
