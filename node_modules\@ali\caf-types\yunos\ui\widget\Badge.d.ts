import TextView = require("../view/TextView");
import View = require("../view/View");
/**
 * <p>Badge define a view set on the top. May set value or text</p>
 * @extends yunos.ui.view.TextView
 * @memberof yunos.ui.widget
 * @public
 * @since 4
 *
 */
declare class Badge extends TextView {
    private _associateView;
    private _targetHandler;
    private _position;
    private _badgeValue;
    private _minValue;
    private _maxValue;
    private _defaultWidth;
    private _defaultHeight;
    private _defaultTextColor;
    private _defaultBorderRadius;
    private _defaultFontSize;
    private _defaultFontWeight;
    private _defaultBackground;
    private _defaultBorderWidth;
    private _defaultBorderColor;
    private _defaultShadowColor;
    private _defaultShadowOffsetX;
    private _defaultShadowOffsetY;
    private _defaultShadowRadius;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 5
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.Badge#defaultStyleName
     * @type {string}
     * @default "Badge"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * The number value show in Badge.
     * @name yunos.ui.widget.Badge#value
     * @type {number}
     * @public
     * @since 4
     *
     */
    public value: number;
    /**
     * Defines the left of the Badge, in pixels.
     * @name yunos.ui.widget.Badge#left
     * @type {number}
     * @public
     * @override
     * @since 4
     *
     */
    public left: number;
    /**
     * Defines the top of the Badge, in pixels.
     * @name yunos.ui.widget.Badge#top
     * @type {number}
     * @public
     * @override
     * @since 4
     *
     */
    public top: number;
    private minValue: number;
    private maxValue: number;
    private associateView: View;
    /**
     * Defines the position of the Badge, with enum position value.
     * @name yunos.ui.widget.Badge#position
     * @type {yunos.ui.widget.Badge.Position}
     * @public
     * @since 4
     *
     */
    public position: string;
    private changePosition(): void;
    /**
     * <p>Enum for Badge position.</p>
     * <p>The badge position set.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly Position: {
        /**
         * Badge set position at left and top of view.
         * @public
         * @since 4
         *
         */
        LeftTop: string;
        /**
         * Badge set position at right and top of view.
         * @public
         * @since 4
         *
         */
        RightTop: string;
        /**
         * Badge set position at left and bottom of view.
         * @public
         * @since 4
         *
         */
        LeftBottom: string;
        /**
         * Badge set position at right and bottom of view.
         * @public
         * @since 4
         *
         */
        RightBottom: string;
        /**
         * Badge set position with set left and top.
         * @public
         * @since 4
         *
         */
        Custom: string;
    };
}
export = Badge;
