import CompositeView = require("../view/CompositeView");
/**
 * <p>This class is a calendar widget for displaying and selecting dates. </p>
 * <p>A user can select a date by taping on it.</p>
 * <p>When the user clicks a date, the calendar widget will emit an "selectdate" event.</p>
 * @example
 * var calendarView = new CalendarView();
 * calendarView.on("selectDate", function(date) {
 *    console.log(date); // Tue Jun 28 2016 00:00:00 GMT+0800 (CST)
 * });
 * @extends yunos.ui.view.View
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class CalendarView extends CompositeView {
    private calendarCellDateViewArray;
    private drawYear;
    private drawMonth;
    private drawDate;
    private currentFocuscalendarCellDateView;
    private container;
    private firstDay;
    private _cellDateFontSize;
    private _cellWidth;
    private _cellHeight;
    private _cellWeekendTitleColor;
    [key: string]: Object;
    /**
     * <p>Get style name of CalendarView.</p>
     * @name yunos.ui.widget.CalendarView#defaultStyleName
     * @type {string}
     * @default "CalendarView"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Set a specified date to CalendarView,<br>
     * <p>it will show the year, the month of the date.</p>
     * @param {Date} date - Sets the selected date.
     * @throws {TypeError} If parameter is not a instance of Date.
     * @public
     * @since 1
     */
    public setDate(date: Date): void;
    /**
     * <p>Select a date in CalendarView, it will focus this date with background color.</p>
     * @fires yunos.ui.widget.CalendarView#selectdate
     * @throws {TypeError} If parameter is not a instance of Date.
     * @param {Date} date - selected date.
     * @public
     * @since 1
     */
    public setSelectDate(date: Date): void;
    /**
     * <p>Get the date of CalenderView.</p>
     * <p>If a date is selected, which will be returned.<br>
     * If no date is selected, 1st date of this month will be returned as default.</p>
     * @return {Date} Get the date of current year and month.
     * @public
     * @since 1
     */
    public getDate(): Date;
    private clearCalendarCellDateViews(): void;
    private generateUI(): void;
    private oncalendarCellDateViewClick(calendarCellDateView: CalendarCellDateView): void;
    private daylightSavingAdjust(date: Date): Date;
    private getFirstDayOfMonth(year: number, month: number): number;
    private getDaysInMonth(year: number, month: number): number;
}
/**
 * @private
 */
declare class CalendarCellDateView extends CompositeView {
    private clickBackgroundView;
    private dateView;
    private lunarDateView;
    private todayBackgroundView;
    private _cellLunarPaddingTop;
    private _cellLunarPaddingBottom;
    private _cellFocusBackgroundColor;
    private _cellWidth;
    private _cellDateFontSize;
    private _cellLunarDateFontSize;
    private _cellLunarDateColor;
    private _cellTodayBackgroundColor;
    private _cellDateColor;
    private date;
    private _cellNotInThisMonthColor;
    [key: string]: Object;
    private generateUI(): void;
    private setDate(date: Date): void;
    private setToday(): void;
    private setNotInThisMonth(): void;
    private unfocus(): void;
    private getDate(): Date;
}
export = CalendarView;
