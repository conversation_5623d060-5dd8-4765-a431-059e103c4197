<partial>
    <property-set name="search">
        <id name="id_sh_title">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_local_title">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_local_more_tips">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_local_more_icon">
            <property name="src">{img(images/ic_arrow_right_light.png)}</property>
        </id>
        <id name="id_online_title">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_search_container">
            <property name="src">{img(images/bg_search_header.png)}</property>
        </id>
        <id name="id_search_icon">
            <property name="src">{img(images/ic_search_header_light.png)}</property>
        </id>
        <id name="id_search_close">
            <property name="src">{img(path/to/right/icon.png)}</property>
        </id>
    </property-set>

    <property-set name="search_result">
        <id name="id_search_keyword">
            <property name="color">{theme.color.Black_3}</property>
        </id>
    </property-set>

    <property-set name="src_no_video">
        <property name="src">{img(images/ic_no_video_light.png)}</property>
    </property-set>

    <property-set name="src_no_history">
        <property name="src">{img(images/ic_no_history_light.png)}</property>
    </property-set>

    <property-set name="search_history_item">
        <property name="color">{theme.color.Black_1}</property>
        <property name="background">#ffffff</property>
        <property name="fontSize">{sdp(24)}</property>
        <property name="fontWeight">{enum.TextView.FontWeight.Normal}</property>
        <property name="lineHeight">{sdp(36)}</property>
        <property name="lineHeightMode">{enum.TextView.LineHeightMode.FixedHeight}</property>
    </property-set>

    <property-set name="search_history_item_warning">
        <property name="color">{theme.color.Warning_1}</property>
        <property name="background">#ffffff</property>
        <property name="fontSize">{sdp(24)}</property>
        <property name="fontWeight">{enum.TextView.FontWeight.Normal}</property>
        <property name="lineHeight">{sdp(36)}</property>
        <property name="lineHeightMode">{enum.TextView.LineHeightMode.FixedHeight}</property>
    </property-set>

    <property-set name="search_more_multistate">
        <property name="multiState">{config.MORE_MULTISTATE_LIGHT}</property>
    </property-set>

    <property-set name="search_more_narrow_multistate">
        <property name="multiState">{config.BTN_MULTISTATE_LIGHT}</property>
    </property-set>
</partial>
