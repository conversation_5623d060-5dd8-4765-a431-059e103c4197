<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    layout="{layout.welcome}"
    propertySetName="welcome">

    <NavigationBar
        id="id_nav"
        title="{string.VIDEO_TITLE}"/>

    <ImageView
        id="id_logo"
        height="{config.BG_VIDEO_HEIGHT}"
        scaleType="{enum.ImageView.ScaleType.Center}"/>

    <ButtonBM
        id="id_btn"
        width="{config.WELCOME_BTN_WIDTH}"
        height="{config.WELCOME_BTN_HEIGHT}"
        text="{string.WELCOME_OPEN_VIDEO}"
        enabled="false"/>

    <CompositeView
        id="id_disclaimer"
        width="{config.WELCOME_DISCLAIMER_WIDTH}"
        height="{config.WELCOME_DISCLAIMER_HEIGHT}"
        layout="{layout.welcome_disclaimer}">
        <CheckBox
            id="id_disclaimer_checkbox"
            width="{config.WELCOME_CHECKBOX_WIDTH}"
            height="{config.WELCOME_CHECKLBOX_HEIGHT}"
            checked="false"/>
        <TextView
            id="id_disclaimer_tips"
            text="{string.WELCOME_DISCLAIMER_TIPS}"
            propertySetName="extend/hdt/FontBody2"/>
        <TextView
            id="id_disclaimer_link"
            width="{config.WELCOME_LINK_WIDTH}"
            text="{string.WELCOME_DISCLAIMER_TITLE}"
            propertySetName="extend/hdt/FontBody2"
            multiState="{config.ITEM_MULTISTATE}"/>
    </CompositeView>
</CompositeView>
