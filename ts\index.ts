/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

process.env.AGIL_ENABLE_BOUNDBOX = "1";
import BMPage = require("extend/hdt/page/BMPage");
import {RouterHelper} from "extend/hdt/util/RouterHelper";
import Page = require("yunos/page/Page");
import StackRouter = require("yunos/appmodel/StackRouter");
import CompositeView = require("yunos/ui/view/CompositeView");
import RelativeLayout = require("yunos/ui/layout/RelativeLayout");
import View = require("yunos/ui/view/View");
import Window = require("yunos/ui/view/Window");
import Resource = require("yunos/content/resource/Resource");
const iRes = Resource.getInstance();
import PageLink = require("yunos/page/PageLink");
import Theme = require("yunos/ui/theme/Theme");
import Consts = require("./Consts");
const RoutePath = Consts.RoutePath;
import Features = require("./Features");
const iVideoModel = require("./model/VideoModel").getInstance();
const iAudioSession = require("./monitor/AudioSession").getInstance();
import log = require("./utils/log");
import Utils = require("./utils/Utils");
const iAccount = require("./utils/AccountHelper").getInstance();
const TAG = "Main";

interface IPage {
    pageapi: {
        mainWindow: {
            stopFastRender: () => void;
            invalidateFastRender: () => void;
        }
    }
    window: { renderNextFrame: () => void }
    CloseExitAnimation: {
        None: number;
    }
}

interface IPresenter {
    onBackKey: () => boolean;
    playVideo: (arg: boolean) => void;
    windowPositionChanged: () => Object;
}

interface INglAdaptor {
    getNlgTTS(arg: string): string;
}

interface IConfig {
    PAGE_MARGIN_LEFT?: number;
    PAGE_MARGIN_TOP?: number;
    PAGE_MARGIN_RIGHT?: number;
    PAGE_MARGIN_BOTTOM?: number;
    PLAYER_BG_COLOR?: number | string;
}

const Config: IConfig = {};

class App extends BMPage {
    private _router: StackRouter;
    private _shown: boolean;
    private _activated: boolean;
    private _nglAdaptor: INglAdaptor;
    private _videoContainer: View;
    private _globalThemeChangedListener: (arg: String) => void;

    useRouter() {
        return false;
    }

    getWindowConfig() {
        return {
            opaque: false,
            extraSwitches: [
                "--fast-render-mode=true"
            ]
        };
    }

    get theme() {
        return "default";
    }

    onFirstFrameCompleted() {
        log.I(TAG, "onFirstFrameCompleted");
    }

    onCreate() {
        log.I(TAG, "onCreate");
        super.onCreate();
        const theme = Theme.getInstance();
        theme.responseToGlobalThemeChange = true;
    }

    onStart() {
        log.I(TAG, "onStart");
        this.closeExitAnimation = (<IPage> <Object> Page).CloseExitAnimation.None;
        this.window.softKeyboardMode = Window.SoftKeyboardMode.AdjustNothing;
        this._activated = false;

        Config.PLAYER_BG_COLOR = "black";
        Config.PAGE_MARGIN_LEFT = this.rootContainer.left;
        Config.PAGE_MARGIN_TOP = this.rootContainer.top;
        Config.PAGE_MARGIN_RIGHT = this.window.width - this.rootContainer.left - this.rootContainer.width;
        Config.PAGE_MARGIN_BOTTOM = this.window.height - this.rootContainer.top - this.rootContainer.height;
        Utils.setWindowWidth(this.window.width);
        Utils.setWindowHeight(this.window.height);
        Utils.setPageWidth(this.rootContainer.width);
        Utils.setPageHeight(this.rootContainer.height);
        log.I(TAG, "onStart", this.window.height, this.window.top, this.rootContainer.height, this.rootContainer.top);

        let container = CompositeView.create({
            id: "id_video_container"
        });
        let layout = new RelativeLayout();
        layout.setLayoutParam("id_video_container", "align", {
            left: "parent",
            top: "parent",
            right: "parent",
            bottom: "parent"
        });
        this._videoContainer = container;
        this.rootContainer.id = "id_root";
        this.rootContainer.layout = layout;
        this.rootContainer.addChild(container);
        this.setPageContainerSize(false);
        this._initRouter(container);

        // fast render
        if ((<IPage><Object> this).pageapi) {
            (<IPage><Object> this).pageapi.mainWindow.stopFastRender();
        }
        this._initTrafficPrompt();
        this._registerWindowPropertyChange();
        this._registerKeyEvent();
        iVideoModel.saveDlnaPlayFlag(false);
        Theme.getInstance().on("globalthemechange", this._globalThemeChangedListener = (currentGlobalThemeName) => {
            log.I(TAG, "globalthemechange", currentGlobalThemeName);
            // 播放页背景色需要保持一致
            if (currentGlobalThemeName === "hdt_light") {
                let presenter = this._router.activatedPresenter;
                if (presenter && presenter.context.routeInfo.path === RoutePath.PLAYER) {
                    this.setPageContainerBackground(true);
                }
            }
        });
    }

    /**
     * 获取云端配置的tts
     */
    getTTS(ttsName: string) {
        let ttsText = this._nglAdaptor.getNlgTTS(ttsName);
        log.D(TAG, "getTTS", ttsName, ttsText);
        if (!ttsText) {
            ttsText = iRes.getString(ttsName);
        }
        return ttsText;
    }

    /**
     * 播放页为全屏，在进入或退出播放页后需要更新container的size
     */
    setPageContainerSize(isFullScreen: boolean) {
        let layout = new RelativeLayout();
        layout.setLayoutParam("id_root", "align", {
            left: "parent",
            top: "parent",
            right: "parent",
            bottom: "parent"
        });
        layout.setLayoutParam("id_root", "margin", {
            left: isFullScreen ? 0 : Config.PAGE_MARGIN_LEFT,
            top: isFullScreen ? 0 : Config.PAGE_MARGIN_TOP,
            right: isFullScreen ? 0 : Config.PAGE_MARGIN_RIGHT,
            bottom: isFullScreen ? 0 : Config.PAGE_MARGIN_BOTTOM
        });
        this.window.layout = layout;
    }

    /**
     * 播放页为全屏，在进入或退出播放页后需要设置container的背景色
     */
    setPageContainerBackground(isFullScreen: boolean) {
        if (isFullScreen) {
            this._videoContainer.background = Config.PLAYER_BG_COLOR;
        } else {
            this._videoContainer.background = "transparent";
        }
    }

    getPagePositionX() {
        return this.window.left + this.rootContainer.left;
    }

    getPagePositionY() {
        return this.window.top + this.rootContainer.top;
    }

    getWindowWidth() {
        log.I(TAG, "getWindowWidth", this.window.width);
        return this.window.width;
    }

    getWindowHeight() {
        log.I(TAG, "getWindowHeight", this.window.height);
        return this.window.height;
    }

    getWindowTop() {
        log.I(TAG, "getWindowTop", this.window.top);
        return this.window.top;
    }

    getWindowSurfaceId() {
        // @ts-ignore
        return this.window.surfaceId;
    }

    _initRouter(container: View) {
        this._router = new StackRouter();
        this._router.container = <CompositeView> container;
        this._router.config.presenterSearchPath = "/src/presenter";
        const routerHelper = new RouterHelper(this._router);
        this._router.config.presenterLoader = (pPath:string)=> routerHelper.getPresenterPath(pPath);
        this._router.run(<Page><Object> this);
        if (iVideoModel.isSkipWelcomePage()) {
            if (this.sourceLink && this.sourceLink.data) {
                try {
                    let data = JSON.parse(this.sourceLink.data);
                    if (data) {
                        // 投屏或语音打开U盘，直接展示相应页面
                        if (data.from === Consts.FromType.DLNA) {
                            this._router.navigate(RoutePath.PLAYER, data, {launchMode: "single"});
                            return;
                        } else if (data.type === "local") {
                            this._router.navigate(RoutePath.LOCAL, {}, {launchMode: "single"});
                            return;
                        }
                    }
                } catch (e) {
                    log.E(TAG, "_initRouter exception:", e);
                }
            }
            this._router.navigate(RoutePath.ONLINE, {}, {launchMode: "single"});
        } else {
            this._router.navigate(RoutePath.WELCOME, {}, {launchMode: "single"});
        }
    }

    /**
     * 启动视频页面时（按进程），重置流量开关和剩余流量提醒的标记位
     */
    _initTrafficPrompt() {
        iVideoModel.saveTrafficPrompt(Consts.TrafficPromptType.OPEN, false);
        iVideoModel.saveTrafficPrompt(Consts.TrafficPromptType.EXHAUSTION, false);
    }

    /**
     * 监听window的property变化，A或B世界因为状态栏高度不一致，需动态调整播放页位置
     */
    _registerWindowPropertyChange() {
        this.window.on("propertychange", (property: string, oldValue: object, newValue: object) => {
            if ("top" === property) {
                log.I(TAG, "propertychange", property, oldValue, newValue);
                let presenter = this._router.activatedPresenter;
                if (presenter && presenter.context.routeInfo.path === RoutePath.PLAYER) {
                    return (<IPresenter> <object> presenter).windowPositionChanged();
                }
            }
        });
    }

    /**
     * 监听按键事件，需判断当前是否为播放页，且音频焦点不在其他多媒体应用时，响应上下键切换视频
     */
    _registerKeyEvent() {
        this.window.on("keydown", (ev: { key: Object, preventDefault: () => void }) => {
            this._handleKeyEvent(false, ev);
        });

        this.window.on("keyup", (ev: { key: Object, preventDefault: () => void }) => {
            this._handleKeyEvent(true, ev);
        });
    }

    _handleKeyEvent(isKeyup: boolean, ev: { key: Object, preventDefault: () => void }) {
        let key = ev.key;
        log.I(TAG, "_handleKeyEvent", isKeyup, key);
        if (key !== Consts.KEY_MEDIA_NEXT && key !== Consts.KEY_MEDIA_PREVIOUS) {
            return;
        }

        let presenter = this._router.activatedPresenter;
        if (!presenter || presenter.context.routeInfo.path !== RoutePath.PLAYER) {
            return;
        }

        if (!iAudioSession.isMediaGainedAudioSession()) {
            if (isKeyup) {
                (<IPresenter> <object> presenter).playVideo(key === Consts.KEY_MEDIA_NEXT);
            }
            ev.preventDefault();
        }
    }

    onBackKey() {
        log.I(TAG, "onBackKey");
        let presenter = this._router.activatedPresenter;
        if (presenter && typeof (<IPresenter> <Object> presenter).onBackKey === "function") {
            return (<IPresenter> <Object> presenter).onBackKey();
        } else {
            this._router.back();
            return true;
        }
    }

    /**
     * 接收onLink
     * 1.语音启动本地视频页
     * 2.投屏启动播放页
     * 3.开启cosmo mock数据模式
     */
    onLink(link: PageLink) {
        log.D(TAG, "onLink", link.data);
        let rawdata = link.data;
        if (rawdata) {
            try {
                let data = JSON.parse(rawdata);
                if (data) {
                    if (data.type === "local") {
                        if (!Features.SUPPORT_USB) {
                            return;
                        }
                        if (iVideoModel.isSkipWelcomePage()) {
                            let presenter = this._router.activatedPresenter;
                            if (presenter && presenter.context.routeInfo.path === RoutePath.PLAYER) {
                                this._router.back();
                            }
                            this._router.navigate(RoutePath.LOCAL, {}, {launchMode: "single"});
                        } else {
                            this._router.navigate(RoutePath.WELCOME, data, {launchMode: "single"});
                        }
                    } else if (data.type === "video") {
                        if (data.from === Consts.FromType.DLNA && !Features.SUPPORT_DLNA) {
                            log.W(TAG, "onLink, dlna not support");
                            return;
                        }
                        if (iVideoModel.isSkipWelcomePage()) {
                            this._router.navigate(RoutePath.PLAYER, data, {launchMode: "single"});
                        } else {
                            this._router.navigate(RoutePath.WELCOME, data, {launchMode: "single"});
                        }
                    } else if (data.type === "test") {
                        if (data.mock === "yes") {
                            iVideoModel.saveMockDataFlag(true);
                        } else {
                            iVideoModel.saveMockDataFlag(false);
                        }
                    }
                }
            } catch (e) {
                log.E(TAG, "onLink exception:", e);
            }
        }
    }

    onShow() {
        log.I(TAG, "onShow");
        this._shown = true;
    }

    onHide() {
        log.I(TAG, "onHide");
        this._shown = false;
    }

    onActivate() {
        log.I(TAG, "onActivate");
        this._activated = true;
    }

    onDeactivate() {
        log.I(TAG, "onDeactivate");
        this._activated = false;
    }

    onStop() {
        log.I(TAG, "onStop");
        this._router.destroy();
        this._router = null;
        Theme.getInstance().off("globalthemechange", this._globalThemeChangedListener);
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        if (this.window) {
            this.window.removeAllListeners("keydown");
            this.window.removeAllListeners("keyup");
        }
    }

    isPageShown() {
        return this._shown;
    }

    isPageActivated() {
        return this._activated;
    }
}

export = App;
