import YObject = require("../core/YObject");
import Point = require("./Point");
import Context = require("./Context");
import Gradient = require("./Gradient");
/**
 * <p>An abstract class represents a shape.<br>
 * A shape subclass should implement containsXY,containsPoint and drawCanvas functions.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @abstract
 * @since 6
 */
declare abstract class Shape extends YObject {
    /**
     * <p>Indicates whether the specified point is inside this shape.</p>
     * <p>An invalid shape never contains any point.</p>
     * @param {number} x - the X coordinate of the point being tested for containment.
     * @param {number} y - the Y coordinate of the point being tested for containment.
     * @return {boolean} true if (x,y) is inside the shape, otherwise false.
     * @public
     * @abstract
     * @since 6
     */
    public abstract containsXY(x: number, y: number): boolean;
    /**
     * <p>Indicates whether the specified point is inside this shape.</p>
     * <p>An invalid shape never contains any point.</p>
     * @param {yunos.graphics.Point} point - the point being tested for containment.
     * @return {boolean} true if the point is inside the shape, otherwise false.
     * @public
     * @abstract
     * @since 6
     */
    public abstract containsPoint(point: Point): boolean;
    /**
     * <p>Draw this shape in canvas</p>
     * @param {yunos.graphics.Context} ctx - the canvas context
     * @param {(string | number | yunos.graphics.Gradient)} fillStyle - the style of shape filling
     * @friend
     * @abstract
     */
    abstract drawCanvas(ctx: Context, fillStyle: string | number | Gradient): void;
}
export = Shape;
