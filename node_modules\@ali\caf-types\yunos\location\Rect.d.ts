import LocationArea = require("./LocationArea");
import Point = require("./Point");
/**
 * <p>An object represents a rectangular geographical area.</p>
 * @extends yunos.location.LocationArea
 * @memberof yunos.location
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 2
 */
declare class Rect extends LocationArea {
    private _northeast;
    private _southwest;
    public constructor();
    /**
     * <p>Defines northeast corner of the rectangular area.</p>
     * @name yunos.location.Rect#northeast
     * @type {yunos.location.Point}
     * @public
     * @since 2
     */
    public northeast: Point;
    /**
     * <p>Defines southwest corner of the rectangular area.</p>
     * @name yunos.location.Rect#southwest
     * @type {yunos.location.Point}
     * @public
     * @since 2
     */
    public southwest: Point;
}
export = Rect;
