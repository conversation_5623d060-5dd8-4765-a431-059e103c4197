/// <reference types="node" />
import TextView = require("../view/TextView");
import Rectangle = require("../../graphics/Rectangle");
import TouchEvent = require("../event/TouchEvent");
import ImageView = require("../view/ImageView");
import GestureEvent = require("../event/GestureEvent");
import FrameAnimation = require("../animation/FrameAnimation");
import EventEmitter = require("../../core/EventEmitter");
import VoiceCommand = require("../voice/VoiceCommand");
import CompositeView = require("../view/CompositeView");
import VoiceEvent = require("../event/VoiceEvent");
/**
 * <p>An abstract class with capabilities to compound TextView and clickable widget.</p>
 * @memberof yunos.ui.widget
 * @extends yunos.ui.view.CompositeView
 * @since 3
 * @public
 */
declare class CompoundButton extends CompositeView {
    private _privTextVerticalAlign;
    private _privTextAlign;
    private _privTextViewColor;
    private _privTextViewElideMode;
    private _privTextViewFontFamily;
    private _privTextViewFontSize;
    private _privTextViewFontWeight;
    private _privTextViewFontStyle;
    private _layoutDirection: number;
    private _currentAnimIndex;
    private _frameAnim;
    private _privTextView: TextView;
    private _leftPadding;
    private _topPadding;
    private _rightPadding;
    private _bottomPadding;
    private _spacing;
    private _contentRect;
    private _layoutType;
    private _stopPhasePropagation;
    private _interactionInterval;
    private _interactionTimer;
    private _textVisibility;
    private _renderLayoutTimer: NodeJS.Timer;
    private _justifyContent;
    protected _group: CompoundButton.CompoundButtonGroup;
    private _layoutWidth;
    private _layoutHeight;
    private _layout_node;
    private _text_node;
    private _rectangle_node;
    private _associatedWidth;
    private _associatedHeight;
    private _rectangleWidth;
    private _rectangleHeight;
    private _privTextWidth;
    private _privTextHeight;
    private _layoutUpdateLayoutSize;
    private _usrLayoutWidth;
    private _usrLayoutHeight;
    private _defaultVoiceCommand;
    private _privTextUsrWidth;
    private _propchangecallback;
    private _textchangecallback;
    private _contentsizechangeCallback;
    private _autoUnchecked;
    private _pressed;
    private _checkable;
    private _autoVoiceCommand;
    private _voiceHandlerBack;
    private _voiceTitleText;
    private _autoRegisterText: boolean;
    /**
     * Create CompoundButton.
     * @public
     * @param {yunos.page.Page | object} context
     * @param {null | object} options
     */
    /**
     * Get current layout type of CompoundButton.
     * @name yunos.ui.widget.CompoundButton#layoutType
     * @type {yunos.ui.widget.CompoundButton.LayoutType}
     * @default yunos.ui.widget.CompoundButton.LayoutType.NOTEXT
     * @public
     * @readonly
     * @since 3
     */
    public readonly layoutType: number;
    /**
     * <p>Spacing between TextView and Content, only effected when layoutType is WITHTEXT</p>
     * @name yunos.ui.widget.CompoundButton#spacing
     * @type {number}
     * @default 10
     * @public
     * @since 3
     */
    public spacing: number;
    /**
     * <p>Spacing between left bounds and content</p>
     * @name yunos.ui.widget.CompoundButton#leftPadding
     * @type {number}
     * @default 0
     * @public
     * @since 3
     */
    public leftPadding: number;
    /**
     * <p>Spacing between right bounds and content</p>
     * @name yunos.ui.widget.CompoundButton#rightPadding
     * @type {number}
     * @default 0
     * @public
     * @since 3
     */
    public rightPadding: number;
    /**
     * <p>Spacing between top bounds and content</p>
     * @name yunos.ui.widget.CompoundButton#topPadding
     * @type {number}
     * @default 0
     * @public
     * @since 3
     */
    public topPadding: number;
    /**
     * <p>Spacing between bottom bounds and content</p>
     * @name yunos.ui.widget.CompoundButton#bottomPadding
     * @type {number}
     * @default 0
     * @public
     * @since 3
     */
    public bottomPadding: number;
    /**
     * <p>CompoundButton text content, effect only if layouType set to WITHTEXT</p>
     * @name yunos.ui.widget.CompoundButton#text
     * @type {string}
     * @default ""
     * @public
     * @since 3
     */
    public text: string;
    /**
     * <p>voiceViewType.</p>
     * @override
     * @protected
     * @since 6
     */
    protected readonly voiceViewType: string;
    private _initAutoVoice(): void;
    private _addAutoRegisterVoiceCommand(): void;
    private _bindAutoVoiceEvent(handler: (...args: Object[]) => void): void;
    private _unbindAutoVoiceEvent(handler: (...args: Object[]) => void): void;
    private _voiceAutoEventHandler(e: VoiceEvent): void;
    /**
     * Defined CompoundButton may handle voice event or not.
     * @name yunos.ui.widget.CompoundButton#voiceEnabled
     * @type {boolean}
     * @override
     * @public
     * @since 5
     */
    public voiceEnabled: boolean;
    /**
     * Get default voicecommand after set voiceEnabled true.
     * @name yunos.ui.widget.CompoundButton#defaultVoiceCommand
     * @type {yunos.ui.voice.VoiceCommand}
     * @readonly
     * @public
     * @since 5
     *
     */
    public readonly defaultVoiceCommand: VoiceCommand;
    /**
     * Set text visible or not.
     * @name yunos.ui.widget.CompoundButton#textVisibility
     * @type {number}
     * @default View.Visibility.None
     * @public
     * @since 4
     *
     */
    public textVisibility: number;
    /**
     * Set text color.
     * @name yunos.ui.widget.CompoundButton#color
     * @type {string}
     * @default #000000
     * @throws {TypeError} If type of param is not string.
     * @public
     * @since 3
     */
    public color: string;
    /**
     * Set text elideMode.
     * @name yunos.ui.widget.CompoundButton#elideMode
     * @type {yunos.ui.view.TextView.ElideMode}
     * @default yunos.ui.view.TextView.ElideMode.None
     * @fires yunos.ui.view.View#propertychange
     * @throws {TypeError} If type of parameter is not TextView.ElideMode.
     * @public
     * @since 4
     *
     */
    public elideMode: number;
    /**
     * <p>The layout justifyContent, such as center, flex-start.</p>
     * @name yunos.ui.widget.CompoundButton#justifyContent
     * @type {yunos.ui.widget.CompoundButton.JustifyContent}
     * @default yunos.ui.widget.CompoundButton.JustifyContent.Center
     * @throws {TypeError} If type of parameter is not JustifyContent.
     * @public
     * @since 6
     */
    public justifyContent: string;
    /**
     * CompoundButton can be unchecked or not.
     * @name yunos.ui.widget.CompoundButton#autoUnchecked
     * @type {boolean}
     * @default true
     * @public
     * @since 4
     */
    public checkable: boolean;
    /**
     * CompoundButton can be unchecked or not.
     * @name yunos.ui.widget.CompoundButton#autoUnchecked
     * @type {boolean}
     * @default true
     * @deprecated
     * @since 5
     */
    private autoUnchecked: boolean;
    /**
     * Set CompoundButton checked state
     * This Function will effect multiState change
     * @param {boolean} checked
     * @param {boolean} animated
     * @public
     * @since 4
     *
     */
    public setChecked(checked: boolean, animated: boolean): void;
    /**
     * The text font-family, such as "sans-serif".
     * @name yunos.ui.widget.CompoundButton#fontFamily
     * @type {string}
     * @throws {TypeError} If type of parameter is not string.
     * @public
     * @since 3
     */
    public fontFamily: string;
    /**
     * <p>The text fontSize unit is px, pt, dp or sp.</p>
     * @name yunos.ui.widget.CompoundButton#fontSize
     * @type {number|string}
     * @default 18
     * @throws {TypeError} If type of param is not a number, or it is not end with "px", "pt", "dp" or "sp".
     * @public
     * @since 3
     */
    public fontSize: number | string;
    /**
     * The text fontWeight, such as Normal, Light, DemiBold, Bold, Black
     * @name yunos.ui.widget.CompoundButton#fontWeight
     * @type {yunos.ui.view.TextView.FontWeight}
     * @default yunos.ui.view.TextView.FontWeight.Normal
     * @throws {TypeError} If type of parameter is not TextView.FontWeight.
     * @public
     * @since 3
     */
    public fontWeight: number;
    /**
     * The text fontStyle, such as TextView.FontStyle.Normal or TextView.FontStyle.Italic.
     * @name yunos.ui.widget.CompoundButton#fontStyle
     * @type {yunos.ui.view.TextView.FontStyle}
     * @default yunos.ui.view.TextView.FontStyle.Normal
     * @throws {TypeError} If type of parameter is not TextView.FontStyle.
     * @public
     * @since 3
     */
    public fontStyle: number;
    /**
     * <p>The text align, such as center, left and right.</p>
     * @name yunos.ui.widget.CompoundButton#textAlign
     * @type {TextView.Align}
     * @default {TextView.Align.Center}
     * @throws {TypeError} If type of parameter is not TextView.Align.
     * @public
     * @since 3
     */
    public textAlign: number;
    /**
     * <p>The text verticalAlign, such as top, middle and bottom.</p>
     * @name yunos.ui.widget.CompoundButton#textVerticalAlign
     * @type {TextView.VerticalAlign}
     * @default {TextView.VerticalAlign.Middle}
     * @throws {TypeError} If type of parameter is not TextView.VerticalAlign.
     * @public
     * @since 3
     */
    public textVerticalAlign: number;
    /**
     * Defines the width of the textView, in pixels. And set the textView._defaultWidth as false.
     * @name yunos.ui.widget.CompoundButton#textWidth
     * @type {number}
     * @public
     * @since 5
     */
    public textWidth: number;
    /**
     * <p>Prevent this event in current phase.</p>
     * @name yunos.ui.widget.CompoundButton#stopPhasePropagation
     * @type {boolean}
     * Set stopPhasePropagation.
     * @public
     * @since 4
     *
     */
    public stopPhasePropagation: boolean;
    /**
     * <p>This property holds touch interaction interval of the CompoundButton.</p>
     * @name yunos.ui.widget.CompoundButton#interactionInterval
     * @param  {number} value
     * @public
     * @since 4
     *
     */
    public interactionInterval: number;
    /**
     * <p>The layout direction, such as LeftRight, RIGHTLEFT.</p>
     * @name yunos.ui.widget.CompoundButton#layoutDirection
     * @type {CompoundButton.LayoutDirection}
     * @default CompoundButton.LayoutDirection.LeftRight
     * @throws {TypeError} If type of parameter is not TextView.LayoutDirection.
     * @public
     * @since 3
     */
    public layoutDirection: number;
    /**
     * <p>The layout width.</p>
     * @name yunos.ui.widget.CompoundButton#layoutWidth
     * @default -1;
     * @throws {TypeError} If type of parameter is not number.
     * @public
     * @since 4
     *
     */
    public layoutWidth: number;
    /**
     * <p>The layout height.</p>
     * @name yunos.ui.widget.CompoundButton#layoutHeight
     * @default -1;
     * @throws {TypeError} If type of parameter is not number.
     * @public
     * @since 4
     *
     */
    public layoutHeight: number;
    /**
     * @type {yunos.graphics.Rectangle}
     * @private
     * @readonly
     */
    private readonly contentRect: Rectangle;
    /**
     * <p>This property holds the width of the view.</p>
     * @name yunos.ui.widget.CompoundButton#width
     * @type {number}
     * @throws {TypeError} If type of parameter is not number.
     * @override
     * @public
     * @since 4
     *
     */
    public width: number;
    /**
     * <p>This property holds the height of the view.</p>
     * @name yunos.ui.widget.CompoundButton#height
     * @type {number}
     * @throws {TypeError} If type of parameter is not number.
     * @override
     * @public
     * @since 4
     *
     */
    public height: number;
    /**
     * Destroy this view.
     * @override
     * @public
     * @since 3
     */
    public destroy(recursive?: boolean): void;
    private bindEventListeners(): void;
    private _needLayoutSelf(): boolean;
    private updateLayoutChildrenToNode(): void;
    private updateSizeToRectangleNode(): void;
    private resetMarginToTextNode(): void;
    private updateMarginToTextNode(): void;
    private updateAssociatedSize(isRow: boolean): void;
    private renderLayout(immediately?: boolean): void;
    private compoundButtonDoLayout(): void;
    private setComputedLayoutParamToView(): void;
    private createPrivTextView(): void;
    /**
     * Return the property holds the checked state of current button，need override.
     * @return {boolean}
     * @protected
     * @abstract
     * @since 3
     */
    protected getCheckedState(): boolean;
    /**
     * @return {yunos.ui.view.ImageView}
     * @protected
     * @abstract
     * @since 3
     */
    protected getAnimationView(): ImageView;
    /**
     * Start to play animation, called by users.
     * @param  {boolean} animated Whether need to play animation when checked or unchecked.
     * @protected
     * @since 3
     */
    protected startAnimation(animated?: boolean): void;
    protected group: Object;
    /**
     * <p>Get the animation rate of playing animation.</p>
     * @protected
     * @since 3
     * @return {number} animationRate default 40
     */
    protected getFrameRate(): int;
    /**
     * <p>Get the animation sequence of this view.</p>
     * @return {string[]} Image src
     * @protected
     * @abstract
     * @since 3
     */
    protected getAnimationSequence(): string[];
    /**
     * Get thie anmation object.
     * @name yunos.ui.widget.CompoundButton#frameAnimation
     * @return {yunos.ui.animation.FrameAnimation}
     * @protected
     * @since 3
     */
    protected readonly frameAnimation: FrameAnimation;
    private readonly pressed: boolean;
    /**
     * Callback when animation init.
     * @protected
     * @abstract
     * @since 3
     */
    protected onAnimationInit(f?: Object): void;
    /**
     * Callback when animation start.
     * @protected
     * @abstract
     * @since 3
     */
    protected onAnimationStart(): void;
    /**
     * Callback when animation complete.
     * @protected
     * @abstract
     * @since 3
     */
    protected onAnimationComplete(): void;
    private initAnimation(): void;
    private doFrameAnimation(): void;
    private handleFrameChange(value: number): void;
    private handleFrameComplete(): void;
    private _playFrameAnim(src: string): void;
    /**
     * To stop frame animation.
     * @protected
     * @since 3
     */
    protected stopAnimation(): void;
    /**
     * Callback when touch start.
     * @param {yunos.ui.TouchEvent} ev input Event
     * @protected
     * @abstract
     * @since 3
     */
    protected onTouchStart(e?: TouchEvent): void;
    /**
     * @param {yunos.ui.TouchEvent} ev input Event
     * @protected
     * @abstract
     * @since 3
     */
    protected onTouchEnd(e?: TouchEvent): void;
    /**
     * @param {yunos.ui.TouchEvent} ev input Event
     * @protected
     * @abstract
     * @since 3
     */
    protected onTouchMove(e?: TouchEvent): void;
    /**
     * Callback when tap.
     * @param {yunos.ui.TouchEvent} ev input Event
     * @protected
     * @abstract
     * @since 3
     */
    protected onTap(e?: GestureEvent): void;
    private initTextLayoutAndCallback(): void;
    /**
     * Enum for JustifyContent
     * @default yunos.ui.widget.CompoundButton.JustifyContent.Center
     * @enum {string}
     * @readonly
     * @public
     * @since 6
     *
     */
    public static readonly JustifyContent: {
        /**
         * Centered on main axis.
         * @public
         * @since 6
         *
         */
        Center: string;
        /**
         * Align start on main axis.
         * @public
         * @since 6
         *
         */
        FlexStart: string;
        /**
         * Align end on main axis.
         * @public
         * @since 6
         *
         */
        FlexEnd: string;
        /**
         * Justified on main axis, the intervals between the items are equal.
         * @public
         * @since 6
         *
         */
        SpaceBetween: string;
        /**
         * Justified on main axis, the spacing between the sides of each item is equal. So, the interval between items is twice as large as the interval between items and borders.
         * @public
         * @since 6
         *
         */
        SpaceAround: string;
    };
    /**
     * <p>Enum for CompoundButton Layout type.</p>
     * @enum {number}
     * @public
     * @readonly
     * @since 3
     */
    public static readonly LayoutType: {
        /**
         * <p>not use.</p>
         * @public
         * @since 3
         */
        NONE: int;
        /**
         * <p>No text.</p>
         * @public
         * @since 3
         */
        NOTEXT: int;
        /**
         * <p>With text.</p>
         * @public
         * @since 3
         */
        WITHTEXT: int;
    };
    /**
     * <p>Enum for CompoundButton Layout Direction.</p>
     * @enum {number}
     * @public
     * @readonly
     * @since 3
     */
    public static readonly LayoutDirection: {
        /**
         * <p>not use.</p>
         * @public
         * @since 3
         */
        NONE: int;
        /**
         * <p>Text is from left to right.</p>
         * @public
         * @since 3
         */
        LEFTRIGHT: int;
        /**
         * <p>Text is from right to left.</p>
         * @public
         * @since 3
         */
        RIGHTLEFT: int;
        /**
         * <p>Text is from top to bottom.</p>
         * @public
         * @since 4
         *
         */
        TOPBOTTOM: int;
        /**
         * <p>Text is from bottom to top.</p>
         * @public
         * @since 4
         *
         */
        BOTTOMTOP: int;
    };
}
declare namespace CompoundButton {
    /**
     * <p>CompoundButtonGroup widget.</p>
     * @extends yunos.core.EventEmitter
     * @memberof yunos.ui.widget.CompoundButton
     * @public
     * @since 4
     *
     */
    class CompoundButtonGroup extends EventEmitter {
        private checkedView;
        private views;
        private callbacks;
        private _animatable;
        /**
         * <p>Create CompoundButtonGroup.</p>
         * @public
         * @since 4
         *
         */
        public constructor();
        public addView(view: CompoundButton): void;
        private changeCheckedView;
        public removeView(view: CompoundButton): void;
        private animatable: boolean;
    }
}
export = CompoundButton;
