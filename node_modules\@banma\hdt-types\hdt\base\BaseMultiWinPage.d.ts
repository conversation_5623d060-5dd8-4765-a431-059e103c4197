import MultiWindowPage = require("yunos/page/MultiWindowPage");
import Window = require("yunos/ui/view/Window");
declare enum MoveDirection {
    Left = 0,
    Right = 1
}
declare type WindowConfig = {
    left: number;
    top: number;
    width: number;
    height: number;
    displayId: number;
    type?: number;
};
declare class BaseMultiWinPage extends MultiWindowPage {
    windowPositons: number;
    private __allWindowConfig;
    private _isLand;
    private screenShareIndicator;
    private _isCustomWindowBackground;
    private _isCustomAnimation;
    private __baseAnimations;
    readonly globalThemeReference: string;
    static readonly MoveDirection: typeof MoveDirection;
    static readonly DisplayNames: {
        [index: number]: string;
    };
    readonly canInterScreenShare: boolean;
    customWindowBackground: boolean;
    customPageAnimation: boolean;
    onCreate(): void;
    readonly isLand: boolean;
    updateWindowBackground(): void;
    getAllWindowConfig(): {
        1: WindowConfig;
        2: WindowConfig;
        4: WindowConfig;
        8: WindowConfig;
        16: WindowConfig;
    };
    movePage(win: Window, direction: MoveDirection, uri?: string, position?: {
        x: number;
        y: number;
    }): void;
    requestScreenStatus(displayId: number, win: Window, callBack: () => void): void;
    private handleMove;
    initWindowMove(window: Window, uri?: string, aRightPosition?: {
        x: number;
        y: number;
    }, bPosition?: {
        x: number;
        y: number;
    }): void;
    private showUnsupportMovePageToast;
    canMove(displayId: number, direction: MoveDirection): boolean;
    private globalGesture;
    destroy(recursive: boolean): void;
    private sendShowOrHideBroadCast;
    private sendMoveStartBroadCast;
}
export = BaseMultiWinPage;
