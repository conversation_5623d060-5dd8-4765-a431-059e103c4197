import View = require("yunos/ui/view/View");
import CompositeView = require("yunos/ui/view/CompositeView");
import PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
import Bitmap = require("yunos/graphics/Bitmap");
/**
 * <p>FaceCollecting view aims for face recognition and face <br>
 * registration scenarios.<br>
 * <p>The FaceCollectingView class provides camera preview,<br>
 * and also face collecting ui.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.view
 * @private
 */
declare class FaceCollectingView extends CompositeView {
    private _cafBitmp;
    private _bitmap;
    private _preview;
    private _isDestroy;
    private _sreen;
    private _cameraPreview;
    private _innerCircle;
    private _outerCircle;
    private _doneCircle;
    private outerAnim;
    private _outerAnim;
    private innerAnim;
    private _innerAnim;
    private _outerDoneAnim;
    private _innerDoneAnim;
    private _doneAnim;
    private _doneGroup;
    /**
     * <p>Constructor that create a face collecting view.</p>
     * @private
     */
    private constructor(...args: Object[]);
    private _startCollectingAnim(): void;
    private _startDoneAnim(): void;
    private _stopAllAnim(): void;
    private _createCollectingAnim(view: View, from: number, to: number): PropertyAnimation;
    private _createDoneAnim(view: View, from: number, to: number): PropertyAnimation;
    private start(): void;
    private stop(): void;
    private _onCameraPreview(bitmap: Bitmap, width: number, height: number): void;
}
export = FaceCollectingView;
