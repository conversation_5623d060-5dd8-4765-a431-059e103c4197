import MotionRecognizer = require("./MotionRecognizer");
/**
 * <p>Motion Gesture recognizer is the base class of all Dynamic Motion Recognizers.</p>
 * <p>Note that this class is never used to instantiate directly.</p>
 * @extends yunos.ui.motion.MotionRecognizer
 * @memberof yunos.ui.motion
 * @abstract
 * @public
 * @since 4
 *
 */
declare class DynamicMotionRecognizer extends MotionRecognizer {
    public readonly gestureType: string;
    /**
     * <p>Enum for the motion </p>
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     * @draft
     */
    public static MotionType: {
        /**
         * <p>Not recgonized</p>
         * @public
         * @since 5
         * @draft
         */
        None: int;
        /**
         * <p> Recgonize wave left</p>
         * @public
         * @since 5
         * @draft
         */
        WaveLeft: int;
        /**
         * <p> Recgonize wave right</p>
         * @public
         * @since 5
         * @draft
         */
        WaveRight: int;
        /**
         * <p> Recgonize wave up</p>
         * @public
         * @since 5
         * @draft
         */
        WaveUp: int;
        /**
         * <p> Recgonize wave down</p>
         * @public
         * @since 5
         * @draft
         */
        WaveDown: int;
        /**
         * <p> Recgonize poke</p>
         * @public
         * @since 5
         * @draft
         */
        Poke: int;
        /**
         * <p> Recgonize finger move clockwise</p>
         * @public
         * @since 5
         * @draft
         */
        ClockWise: int;
        /**
         * <p> Recgonize finger move anticlockwise</p>
         * @public
         * @since 5
         * @draft
         */
        AntiClockWise: int;
        /**
         * <p> Recgonize hand pat down</p>
         * @public
         * @since 5
         * @draft
         */
        PatDown: int;
        /**
         * <p> Recgonize fist move to plam</p>
         * @public
         * @since 5
         * @draft
         */
        FistToPlam: int;
        /**
         * <p> Recgonize nod</p>
         * @public
         * @since 5
         * @draft
         */
        Nod: int;
        /**
         * <p>Recgonize shake</p>
         * @public
         * @since 5
         * @draft
         */
        Shake: int;
        /**
         * <p> Recgonize All motions</p>
         * @public
         * @since 5
         * @draft
         */
        All: int;
    };
}
export = DynamicMotionRecognizer;
