{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "lib": ["es6"], "typeRoots": ["node_modules/@ali/caf-types/@types", "node_modules/@types"], "types": ["node"], "target": "es2016", "noImplicitAny": true, "noEmitOnError": true, "alwaysStrict": true, "preserveConstEnums": true, "sourceMap": false, "outDir": "./src", "baseUrl": ".", "paths": {"*": ["node_modules/@banma/hdt-types/*", "node_modules/@banma/hdt-ui-types/*", "node_modules/@ali/caf-types/*", "*"]}}, "include": ["ts/**/*.ts"], "exclude": ["**/*.spec.ts"]}