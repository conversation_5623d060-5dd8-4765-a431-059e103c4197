import CompositeView = require("../view/CompositeView");
import Bitmap = require("../../graphics/Bitmap");
import TouchEvent = require("../event/TouchEvent");
/**
 * A RatingBar is an widget that shows a rating in
 * stars. The user can touch/drag or use arrow keys to set the rating when using
 * the default size RatingBar
 * @example
 * var ratingBar = new RatingBar();
 * ratingBar.value = 2.5;
 *
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 4
 *
 */
declare class RatingBar extends CompositeView {
    private _startLayout;
    private _bgStarList;
    private _starList;
    private _isIndicator;
    private _pressed;
    private _downPosX;
    private _dragBroken;
    private _bgStarSrc;
    private _starSrc;
    private _starWidth;
    private _starHeight;
    private _numStars;
    private _stepSize;
    private _rating;
    private _inited;
    private _defaultWidth;
    private _defaultHeight;
    private _bgStartLayout;
    private _spacing;
    private initView(): void;
    private initTouchEvent(): void;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.RatingBar#defaultStyleName
     * @type {string}
     * @default "RadioButton"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Defines adaptive enabled state of this view</p>
     * @return {boolean}
     * @override
     * @protected
     * @since 3
     */
    protected adaptiveEnabled(): boolean;
    /**
     * <p>return the layout path that will be load</p>
     * @return {string} the layout path, if your view name is YOURVIEW, default path is "YOURVIEW/YOURVIEW.xml"
     * @override
     * @protected
     * @since 3
     */
    protected adaptiveLayoutFilePath(): string;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * Sets the rating value (the number of stars filled).
     * @name yunos.ui.widget.RatingBar#value
     * @param rating The rating to set.
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 4
     *
     */
    public value: number;
    /**
     * Sets the count of stars to show. In order for these to be shown
     * properly
     * @name yunos.ui.widget.RatingBar#count
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 4
     *
     */
    public count: number;
    /**
     * Sets the step size (granularity) of this rating bar.
     * @name yunos.ui.widget.RatingBar#step
     * @param value The step size of this rating bar. For example, if
     *            half-star granularity is wanted, this would be 0.5.
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @example
     * var ratingBar = new RatingBar();
     * ratingBar.step = 0.5;
     * @public
     * @since 4
     *
     */
    public step: number;
    /**
     * Set item spacing.
     * @name yunos.ui.widget.RatingBar#spacing
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @example
     * var ratingBar = new RatingBar();
     * ratingBar.spacing = 10;
     * @public
     * @since 4
     *
     */
    public spacing: number;
    /**
     * Set item width.
     * @name yunos.ui.widget.RatingBar#itemWidth
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @example
     * var ratingBar = new RatingBar();
     * ratingBar.itemWidth = 80;
     *
     * @public
     * @since 4
     *
     */
    public itemWidth: number;
    /**
     * Set item height.
     * @name yunos.ui.widget.RatingBar#itemHeight
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @example
     * var ratingBar = new RatingBar();
     * ratingBar.itemHeight = 80;
     *
     * @public
     * @since 4
     *
     */
    public itemHeight: number;
    /**
     * Item background resource.
     * @name yunos.ui.widget.RatingBar#itemBgSource
     * @type {String | Bitmap}
     *
     * @public
     * @since 4
     *
     */
    public itemBgSource: string | Bitmap;
    /**
     * Item resource.
     * @name yunos.ui.widget.RatingBar#itemSource
     * @type {String | Bitmap}
     *
     * @public
     * @since 4
     *
     */
    public itemSource: string | Bitmap;
    private createBgStar(): void;
    private createStar(): void;
    private updateLayout(): void;
    /**
     * Whether this rating bar should only be an indicator (thus non-changeable
     * by the user).
     * @name yunos.ui.widget.RatingBar#isIndicator
     * @type {boolean}
     * @throws {TypeError} If parameter is not a boolean.
     * @example
     * var ratingBar = new RatingBar();
     * ratingBar.isIndicator = true;
     * @public
     * @since 5
     */
    public isIndicator: boolean;
    private onTouchDown(ev: TouchEvent): void;
    private checkPosIndex(x: number): number;
    private onTouchMove(ev: TouchEvent): void;
    private onTouchEnd(e?: TouchEvent): void;
    private onTouchCancel(e?: TouchEvent): void;
    private recalcRating(x: number): void;
}
export = RatingBar;
