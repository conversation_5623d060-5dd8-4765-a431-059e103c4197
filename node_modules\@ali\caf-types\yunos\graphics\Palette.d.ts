import YObject = require("../core/YObject");
import Color = require("./Color");
import Bitmap = require("yunos/graphics/Bitmap");
interface ColorData {
    color?: Color;
    hsv?: number[];
    x?: number;
    y?: number;
}
/**
 * A helper class to extract prominent colors from an Bitmap
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 2
 */
declare class Palette extends YObject {
    private _bitmap;
    private _colorDataList;
    /**
     * Create a Palette instance with bitmap
     * @param {yunos.graphics.Bitmap}
     * @public
     * @since 2
     */
    public constructor(bitmap: Bitmap);
    private generate(): void;
    /**
     * A number, or a string containing a number.
     * @typedef {Object} yunos.graphics.Palette~ColorInfo
     * @property {yunos.graphics.Color} color - one promary color in this picture
     * @property {number} x - position x of this color in this picture
     * @property {number} y - position y of this color in this picture
     * @property {string} hsv - this color in hsv string
     * @public
     * @since 2
     */
    /**
     * Get gray gradient colors form bitmap.
     * @return {yunos.graphics.Palette~ColorInfo} return all primary color in bitmap
     * @public
     * @since 2
     */
    public getPrimaryColors(): ColorData[];
    private getCommonColors(): ColorData[];
}
export = Palette;
