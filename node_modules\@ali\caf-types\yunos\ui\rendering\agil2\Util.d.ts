/// <reference types="node" />
import YObject = require("../../../core/YObject");
import Bitmap = require("../../../graphics/Bitmap");
import CubicBezier = require("../../animation/CubicBezier");
import Node = require("./Node");
import WindowNode = require("./WindowNode");
import ContextNode = require("./ContextNode");
import Agil2Types = require("./Agil2Types");
interface IMap {
    set(h: number, obj: Node | ContextNode): void;
    get(h: number | string): Node | ContextNode;
    delete(h: number): void;
}
declare class Util extends YObject {
    private static _lastColor;
    private static readonly cb: Agil2Types.CmdBufferClass;
    private static readonly pb: {
        createInitialWindow(...arg: Object[]): Object[];
        createWindow(...arg: Object[]): Object[];
        destroyWindow(v: number): void;
    };
    private static readonly cmd: {
        [key: string]: number;
    };
    private static getImageSize(src: string): {
        width: number;
        height: number;
    };
    private static createEasingCurve(timingFunction: string): CubicBezier;
    private static colorToRGBA(color: string): string;
    private static colorToRGBANumber(color: number | string): number;
    private static colorToARGB(color: string): string;
    private static colorToARGBNumber(color: number | string): number;
    private static getBitmapColorType(pixelFormat: number): 1 | 2 | 5;
    private static addImageBuffer(cmdId: number, handle: number, id: string, value: Buffer | Bitmap): void;
    private static clearBuffer(url: string): void;
    private static addBg(handleOrView: number, color: number): void;
    private static getRandomColor(step: number, saturation: number, lightness: number): number;
    private static requestTextLayout(handle: number, text: string, family: string, pixelSize: number, weight?: int, italic?: int, letterSpacing?: int, wordSpacing?: int, wrapMode?: int, textFormat?: int, elideMode?: int, maxLineCount?: int, lineHeightMode?: int, textChanged?: int, lineHeight?: int, lineSpacing?: int, width?: int): void;
    private static requestFontAdd(fontFamilyName: string, filePath: string): void;
    private static requestImageSize(handle: number, src: string): void;
    private static requestLinkAt(handle: number, x: number, y: number): void;
    private static requestPositionAt(handle: number, x: number, y: number): void;
    private static requestPositionToRectangle(handle: number, index: number): void;
    private static requestCursorSelectionPosition(handle: number): void;
    private static requestItemRect(handle: number): void;
    private static requestShowSoftkeyboard(handle: number): void;
    private static requestSoftkeyboardLayoutReady(handle: number): void;
    private static requestIsSoftkeyboardVisible(handle: number): void;
    private static requestHideSoftkeyboard(handle: number): void;
    private static requestActiveSoftkeyboard(handle: number): void;
    private static requestDeactiveSoftkeyboard(handle: number): void;
    private static requestConfigSoftkeyboard(handle: number, inputHints: number, inputReturnKeyType: number): void;
    private static resetSoftkeyboard(handle: number): void;
    private static pointInPath(handle: number, stroke: number, evenodd: number, x: number, y: number): void;
    private static getImageData(handle: number, x: number, y: number, w: number, h: number): void;
    private static fetchScreenSize(): void;
    private static flushAllWindow(): void;
    private static _mainWindowCreated: boolean;
    private static _initFromSubWindow: boolean;
    private static _screenWidth: int;
    private static _screenHeight: int;
    private static _viewMap: IMap | Map<string | number, Node | ContextNode>;
    private static _windowMap: Map<number, WindowNode>;
}
export = Util;
