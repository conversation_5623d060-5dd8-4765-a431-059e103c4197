import CompositeView = require("../view/CompositeView");
/**
 * <p>The comboBox provides a way for the user to quickly select a value from a value set.</p>
 * <p>By default, the comboBox displays its currently selected value.</p>
 * <p>Touch the comboBox to display a drop-down menu listing all the other available values from which the user can select a new value.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @private
 */
declare class ComboBox extends CompositeView {
    private _isShowing;
    private _closeAnimationFinished;
    private _closeIsCalled;
    private _showAnimationFinished;
    private _listView;
    private _textView;
    private _modalLayer;
    private _showAnimation;
    private _closeAnimation;
    private _items;
    private _currentText;
    private _currentIndex;
    private _imageView;
    private _offsetX;
    private _offsetY;
    private _dropDownBackground;
    private _dropDownWidth;
    private _comboBoxWidth;
    private _comboBoxHeight;
    private _textColor;
    private _leftPadding;
    private _rightPadding;
    private _pressedColor;
    private _comboBoxBorderColor;
    private _comboBoxBorderWidth;
    private _dropDownBorderWidth;
    private _dropDownBorderColor;
    private _dropDownDividerHeight;
    private _dropDownButton;
    private _dropDownPressedButton;
    private _textSize;
    /**
     * <p>Amount of pixels by which the drop down should be offset horizontally.</p>
     * <p>If the value of the offsetX property is greater than 0, the drop down menu will be offset to the left relative to the comboBox.</p>
     * <p>If the value of the offsetX property is less than 0, the drop down menu will be offset to the right relative to the comboBox.</p>
     * @name yunos.ui.widget.ComboBox#offsetX
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private offsetX: number;
    /**
     * <p>Amount of pixels by which the drop down should be offset vertically.</p>
     * <p>If the value of the offsetY property is greater than 0, the drop down menu will be offset downwards relative to the comboBox.</p>
     * <p>If the value of the offsetY property is less than 0, the drop down menu will be offset upwards relative to the comboBox.</p>
     * @name yunos.ui.widget.ComboBox#offsetY
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private offsetY: number;
    /**
     * <p>dropDownWidth property set the width of the drop-down menu.</p>
     * @name yunos.ui.widget.ComboBox#dropDownWidth
     * @type {number}
     * @default 500
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is less than 0.
     * @private
     */
    private dropDownWidth: number;
    /**
     * <p>dropDownBackground property set the background of the drop-down menu.</p>
     * @name yunos.ui.widget.ComboBox#dropDownBackground
     * @type {string}
     * @default transparent
     * @throws {TypeError} If parameter is a valid color type.
     * @private
     */
    private dropDownBackground: string;
    /**
     * <p>currentText property set the current text of the comboBox.</p>
     * @name yunos.ui.widget.ComboBox#currentText
     * @type {string}
     * @throws {TypeError} If parameter is not a string.
     * @throws {RangeError} If parameter is not in this.items.
     * @private
     */
    private currentText: string;
    /**
     * <p>currentIndex property set the current index of the comboBox.</p>
     * @name yunos.ui.widget.ComboBox#currentIndex
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private currentIndex: number;
    /**
     * <p>items property set the items lists of the drop-down menu.</p>
     * @name yunos.ui.widget.ComboBox#items
     * @type {string[]}
     * @throws {TypeError} If parameter is not an Array of strings.
     * @private
     */
    private items: string[];
    private itemselect(itemView: Object, position: number): void;
    private init(): void;
    private setCurrentText(text: string): void;
    private dropDownShow(): void;
    private dropDownClose(): void;
    private dropDownHide(): void;
    private calculateShowPosition(): void;
    private refreshListView(): void;
    private onBackkey(): void;
    private showAnimation(): void;
    private closeAnimation(): void;
    private onShowAnimationComplete(): void;
    private onCloseAnimationComplete(): void;
    private onTouchEnd(): void;
}
export = ComboBox;
