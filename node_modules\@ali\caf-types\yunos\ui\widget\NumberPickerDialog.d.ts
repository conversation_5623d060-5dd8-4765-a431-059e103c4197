import AlertDialog = require("./AlertDialog");
import NumberPicker = require("./NumberPicker");
import KeyEvent = require("yunos/ui/event/KeyEvent");
import { StringObjectKV } from "../util/TypeHelper";
/**
 * <p>A dialog that prompts the use for number using NumberPicker.</p>
 * @extends yunos.ui.widget.AlertDialog
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class NumberPickerDialog extends AlertDialog {
    private _numberPicker;
    private _isAutoTheme;
    private _pickerWidth;
    private _pickerHeight;
    private _pickerVerticalPadding;
    private _pickerOkColor;
    /**
     * <p>Constructor that create a NumberPickerDialog.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy a NumberPickerDialog.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds the value of the NumberPickerDialog.</p>
     * @name yunos.ui.widget.NumberPickerDialog#value
     * @type {number}
     * @throws {TypeError} If parameter is not an integer.
     * @throws {RangeError} If parameter is not between minValue and maxValue.
     * @public
     * @since 1
     */
    public value: number;
    /**
     * <p>Lower value of the range of numbers allowed for the NumberPickerDialog.</p>
     * @name yunos.ui.widget.NumberPickerDialog#minValue.
     * @type {number}
     * @throws {TypeError} If parameter is not an integer.
     * @public
     * @since 1
     */
    public minValue: number;
    /**
     * <p>Upper value of the range of numbers allowed for the NumberPickerDialog.</p>
     * @name yunos.ui.widget.NumberPickerDialog#maxValue.
     * @type {number}
     * @throws {TypeError} If parameter is not an integer.
     * @public
     * @since 1
     */
    public maxValue: number;
    /**
     * <p>This property holds the array of the numberPickerDialog content, sets the values to be displayed.</p>
     * <p>The length of the displayed values array must be equal to the range of the selectable numbers which equals to.</p>
     * @name yunos.ui.widget.NumberPickerDialog#displayedValues
     * @type {string[]}
     * @throws {TypeError} If parameter is not an array.
     * @throws {TypeError} If parameter is not a string array.
     * @public
     * @since 1
     */
    public displayedValues: string[];
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.NumberPickerDialog#defaultStyleName
     * @type {string}
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: "NumberPickerDialogAuto" | "NumberPickerDialog";
    /**
     * <p>Get the default style name of number picker.</p>
     * <p>Override this getter to reset NumberPicker's style.</p>
     * @name yunos.ui.widget.NumberPickerDialog#pickerStyleName
     * @type {string}
     * @default ""
     * @readonly
     * @public
     * @since 6
     *
     */
    public readonly pickerStyleName: string;
    /**
     * <p>Get the content view. Use a NumberPicker as the custom view.</p>
     * @returns {yunos.ui.view.View} the content view.
     * @protected
     * @since 1
     */
    /**
     * <p>Get the content view. Use a NumberPicker as the custom view.</p>
     * @returns {yunos.ui.view.CompositeView} the content view.
     * @override
     * @public
     * @since 6
     */
    public getContentView(): NumberPicker;
    /**
     * <p>Listener function for button tap event.</p>
     * @override
     * @protected
     * @since 1
     */
    protected onTap(index: number): void;
    /**
     * <p>Apply theme style for NumberPickerDialog.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: StringObjectKV): void;
    /**
     * <p>This property always returns the empty string.</p>
     * @name yunos.ui.widget.NumberPickerDialog#message
     * @type {string}
     * @public
     * @override
     * @since 4
     *
     */
    public message: string;
    /**
     * Handle the key up event.
     * @param {yunos.ui.event.KeyEvent} e - the keyevent info
     * @protected
     * @since 3
     *
     */
    protected onKeyUp(e: KeyEvent): boolean;
}
export = NumberPickerDialog;
