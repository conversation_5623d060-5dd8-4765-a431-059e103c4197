import DataColumn = require("yunos/database/sqlite/DataColumn");
import DataError = require("yunos/database/sqlite/DataError");
import ForeignKey = require("yunos/database/sqlite/ForeignKey");
import SQLiteDatabase = require("yunos/database/sqlite/SQLiteDatabase");
import SQLiteTransaction = require("yunos/database/sqlite/SQLiteTransaction");
export declare function buildDeleteTableSQL(tableName: string): string;
export declare function buildCreateTableSQL(tableName: string, columns: DataColumn[], foreignKeys: ForeignKey[]): string;
export declare function buildTableInfoSQL(tableName: string, columns: DataColumn[], foreignKeys: ForeignKey[]): string;
export declare function buildDeleteSyncTable(tableName: string): string;
export declare function buildCreateSyncTable(): string;
export declare function buildCreateSyncDbSQL(syncDbTblName: string): string;
export declare function saveDbName(usrDb: SQLiteDatabase, callback: (error: DataError) => void): void;
export declare function prepareSyncInternalTbl(dbInstance: SQLiteDatabase, transaction: SQLiteTransaction, callback: (error: DataError) => void): void;
