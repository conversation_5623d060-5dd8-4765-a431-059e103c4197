import CompositeView = require("../view/CompositeView");
/**
 * <p>Indicator is a basic widget that consisted of a number of circles.</p>
 * <p>Indicator widget can automatically change the circles status (size and opacity) when currentIndex changes.</p>
 * <p>It should be always used for other widgets, such as SwipeView.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class Indicator extends CompositeView {
    private _dotInactiveAnim;
    private _dotActiveAnim;
    private _dotTempAnim;
    private dotHideAnims;
    private dotShowAnims;
    private _animationGroup;
    private _count;
    private _dotList;
    private _currentIndex;
    private _gActiveLength;
    private _gInActiveLength;
    private _gDotWidth;
    private _gDotHeight;
    private _dotWidth;
    private _dotHeight;
    private _dotActiveLength;
    private _isAutoTheme;
    private _dotActiveBackground;
    private _dotInactiveBackground;
    private _dotEllipsisBackground;
    private _dotColor;
    private _itemRadius;
    private _styleType;
    private _orientation;
    private _dotInactiveOpacity;
    private _margin;
    private _defaultLayout;
    private _centerLayout;
    private _defaultWidth;
    private _defaultHeight;
    private _defaultRadius;
    private _animationDuration;
    private _centerChilds;
    private _leftEllipsis;
    private _rightEllipsis;
    private _dotActiveOpacity;
    /**
     * <p>Create an Indicator.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this Indicator.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    private onPropertyChange(property: string, oldValue: number, value: number): void;
    /**
     * <p>This property holds the total number of circles. By default the count is 0.</p>
     * @name yunos.ui.widget.Indicator#count
     * @type {number}
     * @throws {TypeError} If this value is not a positive integer.
     * @throws {RangeError} If this value must be greater than or equals 0.
     * @public
     * @since 1
     */
    public count: number;
    private createDot(index: number): CompositeView;
    private adjustElements(): void;
    /**
     * <p>This property holds the distance between two circles. The default margin is 9dp.</p>
     * @name yunos.ui.widget.Indicator#margin
     * @type {number}
     * @throws {TypeError} If this value is not a positive integer.
     * @throws {RangeError} If this value must be greater than or equals 0.
     * @public
     * @since 1
     */
    public margin: number;
    /**
     * <p>This property holds the color of circle, by default the color is "#ffffff".</p>
     * @name yunos.ui.widget.Indicator#color
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 1
     */
    public color: string;
    /**
     * <p>This property holds the radius of normal circles, namely not the circle of current index.</p>
     * @name yunos.ui.widget.Indicator#radius
     * @type {number}
     * @throws {TypeError} If this value is not a positive integer.
     * @throws {RangeError} If this value must be greater than or equals 0.
     * @public
     * @since 1
     */
    public radius: number;
    /**
     * <p> Style type of indicator. </p>
     * @name yunos.ui.widget.Indicator#styleType
     * @type {number}
     * @throws {TypeError} If this value is not a number
     * @since 3
     *
     */
    private styleType: number;
    /**
     * <p>This property holds the index of the current bigger circle. By default the current index is 0. The index should be [0, count - 1].</p>
     * @name yunos.ui.widget.Indicator#currentIndex
     * @type {number}
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If this value must be between 0 and count - 1.
     * @public
     * @since 1
     */
    public currentIndex: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.Indicator#defaultStyleName
     * @type {string}
     * @default "Indicator"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: "IndicatorVertical" | "Indicator";
    /**
     * <p>This property holds the orientation between Vertical and Horizontal.</p>
     * @name yunos.ui.widget.Indicator#orientation
     * @type {number}
     * @throws {TypeError} If this value is not a positive integer.
     * @throws {RangeError} If this value must be Vertical or Horizontal.
     * @public
     * @since 4
     *
     */
    public orientation: number;
    private updategDotProp;
    /**
     * <p>dot length in active state</p>
     * @name yunos.ui.widget.Indicator#dotActiveLength
     * @type {number}
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If this value Less than zero
     * @public
     * @since 6
     */
    public dotActiveLength: number;
    /**
     * <p>dot width</p>
     * @name yunos.ui.widget.Indicator#dotWidth
     * @type {number}
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If this value Less than zero
     * @public
     * @since 6
     */
    public dotWidth: number;
    /**
     * <p>dot height</p>
     * @name yunos.ui.widget.Indicator#dotHeight
     * @type {number}
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If this value Less than zero
     * @public
     * @since 6
     */
    public dotHeight: number;
    /**
     * apply theme style for indicator
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    private resetUI(): void;
    private initLayout(): void;
    private initializeAnimation(): void;
    private updateAnimProp;
    private endAnimation(): void;
    private showAnimation(oldIndex: number, currentIndex: number): void;
    private resetAnimTarget;
    private isLeftIndex;
    private isCenterIndex;
    private isRightIndex;
    private showView;
    private showActiveView;
    private hideView;
    private updateViewsVisible;
    private hideViews;
    private hideLeftViews;
    private hideRightViews;
    private swapViewAttrs;
    private initHideAnimView(start: number, end: number, oldActive: number): int;
    private initShowAnimView;
    private initAnimationView(oldIndex: number, currentIndex: number): void;
    private updateDotStyle(styleName: string): void;
    private createEllipsis;
    private initEllipsisLayout;
    /**
     * Enum for Indicator Style
     * @readonly
     * @enum {number}
     * @default yunos.ui.widget.Indicator.Line
     * @public
     * @since 3
     */
    public static readonly StyleType: {
        Line: int;
        Circle: int;
    };
    /**
     * Enum for Indicator orientation.
     * @readonly
     * @enum {number}
     * @default yunos.ui.view.Indicator.Orientation.Horizontal
     * @public
     * @since 3
     */
    public static readonly Orientation: {
        /**
         * The oritation is horizontal
         * @public
         * @since 3
         */
        Horizontal: int;
        /**
         * The oritation is vertical
         * @public
         * @since 3
         */
        Vertical: int;
    };
}
export = Indicator;
