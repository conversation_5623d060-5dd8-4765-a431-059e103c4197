<partial>
    <property-set name="online">
        <id name="id_category_icon">
            <property name="src">{img(images/ic_category.png)}</property>
        </id>
        <id name="id_category_title">
            <property name="color">{theme.color.Brand_1}</property>
        </id>
        <id name="id_cp_logo">
            <property name="src">{img(images/ic_cp_logo.png)}</property>
        </id>
        <id name="id_qr_code_loading_container">
            <property name="background">{color.ONLINE_QR_CODE_BG_COLOR}</property>
        </id>
        <id name="id_network_tip">
            <property name="color">{theme.color.White_3}</property>
        </id>
        <id name="id_qr_code_tips_container">
            <property name="background">#2C3239</property>
        </id>
        <id name="id_qr_code_tips">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_error_info">
            <property name="color">{theme.color.White_3}</property>
        </id>
    </property-set>

    <property-set name="online_nav_right_item_normal">
        <id name="id_usb_first">
            <property name="iconSrc">{img(images/ic_usb_first.png)}</property>
        </id>
        <id name="id_dlna">
            <property name="iconSrc">{img(images/ic_dlna.png)}</property>
        </id>
        <id name="id_search">
            <property name="iconSrc">{img(images/ic_search.png)}</property>
        </id>
    </property-set>

    <property-set name="online_nav_right_item_narrow">
        <id name="id_usb_first">
            <property name="src">{img(images/ic_usb_first.png)}</property>
            <property name="multiState">{config.BTN_MULTISTATE}</property>
        </id>
        <id name="id_usb_second">
            <property name="multiState">{config.BTN_MULTISTATE}</property>
        </id>
        <id name="id_dlna">
            <property name="src">{img(images/ic_dlna.png)}</property>
            <property name="multiState">{config.BTN_MULTISTATE}</property>
        </id>
        <id name="id_search">
            <property name="src">{img(images/ic_search.png)}</property>
            <property name="multiState">{config.BTN_MULTISTATE}</property>
        </id>
    </property-set>

    <property-set name="online_category_item">
        <id name="id_title">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_focus_image">
            <property name="src">{img(images/ic_online_category_item_focus.png)}</property>
        </id>
    </property-set>

    <property-set name="online_item">
        <id name="id_mask">
            <property name="src">{img(images/ic_online_item_mask.png)}</property>
        </id>
        <id name="id_hot_icon">
            <property name="src">{img(images/ic_hot.png)}</property>
        </id>
        <id name="id_hot_num">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_size">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_title">
            <property name="color">{theme.color.White_2}</property>
        </id>
        <id name="id_last_played">
            <property name="src">{img(images/ic_online_item_focus.png)}</property>
        </id>
    </property-set>

    <property-set name="src_usb">
        <property name="src">{img(images/ic_usb.png)}</property>
    </property-set>

    <property-set name="src_usb_second">
        <property name="src">{img(images/ic_usb_second.png)}</property>
    </property-set>

    <property-set name="icon_src_usb">
        <property name="iconSrc">{img(images/ic_usb.png)}</property>
    </property-set>

    <property-set name="icon_src_usb_second">
        <property name="iconSrc">{img(images/ic_usb_second.png)}</property>
    </property-set>

    <property-set name="src_ksqr">
        <property name="src">{img(images/ic_ksqr.png)}</property>
    </property-set>
</partial>
