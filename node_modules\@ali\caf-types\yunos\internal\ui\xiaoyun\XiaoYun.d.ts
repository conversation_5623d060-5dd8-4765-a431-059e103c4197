import TTS = require("./TTS");
import EventEmitter = require("yunos/core/EventEmitter");
import VoiceModality = require("yunos/ui/mmi/VoiceModality");
/**
 * @friend
 */
declare class <PERSON><PERSON><PERSON> extends EventEmitter {
    static getInstance(x: Object): XiaoYun;

    static releaseInstance(x: Object): void;

    setMultiRoundDialog: (val: boolean) => void;
    say: (v1: TTS | string, v2: string) => void;
    getTTS: () => TTS;
    hide: (v1: boolean, reason: number) => void;
    updateConfig: (v: Object) => void;
    setAppContext: (v: Object) => void;
    clearAppContext: (v: Object) => void;
    voiceReady: boolean;
    isShow: boolean;
    fullDuplexStatus: string;
    audioSessionStatus: string;
    startSpeechAuto: () => void;
    startSpeech: (val: boolean) => void;
    launchDialog: (v1: string, v2: string, v3: Object, v4 ?:number) => void;
    requestAbandonVoiceAudioSession: () => void;
}

export = XiaoYun;
