export class CafMatrix {

    constructor(mat?: CafMatrix);

    identity(): void;

    inverse(): CafMatrix;

    rotate(rad: number): void;

    toArray(): number[];

    rotate3D(x: number, y: number, z: number, rad: number): void;

    scale3D(x: number, y: number, z: number): void;

    translate3D(x: number, y: number, z: number): void;

    assign(mat: CafMatrix): void;

    multiply(mat: CafMatrix): void;

    translate(x: number, y: number, z?: number): void;

    scale(x: number, y: number, z?: number): void;

    skew(x: number, y: number, z?: number): void;

    vectorMul(v: number[]): number[];

    vectorMul3D(v: number[]): number[];

    invVectorMul3D(v: number[]): number[];
}
