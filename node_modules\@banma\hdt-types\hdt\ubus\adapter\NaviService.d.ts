import YObject = require("yunos/core/YObject");
declare class NaviServiceAdapter extends YObject {
    private _iface;
    private bus;
    private service;
    private _permission;
    constructor(permission?: string);
    registerrequest(cb: (message: string) => Promise<string>): void;
    emitcityChanged(Address: string): boolean;
    emitnaviStatusChanged(status: number, data: string): boolean;
    emitstartNavi(data: string): boolean;
    emitnotifyGuideInfoChanged(data: string): boolean;
    emitdriveModeChanged(status: number): boolean;
    emitpreferSettingChanged(data: string): boolean;
    emitnotifyLAppCustomEvent(data: string): boolean;
    destroy(): void;
}
export = NaviServiceAdapter;
