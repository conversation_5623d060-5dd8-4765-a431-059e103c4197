/// <reference types="node" />
import Cursor = require("yunos/provider/Cursor");
import DataError = require("yunos/database/sqlite/DataError");
import { SQLiteCursorAddon } from "node_sql_caf.node";
declare class SQLiteCursor extends Cursor {
    private _cursor;
    /**
     * @private
     * @hiddenOnPlatform auto
     */
    public constructor(cursor: SQLiteCursorAddon);
    /**
     * <p>Count of data rows in cursor</p>
     * @name yunos.provider.Cursor#count
     * @type {number}
     * @readonly
     * @public
     * @override
     * @since 2
     */
    public readonly count: number;
    /**
     * <p>The column count of the data schema in cursor</p>
     * @name yunos.provider.Cursor#columnCount
     * @type {number}
     * @readonly
     * @public
     * @override
     * @since 2
     */
    public readonly columnCount: number;
    /**
     * <p>The names of all columns</p>
     * @name yunos.provider.Cursor#columnNames
     * @type {string[]}
     * @readonly
     * @public
     * @override
     * @since 2
     */
    public readonly columnNames: string[];
    /**
     * <p>Move the cursor by a relative amount, forward or backward, from the<br>
     * current position.</p>
     * <p>Cursor moves forward in case offset is positive while it moves backward<br>
     * if offset is negative.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let ret = cursor.moveSync(1);
     *    if (ret === true) {
     *        // success
     *    } else {
     *        // fail
     *    }
     *
     * @param {number} offset - The offset to be applied from the current position
     * @return {boolean} indicates whether cursor is moved to the expected position
     * @public
     * @override
     * @since 2
     */
    public moveSync(offset: number): boolean;
    /**
     * <p>Move the cursor to an absolute position.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let ret = cursor.moveToPositionSync(1);
     *    if (ret === true) {
     *        // move to position 1 successfully
     *    } else {
     *        // move to position 1 failed
     *    }
     *
     * @param {number} position - The absolute position to move
     * @throws {yunos.database.sqlite.DataError} If the given param is invalid or error occurs while
     * moving cursor
     * @return {boolean} indicates whether cursor is moved to the expected position
     * @public
     * @override
     * @since 2
     */
    public moveToPositionSync(pos: number): boolean;
    /**
     * <p>Async interface to move the cursor to a relative amount, forward or<br>
     * backward, from the current position.</p>
     *
     * @example
     *
     *    let moveCallback = function(error)  {
     *        if (error  === null) {
     *            cursor.getValue(0);
     *        }
     *    }
     *    cursor.move(1, moveCallback);
     *
     * @param {number} offset - The offset to be applied from the current position
     * @param {yunos.provider.Cursor~moveCallback} callback Callback function to handle the move result
     * @public
     * @override
     * @since 2
     */
    public move(offset: number, onMove: (error: DataError) => void): void;
    /**
     * <p>Async interface to move the cursor to to an absolute position.</p>
     *
     * @example
     *
     *    let moveCallback = function(error)  {
     *        if (error  === null) {
     *            cursor.getValue(0);
     *        }
     *    }
     *    cursor.moveToPosition(1, moveCallback);
     *
     * @param {number} pos - The absolute position to move
     * @param {yunos.provider.Cursor~moveCallback} callback - Callback function to handle the move result
     * @public
     * @override
     * @since 2
     */
    public moveToPosition(pos: number, onMove: (error: DataError) => void): void;
    /**
     * <p>Get the zero-based index for the given column name, or -1 if the column<br>
     * doesn't exist.</p>
     *
     * @param {string} columnName - The name of the target column
     * @return {number} Return the zero-based column index for the given column name
     * @public
     * @override
     * @since 2
     */
    public getColumnIndex(columnName: string): number;
    /**
     * <p>Get the column name corresponding to the spcified column index.</p>
     *
     * @param {number} columnIndex - The zero-based index of the target column.
     * @return {string} Return the column name for the given column index.
     * @public
     * @override
     * @since 2
     */
    public getColumnName(columnIndex: number): string;
    /**
     * <p>Get the value at the column corresponding to the spcified column index.</p>
     *
     * @param {number} columnIndex - The zero-based index of the target column
     * @return {string|number|Object} Returns the value of the column;
     * @public
     * @override
     * @since 2
     */
    public getValue(columnIndex: number): Object;
    private getString(columnIndex: number): string;
    private getNumber(columnIndex: number): number;
    private getBlob(columnIndex: number): Buffer;
    /**
     * <p>Get the data type of the specified column</p>
     *
     * @param {number} columnIndex - The zero-based index of the target column
     * @return {yunos.provider.Cursor.CursorDataType} Returns the column data type which is
     * enum CursorDataType, such as: CursorDataType.DataTypeString
     * @public
     * @override
     * @since 2
     */
    public getType(columnIndex: number): number;
    /**
     * <p>Check if the specified column is NULL</p>
     *
     * @param {number} columnIndex - The zero-based index of the target column
     * @return {boolean} Whether the column is NULL.
     * @public
     * @override
     * @since 2
     */
    public isNull(columnIndex: number): boolean;
    /**
     * <p>Close the cursor.</p>
     *
     * @return {boolean} Whether close cursor success.
     * @public
     * @override
     * @since 2
     */
    public close(): boolean;
    /**
     * <p>Check whether the cursor is closed.</p>
     *
     * @return {boolean} Whether cursor is closed. true: yes, false: no
     * @public
     * @override
     * @since 2
     */
    public isClosed(): boolean;
}
export = SQLiteCursor;
