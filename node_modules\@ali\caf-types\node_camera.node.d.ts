declare namespace VideoCaptureNode {
    class VideoCapture {
        RequestCameraAccess(usageType: number, flags: number): number;
        ReleaseCameraAccess(): void;
        StartStream(streamType: number, reservedInt: number, connectionName: string): void;
        StopStream(streamType: number): void;
        SetStreamSize(streamType: number, width: number, height: number): void;
        GetStreamSize(streamType: number): void;
        GetStreamSizesSupported(streamType: number): Object[];
        SetDisplayRotation(rotation: number): void;
        SetCustomCallbackFlag(flag: number): void;
        IsPreviewEnabled(): boolean;
        TriggerPreCaptureMetering(): boolean;
        SetPreviewFrameRate(fps: number, flush: boolean): void;
        GetPreviewFrameRate(): number;
        GetPreviewFrameRatesSupported(): number[];
        SetRecordingFlag(flag: boolean, flush: boolean): void;
        StartAutoFocus(): void;
        CancelAutoFocus(): void;
        SetFocusMode(mode: string, flush: boolean): void;
        GetFocusMode(): string;
        GetFocusModesSupported(): string[];
        GetFocusAreas(): Object[];
        SetFocusAreas(area: Object[], flush: boolean): void;
        GetMaxNumFocusAreas(): number;
        GetMeteringAreas(): Object[];
        SetMeteringAreas(area: Object[], flush: boolean): void;
        GetMaxNumMeteringAreas(): number;
        GetZoom(): number;
        SetZoom(value: number, flush: boolean): void;
        GetMaxZoom(): number;
        GetZoomRatios(): number[];
        IsZoomSupported(): boolean;
        GetExposureCompensation(): number;
        SetExposureCompensation(value: number, flush: boolean): void;
        GetMaxExposureCompensation(): number;
        GetMinExposureCompensation(): number;
        GetExposureCompensationStep(): number;
        SetAutoExposureLock(lock: boolean, flush: boolean): void;
        GetAutoExposureLock(): boolean;
        IsAutoExposureLockSupported(): boolean;
        SetFlashMode(mode: string, flush: boolean): void;
        GetFlashMode(): string;
        GetFlashModeSupported(): string[];
        SetWhiteBalance(mode: string, flush: boolean): void;
        GetWhiteBalance(): string;
        GetWhiteBalanceModeSupported(): string[];
        SetAutoWhiteBalanceLock(lock: boolean, flush: boolean): void;
        GetAutoWhiteBalanceLock(): boolean;
        IsAutoWhiteBalanceLockSupported(): boolean;
        SetAntibanding(mode: string, flush: boolean): void;
        GetAntibanding(): string;
        GetAntibandingModeSupported(): string[];
        SetJpegRotation(value: number, flush: boolean): void;
        GetJpegRotation(): number;
        SetJpegQuality(value: number, flush: boolean): void;
        GetJpegQuality(): number;
        SetCaptureMode(value: number, flush: boolean): void;
        GetCaptureMode(): number;
        SetGpsLatitude(lat: number, flush: boolean): void;
        SetGpsLongitude(lon: number, flush: boolean): void;
        SetGpsAltitude(alt: number, flush: boolean): void;
        SetGpsProcessingMethod(method: string, flush: boolean): void;
        SetGpsTimestamp(timestamp: number, flush: boolean): void;
        RemoveGpsData(): void;
        SetExtensionValue(extension: number, value: string, flush: boolean): void;
        RegisterOpenCallback(func: Function): void;
        RegisterTaskCallback(func: Function): void;
        On(type: string, func: Function): void;
        Delete(): void;
    }
}
/* eslint-disable no-redeclare */
declare class VideoCaptureNode {
    static Create(videoCaptureId: number, workMode: number): VideoCaptureNode.VideoCapture;
    static AsyncCreate(videoCaptureId: number, workMode: number): VideoCaptureNode.VideoCapture;
    static GetDeviceTypeOfVideoCapture(): number;
    static GetNumberOfVideoCapture(): number;
    static GetValidVideoCaptureId(): number[];
    static GetVideoCaptureInfo(videoCaptureId: number): Object;
    static GetSupportedViews(): number[];
    static CreateWithView(viewType: number): VideoCaptureNode.VideoCapture;
}

export = VideoCaptureNode;
