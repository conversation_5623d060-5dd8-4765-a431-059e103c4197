import Dialog = require("../widget/Dialog");
import TextView = require("../view/TextView");
import View = require("../view/View");
import CompositeView = require("../view/CompositeView");
import { MultiState } from "../util/TypeHelper";
import UIEvent = require("../event/UIEvent");
interface IButton {
    text?: string;
    fontSize?: string | number;
    color?: string;
    multiState?: MultiState;
}
interface IButtonWithStyle {
    text?: string;
    style?: string;
}
/**
 * <p>AlertDialog widget.</p>
 * <p>The AlertDialog is an extension of the Dialog Class. You should use it for dialogs that use any of the following features:<br>
 * 1. A title | None title;<br>
 * 2. A text message;<br>
 * 3. One, two, or three buttons.</p>
 * @extends yunos.ui.widget.Dialog
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class AlertDialog extends Dialog {
    protected _containerPaddings: number | number[];
    protected _minHeightFrame: number;
    protected _maxHeightFrame: number;
    protected _minHeightContent: number;
    protected _maxHeightContent: number;
    private _bacgroundCapInsets;
    private _containerClipRadius;
    private _containerShadowColor;
    private _containerShadowRadius;
    private _containerShadowOffsetX;
    private _containerShadowOffsetY;
    private _wrapHeightType;
    private _titleContainer;
    private _titleHeight;
    private _titleView;
    private _title;
    private _titleColor;
    private _titleFontSize;
    private _titleFontWeight;
    private _titleBackground;
    private _titleFontFamily;
    protected _titleDividerLineWidth: number;
    protected _buttonDividerLineWidth: number;
    protected _titleHeightNoTitle: number;
    private _closeIconLeft;
    private _closeIcon;
    private _closeIconEnable;
    private _closeIconSrc;
    private _closeIconMargin;
    private _closeIconMultiState;
    private _actionIcon;
    private _actionIconEnable;
    private _actionIconSrc;
    private _actionIconMargin;
    private _actionIconMultiState;
    protected _customContentApplyMargins: boolean;
    protected _horizontalMargin: number | number[];
    protected _verticalMargin: number | number[];
    protected _content: string | CompositeView;
    protected _contentView: CompositeView;
    protected _contentColor: string;
    protected _contentBackground: string;
    protected _contentFontSize: string;
    protected _noTitleMarginTop: number;
    protected _noButtonMarginBottom: number;
    private _originContentView;
    private _textView;
    private _lineSpacing;
    private _contentHeightListener;
    private _customContentView;
    private _isCustomContent;
    private _textLineHeight;
    private _textLineHeightMode;
    private _maxButtonLength;
    private _buttonContainer;
    private _buttons;
    protected _buttonsView: CompositeView;
    private _buttonArray;
    private _buttonBorderRadius;
    private _buttonFontSize;
    private _buttonHeight;
    protected _buttonSpacing: number;
    private _normalButtonMultiState;
    private _positiveButtonMultiState;
    private _negativeButtonMultiState;
    private _buttonMultiStateTransition;
    private _buttonShadowParam;
    private _buttonShadowColor;
    private _buttonBorderWidth;
    private _buttonCanvasDrawablePath;
    private _buttonHorizontalMargin;
    private _buttonOnlyOneWidth;
    private _takeOverMode;
    protected _result: Object;
    protected _platform: string;
    private _motionTransform;
    private _blueLeft;
    private _blueRight;
    private _blueTop;
    private _redRight;
    private _greenLeft;
    private _grayRight;
    private _motionDialogBackgroundImage;
    private _im;
    private _multimodeGesture;
    private _motionStartHandle;
    private _motionMoveHandle;
    private _motionEndHandle;
    private _motionWakeUpHandle;
    private _currentMode;
    private _resolver;
    private _motionDialogCore;
    private _animationGroup;
    private _backgroundAnimation;
    private _mainAnimation;
    private _motionDialogLayout;
    private _selected;
    private _motionStateObj;
    private _initMotionDialogCore;
    private _animationToMotionPhase;
    private _motionButtonArray;
    private _rightBackgroundView;
    private _leftBackgroundView;
    private _topBackgroundView;
    private _bottomBackgroundView;
    private _normalObject;
    private _resultEvent;
    private _motionCloseSrc;
    private _motionCoreInited;
    /**
     * <p>Create an AlertDialog.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this AlertDialog.</p>
     * @param {boolean} recursive - destroy the children in the AlertDialog if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * <p>Destructor that destroy this AlertDialog.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>This property holds title of the dialog.</p>
     * @name yunos.ui.widget.AlertDialog#title
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 1
     */
    public title: string;
    /**
     * <p>This property holds message content of the dialog.</p>
     * @name yunos.ui.widget.AlertDialog#message
     * @type {string}
     * @throws {TypeError} If this value is not a string.
     * @public
     * @since 1
     */
    public message: string;
    /**
     * <p>This property holds text and color (if specified) of buttons for this dialog.</p>
     * <p>Each item in array can be two types:<br>
     * 1.string, which means that the title of button.<br>
     * 2.object, which should include two key-value pairs: text and color. Both are string.</p>
     * @name yunos.ui.widget.AlertDialog#buttons
     * @type {Object[] | string[]}
     * @throws {TypeError} If this value is not an array of object or string.
     * @public
     * @since 1
     */
    /**
     * <p>This property holds text and color (if specified) of buttons for this dialog.</p>
     * <p>Each item in array can be three types:<br>
     * 1.string, which means that the title of button.<br>
     * 2.object, which should include key-value pairs: text, fontSize, color. Both are string.</p>
     * 3.object, which should include key-value pairs: text, and style. Both are string.</p>
     * @name yunos.ui.widget.AlertDialog#buttons
     * @type {Object[] | string[]}
     * @throws {TypeError} If this value is not an array of object or string.
     * @public
     * @since 4
     *
     */
    /**
     * <p>This property holds text and color (if specified) of buttons for this dialog.</p>
     * <p>Each item in array can be three types:<br>
     * 1.string, which means that the title of button.<br>
     * 2.object, which should include key-value pairs: text, fontSize, color. Both are string.</p>
     * 3.object, which should include key-value pairs: text, and style. Both are string.</p>
     * @name yunos.ui.widget.AlertDialog#buttons
     * @type {(Object | string)[]}
     * @throws {TypeError} If this value is not an array of object or string.
     * @public
     * @since 6
     *
     */
    public buttons: (IButton | IButtonWithStyle | string)[];
    /**
     * <p>This property holds whether the dialog should be closed when tap the buttons of the dialog.</p>
     * @name yunos.ui.widget.AlertDialog#takeOverMode
     * @type {boolean}
     * @default true
     * @throws {TypeError} If this value is not a boolean.
     * @public
     * @since 3
     */
    public takeOverMode: boolean;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.AlertDialog#defaultStyleName
     * @type {string}
     * @default "AlertDialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    private motionTransform: boolean;
    /**
     * <p>Get the option button at index.</p>
     * @example
     * myAlertDialog.getButton(0).enabled = false;
     * @param {number} index - the index of the option button
     * @returns {yunos.ui.widget.Button} the option button at index.
     * @throws {TypeError} If this value is not a number.
     * @throws {RangeError} If option button at index does not exist.
     * @public
     * @since 2
     */
    public getButton(index: number): View;
    /**
     * <p>Start to show the AlertDialog and display it on screen.<br>
     * The AlertDialog is placed in the application layer and opaque.<br>
     * Furthermore: x, y take effect when both value present.<br>
     * Otherwise: top, left will be determined by current theme setting</p>
     * @param {number} [x] - the left position of the AlertDialog.
     * @param {number} [y] - the top position of the AlertDialog.
     * @public
     * @override
     * @since 5
     */
    public show(x?: number, y?: number): void;
    /**
     * <p>Set the content view of the alertdialog.<br>
     * Note that the height of the contentView must be setted. Width is not nessary since the alertdialog will automatically adjust width of the contentView.<br>
     * Set the param to null if you want to remove your customized contentView.</p>
     * @param {yunos.ui.view.View|null} view - the content view.
     * @throws {TypeError} If this value is not a View or null.
     * @public
     * @since 2
     */
    public setContentView(view: View): void;
    /**
     * <p>Get the content view. When designing an AlertDialog with custom view, override this method.</p>
     * <p>Note that you must also override message api when you override this function in your customized dialog.</p>
     * @returns {yunos.ui.view.View} the content view.
     * @protected
     * @since 1
     */
    /**
     * <p>Get the content view. When designing an AlertDialog with custom view, override this method.</p>
     * <p>Note that you must also override message api when you override this function in your customized dialog.</p>
     * @returns {yunos.ui.view.CompositeView} the content view.
     * @public
     * @since 6
     */
    public getContentView(): CompositeView;
    /**
     * <p>Get the title container.</p>
     * @returns {yunos.ui.view.CompositeView} the title container.
     * @readonly
     * @public
     * @since 6
     */
    public getTitleContainer(): CompositeView;
    /**
     * <p>Get the button container.</p>
     * @returns {yunos.ui.view.CompositeView} the button container.
     * @readonly
     * @public
     * @since 6
     */
    public getButtonContainer(): CompositeView;
    /**
     * <p>Refresh paddings of dialog, it will affect title, content and buttons.<p>
     * @returns {boolean} is paddings refreshed
     * @protected
     * @since 6
     */
    protected refreshDialogPaddings(): boolean;
    /**
     * <p>Refresh paddings of content view.<p>
     * @returns {boolean} is paddings refreshed
     * @protected
     * @since 6
     */
    protected refreshContentPaddings(): boolean;
    /**
     * <p>Refresh paddings of button contaniner.<p>
     * @protected
     * @since 6
     */
    protected refreshButtonPaddings(): void;
    /**
     * <p>Refresh location of top icons when icon mode update.<p>
     * @protected
     * @since 6
     */
    protected refreshIconPosition(): void;
    /**
     * <p>Refresh location of close icon when margin update.<p>
     * @protected
     * @since 6
     */
    protected refreshCloseIconMargin(): void;
    /**
     * <p>Refresh location of action icon when margin update.<p>
     * @protected
     * @since 6
     */
    protected refreshActionIconMargin(): void;
    /**
     * <p>Parse margin params from style.<p>
     * @protected
     * @since 6
     */
    protected parseMargin(margin: number | number[]): {
        top: number;
        left: number;
        bottom: number;
        right: number;
    };
    /**
     * <p>Adjust content view layout to fit for no-title and no-button scenes.<p>
     * @protected
     * @since 6
     */
    protected adjustContentLayout(): void;
    private adjustTitleHeight(offset?: number): void;
    /**
     * <p>Apply theme style for alertdialog.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Listener function for button tap event.</p>
     * @param {number} index - the button index.
     * @protected
     * @since 1
     */
    protected onTap(index: number, event?: UIEvent): void;
    public close(): void;
    /**
     * <p>Defines adaptive enabled state of this view</p>
     * @return {boolean}
     * @protected
     * @since 3
     */
    protected adaptiveEnabled(): boolean;
    /**
     * <p>return the layout path that will be load</p>
     * @return {string} the layout path, if your view name is YOURVIEW, default path is "YOURVIEW/YOURVIEW.xml"
     * @protected
     * @since 3
     */
    protected adaptiveLayoutFilePath(): string;
    private initializeMotionCoreView(): void;
    private uninitializeMotionCoreView(): void;
    private registerMotionEventHandler(): void;
    private unregisterMotionHandler(): void;
    private onTextViewContentSizeChange(textView: TextView, contentWidth: number, contentHeight: number): void;
    private onTextViewLineCountChange(textView: TextView): void;
    private transformToMotionDialog(duration: number): void;
    private transformToNormalDialog(duration: number): void;
    private initMotionButtons(): void;
    private saveStatus(): void;
    private restoreStatus(): void;
    private initializeMotionDialog(): void;
    private checkFireTap(close?: boolean): void;
    private onSelect(index: number): void;
    private updateUI(): void;
    private initializeMotionDialogView(): void;
    private layoutPath(mode: string): string;
    private handleAnimationFrame(delta: number): void;
    private _onAnimationCompleted(): void;
    private _updateHeightIfNeeded(view: View): void;
    /**
     * <p>Enum for text color of AlertDialog option buttons.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly TextColor: {
        /**
         * TextColor for Normal state button.
         * @public
         * @since 2
         */
        Normal: string;
        /**
         * TextColor for Negative state button.
         * @public
         * @since 2
         */
        Negative: string;
        /**
         * TextColor for Positive state button.
         * @public
         * @since 2
         */
        Positive: string;
    };
    /**
     * <p>Enum for Button Style of AlertDialog option buttons.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 4
     *
     */
    public static readonly ButtonStyle: {
        /**
         * <p>Button style for primary inform style</p>
         * @public
         * @since 4
         *
         */
        Normal: string;
        /**
         * <p>Button style for warning style</p>
         * @public
         * @since 4
         *
         */
        Positive: string;
        /**
         * <p>Button style for negative style</p>
         * @public
         * @since 4
         *
         */
        Negative: string;
    };
    /**
     * <p>Enum for style of AlertDialog ui and animations</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 3
     */
    public static readonly StyleName: {
        /**
         * <p>The style for AlertDialog, showing and closing in the center of the screen.</p>
         */
        Normal: string;
        /**
         * <p>The style for AlertDialog, showing and closing from the left of the screen</p>
         * <p>just on auto platform</p>
         */
        Auto: string;
    };
    private static readonly ActionMode;
    private static readonly WrapHeightType;
}
export = AlertDialog;
