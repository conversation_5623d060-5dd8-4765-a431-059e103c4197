import { MultiState } from "yunos/ui/util/TypeHelper";
import TabBar = require("yunos/ui/view/TabBar");
import View = require("yunos/ui/view/View");
import Bitmap = require("yunos/graphics/Bitmap");
interface IStyle {
    defaultAnimViewHeight: number;
    defaultAnimViewWidth: number;
    defaultAnimViewMarginRight: number;
    defaultAnimViewMarginBottom: number;
    textAnimationDuration: number;
    tabItemMultistate: MultiState;
    currentTextMainColor: string;
    textMainColor: string;
    defaultCurrentTextWeight: string;
    defaultTextWeight: string;
    iconSelectedColor: string;
    iconNormalColor: string;
}
declare class MultiSwitchBML extends TabBar {
    private __highlightBarHeight;
    private __highlightBarWidth;
    private __highlightBarMarginBottom;
    private __highlightBarMarginRight;
    private __textAnimationDuration;
    private _leftAnimation;
    private __animationGroup;
    private __animView;
    private _items;
    private _srcItems;
    private __currentTextMainColor;
    private __textMainColor;
    private __defaultCurrentTextWeight;
    private __defaultTextWeight;
    private _iconNormalColor;
    private _iconSelectedColor;
    private __setItemWidth;
    readonly defaultStyleName: string;
    addItem(text: string, icon?: string | Bitmap): void;
    itemWidth: number;
    width: number;
    canSwipeFlag: boolean;
    private _canSwipeFlag;
    srcItems: Array<{
        text: string;
        src?: string | Bitmap;
    }>;
    items: string[];
    setAnimViewHidden(): void;
    setInitialIndex(value: number): void;
    constructor();
    applyStyle(style: IStyle): void;
    protected updateStyle(style: IStyle, diffStyle: IStyle): void;
    private _getItemIcon;
    protected doAnimation(currentIndex: number, oldIndex: number): void;
    protected getAnimationView(): View;
    private _getAnimationGroup;
    private _updateAnimViewPosition;
    private _getAnimViewLeft;
    private _getAnimViewTop;
    private _updateLeftAnimation;
    private _getRealItemWidth;
    destroy(recursive?: boolean): void;
}
export = MultiSwitchBML;
