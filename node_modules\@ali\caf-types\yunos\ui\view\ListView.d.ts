import View = require("./View");
import Point = require("../../graphics/Point");
import ScrollableView = require("./ScrollableView");
import TouchEvent = require("../event/TouchEvent");
import ListItem = require("./listview/ListItem");
import Group = require("./listview/Group");
import VoiceEvent = require("../event/VoiceEvent");
import GestureEvent = require("../event/GestureEvent");
import KeyEvent = require("../event/KeyEvent");
import HeaderListAdapter = require("../adapter/HeaderListAdapter");
import ScrollBar = require("../widget/ScrollBar");
import GroupAdapter = require("../adapter/GroupAdapter");
import BaseAdapter = require("../adapter/BaseAdapter");
import CursorAdapter = require("../adapter/CursorAdapter");
import RelativeLayout = require("../layout/RelativeLayout");
/**
 * <p>ListView that shows items in a scrolling list.</p>
 * <p>Scrolling orientation can be vertical or horizontal, default is vertical.</p>
 * @extends yunos.ui.view.ScrollableView
 * @memberof yunos.ui.view
 * @public
 * @since 1
 */
declare class ListView extends ScrollableView {
    protected _adapter: BaseAdapter | CursorAdapter | GroupAdapter | HeaderListAdapter;
    private _spacing;
    private _requestedHeight;
    private _firstPosition: number;
    private _layoutMode;
    private _specificPositon;
    private _specificStart;
    private _itemCount;
    private _dataChange;
    private _lastX;
    private _lastY;
    private _trackTimer;
    private _recycler;
    private _flinger;
    private _headers: View[];
    private _footers: View[];
    private _scrollState;
    private _itemHeight;
    private _itemWidth;
    private _mscrollY;
    private _mscrollX;
    private _contentTimer;
    private _angleThreshold;
    private _emptyView;
    private _animationScrollY;
    private _animationScrollX;
    private _reuseItem;
    private _headerCount;
    private _layoutFromEnd;
    private _updateCache;
    private _onTouchEndFunc: (e: TouchEvent) => void;
    private _tapRecognizer;
    private _longPressRecognizer;
    private _onDataChangeFunc;
    private _onAdapterDataChangeFunc;
    private _startX;
    private _startY;
    private _startmScrollX;
    private _startmScrollY;
    protected _oldScrollY: number;
    private _oldScrollX;
    private _scrollToCommand;
    private _inertTimer;
    private _smoothToPositionDuration;
    private _listviewFocusedWidth;
    private _listviewPressed;
    private _listviewFocused;
    private _recordFocusIdx;
    private _isFooterHideable;
    private _footerHeight;
    private _itemLayerIndexLayout;
    private _autoScrollAnimation;
    private _autoScrollStep;
    private _defaultSpacing;
    /**
     * Constructor that create a list view
     * @public
     * @since 1
     */
    public constructor(...args: Object[]);
    /**
     * <p>Destructor that destroy this list view.</p>
     * @param {boolean} recursive - destroy the children in the CompositeView if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * Destructor that destroy this list view
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * the data adapter of the ListView.
     * @name yunos.ui.view.ListView#adapter
     * @type {yunos.ui.adapter.BaseAdapter}
     * @public
     * @since 1
     */
    public adapter: BaseAdapter | GroupAdapter | CursorAdapter;
    private focusIndex: number;
    private focusPosition: number;
    private focusToPosition(pos: number): void;
    /**
     * The divider height(px) of the ListView.
     * @name yunos.ui.view.ListView#dividerHeight
     * @type {number}
     * @public
     * @since 1
     */
    public dividerHeight: number;
    /**
     * The spacing(px) of the ListView.
     * @name yunos.ui.view.ListView#spacing
     * @type {number}
     * @public
     * @since 2
     */
    public spacing: number;
    /**
     * The max height(px) of the ListView. this atrribute only works where this ListView height not set up.
     * @name yunos.ui.view.ListView#maxHeight
     * @type {number}
     * @default Infinty
     * @throws {TypeError} If type of parameter is not a number.
     * @public
     * @since 1
     */
    public maxHeight: number;
    /**
     * Defines height of this view, in pixels.
     * @name yunos.ui.view.ListView#height
     * @type {number}
     * @override
     * @public
     * @since 1
     */
    public height: number;
    /**
     * The scroll orientation of the ListView, default is ListView.Orientation.Vertival.
     * @name yunos.ui.view.ListView#orientation
     * @type {yunos.ui.view.ListView.Orientation}
     * @throws {TypeError} If type of parameter is not ListView.Orientation.
     * @public
     * @since 1
     */
    public orientation: number;
    /**
     * <p>The associated horizontal scroll bar. Only allow set one horizontalScrollBar.</p>
     * @name yunos.ui.view.ListView#horizontalScrollBar
     * @type {yunos.ui.widget.ScrollBar}
     * @throws {TypeError} If value is not instanceof ScrollBar
     * @public
     * @since 1
     */
    public horizontalScrollBar: ScrollBar;
    /**
     * <p>The associated vertical scroll bar. Only allow set one verticalScrollBar.</p>
     * @name yunos.ui.view.ListView#verticalScrollBar
     * @type {yunos.ui.widget.ScrollBar}
     * @throws {TypeError} If value is not instanceof ScrollBar
     * @public
     * @since 1
     */
    public verticalScrollBar: ScrollBar;
    /**
     * ListView scroll state
     * @name yunos.ui.view.ListView#scrollState
     * @type {yunos.ui.view.ListView.ScrollState}
     * @readonly
     * @public
     * @since 1
     */
    public readonly scrollState: number;
    /**
     * the scrollY of the ListView.
     * @name yunos.ui.view.ListView#scrollY
     * @type {number}
     * @override
     * @public
     * @since 2
     */
    public scrollY: number;
    /**
     * The scrollX of the ListView.
     * @name yunos.ui.view.ListView#scrollX
     * @type {number}
     * @override
     * @public
     * @since 2
     */
    public scrollX: number;
    /**
     * The contentWidth of the ListView.
     * @name yunos.ui.view.ListView#contentWidth
     * @type {number}
     * @readonly
     * @override
     * @public
     * @since 2
     */
    public readonly contentWidth: number;
    /**
     * The contentHeight of the ListView.
     * @name yunos.ui.view.ListView#contentHeight
     * @type {number}
     * @readonly
     * @override
     * @public
     * @since 2
     */
    public readonly contentHeight: number;
    /**
     * EmptyView, ListView will show empty view when adapter is empty.
     * @name yunos.ui.view.ListView#emptyView
     * @type {yunos.ui.view.View|null}
     * @public
     * @since 2
     */
    public emptyView: View;
    /**
     * Scrollable container current offset Y.
     * @name yunos.ui.view.ListView#containerOffsetY
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly containerOffsetY: number;
    /**
     * Scrollable container current offset X.
     * @name yunos.ui.view.ListView#containerOffsetX
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly containerOffsetX: number;
    /**
     * ListView layout item view from end to start.
     * @name yunos.ui.view.ListView#layoutFromEnd
     * @type {boolean}
     * @default false
     * @public
     * @since 2
     */
    public layoutFromEnd: boolean;
    /**
     * This ListView reuse item or not.
     * @name yunos.ui.view.ListView#reuseItem
     * @type {boolean}
     * @private
     */
    private reuseItem: boolean;
    /**
     * @private
     */
    private mscrollY: number;
    /**
     * @private
     */
    private mscrollX: number;
    /**
     * Left padding of ListView.
     * @name yunos.ui.view.ListView#paddingLeft
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingLeft: number;
    /**
     * Right padding of ListView.
     * @name yunos.ui.view.ListView#paddingRight
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingRight: number;
    /**
     * Top padding of ListView.
     * @name yunos.ui.view.ListView#paddingTop
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingTop: number;
    /**
     * Bottom padding of ListView.
     * @name yunos.ui.view.ListView#paddingBottom
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingBottom: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.view.ListView#defaultStyleName
     * @type {string}
     * @default "ListView"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * <p>Remove the horizontal scroll bar and its binding event.</p>
     * @protected
     * @since 1
     */
    protected removeHorizontalScrollBar(): void;
    /**
     * <p>Remove the vertical scroll bar and its binding event.</p>
     * @protected
     * @since 1
     */
    protected removeVerticalScrollBar(): void;
    /**
     * <p>Get event offset.</p>
     * @override
     * @protected
     * @since 2
     */
    protected getOffset(): number[];
    /**
     * Find the child view at the point
     * @param {yunos.graphics.Point} point - the point.
     * @return {yunos.ui.view.View} return a child view which contains the specified point and is on the top of all children,
     * otherwise return null.
     * @public
     * @since 1
     */
    public findChildAtPoint(point: Point): View;
    /**
     * Maps a point to a position in the list.
     * @param {yunos.graphics.Point} point - local coordinate point
     * @return {number} The position of the item
     * @public
     * @since 1
     */
    public pointToPosition(point: Point): number;
    /**
     * Add a fixed view to appear at the begin of the list. If this method can be
     * called more than once, the views will appear in the order they were
     * added.
     * @param {yunos.ui.view.View} view - The view to add.
     * @throws {TypeError} If type of parameter is not View.
     * @public
     * @since 1
     */
    public addHeader(view: View, fromTop?: boolean): void;
    /**
     * Removes a previously-added header view.
     * @param {yunos.ui.view.View} view - The view to remove
     * @return {boolean} true if the view was removed, false if the view was not a header
     *         view
     * @public
     * @since 1
     */
    public removeHeader(view: View): boolean;
    /**
     * Add a fixed view to appear at the end of the list. If this method can be
     * called more than once, the views will appear in the order they were
     * added.
     * @param {yunos.ui.view.View} view - The footer view to add.
     * @throws {TypeError} If type of parameter is not View.
     * @public
     * @since 1
     */
    public addFooter(view: View, fromTop?: boolean): void;
    /**
     * Removes a previously-added footer view.
     * @param {yunos.ui.view.View} view - The view to remove
     * @return {boolean} true if the view was removed, false if the view was not a footer
     *         view
     * @public
     * @since 1
     */
    public removeFooter(view: View): boolean;
    /**
     * header view count
     * @return {number} header view count
     * @public
     * @since 1
     */
    public getHeaderViewsCount(): number;
    /**
     * footer view count
     * @return {number} footer view count
     * @public
     * @since 1
     */
    public getFooterViewsCount(): number;
    /**
     * Scrolls the list items within the view by a specified number of pixels over duration milliseconds.
     * @param {number} offset - the amount of pixels to scroll in this ListView orientation.
     * @param {number} duration=0 - duration of scrolling animation in milliseconds, default is 0.
     * @public
     * @since 1
     */
    public scrollBy(offset: number, duration?: int): void;
    private _focusToIndex(index: number): void;
    /**
     * Smooth scroll to the specified position.
     * @param  {number} index - Target position.
     * @param  {number} duration - Duration of animation.
     * @public
     * @since 3
     *
     */
    public smoothToPosition(index: number): void;
    private onAutoScroll(index: number): void;
    /**
     * Returns the position within the adapter's data set for the first item
     * displayed on screen.
     * @return {number} The position within the adapter's data set
     * @public
     * @since 1
     */
    public getFirstVisiblePosition(): number;
    /**
     * <p>arrive the postion item. the position item will be positioned appropriately.</p>
     * <p>If the specified  position is less than 0, then the item at position 0 will be located.</p>
     * <p>If the specified  position is more than adapter data array length, then the last item will be located.</p>
     * @param {number} position - The position within the adapter's data set.
     * @throws {TypeError} If type of parameter is not number.
     * @public
     * @since 1
     */
    public arriveAt(position: number): void;
    /**
     * <p>Find the direct child view at point.</p>
     * @param {yunos.graphics.Point} point - the point in the view.
     * @return {yunos.ui.view.View} Returns the direct child of ListView which contains the specified point.
     * @override
     * @protected
     * @since 2
     */
    protected findDirectChildViewAtPoint(point: Point): View;
    /**
     * Implement this to apply style
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - Style config from theme.
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    /**
     * <p>Do a scroll reback at x-axis</p>
     * @override
     * @protected
     * @since 2
     */
    protected onRebackX(): void;
    /**
     * <p>Do a scroll reback at y-axis.</p>
     * @override
     * @protected
     * @since 2
     */
    protected onRebackY(): void;
    /**
     * <p>Stop the auto scrolling.</p>
     * @override
     * @protected
     * @since 2
     */
    protected stopAutoScroll(): void;
    /**
     * Handle the key up event.
     * @param {yunos.ui.event.KeyEvent} e - the keyevent info
     * @protected
     * @since 1
     */
    protected onKeyUp(e: KeyEvent): boolean;
    private focusNext(): boolean;
    private focusPrevious(): boolean;
    /**
     * Get the focus for this view.
     * @fires yunos.ui.view.View#propertychange
     * @override
     * @public
     * @since 3
     *
     */
    public focus(): void;
    private onDescendantsFocusChange(e: Object): void;
    /**
     * Handle the pan gesture start event processing.
     * @param {yunos.ui.event.GestureEvent} e - the pan gesture event info
     * @protected
     * @since 1
     */
    protected onPanStart(e: GestureEvent): void;
    /**
     * Handle the pan gesture move event processing.
     * @param {yunos.ui.event.GestureEvent} e - the pan gesture event info
     * @protected
     * @since 1
     */
    protected onPanMove(e: GestureEvent): void;
    /**
     * Handle the pan gesture end event processing.
     * @param {yunos.ui.event.GestureEvent} e - the pan gesture event info
     * @protected
     * @since 1
     */
    protected onPanEnd(e: GestureEvent): void;
    /**
     * Handle the pan gesture cancel event processing.
     * @param {yunos.ui.event.GestureEvent} e - the pan gesture event info
     * @protected
     * @since 1
     */
    protected onPanCancel(e: GestureEvent): void;
    /**
     * Handle the touch start event processing.
     * @protected
     * @since 1
     */
    protected onTouchStart(e: TouchEvent): void;
    /**
     * Handle the touch move event processing.
     * @protected
     * @since 2
     */
    protected onTouchMove(e: TouchEvent): void;
    /**
     * Handle the touch move event processing in capture phase.
     * @protected
     * @since 2
     */
    protected onTouchMoveCapture(e: TouchEvent): void;
    private onReback(): void;
    private prepareItem(startIndex: number, itemCount: number): void;
    private fillDown(pos: number, next: number): void;
    private fillUp(pos: number, next: number): void;
    private obtainView(position: number): View;
    private _handleVoiceSelect(view: View, e: VoiceEvent, position: number): void;
    private setScrollState(state: number): void;
    private getChildAt(position: number): View;
    private getChildByDataPosition(dataPosition: number): View;
    private getChildCount(): number;
    private buildItem(position: number, reusedView: View, viewType: string): View;
    private setupChild(child: View, position: number, y: number, flowDown: boolean): void;
    private trackScrollY(deltaY: number, incrementalDeltaY: number): boolean;
    private trackScrollX(deltaX: number, incrementalDeltaX: number): boolean;
    private doLayoutVertical(): void;
    private doLayoutHorizontal(): void;
    private fillGapX(down: boolean): void;
    private fillGapY(down: boolean): void;
    private fillSpecificX(position: number, start: number): void;
    private fillSpecificY(position: number, start: number): void;
    private adjustViewsUpOrDown(): void;
    private lastItemReachEnd(): boolean;
    private adjustTooHigh(childCount: number): void;
    private adjustTooLow(childCount: number): void;
    private keepScreenState(): void;
    private detachViewsFromParent(start: number, count: number): void;
    private detachAllChildren(destroy?: boolean): void;
    private detachHeaderAndFooter(): void;
    private layoutChildren(): void;
    private addReusedView(view: View, index: number): void;
    private findChildPosition(child: View): number;
    private onTap(e: GestureEvent): void;
    private onLongPress(e: GestureEvent): void;
    private onDataChange(command: number, position: number): void;
    private handleDoLayout(): void;
    private doUpdate(position: number): void;
    private onScrollStateChange(state: number): void;
    private updateScrollBar(changeValue: boolean, needHide: boolean): void;
    private destroyHeader(): void;
    private destroyFooter(): void;
    private resetOffset(): void;
    private clearRecycler(): void;
    private setScrollY(value: number): void;
    private setScrollX(value: number): void;
    private onMScrollYChange(): void;
    /**
     * Defined listview handle voice event, default handle page next and page previous.
     * @name yunos.ui.view.ListView#voiceEnabled
     * @type {boolean}
     * @public
     * @override
     * @since 4
     *
     */
    public voiceEnabled: boolean;
    private onVoiceScrollTo(index: number): void;
    private getSelectOutRangeString(): string;
    private getAutoIndexLayout(): RelativeLayout;
    private getChildByPosition(dataPosition: number): View;
    private scrollItemSizeForward(): void;
    private scrollItemSizeBackward(): void;
    protected compareWithContentSize(): number;
    public isReachHorizontalStart(): boolean;
    public isReachHorizontalEnd(): boolean;
    public isReachVerticalStart(): boolean;
    public isReachVerticalEnd(): boolean;
    private isHorizontalLayout(): boolean;
    private static readonly VIEW_TYPE_HEADER = "header";
    private static readonly VIEW_TYPE_FOOTER = "footer";
    /**
     * Enum for ListView ScrollState.
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly ScrollState: {
        /**
         * ListView scroll idle and this is the default value.
         * @public
         * @since 2
         *
         */
        IDLE: int;
        /**
         * ListView touch scroll.
         * @public
         * @since 2
         *
         */
        SCROLL: int;
        /**
         * ListView on fling.
         * @public
         * @since 2
         *
         */
        FLING: int;
        /**
         * ListView touch over sroll.
         * @public
         * @since 2
         *
         */
        OVERSCROLL: int;
        /**
         * ListView over fling.
         * @public
         * @since 2
         *
         */
        OVERFLING: int;
    };
    /**
     * Enum for ListView Orientation.
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly Orientation: {
        /**
         * ListView vertical orientation.
         * @public
         * @since 2
         *
         */
        Vertical: int;
        /**
         * ListView horizontal orientation.
         * @public
         * @since 2
         *
         */
        Horizontal: int;
    };
    private static Group: typeof Group;
    private static ListItem: typeof ListItem;
}
export = ListView;
