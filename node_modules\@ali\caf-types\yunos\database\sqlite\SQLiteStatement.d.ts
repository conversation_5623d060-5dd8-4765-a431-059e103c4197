import Cursor = require("yunos/provider/Cursor");
import DataError = require("yunos/database/sqlite/DataError");
import { SQLiteStatementAddon } from "node_sql_caf.node";
import SQLiteTransaction = require("yunos/database/sqlite/SQLiteTransaction");
import YObject = require("yunos/core/YObject");
/**
 * <p>The function which indicates the result of the execution of SQL command.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteStatement~executeCommandCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the result of the SQL command
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of data insertion.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteStatement~insertCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the failure when inserting the data
 * @param {number} rowId - The row id of the inserted data row
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of SQLite data operations such<br>
 * as delete/update.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteStatement~dataChangeCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the failure when executing the data operation
 * @param {number} affectRows - The number of data records that were affected
 * by the operation
 * @public
 * @since 2
 */
/**
 * <p>The function which indicates the result of SQLite data query.</p>
 * <p>Developer of application can implement the callback to handle the result.</p>
 * @callback yunos.database.sqlite.SQLiteStatement~queryCallback
 * @param {yunos.database.sqlite.DataError} error - The error object that indicates
 * the failure when querying the data
 * @param {Cursor} cursor - The SQLiteCursor object which provides APIs to access the
 * query result
 * @public
 * @since 2
 */
/**
 * <p>Statement class, which represents a instance of a sql statement in SQLite.</p>
 * <p>A statement instance can be got via SQLiteDatabase.createStatement()</p>
 *
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class SQLiteStatement extends YObject {
    private _statement;
    private _type;
    /**
     * Create a SQLiteStatement instance.
     *
     * @param {Object} statement - The SQLiteStatement addon object
     * @param {yunos.database.SQLiteStatement.StatementType} type - The statement type
     * @private
     * @hiddenOnPlatform auto
     */
    public constructor(statement: SQLiteStatementAddon, type: number);
    /**
     * <p>The type of the statement such as insert/update/query etc.</p>
     * @name yunos.database.sqlite.SQLiteStatement#type
     * @type {yunos.database.sqlite.SQLiteStatement.StatementType}
     * @readonly
     * @public
     * @since 2
     */
    public readonly type: number;
    /**
     * <p>Attach the statement to the specified transaction which means the<br>
     * will be executed within the transaction.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "insert into contacts values ('ccc', '**********')", null, null);
     *            let isExclusive = true;
     *            let transaction = dbInstance.createTransaction(isExclusive);
     *            statement.attach(transaction);
     *            transaction.begin(function(error) {
     *                // statement is executed in the transaction
     *                let uri = statement.executeCommandSync();
     *            });
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteTransaction} transaction - As long as the
     * statement is attached to the specified transaction, the sql of the statement
     * is executed as a part of the transaction.
     * @return {boolean} true: attached successfully, false: attach failed
     * @throws {yunos.database.sqlite.DataError} If give transaction is invalid.
     * @public
     * @since 2
     */
    public attach(transaction: SQLiteTransaction): boolean;
    /**
     * <p>Detach the statement from the transaction if it is already attached to<br>
     * a transaction.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let isExclusive = true;
     *            let statement = dbInstance.createStatement(
     *                "insert into contacts values ('ccc', '**********')", null, null);
     *            let transaction = dbInstance.createTransaction(isExclusive);
     *            statement.attach(transaction);
     *            statement.detach();
     *            // statement is not executed in the transaction
     *            let uri = statement.executeCommandSync();
     *        }
     *    });
     *
     * @return {boolean} true: detached successfully, false: detach failed
     * @public
     * @since 2
     */
    public detach(): boolean;
    /**
     * <p>Sync interface to execute the SQL command.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     * <p>And QUERY command can't be executed using this API.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "insert into contacts values ('ccc', '**********')", null, null);
     *
     *            statement.executeCommandSync();
     *        }
     *    });
     *
     * @return {number|yunos.provider.Cursor} the data type of result depends on
     * statement type. Number for insert/update/delete. Cursor for query.
     * @throws {yunos.database.sqlite.DataError} If command executes fail.
     * @public
     * @since 2
     */
    public executeCommandSync(): number | Cursor | boolean;
    /**
     * <p>Async interface to execute the SQL command.</p>
     * <p>Note: QUERY command can't be executed using this API.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let version = 1;
     *    let helper = new SQLiteOpenHelper(dbPath, version);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "insert into contacts values ('ccc', '**********')", null, null);
     *            statement.executeCommand(function(error) {
     *                if (error === null) {
     *                    console.log("---- execution is succ.");
     *                }
     *            });
     *        }
     *    });
     *
     *
     * @param {yunos.database.sqlite.SQLiteStatement~executeCommandCallback} callback -
     * The callback function that handles the result
     * @throws {yunos.database.sqlite.DataError} If callback is invalid.
     * @public
     * @since 2
     */
    public executeCommand(onExec: (error: DataError) => void): void;
    /**
     * <p>Execute the insert statement and return the rowID of the new record.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "insert into contacts values ('ccc', '**********')", null, null);
     *
     *            let uri = statement.executeInsertSync();
     *        }
     *    });
     *
     * @return {number} The rowID of the new inserted record
     * @public
     * @throws {yunos.database.sqlite.DataError} If insert is executed fail.
     * @since 2
     */
    public executeInsertSync(): number;
    /**
     * <p>Async interface to execute the insert statement.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "insert into contacts values ('ccc', '**********')", null, null);
     *
     *            let exeInsertCallback = function(error, rowId) {
     *                if (error === null) {
     *                    console.log("---- insertion is done.----");
     *                }
     *            };
     *            statement.executeInsert(exeInsertCallback);
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteStatement~insertCallback} onInsert - The
     * callback function that handles the result
     * @throws {yunos.database.sqlite.DataError} If callback is invalid.
     * @public
     * @since 2
     */
    public executeInsert(onInsert: (error: DataError, rowId: number) => void): void;
    /**
     * <p>Execute the statement(delete or update) and return the number of affect rows.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "delete from contacts", null, null);
     *
     *            let delRows = statement.executeUpdateDeleteSync();
     *        }
     *    });
     *
     * @return {number} The count of rows that are affected(deleted or updated) by
     * this statement
     * @throws {yunos.database.sqlite.DataError} If statement is executed fail.
     * @public
     * @since 2
     */
    public executeUpdateDeleteSync(): number;
    /**
     * <p>Async interface for executing the statement(delete or update) , result<br>
     * return in callback.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "delete from contacts", null, null);
     *
     *            let exeDeleteCallback = function(error, affectRows) {
     *                if (error === null) {
     *                    console.log("---- deletion is done.----");
     *                }
     *            };
     *            statement.executeUpdateDelete(exeDeleteCallback);
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteStatement~dataChangeCallback} callback -
     * The callback function that handles the result
     * @throws {yunos.database.sqlite.DataError} If callback is invalid.
     * @public
     * @since 2
     */
    public executeUpdateDelete(callback: (error: DataError, affectedRows: number) => void): void;
    /**
     * <p>Sync interface to query SQLite database and return the result via Cursor.</p>
     * <p>Note: Sync API is not recommended as it may block main thread.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "select * from contacts", null, null);
     *            let cursor = statement.executeQuerySync();
     *        }
     *    });
     *
     * @return {yunos.provider.Cursor} Return the query result with a Cursor object;
     * @throws {yunos.database.sqlite.DataError} If statement is executed fail.
     * @public
     * @since 2
     */
    public executeQuerySync(): Cursor;
    /**
     * <p>Async interface to query database and return query result via Cursor.</p>
     *
     * @example
     *
     *    let instance = require("yunos/cloudfs/Context").getInstance();
     *    let dbPath = instance.getLocalDir() + "/test.db";
     *    let SQLiteOpenHelper = require("yunos/database/sqlite").SQLiteOpenHelper;
     *    let SQLiteOpenMode = require("yunos/database/sqlite/SQLiteDatabase").OpenMode;
     *    let helper = new SQLiteOpenHelper(dbPath, 1);
     *    let dbInstance;
     *    helper.getDatabase(SQLiteOpenMode.OpenReadWrite, function(error, db) {
     *        if (error === null) {
     *            dbInstance = db;
     *            let statement = dbInstance.createStatement(
     *                "select * from contacts", null, null);
     *
     *            let onQuery = function(error, cursor)  {
     *                if (error === null) {
     *                    console.log("cursor.count = " + cursor.count);
     *                }
     *            }
     *            statement.executeQuery(onQuery);
     *        }
     *    });
     *
     * @param {yunos.database.sqlite.SQLiteStatement~queryCallback} callback - Callback
     * function for receiving query result
     * @throws {yunos.database.sqlite.DataError} If callback is invalid.
     * @public
     * @since 2
     */
    public executeQuery(onQuery: (error: DataError, cursor: Cursor) => void): void;
    /**
     * <p>Clear the args binding to the statement, prepare for next binding.</p>
     *
     * @throws {yunos.database.sqlite.DataError} If clear args is executed fail.
     * @public
     * @since 2
     */
    public clearArgs(): void;
    /**
     * <p>Bind args for the SQLiteStatement, for one statement, we can bind<br>
     * different args and reuse it.</p>
     *
     * @param {string[]|number|Buffer[]} args - Parameter array bind to sql
     * @return {boolean} true: bind success, false: bind fail
     * @throws {yunos.database.sqlite.DataError} If given args is invalid or bind args is failed.
     * @public
     * @since 2
     */
    public bindArgs(args: Object[]): boolean;
    /**
     * <p>Enum for SQLite statement type.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly StatementType: {
        /**
         * Constant of the type of statement to insert data
         * @public
         * @since 2
         */
        InsertStatement: int;
        /**
         * Constant of the type of statement to delete data
         * @public
         * @since 2
         */
        DeleteStatement: int;
        /**
         * Constant of the type of statement to update data
         * @public
         * @since 2
         */
        UpdateStatement: int;
        /**
         * Constant of the type of statement to query data
         * @public
         * @since 2
         */
        QueryStatement: int;
        /**
         * Constant of the unknown type of statement
         * @public
         * @since 2
         */
        UnknownStatement: int;
    };
}
export = SQLiteStatement;
