import { IRequestData } from "../dynamic-request/interfaces";
import { IDeviceTokenInfo } from "./interfaces";
declare class PKIWrapper {
    static readonly instance: PK<PERSON>Wrapper;
    private shepherdService;
    private constructor();
    convertPKIData(data: IRequestData): Promise<IRequestData>;
    getDeviceToken(): Promise<IDeviceTokenInfo>;
    appendPKIData(data: IRequestData, res: IDeviceTokenInfo): IRequestData;
    generateSortedConetent(data: IRequestData): string;
    md5Sign(originalContent: string, random: string): string;
}
declare const _default: PKIWrapper;
export = _default;
