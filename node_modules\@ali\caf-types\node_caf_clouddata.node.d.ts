declare class CloudData {
    release(): void;
    /* ProfileStore */
    initProfileStore(uri: string, appName: string, callback: (value: string) => void): Object;
    loadData(callback: (value: string) => void): Object;
    getValue(key: string): Object;
    setValue(key: string, value: string, accountId: string): Object;
    pushData(timeout: number): Object;
    pushDataAsync(callback: (value: string) => void): Object;
    pullData(timeout: number): Object;
    pullDataAsync(callback: (value: Object) => void): Object;
    setPushStrategy(count: number, timeout: number): Object;
    preLoadLocalConfig(filePath: string): Object;
    setAppInfo(appName: string, filePath: string): Object;

    /* FileSyncer */
    initFileSyncer(uri: string, appName: string, appObjectName: string, callback: (value: string) => void): void;
    pushFile(fileName: string): Object;
    pullFile(fileName: string): Object;

    /* ResourceSubscriber */
    initResourceSubscriber(procId: string, filePath: string, solution:number, callback: (value: string) => void): Object;
    sendReqResourceSignal(): Object;
    sendAppConfirmSignal(fileInfo: string): Object;
    setCheckFileCallback(callback: (value: string) => boolean):Object;
    sendEvent(procId: string, param: string, data: string, extend: string): Object;
}

export = CloudData;