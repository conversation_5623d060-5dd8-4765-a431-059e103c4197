<partial>
    <property-set name="local">
        <id name="id_icon">
            <property name="src">{img(images/bg_video_light.png)}</property>
        </id>
        <id name="id_info">
            <property name="color">{theme.color.Black_2}</property>
        </id>
        <id name="id_support_format_tip_1">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_support_format_tip_2">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_support_format_tip_3">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_loading_text">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_order">
            <property name="multiState">{config.ITEM_MULTISTATE}</property>
        </id>
        <id name="id_order_title">
            <property name="color">{theme.color.Black_3}</property>
        </id>
        <id name="id_order_icon">
            <property name="src">{img(images/ic_arrow_down_light.png)}</property>
        </id>
    </property-set>

    <property-set name="local_item">
        <id name="id_local_item">
            <property name="background">#ffffff</property>
        </id>
        <id name="id_last_played">
            <property name="src">{img(images/ic_local_item_focus.png)}</property>
        </id>
        <id name="id_local_title">
            <property name="color">{theme.color.Black_2}</property>
        </id>
        <id name="id_local_type">
            <property name="color">{theme.color.Black_2}</property>
        </id>
        <id name="id_local_totletime">
            <property name="color">{theme.color.Black_3}</property>
        </id>
    </property-set>
</partial>
