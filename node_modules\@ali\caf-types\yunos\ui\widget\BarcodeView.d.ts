import CompositeView = require("yunos/ui/view/CompositeView");
/**
 * <p>Barcode view that scan barcode from camera preview.</p>
 * <p>The BarcodeView class can open a camera and scan camera preview,
 * it also provide scan ui.</p>
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @relyon YUNOS_SYSCAP_VIDEOCAPTURE
 * @private
 */
declare class BarcodeView extends CompositeView {
    private _videoCaptureConnector;
    private _surfaceView;
    private _scanning;
    private _lineScan;
    private _scanView;
    private _torchView;
    private _tipView;
    private _scanTime;
    private _scanViewAni;
    private start(): void;
    private stop(): void;
    private getVideoCapture(): Object;
    private _updateScanningUI(): void;
}
export = BarcodeView;
