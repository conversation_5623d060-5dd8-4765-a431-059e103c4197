
declare class addon {
    static execCmd: Function;
    static addCallback: Function;
    static clearCallback: Function;
    static activate: Function;
    static deactivate: Function;
    static destroy: Function;
    static enterSpecifiedMode: Function;
    static leaveSpecifiedMode(uri: string, token: string): void;
    static focusWindow(token: string, isActive: boolean): void;
    static sendTextQuery(token: string, text: string, voiceZone:number): void;
    static sendJsonQuery(token: string, domain: string, intent: string, slots: string, voiceZone:number): void;
    static stopRecording(token: string, forceDisable: boolean): void;
    static startRecording(token: string, ): void;
    static addSystemCmd(token: string, data: string): void;
    static sessionEnd(token: string, sessionId: string, voiceZone: number): void;
}

export = addon;