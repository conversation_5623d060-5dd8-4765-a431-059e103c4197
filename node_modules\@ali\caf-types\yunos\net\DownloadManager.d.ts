import EventEmitter = require("yunos/core/EventEmitter");
import UBus = require("ubus");
import PageLink = require("yunos/page/PageLink");
/**
 * <p>The DownloadManager controls the download process of download manager.</p>
 * @extends yunos.core.EventEmitter
 * @memberof yunos.net
 * @public
 * @since 2
 */
declare class DownloadManager extends EventEmitter {
    private _busName;
    private _busPath;
    private _interfaceName;
    private _ubus;
    private _iface;
    private _rule;
    /**
     * Create Download Mananger.
     * @public
     * @since 2
     */
    public constructor();
    /**
     * Destroy the DownloadManager. Call this only when you want to trim memory.</p>
     * @public
     */
    public destroy(): void;
    /**
     * <p>Download Url Callback</p>
     * @callback yunos.net.DownloadManager~downloadUrlCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @param {number} reqId - Download request Id.
     * @public
     * @since 2
     */
    /**
     * <p>Init a url download with the url and options.</p>
     * @example
     * let options = {method:"GET"}
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {string} url - A manditory parameter for the download request.
     * @param {Object} [option] - Optional parameter list.
     * @param {string} [option.name] - Suggest file name, and the final file name is available in the complete callback data.
     * @param {number} [option.fd] - The file descriptor to save the download content, used for the App's protected file downloading.
     * @param {string} [option.method=GET] - Url request method POST/GET, GET by default.
     * @param {boolean} [option.lowPriority] - Download request could be pending or not due to the limited downloading number.
     * @param {string} [option.referer] - Referer Url for the Download request.
     * @param {yunos.net.DownloadManager~downloadUrlCallback} callback - Callback function.
     * @public
     * @since 2
     */
    public downloadUrl(url: string, ...args: Object[]): void;
    /**
     * <p>Pause Callback</p>
     * @callback yunos.net.DownloadManager~pauseCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @param {number} reqId - Download request Id.
     * @public
     * @since 2
     */
    /**
     * <p>Pause a url download specified by the request id.</p>
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {number} reqId - Request id specified.
     * @param {yunos.net.DownloadManager~pauseCallback} callback - Callback function.
     * @public
     * @since 2
     */
    public pause(reqId: number, callback: (e: number, reqId: number) => void): void;
    /**
     * <p>Resume Callback</p>
     * @callback yunos.net.DownloadManager~resumeCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @param {number} reqId - Download request Id.
     * @public
     * @since 2
     */
    /**
     * <p>Resume a url download specified by the request id.</p>
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {number} reqId - Request id specified.
     * @param {yunos.net.DownloadManager~resumeCallback} callback - Callback function.
     * @public
     * @since 2
     */
    public resume(reqId: number, callback: (e: number, reqId: number) => void): void;
    /**
     * <p>Cancel Callback</p>
     * @callback yunos.net.DownloadManager~cancelCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @param {number} reqId - Download requestt Id.
     * @public
     * @since 2
     */
    /**
     * <p>Cancel a url download specified by the request id.</p>
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {number} reqId - Request id specified.
     * @param {yunos.net.DownloadManager~cancelCallback} callback - Callback function.
     * @public
     * @since 2
     */
    public cancel(reqId: number, callback: (e: number, reqId: number) => void): void;
    /**
     * <p>Remove Callback</p>
     * @callback yunos.net.DownloadManager~removeCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @param {number} count - The number of the removed requests.
     * @public
     * @since 2
     */
    /**
     * <p>Remove a download from history specified by the request id.</p>
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {number[]} reqIds - Request id list specified.
     * @param {boolean} [deleteFile] - Delete the download file together, false by default.
     * @param {yunos.net.DownloadManager~removeCallback} callback - Callback function.
     * @example
     * reqIds = [1, 2, 3]
     * @public
     * @since 2
     */
    public remove(reqIds: number[], ...args: Object[]): void;
    /**
     * Object used to describe the info of the query data.
     * @typedef {Object} yunos.net.DownloadManager~QueryData
     * @property {number} reqId - The download request id.
     * @property {string} url - The download request url.
     * @property {string} name - The download item suggest name.
     * @property {string} path - The download item full path if sucesses, maybe null when a fd specified.
     * @property {yunos.net.DownloadManager.Status} status - The download status.
     * @property {number} receivedBytes - The received bytes.
     * @property {number} totalBytes - The total size in byte.
     * @property {Date} startTime - The download start time.
     * @property {Date} endTime - The download end time.
     * @property {boolean} isPaused - The download is paused or not.
     * @public
     * @since 2
     */
    /**
     * <p>Query Callback</p>
     * @callback yunos.net.DownloadManager~queryCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @param {yunos.net.DownloadManager~QueryData[]} data - List of the query data.
     * @public
     * @since 2
     */
    /**
     * <p>Query download items specified by the request id.</p>
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {number} reqId - Request id, -1 for all items.
     * @param {yunos.net.DownloadManager~queryCallback} callback - Callback function.
     * @public
     * @since 2
     */
    public query(reqId: number, callback: (e: number, d: DownloadManager.QueryData[]) => void): void;
    /**
     * Object used to describe the info of the content data.
     * @typedef {Object} yunos.net.DownloadManager~ContentData
     * @property {string} title - The title of the download item content.
     * @property {string} text - The text of the download item content.
     * @property {string} icon - The icon file path of the download item.
     * @property {yunos.page.PageLink} link - The pagelink url of the download item.
     * @public
     * @since 2
     */
    /**
     * Object used to describe the info of the action data.
     * @typedef {Object} yunos.net.DownloadManager~ActionData
     * @property {string} name - The name of the download action.
     * @property {string} icon - The icon file path of the download action.
     * @property {yunos.page.PageLink} link - The pagelink url of the download action.
     * @public
     * @since 2
     */
    /**
     * <p>SetNotification Callback</p>
     * @callback yunos.net.DownloadManager~setNotificationCallback
     * @param {yunos.net.DownloadManager.Error} error - Error code.
     * @public
     * @since 2
     */
    /**
     * <p>Set download items pagelinks of content and actions in the notification UI</p>
     * @permission ACCESS_DOWNLOAD_MANAGER.permission.yunos.com
     * @param {number} reqId - Request id of the download item.
     * @param {yunos.net.DownloadManager~ContentData} content - The content page link used by the download item.
     * @param {yunos.net.DownloadManager~ActionData[]} data - List of the actions data.
     * @param {yunos.net.DownloadManager.Status} [status] - The download status, in which the actions will be shown.
     * @param {yunos.net.DownloadManager~setNotificationCallback} callback - Callback function.
     * @example
     * var content = {};
     * content.title = DownloadManager.Variable.Name;
     * content.text = "yunos download";
     * var PageLink = require("yunos/page/PageLink");
     * content.link = new PageLink("page://download.yunos.com/");
     * @public
     * @since 2
     */
    public setNotification(reqId: number, content: DownloadManager.ContentData, data: DownloadManager.ActionData[], ...args: Object[]): void;
    private getInterface(callback: (err: number, reqId?: number) => void): UBus.Interface;
    private setupDownload(): void;
    private setInterface4Test(iface: UBus.Interface): void;
    /**
     * <p>Enum for DownLoad Status.
     * Allow four types of mode such as InProgress, Complete, Cancelled and Interrupted.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Status: {
        [key: string]: number;
    };
    /**
     * <p>Enum for DownLoad Error list.</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Error: {
        [key: string]: number;
    };
    /**
     * <p>Enum for DownLoad variable list.</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly Variable: {
        [key: string]: string;
    };
    private static status2Error(status: number): number;
    private static parseBusError(err: Error): number;
    private static status2Str(status: number): string;
}
declare namespace DownloadManager {
    interface DownloadOptions {
        name?: string;
        fd?: number;
        method?: string;
        lowPriority?: boolean;
        referer?: string;
    }
    interface QueryData {
        reqId: number;
        url: string;
        name: string;
        path: string;
        status: number;
        receivedBytes: number;
        totalBytes: number;
        startTime: Date;
        endTime: Date;
        isPaused: boolean;
    }
    class ContentData {
        private title?: string;
        private text?: string;
        private icon?: string;
        private link?: PageLink;
    }
    interface ActionData {
        name?: string;
        icon?: string;
        link?: PageLink;
    }
    interface CallbackData {
        error?: number;
        fullName?: string;
        size?: number;
        status?: number;
        isPaused?: boolean;
        receivedBytes?: number;
        totalBytes?: number;
        bps?: number;
    }
}
export = DownloadManager;
