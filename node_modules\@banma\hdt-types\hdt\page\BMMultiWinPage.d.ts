import StackRouter = require("yunos/appmodel/StackRouter");
import CompositeView = require("yunos/ui/view/CompositeView");
import BaseMultiWinPage = require("../base/BaseMultiWinPage");
declare class BMMultiWinPage extends BaseMultiWinPage {
    readonly carMode: string;
    router: StackRouter;
    readonly modeConfig: import("../system/Types").IModeConfig;
    readonly rootContainer: CompositeView;
    private _rootContainer;
    private mRouter;
    onCreate(): void;
    onStart(): void;
    protected onStop(): void;
    protected isUsingFastRender(): boolean;
    protected usingCommonBackground(): boolean;
    protected useRouter(): boolean;
    protected usingPresenterFallback(): boolean;
    private initRootView;
    private _getAppMargin;
    private _getRootContainerSize;
}
export = BMMultiWinPage;
