/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";

const Consts = {
    SCENE_ID: "ff4725fd02023c652c781d771abf0f69",
    SUPPORT_VOICE_CMD: true,

    VIDEO_PAGE_URI: "page://video.alios.cn/video",
    VIDEO_PACKAGE_NAME: "video.alios.cn",
    XIAOYUN_PACKAGE_NAME: "xiaoyun",
    SETTING_PACKAGE_NAME: "systemsetting.ivi.com",

    TYPE_STRING: "string",
    TYPE_NUMBER: "number",

    KEY_MEDIA_NEXT: "MediaNext",
    KEY_MEDIA_PREVIOUS: "MediaPrevious",

    CODE_SUCCESS: 200, // 成功
    CODE_PARAMETER_INVALID: 400, // 参数无效
    CODE_PERMISSION_FAILED: 403, // 权限校验不通过
    CODE_SERVER_EXCEPTION: 500, // 系统异常
    CODE_SERVER_INAVAILABLE: 502, // 服务提供者不可用
    CODE_DATA_INVALID: 600, // 服务数据有问题：数据不完整或者异常

    EV_HIGHLIGHT_URL_CHANGED: "ev_highlight_url_changed",
    EV_STORAGE_STATE_CHANGED: "ev_storage_state_changed",
    EV_ITEM_LIST_CHANGED: "ev_item_list_changed",

    LOADING_MORE_TIMEOUT: 3000,
    UBUS_TIMEOUT: 10000,
    DEFAULT_SPEED_LIMIT: 300,
    DEFAULT_SPEED_DISCLAIMER: 120,
    DEFAULT_REQUEST_NUM: 30,
    DEFAULT_THRESHOLD_NUM: 10,

    MAX_VOLUME_NUM: 2,
    MAX_SEARCH_VIDEO_NUM: 3,
    MAX_SEARCH_HISTORY_NUM: 10,
    MAX_DISK_HIGHLIGHT_NUM: 5,
    MAX_DISK_ELAPSED_TIME_NUM: 500,
    MAX_DLNA_ELAPSED_TIME_NUM: 5,

    LAND_MAX_SEARCH_LOCAL_VIDEO_NUM: 4,
    LAND_NARROW_MAX_SEARCH_LOCAL_VIDEO_NUM: 2,
    LAND_LOAD_VIDEO_NUM_ONE_TIME: 3,

    RoutePath: {
        WELCOME: "welcome",
        ONLINE: "online",
        LOCAL: "local",
        PLAYER: "player",
        DLNA: "dlna",
        SEARCH: "search",
        SEARCH_MORE: "searchMore",
        BACK: "back"
    },

    FromType: {
        ONLINE: "online",
        LOCAL: "local",
        SEARCH_ONLINE: "search_online",
        SEARCH_LOCAL: "search_local",
        DLNA: "DLNA"
    },

    RequestType: {
        VIDEO_TODAY: 0,
        VIDEO_CATEGORY: 1,
        SEARCH: 2,
        CATEGORY: 3,
        CPINFO: 4,
        LIMIT: 5,
        CHECK: 6
    },

    FailReason: {
        NETWORK: "network",
        TRAFFIC: "traffic",
        SWITCH: "switch",
        SYSTEM: "system"
    },

    TrafficPromptType: {
        EXHAUSTION: 0,
        OPEN: 1
    },

    ControlType: {
        PREV: "prev",
        NEXT: "next",
        PAUSE: "pause",
        PLAY: "play",
        STOP: "stop"
    },

    // common
    WELCOME_DISCLAIMER_CONTENT: "welcome_disclaimer_content",
    ONLINE_USB_TITLE: "online_usb_title",
    SRC_USB: "src_usb",
    SRC_KSQR: "src_ksqr",
    SRC_NO_VIDEO: "src_no_video",
    SRC_NO_HISTORY: "src_no_history",
    PLAYER_TITLE: "player_title",
    PLAYER_BACK_MULTISTATE: "player_back_multistate",
    PLAYER_PLAY_MULTISTATE: "player_play_multistate",
    PLAYER_PAUSE_MULTISTATE: "player_pause_multistate",

    // land
    SRC_USB_SECOND: "src_usb_second",
    ICON_SRC_USB: "icon_src_usb",
    ICON_SRC_USB_SECOND: "icon_src_usb_second",
    SEARCH_HISTORY_ITEM: "search_history_item",
    SEARCH_HISTORY_ITEM_WARNING: "search_history_item_warning",
    SEARCH_MORE_MULTISTATE: "search_more_multistate",
    SEARCH_MORE_NARROW_MULTISTATE: "search_more_narrow_multistate"
};

export = Consts;
