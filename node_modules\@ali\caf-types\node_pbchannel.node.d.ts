import {NodeMediaMeta} from "node_multimedia.node";
export declare interface NodePlaybackChannelManager {
    new(): NodePlaybackChannelManager;
    dispatchPlaybackEvent(pbEvent: NodeMMParam, needWakeLock: boolean): void;
    getEnabledChannels(): string[];
    setListener(callback: (msg: string, param1: Object, param2: Object, obj: Object) => void): void;
}

export declare interface NodePlaybackChannel{
    new(tag: string, packageName: string): NodePlaybackChannel;
    getPlaybackRemoteId(): string;
    setUsages(flags: number): void;
    setEnabled(enable: boolean): void;
    isEnabled(): boolean;
    setPlaybackState(): void;
    setPlaybackState(nodeState: NodePlaybackState): void;
    setPlaylist(playlist: number): void;
    setPlaylistTitle(title: string): void;
    setMetadata(nodeMediaMeta: NodeMediaMeta): void;
    setListener(callback: (msg: number, param1: Object, param2: Object, obj: Object) => void): void;
    destroy(): void;
}

export declare interface NodePlaybackRemote {
    new(remoteId: string): NodePlaybackRemote;
    getMetadata(): NodeMediaMeta;
    getPlaybackState(): NodePlaybackState;
    getPlaylist(): number;
    getPlaylistTitle(): string;
    getPackageName(): string;
    getUsages(): number;
    getTag(): string;
    sendPlaybackEvent(nodeParam: NodeMMParam): void;
    play(): void;
    pause(): void;
    seekTo(pos: number): void;
    stop(): void;
    next(): void;
    previous(): void;
    fastForward(): void;
    rewind(): void;
    setListener(callback: (msg: number, param1: Object, param2: Object, obj: Object) => void): void;
    destroy(): void;
}

export declare interface NodeMMParam {
}

export declare interface NodePlaybackState{
    new(): NodePlaybackState;
    setState(state: number, pos: number, speed: number, updateTime?: number): void;
    setCapabilities(cap: number): void;
    setUpdateTime(time: number): void;
    setBufferPosition(pos: number): void;
    getPosition(): number;
    getBufferPosition(): number;
    getSpeed(): number;
    getState(): number;
    getCapabilities(): number;
    getUpdateTime(): number;
    dump(): void;
    writeToMsg(msg: Object): void;
    readFromMsg(msg: Object) :boolean;
    readFromMMParam(nodeParam: NodeMMParam): boolean;
    destroy(): void;
}

export const NodePlaybackChannelManager: NodePlaybackChannelManager;
export const NodePlaybackChannel: NodePlaybackChannel;
export const NodePlaybackState: NodePlaybackState;
