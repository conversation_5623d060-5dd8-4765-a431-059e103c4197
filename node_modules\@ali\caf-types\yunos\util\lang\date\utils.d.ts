export declare const zeroFill: (number: number, targetLength: number, forceSign?: boolean) => string;
export declare const isFunction: (input: Object) => boolean;
export declare const isNumber: (input: Object) => boolean;
export declare const hasOwnProp: (a: Object, b: Object) => boolean;
export declare const absCeil: (number: number) => number;
export declare const absFloor: (number: number) => number;
export declare const absRound: (number: number) => number;
export declare const toInt: (argumentForCoercion: number) => number;
