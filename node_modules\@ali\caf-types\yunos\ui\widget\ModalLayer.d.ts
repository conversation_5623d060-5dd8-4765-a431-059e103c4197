import CompositeView = require("../view/CompositeView");
import Window = require("../view/Window");
import View = require("../view/View");
import PropertyAnimation = require("../animation/PropertyAnimation");
import ClipboardMenu = require("./ClipboardMenu");
import ListView = require("../view/ListView");
import ScrollableView = require("../view/ScrollableView");
import TextAreaClass = require("../view/TextArea");
interface LocateRect {
    left: number;
    top: number;
    width: number;
    height: number;
}
/**
 * @private
 */
declare class ModalLayer extends CompositeView {
    private _layoutflagsChangeFunc;
    private _systemUIChangeFunc;
    private _forwardFocusdView: View;
    private _originHeight;
    private handleWheelFunc;
    private handleMouseFunc;
    private _layoutFlags;
    private _systemUiVisibility;
    private _rootView;
    private _visibleViewArray: View[];
    private _isShown;
    private _modalLayerType;
    private _softKeyboardMode;
    private _showBackgroundAnimation;
    private _marginVertical;
    private _windowHeight;
    private _windowWidth;
    private _windowLeft;
    private _windowTop;
    private _currentOrientation;
    private _parentWindow;
    private _locateType;
    private _locateRect;
    private _fullScreenPadding;
    private allowTouchEvents: boolean;
    private _windowOptions;
    private _clipboardMenu;
    private _screenHeight;
    private _touchModality;
    private _mouseModality;
    private _isActive;
    private _wakeupEnabled;
    private _quickWordsEnabled;
    private _isHideWithWindow;
    private _screenWidth;
    private _statusBarHeight;
    private _newConfig;
    private _keyboardModality;
    private _windowInsets;
    private _closeBackgroundAnimation;
    private _originTop;
    private _margins: number[];
    private _possibleRadius;
    private _keyboardAdjustSizeTimer;
    private _resizeHandle;
    private _voiceViewsMap;
    private _onPageStopFunc;
    /**
     * <p>Create a modal layer.</p>
     */
    public constructor(...args: Object[]);
    private wakeupEnabled: boolean;
    private quickWordsEnabled: boolean;
    private loseFocusInTouchMode(): void;
    private isCarSystemDialog(): boolean;
    private static isCarSystemMode(mode: number): boolean;
    private setWindowPosition(): void;
    private setLocateType(value: number): void;
    private setLocateRect(value: LocateRect): void;
    private setMargins(value: number[]): void;
    private setPossibleRadius(value: number): void;
    private getDefaultWindowSize(): void;
    private refreshRect(callback?: (rect: LocateRect) => void): void;
    private updateWindowSize(): void;
    private readonly statusBarHeight: number;
    private onSizeChange(reason: number, newWidth: number, newHeight: number, newX: number, newY: number, extendsData: number): void;
    private _adjustResizeForSoftKeyboard(reason: number, newWidth: number, newHeight: number): void;
    private _adjustPanForSoftKeyboard(reason: number, top: number): void;
    private readonly isShown: boolean;
    private renderNextFrame(): void;
    private onWindowActiveChange(isActive: boolean): void;
    private makeVoiceCommandUpdate(): void;
    private readonly showAnimation: PropertyAnimation;
    private readonly closeAnimation: PropertyAnimation;
    private makeVisibleViewArray(): void;
    private coverdCheck(coverdView: View, target: View): boolean;
    private coverd(target: View): boolean;
    private show(): void;
    private getWindowInsets(): Window.WindowInsets;
    private hide(): void;
    private hideWithPage(): void;
    private showWithPage(): void;
    private close(): void;
    private addDialog(): void;
    private removeDialog(): void;
    private addToast(): void;
    private removeToast(): void;
    private registerVoiceView(view: View): void;
    private unRegisterVoiceView(view: View): void;
    private forceRenderFrame(start: boolean): void;
    private softKeyboardMode: number;
    private readonly modalLayerType: number;
    /**
     * Dispatch a "close" event when the dialog is shown on the window.
     * @private
     */
    private readonly surfaceId: number;
    /**
     * check ModalLayer is actived.
     * @name yunos.ui.view.ModalLayer#active
     * @type {boolean}
     * @readonly
     * @private
     */
    private readonly active: boolean;
    private readonly isInteractiveFocus: boolean;
    private readonly currentOrientation: number;
    private readonly displayId: number;
    /**
     * Whether dialog will hide when its parent window hide.
     * @type {boolean}
     * @throws {TypeError} If this value is not a boolean.
     * @private
     */
    private isHideWithWindow: boolean;
    private hideWithWindow(): void;
    private addClipboard(clipboard: ClipboardMenu): void;
    private onKeyEvent(type: number, keycode: number, text: string, timestamp: number): void;
    private setHandledKey(key: Object, value: Object): void;
    private makeWindowDirty(immediately?: boolean): void;
    private getClipboardMenu(): ClipboardMenu;
    private clipboardShowing(): boolean;
    private onWindowResize(reason?: number, newWidth?: number, newHeight?: number): void;
    private getScreenPosition(view: View): {
        left: number;
        top: number;
    };
    private _getClipboardMenuClass(): typeof ClipboardMenu;
    private _getScrollableViewClass(): typeof ScrollableView;
    private _getListViewClass(): typeof ListView;
    private _getTextAreaClass(): typeof TextAreaClass;
    private activeSoftkeyboard(): void;
    private deactiveSoftkeyboard(): void;
    private isSoftKeyboardShown(): boolean;
    private configSoftkeyboard(inputHints: number, inputReturnKeyType: number): void;
    private showSoftkeyboard(): void;
    private hideSoftkeyboard(): void;
    private resetSoftkeyboard(): void;
    private computeWindowRect(): void;
    private computeWindowRectAuto(): void;
    private computeWindowRectMatchWindow(): void;
    private computeWindowRectSpecify(): void;
    private applyFullScreenRect(isFitPadding?: boolean): void;
    private fitWindowRectWithMargins(rect: LocateRect, margins: number[]): LocateRect;
    private updateWindowRect(rect: LocateRect): void;
    private updateWindowRadius(): void;
    private getWindowRect(): LocateRect;
    private static computeFullScreenPadding(value: number | number[]): number[];
    private static readonly Type: {
        [index: string]: number;
    };
    private static readonly LocateType: {
        Auto: int;
        MatchWindow: int;
        FullScreen: int;
        FullScreenFit: int;
        SpecifyInScreen: int;
    };
}
export = ModalLayer;
