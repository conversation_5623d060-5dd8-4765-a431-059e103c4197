/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets

import BaseAdapter = require("yunos/ui/adapter/BaseAdapter");
import View = require("yunos/ui/view/View");
import ToggleButtonBM = require("extend/hdt/control/ToggleButtonBM");
import {IVoiceEvent} from "../Types";
const Consts = require("../Consts");
const log = require("../utils/log");
const TAG = "CategoryAdapter";

interface ICategoryData {
    category: string;
    selected: boolean;
    index: number;
}

interface ICategoryView extends ToggleButtonBM {
    listener: (...args: Object[]) => void;
    data: ICategoryData;
}

interface IView {
    exclusiveTouch: boolean;
}

class CategoryAdapter extends BaseAdapter {
    private _selectListener: (itemView: View, position: number, point: object, voice: boolean) => void;

    constructor() {
        super();
        log.I(TAG, "CategoryAdapter");
    }

    createItem(position: number, convertView: ICategoryView) {
        if (!convertView) {
            convertView = <ICategoryView> (new ToggleButtonBM());
            (<IView> (<Object> convertView)).exclusiveTouch = true;
            if (Consts.SUPPORT_VOICE_CMD) {
                convertView.voiceEnabled = true;
                convertView.voiceSelectMode = View.VoiceSelectMode.Custom;
                const VoiceCommand = require("yunos/ui/voice/VoiceCommand");
                convertView.defaultVoiceCommand.recognitionQuality = VoiceCommand.RecognitionQuality.LOW;
                if (convertView.listener) {
                    convertView.off("voice", convertView.listener);
                    convertView.listener = null;
                }
                let listener = (e: IVoiceEvent) => {
                    log.I(TAG, "voice", position);
                    this._selectListener(convertView, position, null, true);
                    e.endLocalTask();
                };
                convertView.listener = listener;
                convertView.on("voice", listener);
            }

            convertView.on("touchend", () => {
                this._resetDataSelected();
                let itemIndex = convertView.data.index;
                log.I(TAG, "touchend", position, itemIndex);
                convertView.data.selected = true;
                if (this._selectListener) {
                    this._selectListener(convertView, itemIndex, null, false);
                }
            });
        }

        let categoryData = <ICategoryData> this.data[position];
        convertView.data = categoryData;
        convertView.data.index = position;
        if (categoryData) {
            convertView.text = categoryData.category ? categoryData.category : "";
            convertView.checked = categoryData.selected;
        }
        return convertView;
    }

    _resetDataSelected() {
        let len = this.data.length;
        for (let i = 0; i < len; i++) {
            (<ICategoryData> this.data[i]).selected = false;
        }
    }

    registerSelectListener(callback: (itemView: View, position: number, point: object, voice: boolean) => void) {
        this._selectListener = callback;
    }
}

export = CategoryAdapter;
