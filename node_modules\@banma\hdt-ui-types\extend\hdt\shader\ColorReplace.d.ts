import Shader = require("yunos/graphics/shader/Shader");
declare class ColorReplace extends Shader {
    private _newColor;
    private _originColor;
    private _range;
    private _isHorizontal;
    constructor(originColor: string, newColor: string);
    protected getFragmentShader(): string;
    newColor: string;
    originColor: string;
    range: number;
    isHorizontal: number;
}
export = ColorReplace;
