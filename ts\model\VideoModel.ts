/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";
// jscs:disable disallowQuotedKeysInObjects

import Model = require("yunos/appmodel/Model");
import ConfigStore = require("yunos/content/ConfigStore");
import Consts = require("../Consts");
import log = require("../utils/log");
import Utils = require("../utils/Utils");
import fs = require("fs");

const USER_AGREEMENT_PATH = "/private/video/userAgreement";
const POPUP_TRAFFIC_OPEN_KEY = "popup_traffic_open";
const POPUP_TRAFFIC_EXHAUSTION_KEY = "popup_traffic_exhaustion";
const USB_TITLE_CACHE_KEY = "usb_title";
const PAGE_INFO_KEY = "page_info";
const PAGE_REFRESH_DATE_KEY = "page_refresh_date";
const HIGHLIGHT_SUFFIX = "_hl";
const MOCK_DATA_FLAG_KEY = "mock_data_flag";
const DLNA_PLAY_FLAG_KEY = "dlna_play_flag";
const VOLUME_LIST = "volume_list";
const ACCOUNT_INFO_KEY = "account_info_key";
const TAG = "VideoModel";

interface IAccountInfo {
    accountName: string;
    token: string;
}

class VideoModel extends Model {
    private static _instance: VideoModel;
    private _configStore: ConfigStore;

    constructor() {
        super();
        this._configStore = ConfigStore.getInstance("VIDEO_CONFIGSTORE");
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new VideoModel();
        }
        return this._instance;
    }

    /**
     * 获取账号信息
     */
    getAccountInfo() {
        if (!this._configStore) {
            log.W(TAG, "getAccountInfo, configStore is null");
            return false;
        }

        let accountInfo: IAccountInfo;
        try {
            let rawData: string = <string> this._configStore.get(ACCOUNT_INFO_KEY, null);
            if (rawData) {
                accountInfo = JSON.parse(rawData);
            }
            log.D(TAG, "getAccountInfo,", accountInfo);
        } catch (e) {
            log.E(TAG, "getAccountInfo parse failed! e:", e);
        }
        return accountInfo;
    }

    /**
     * 保存账号信息
     */
    saveAccountInfo(accountName: string, token: string) {
        if (!this._configStore) {
            log.W(TAG, "saveAccountInfo, configStore is null");
            return;
        }

        let accountInfo = {
            accountName: accountName,
            token: token
        };
        let jsonData = JSON.stringify(accountInfo);
        this._configStore.put(ACCOUNT_INFO_KEY, jsonData);
        this._configStore.apply((err) => {
            if (err) {
                log.D(TAG, "saveAccountInfo failed");
            } else {
                log.D(TAG, "saveAccountInfo success");
            }
        });
    }

    /**
     * 获取模拟数据的标记位
     */
    getMockDataFlag() {
        if (!this._configStore) {
            log.W(TAG, "getMockDataFlag, configStore is null.");
            return false;
        }
        let isMock = this._configStore.get(MOCK_DATA_FLAG_KEY, false);
        log.I(TAG, "getMockDataFlag", isMock);
        return isMock;
    }

    /**
     * 保存模拟数据的标记位
     */
    saveMockDataFlag(isMock: boolean) {
        if (!this._configStore) {
            log.W(TAG, "saveMockDataFlag, configStore is null.");
            return;
        }
        this._configStore.put(MOCK_DATA_FLAG_KEY, isMock);
        log.I(TAG, "saveMockDataFlag", isMock);
        this._configStore.apply((err) => {
            if (err) {
                log.I(TAG, "saveMockDataFlag failed");
            } else {
                log.I(TAG, "saveMockDataFlag success");
            }
        });
    }

    /**
     * 检查是否跳过欢迎页
     */
    isSkipWelcomePage() {
        let userAgreement = fs.existsSync(USER_AGREEMENT_PATH);
        log.I(TAG, "isSkipWelcomePage", userAgreement);
        return userAgreement;
    }

    /**
     * 保存是否跳过欢迎页
     */
    saveSkipWelcomePage() {
        log.I(TAG, "saveSkipWelcomePage");
        fs.writeFile(USER_AGREEMENT_PATH, "", (err) => {
            log.I(TAG, "saveSkipWelcomePage", err);
        });
    }

    /**
     * 获取高亮视频的URL
     */
    loadHighlightUrl(from: string, path: string) {
        if (!this._configStore) {
            log.W(TAG, "loadHighlightUrl, configStore is null.");
            return;
        }

        log.I(TAG, "loadHighlightUrl", from, path);
        if (from === Consts.FromType.ONLINE || from === Consts.FromType.SEARCH_LOCAL) {
            return this._configStore.get(from + HIGHLIGHT_SUFFIX, "");
        } else if (from === Consts.FromType.LOCAL) {
            let localHighlight = <string[]> this._configStore.get(from + HIGHLIGHT_SUFFIX, []);
            let hlIndex = localHighlight.findIndex((hlUrl) => {
                return hlUrl.indexOf(path) >= 0;
            });
            if (hlIndex !== -1) {
                return localHighlight[hlIndex];
            }
        }
        return;
    }

    /**
     * 保存高亮视频的URL，在线视频直接存储，U盘视频以数组存储
     */
    saveHighlightUrl(from: string, path: string, url: string, index: number) {
        if (!this._configStore) {
            log.W(TAG, "saveHighlightUrl, configStore is null.");
            return;
        }
        if (!url) {
            log.W(TAG, "saveHighlightUrl, url is null.");
            return;
        }

        log.I(TAG, "saveHighlightUrl", from, path, url, index);
        if (from === Consts.FromType.ONLINE || from === Consts.FromType.SEARCH_LOCAL) {
            this._configStore.put(from + HIGHLIGHT_SUFFIX, url);
            this._configStore.apply((err) => {
                log.D(TAG, "saveHighlightUrl err", err);
            });
        } else if (from === Consts.FromType.LOCAL) {
            let localHighlight = <string[]> this._configStore.get(from + HIGHLIGHT_SUFFIX, []);
            let hlIndex = localHighlight.findIndex((hlUrl) => {
                return hlUrl.indexOf(path) >= 0;
            });
            if (hlIndex !== -1) {
                localHighlight.splice(hlIndex, 1);
            }
            localHighlight.unshift(url);
            if (localHighlight.length > Consts.MAX_DISK_HIGHLIGHT_NUM) {
                localHighlight = localHighlight.slice(0, Consts.MAX_DISK_HIGHLIGHT_NUM);
            }
            this._configStore.put(from + HIGHLIGHT_SUFFIX, localHighlight);
            this._configStore.apply((err) => {
                log.D(TAG, "saveHighlightUrl err", err);
            });
        }
        this.emit(Consts.EV_HIGHLIGHT_URL_CHANGED, path, url, index);
    }

    /**
     * 移除高亮视频的URL
     */
    removeHighlightUrl(from: string, path: string) {
        if (!this._configStore) {
            log.W(TAG, "removeHighlightUrl, configStore is null.");
            return;
        }

        log.I(TAG, "removeHighlightUrl", from, path);
        if (from === Consts.FromType.ONLINE || from === Consts.FromType.SEARCH_LOCAL) {
            this._configStore.remove(from + HIGHLIGHT_SUFFIX);
        } else if (from === Consts.FromType.LOCAL) {
            let localHighlight = <string[]> this._configStore.get(from + HIGHLIGHT_SUFFIX, []);
            let hlIndex = localHighlight.findIndex((hlUrl) => {
                return hlUrl.indexOf(path) >= 0;
            });
            if (hlIndex !== -1) {
                localHighlight.splice(hlIndex, 1);
            }
            this._configStore.put(from + HIGHLIGHT_SUFFIX, localHighlight);
            this._configStore.apply((err) => {
                log.D(TAG, "removeHighlightUrl err", err);
            });
        }
    }

    /**
     * 保存volume的状态信息
     */
    saveVolumeState(path: string, time: number) {
        if (!this._configStore) {
            log.W(TAG, "saveVolumeState, configStore is null.");
            return;
        }

        log.I(TAG, "saveVolumeState", path, time);
        let found = false;
        let array = <Object[]> this._configStore.get(VOLUME_LIST, []);
        for (let i = 0; i < array.length; i++) {
            let item = <{path: string, time: number}> array[i];
            if (item && item.path === path) {
                found = true;
                item.time = time;
                break;
            }
        }

        log.I(TAG, "saveVolumeState, found:", found);
        if (!found) {
            let temp = {
                path: path,
                time: time
            }
            array.push(temp);
        }

        log.I(TAG, `saveVolumeState ${array}`);
        this._configStore.put(VOLUME_LIST, array);
        this._configStore.apply((err) => {
            if (err) {
                log.E(TAG, `saveVolumeState ${path} failed`);
            } else {
                log.I(TAG, `saveVolumeState ${path} success`);
            }
        });
    }

    /**
     * 移除volume的状态信息
     */
    removeVolumeState(path: string) {
        if (!this._configStore) {
            log.W(TAG, "removeVolumeState, configStore is null.");
            return;
        }

        log.I(TAG, "removeVolumeState", path);
        let found = false;
        let array = <Object[]> this._configStore.get(VOLUME_LIST, []);
        if (array.length <= 2) {
            return;
        }
        for (let i = 0; i < array.length; i++) {
            let item = <{path: string, time: number}> array[i];
            if (item && item.path === path) {
                array.splice(i, 1);
                this.removeHighlightUrl(Consts.FromType.LOCAL, path);
                this.clearElapsedTime(Consts.FromType.LOCAL, path);
                found = true;
                break;
            }
        }

        log.I(TAG, "removeVolumeState", found);
        if (!found) {
            return;
        }

        log.I(TAG, `removeVolumeState ${array}`);
        this._configStore.put(VOLUME_LIST, array);
        this._configStore.apply((err) => {
            if (err) {
                log.E(TAG, `removeVolumeState ${path} failed`);
            } else {
                log.I(TAG, `removeVolumeState ${path} success`);
            }
        });
    }

    /**
     * 获取视频的播放时间信息
     */
    loadElapsedTime(fromType: string, url: Object): Object {
        if (!this._configStore) {
            log.W(TAG, "loadElapsedTime, configStore is null.");
            return [];
        }
        log.I(TAG, "loadElapsedTime", fromType, url);
        let array = <Object[]> this._configStore.get(fromType, []);
        for (let i = 0; i < array.length; i++) {
            let item = <{ url: Object, time: string }>array[i];
            if (item) {
                log.D(TAG, "loadElapsedTime", item.url);
                if (item.url === url) {
                    return parseInt(item.time);
                }
            }
        }
        return 0;
    }

    /**
     * 保存视频的播放时间信息
     */
    saveElapsedTime(fromType: string, url: Object, time: number) {
        log.I(TAG, "saveElapsedTime", fromType, url, time);
        if (!this._configStore) {
            log.W(TAG, "saveElapsedTime, configStore is null.");
            return;
        }
        let array = <Object[]> this._configStore.get(fromType, []);

        for (let i = 0; i < array.length; i++) {
            let item = <{ url: Object }>array[i];
            if (item && item.url === url) {
                array.splice(i, 1);
                break;
            }
        }
        if (time && time > 0) {
            // array.unshift(url + "," + time);
            let tempdata = {
                "url": url,
                "time": time
            };
            array.push(tempdata);
        }

        log.I(TAG, "saveElapsedTime", array.length);
        let maxNum = fromType === Consts.FromType.DLNA ? Consts.MAX_DLNA_ELAPSED_TIME_NUM : Consts.MAX_DISK_ELAPSED_TIME_NUM;
        if (array.length > maxNum) {
            array.splice(0, 1);
        }

        this._configStore.put(fromType, array);
        this._configStore.apply((err) => {
            if (err) {
                log.E(TAG, `saveElapsedTime ${url} added failed`);
            } else {
                log.I(TAG, `saveElapsedTime ${url} added success`);
            }
        });
    }

    /**
     * 清除视频的播放时间信息
     */
    clearElapsedTime(fromType: string, path: string) {
        if (!this._configStore) {
            log.W(TAG, "clearElapsedTime, configStore is null.");
            return;
        }

        log.I(TAG, "clearElapsedTime", fromType, path);
        let found = false;
        let array = <Object[]> this._configStore.get(fromType, []);
        // 一个U盘可能会记录多个视频播放时间，需要从后往前循环
        for (let i = array.length - 1; i >= 0; i--) {
            let item = <{url: string, time: number}> array[i];
            if (item && item.url.startsWith(path)) {
                array.splice(i, 1);
                found = true;
            }
        }

        log.I(TAG, "clearElapsedTime", found);
        if (!found) {
            return;
        }

        this._configStore.put(path, array);
        this._configStore.apply((err) => {
            if (err) {
                log.E(TAG, `clearElapsedTime failed`);
            } else {
                log.I(TAG, `clearElapsedTime success`);
            }
        });
    }

    /**
     * 获取是否流量提醒
     */
    isTrafficPrompt(type: number) {
        log.I(TAG, "isTrafficPrompt", type);
        if (!this._configStore) {
            log.W(TAG, "isTrafficPrompt, configStore is null.");
            return [];
        }
        let key = type === Consts.TrafficPromptType.OPEN ? POPUP_TRAFFIC_OPEN_KEY
            : POPUP_TRAFFIC_EXHAUSTION_KEY;
        let prompt = this._configStore.get(key, false);
        log.I(TAG, "isTrafficPrompt", prompt);
        return prompt;
    }

    /**
     * 保存是否流量提醒
     */
    saveTrafficPrompt(type: number, isPrompt: Object) {
        log.I(TAG, "saveTrafficPrompt", type, isPrompt);
        if (!this._configStore) {
            log.W(TAG, "saveTrafficPrompt, configStore is null.");
            return;
        }
        let key = type === Consts.TrafficPromptType.OPEN ? POPUP_TRAFFIC_OPEN_KEY
            : POPUP_TRAFFIC_EXHAUSTION_KEY;
        this._configStore.put(key, isPrompt);
        this._configStore.apply((err) => {
            if (err) {
                log.I(TAG, "saveTrafficPrompt failed");
            } else {
                log.I(TAG, "saveTrafficPrompt success");
            }
        });
    }

    /**
     * 获取USB的title
     */
    getUSBTitle(usbtitle: Object) {
        let result;
        let cacheUsbTitle = this.getCacheUsbTitle();
        if (cacheUsbTitle && cacheUsbTitle !== null) {
            if (cacheUsbTitle.enTitle === usbtitle) {
                result = cacheUsbTitle.deTitle;
            } else {
                result = Utils.decodeUdiskLabel(<Object[]>usbtitle);
                let info = {
                    enTitle: usbtitle,
                    deTitle: result
                };
                this.setCacheUsbTitle(info);
            }
        } else {
            result = Utils.decodeUdiskLabel(<Object[]>usbtitle);
            let info = {
                enTitle: usbtitle,
                deTitle: result
            };
            this.setCacheUsbTitle(info);
        }
        log.I(TAG, "getUSBTitle: ", result);
        return result;
    }

    setCacheUsbTitle(info: Object) {
        if (!info) {
            log.I(TAG, "setCacheUsbTitle negative");
            return false;
        }
        let jsonData = JSON.stringify(info);
        this._configStore.put(USB_TITLE_CACHE_KEY, jsonData);
        this._configStore.apply((err) => {
            if (err) {
                log.D(TAG, "setCacheUsbTitle failed");
            } else {
                log.D(TAG, "setCacheUsbTitle success");
            }
        });
        return true;
    }

    getCacheUsbTitle() {
        let rawData: string;
        if (this._configStore) {
            rawData = <string> this._configStore.get(USB_TITLE_CACHE_KEY, null);
        }
        let usbTitle;
        try {
            if (rawData && rawData !== null) {
                usbTitle = JSON.parse(rawData);
            }
        } catch (e) {
            log.E(TAG, "getCacheUsbTitle parse failed! e:", e);
        }
        return usbTitle;
    }

    /**
     * 获取页面信息
     */
    getPageInfo() {
        let pageInfo = <Object> {
            pageNo: 1,
            videoIndex: 0,
            categoryIndex: 0,
            categoryName: ""
        };

        if (!this._configStore) {
            log.W(TAG, "getPageInfo, configStore is null");
            return pageInfo;
        }

        let rawData = <string> this._configStore.get(PAGE_INFO_KEY, "");
        try {
            log.I(TAG, "getPageInfo", rawData);
            if (rawData) {
                pageInfo = JSON.parse(rawData);
            }
        } catch (e) {
            log.E(TAG, "getPageInfo parse failed!", e);
        }

        return pageInfo;
    }

    /**
     * 保存页面信息
     */
    savePageInfo(pageNo: number, videoIndex: number, categoryIndex: number, categoryName: string) {
        if (pageNo < 1) {
            log.W(TAG, "savePageInfo, pageNo invalid");
            return;
        }

        if (!this._configStore) {
            log.W(TAG, "savePageInfo, configStore is null");
            return;
        }

        let info = {
            pageNo: pageNo,
            videoIndex: videoIndex,
            categoryIndex: categoryIndex,
            categoryName: categoryName
        };
        let jsonData = JSON.stringify(info);
        this._configStore.put(PAGE_INFO_KEY, jsonData);
        this._configStore.apply((err) => {
            if (err) {
                log.I(TAG, "savePageInfo failed");
            } else {
                log.I(TAG, "savePageInfo success");
            }
        });
    }

    /**
     * 获取页面刷新的日期
     */
    getPageRefreshDate() {
        if (!this._configStore) {
            log.W(TAG, "getPageRefreshDate, configStore is null");
            return "";
        }

        let dateString = this._configStore.get(PAGE_REFRESH_DATE_KEY, "");
        log.I(TAG, "getPageRefreshDate", dateString);
        return dateString;
    }

    /**
     * 保存页面刷新的日期
     */
    savePageRefreshDate(dateString: string) {
        if (!this._configStore) {
            log.W(TAG, "savePageRefreshDate, configStore is null");
            return;
        }

        log.I(TAG, "savePageRefreshDate", dateString);
        this._configStore.put(PAGE_REFRESH_DATE_KEY, dateString);
        this._configStore.apply((err) => {
            if (err) {
                log.I(TAG, "savePageRefreshDate failed");
            } else {
                log.I(TAG, "savePageRefreshDate success");
            }
        });
    }

    /**
     * 获取投屏的标记位
     */
    getDlnaPlayFlag() {
        if (!this._configStore) {
            log.W(TAG, "getDlnaPlayFlag, configStore is null.");
            return false;
        }
        let flag = this._configStore.get(DLNA_PLAY_FLAG_KEY, false);
        log.I(TAG, "getDlnaPlayFlag", flag);
        return flag;
    }

    /**
     * 保存投屏的标记位
     */
    saveDlnaPlayFlag(flag: boolean) {
        if (!this._configStore) {
            log.W(TAG, "saveDlnaPlayFlag, configStore is null.");
            return;
        }
        this._configStore.put(DLNA_PLAY_FLAG_KEY, flag);
        log.I(TAG, "saveDlnaPlayFlag", flag);
        this._configStore.apply((err) => {
            if (err) {
                log.I(TAG, "saveDlnaPlayFlag failed");
            } else {
                log.I(TAG, "saveDlnaPlayFlag success");
            }
        });
    }
}

export = VideoModel;
