import YObject = require("yunos/core/YObject");
/**
 * <p>SQLQueryBuilder class, which provides APIs to generate SQL command.</p>
 *
 * @example
 * example of use SQLQueryBuilder:
 *
 * let SQLBuilder = require("yunos/database/sqlite/SQLQueryBuilder");
 * let sql = SQLBuilder.buildSQLWithArgs("table1", ["name", "number"],
 *     "name like ?", "name", true, "0, 10", "name", "SUM(score)<100");
 *
 * @extends yunos.core.YObject
 * @memberof yunos.database.sqlite
 * @public
 * @since 2
 */
declare class SQLQueryBuilder extends YObject {
    /**
     * Create a SQLQueryBuilder instance.
     * @public
     * @since 2
     */
    public constructor();
    /**
      * <p>Build SQL command for query with specified arguments.</p>
      *
      * @example
      *
      * let SQLBuilder = require("yunos/database/sqlite/SQLQueryBuilder");
      * let sql = SQLBuilder.buildSQLWithArgs("table1", ["name", "number"],
      *     "name like ?", "name", true, "0, 10", "name", "SUM(score)<100");
      *
      * @param {string} table - the name of the table for query
      * @param {string[]} projection - A string list which indicates the columns
      * that will be included in the query results
      * @param {string} where - The string that indicates the query conditions
      * @param {string} sortOrder - The string that indicates how to order the query
      * results
      * @param {boolean} distinct - The flag which indicates whether the SQL DISTINCT
      * keyword is used in the SQL command
      * @param {string} limit - The value of the SQL LIMIT keyword
      * @param {string} groupBy - The value of the SQL GROUP BY keyword
      * @param {string} having - The value of the SQL HAVING keyword
      * @return {string} the SQL command in string
      * @public
      * @since 2
      */
    public static buildSQLWithArgs(table: string, projection: string[], where: string, sortOrder: string, distinct: boolean, limit: string, groupBy: string, having: string): string;
}
export = SQLQueryBuilder;
