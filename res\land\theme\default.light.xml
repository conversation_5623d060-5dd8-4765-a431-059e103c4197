<theme name="default" extend="hdt_light">
    <style name="Slider">
        <item name="height">{config.CONTROL_SLIDER_HEIGHT}</item>
        <item name="default_handle_view_offset">{config.CONTROL_SLIDER_HANDLE_VIEW_OFFSET}</item>
        <item name="handle_width">{config.CONTROL_SLIDER_HANDLE_WIDTH}</item>
        <item name="handle_height">{config.CONTROL_SLIDER_HANDLE_HEIGHT}</item>
        <item name="handle_scale">{config.CONTROL_SLIDER_HANDLE_SCALE}</item>
        <item name="line_weight">{config.CONTROL_SLIDER_LINE_WEIGHT}</item>
        <item name="line_border_radius">{config.CONTROL_SLIDER_LINE_BORDER_RADIUS}</item>
        <item name="line_color">{color.CONTROL_SLIDER_BG_COLOR}</item>
        <item name="line_disabled_color">{color.CONTROL_SLIDER_BG_COLOR_DISABLE}</item>
        <item name="line_progress_color">{color.CONTROL_SLIDER_PROGRESS_COLOR}</item>
        <item name="line_progress_disabled_color">{color.CONTROL_SLIDER_PROGRESS_COLOR_DISABLE}</item>
        <item name="normal_handler">{img(images/slider/ic_slider_handler_normal.png)}</item>
        <item name="disabled_handler">{img(images/slider/ic_slider_handler_disable.png)}</item>
        <item name="pressed_handler">{img(images/slider/ic_slider_handler_pressed.png)}</item>
    </style>

    <style name="Tip">
        <item name="background_color">{img(images/slider/bg_slider_tip.png)}</item>
        <item name="padding_bottom">{config.PLAYER_SLIDER_TIP_PADDING_BOTTOM}</item>
        <item name="font_size">{sdp(20)}</item>
    </style>

    <include file="./welcome.light.xml"/>
    <include file="./online.light.xml"/>
    <include file="./local.light.xml"/>
    <include file="./search.light.xml"/>
    <include file="./player.light.xml"/>
    <include file="./dlna.light.xml"/>
    <include file="./empty.light.xml"/>
    <include file="./error.light.xml"/>
</theme>
