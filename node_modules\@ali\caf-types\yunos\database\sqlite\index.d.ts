import SQLiteDatabase = require("yunos/database/sqlite/SQLiteDatabase");
import SQLiteStatement = require("yunos/database/sqlite/SQLiteStatement");
import SQLiteOpenHelper = require("yunos/database/sqlite/SQLiteOpenHelper");
import DataError = require("yunos/database/sqlite/DataError");
import DataColumn = require("yunos/database/sqlite/DataColumn");
import ForeignKey = require("yunos/database/sqlite/ForeignKey");
import SQLiteTransaction = require("yunos/database/sqlite/SQLiteTransaction");
import SQLQueryBuilder = require("yunos/database/sqlite/SQLQueryBuilder");
declare const _default: {
    readonly SQLiteDatabase: typeof SQLiteDatabase;
    readonly SQLiteStatement: typeof SQLiteStatement;
    readonly SQLiteOpenHelper: typeof SQLiteOpenHelper;
    readonly DataError: typeof DataError;
    readonly DataColumn: typeof DataColumn;
    readonly ForeignKey: typeof ForeignKey;
    readonly SQLiteTransaction: typeof SQLiteTransaction;
    readonly SQLQueryBuilder: typeof SQLQueryBuilder;
};
export = _default;
