declare namespace LightService {
    class LightServiceManager {
        getInstance:() => LightServiceManager;
        collect: (data: Object|Array<Object>, callback: (err: number) => void) => void;
        uncollect: (data: string|Array<string>, callback: (err: number) => void) => void;
        isCollected: (data: string|Array<string>, callback: (err: number, result: string) => void,
            timeout: number) => boolean;
    }
}
export = LightService;
