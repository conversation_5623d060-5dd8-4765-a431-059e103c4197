/**
 * Matrix class for internal use, maybe replaced in future
 * @private
 * @since 4
 *
 * 3 x 3 Matrix helper class
 */
export declare class Matrix3 {
    public elements: Array<number>;
    public constructor();
    private resetIdentity(): this;
    private set(n11: number, n12: number, n13: number, n21: number, n22: number, n23: number, n31: number, n32: number, n33: number): Matrix3;
    public multiply(b: Matrix3): Matrix3;
    public premultiply(b: Matrix3): Matrix3;
    public translate(x: number, y: number): Matrix3;
    private scale(x: number, y: number): Matrix3;
    private rotate(angle: number): Matrix3;
    private fromArray(array: Array<number>): Matrix3;
    private static Multiply(a: Matrix3, b: Matrix3): void;
}
