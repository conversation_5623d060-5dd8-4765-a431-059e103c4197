import ClipboardContent = require("core/clipboard/clipboard_content");
import ClipItem = require("yunos/content/ClipItem");
import YObject = require("yunos/core/YObject");
/**
 * <p>Use this class to describe the clipped content of clipboard.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.content
 * @public
 * @since 1
 * @hiddenOnPlatform auto
 */
declare class ClipContent extends YObject {
    private static readonly CONTENT_TYPE_TEXT: number;
    private static readonly CONTENT_TYPE_HTML: number;
    private static readonly CONTENT_TYPE_URI: number;
    private static readonly CONTENT_TYPE_DATA: number;
    private mItems;
    /**
     * @public
     * @since 1
     */
    public constructor();
    /**
     * <p>Create a clipped content with a plain text.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * let clipboardManager = ClipboardManager.getInstance();
     * let contentText = ClipContent.newText("test text");
     * clipboardManager.setContent(contentText, function(r, err) {
     *     console.log(r + ":" + err);
     * });
     * @param {string} text - a plain text.
     * @return {Object} content instance of ClipContent.
     * @public
     * @since 1
     */
    public static newText(text: string): ClipContent;
    /**
     * <p>Create a clipped content with a html text.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * const TestHtml = "<html><head>head</head><body><div>body</div></body></html>";
     * let clipboardManager = ClipboardManager.getInstance();
     * let contentHtml = ClipContent.newHtmlText(TestHtml);
     * clipboardManager.setContent(contentHtml, function(r, err) {
     *     console.log(r + ":" + err);
     * });
     * @param {string} htmlText - a html text.
     * @return {Object} content instance of ClipContent.
     * @public
     * @since 1
     */
    public static newHtmlText(htmlText: string): ClipContent;
    /**
     * <p>Create a clipped content with a uri text.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * const TestUri = "https://www.yunos.com";
     * let clipboardManager = ClipboardManager.getInstance();
     * let contentUri = ClipContent.newUri(TestUri);
     * clipboardManager.setContent(contentUri, function(r, err) {
     *     console.log(r + ":" + err);
     * });
     * @param {string} uri - a uri text.
     * @return {Object} content instance of ClipContent.
     * @public
     * @since 1
     */
    public static newUri(uri: string): ClipContent;
    /**
     * <p>Create a clipped content with a raw data element.
     * the data size max limited is 4M bytes.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * let buf = new Buffer(1024 * 1024);
     * for (let i = 0; i < 1024 * 1024; ++i) {
     *     buf[i] = 0x55;
     * }
     * let data = {mimeType: "image/png", size: 1024 * 1024, buffer: buf};
     * let contentData = ClipContent.newData(data);
     * clipboardManager.setContent(contentData, function(r, err) {
     *     console.log(r + ":" + err);
     * });
     * @param {Object} data - the raw data object, composed of:
     *                 mimeType {String}, size {Number}, buffer {Object(Node Buffer)}
     * @return {Object} content instance of ClipContent.
     * @public
     * @since 1
     */
    public static newData(data: Object): ClipContent;
    /**
     * <p>Get item count of clipped content.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasText()) {
     *                 console.log(item.getText());
     *             }
     *         }
     *     }
     * });
     * @return {number} the item count of Clipped content.
     * @public
     * @since 1
     */
    public getItemCount(): number;
    /**
     * <p>Get item at index of clipped content.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * const ClipContent = require("yunos/content/ClipContent");
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasText()) {
     *                 console.log(item.getText());
     *             }
     *         }
     *     }
     * });
     * @param {number} index - the item index.
     * @return {Object} object instance of ClipItem.
     * @public
     * @since 1
     */
    public getItemAt(index: number): ClipItem;
    /**
     * <p>Add item to clipboard content.</p>
     * <p>If the item contained raw data element, other items would be discarded,
     * and the data size max limited is 4M bytes.</p>
     * @example
     * const ClipContent = require("yunos/content/ClipContent");
     * let content = new ClipContent();
     * let item = new ClipItem(ClipContent.CONTENT_TYPE_TEXT, "test text");
     * content.addItem(item);
     * @param {Object} item - the item want to add.
     * @public
     * @since 1
     */
    public addItem(item: ClipItem): void;
    private readFromNative(content: ClipboardContent): void;
}
export = ClipContent;
