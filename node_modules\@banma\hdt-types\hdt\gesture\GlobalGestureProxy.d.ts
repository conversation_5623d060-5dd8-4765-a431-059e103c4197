import UBus = require("ubus");
declare class GlobalGestureProxy {
    private _ubus;
    private _iface;
    private _ruleObj;
    private static _proxy;
    static readonly _SignalName: string;
    static getInstance(): GlobalGestureProxy;
    static readonly GestureType: {
        DOUBLE_FINGERS: int;
        MULTI_FINGERS: int;
        FIVE_FINGERS: int;
    };
    static readonly GestureScrollStatus: {
        START: int;
        MOVE: int;
        END: int;
    };
    static readonly GestureSwipeDirection: {
        NONE: int;
        FROM_TOP: int;
        FROM_RIGHT: int;
        FROM_BOTTOM: int;
        FROM_LEFT: int;
        ZOOM_IN: int;
        ZOOM_OUT: int;
        TO_LEFT: int;
        TO_RIGHT: int;
    };
    static readonly GestureDisplayId: {
        A1: int;
        A2: int;
        B: int;
        C: int;
    };
    constructor();
    onSignal(sigName: string, listener: (err: Error, msg: UBus.Message) => void): Object;
    removeOnSignal(): void;
}
export = GlobalGestureProxy;
