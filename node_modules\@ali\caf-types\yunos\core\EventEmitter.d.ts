import YObject = require("../core/YObject");
/**
 * <p>With EventEmitter class you can implement an event base class,
 * you can use on and emit method like require("events").</p>
 * @example
 * class NotificationCenter extends EventEmitter {}
 * let notify = new NotificationCenter();
 * notify.on("foo", function() {
 *     console.log("bar");
 * })
 * notify.emit("foo");
 * @extends yunos.core.YObject
 * @memberof yunos.core
 * @public
 * @since 1
 */
declare class EventEmitter extends YObject {
    private __events;
    /**
     * Create EventEmitter.
     * @public
     * @since 1
     */
    public constructor();
    /**
     * Destroy the EventEmitter object.
     * @override
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * <p>Adds the listener function to the end of the listeners array for the event named eventName.&nbsp;
     * No checks are made to see if the listener has already been added.&nbsp;
     * Multiple calls passing the same combination of eventName and listener will result in the listener being added,&nbsp;
     * and called, multiple times.</p>
     * @param {string} eventName - The name of the event.
     * @param {Function} listener - The callback function.
     * @public
     * @since 1
     */
    public on(eventName: string, listener: (...args: Object[]) => void): this;
    /**
     * <p>Alias for {@link yunos.core.EventEmitter#on}.</p>
     * @public
     * @since 1
     */
    public addListener(eventName: string, listener: (...args: Object[]) => void): this;
    /**
     * <p>Alias for {@link yunos.core.EventEmitter#on}.</p>
     * @public
     * @since 1
     */
    public addEventListener(eventName: string, listener: (...args: Object[]) => void): this;
    /**
     * <p>Removes the specified listener from the listener array for the event named eventName.</p>
     * @param {string} eventName - The name of the event.
     * @param {Function} listener - The callback function.
     * @public
     * @since 2
     */
    public off(eventName: string, listener: (...args: Object[]) => void): this;
    /**
     * <p>Alias for {@link yunos.core.EventEmitter#off}.</p>
     * @public
     * @since 2
     */
    public removeListener(eventName: string, listener: (...args: Object[]) => void): this;
    /**
     * <p>Alias for {@link yunos.core.EventEmitter#off}.</p>
     * @public
     * @since 1
     */
    public removeEventListener(eventName: string, listener: (...args: Object[]) => void): this;
    /**
     * <p>Removes all listeners, or those of the specified eventName.</p>
     * @param {string} [eventName] - The name of the event.
     * @public
     * @since 1
     */
    public removeAllListeners(eventName?: string): this;
    /**
     * <p>Alias for {@link yunos.core.EventEmitter#removeAllListeners}.</p>
     * @public
     * @since 1
     */
    public removeAllEventListeners(eventName?: string): this;
    /**
     * <p>Synchronously calls each of the listeners registered for the event named eventName,&nbsp;
     * in the order they were registered, passing the supplied arguments to each.</p>
     * @function yunos.core.EventEmitter#emit
     * @param {string} eventName - The name of the event.
     * @param {...*} args - Event params.
     * @return {boolean} Returns true if the event had listeners, false otherwise.
     * @public
     * @since 1
     */
    public emit(eventName: string, ...args: Object[]): boolean;
    /**
     * <p>Alias for {@link yunos.core.EventEmitter#emit}.</p>
     * @public
     * @since 2
     */
    public dispatchEvent(eventName: string, ...args: Object[]): boolean;
    /**
     * <p>Creates and returns a copy of this event emitter.</p>
     * @return {yunos.core.EventEmitter} a copy of this event emitter.
     * @override
     * @public
     * @since 2
     */
    public clone(): EventEmitter;
    /**
     * <p>Adds a one time listener function for the event named eventName.&nbsp;
     * The next time eventName is triggered, this listener is removed and then invoked.</p>
     * @param {string} eventName - The name of the event.
     * @param {Function} listener - The callback function.
     * @public
     * @since 1
     */
    public once(eventName: string, listener: (...args: Object[]) => void): this;
    private hasEventListener(event: string): boolean;
    private listenerCount(eventName: string): number;
    private listeners(eventName: string): Object[];
    private doAddListener(eventName: string, listener: (...args: Object[]) => void): this;
    private doRemoveListener(eventName: string, listener: (...args: Object[]) => void): this;
    private doRemoveAllListeners(eventName: string): this;
    private doEmit(eventName: string, ...args: Object[]): boolean;
}
export = EventEmitter;
