import YObject = require("../core/YObject");
import WebView = require("../ui/view/WebView");
/**
 * <p>A WebViewHandler allows you to send and process Message and Runnable objects
 * associated with a WebView.</p>
 *
 * @extends yunos.core.YObject
 * @memberof yunos.web
 * @public
 * @since 2
 */
declare class WebViewHandler extends YObject {
    private _requestID;
    private _url;
    private _webView;
    public constructor(webView: WebView);
    /**
     * Gets or sets current permission request ID.
     * @name yunos.web.WebViewHandler#requestID
     * @type {number}
     * @public
     * @since 2
     */
    public requestID: number;
    /**
     * Gets or sets URL which requests current permission.
     * @name yunos.web.WebViewHandler#url
     * @type {string}
     * @public
     * @since 2
     */
    public url: String;
    /**
     * Gets or sets WebView associate with this handler.
     * @name yunos.web.WebViewHandler#webView
     * @type {Object}
     * @public
     * @since 2
     */
    public webView: WebView;
}
declare namespace WebViewHandler {
    /**
     * <p>This class is the argument type in permission callback of {yunos.web.WebViewClient},
     * it contains permission request ID.</p>
     *
     * @extends yunos.web.WebViewHandler
     * @memberof yunos.web.WebViewHandler
     * @public
     * @since 2
     */
    class WebViewPermission extends WebViewHandler {
        /**
         * <p>Response whether permission is allowed to request from WebViewClient.</p>
         * @example this API should be called with yunos.web.WebViewClient.onGeolocationPermission() returns true.
         * <pre>
         *  let WebView = require("yunos/ui/view/WebView"),
         *      WebViewClient = require("yunos/web/WebViewClient");
         *  let webview = new WebView();
         *  class TestWebViewClient extends WebViewClient {
         *      onGeolocationPermission(webView, permission) {
         *          setTimeout(function() {
         *              permission.invokeCallback(true);
         *          }, 100);
         *          return true;
         *      }
         *  };
         *  webview.client = new TestWebViewClient();
         * </pre>
         * @param {boolean} whether permission is allowed.
         * @public
         * @since 2
         */
        public invokeCallback(isAllowed: boolean): void;
    }
    /**
     * <p>Represents a request for handling an SSL error. Instances of this class are
     * created by the WebView and passed to onReceivedSslError(webView, error, url, fatal, handler).</p>
     *
     * @extends yunos.web.WebViewHandler
     * @memberof yunos.web.WebViewHandler
     * @public
     * @since 2
     */
    class SslErrorHandler extends WebViewHandler {
        /**
         * Proceed with the SSL certificate.
         * @public
         * @since 2
         */
        public proceed(): void;
        /**
         * Cancel this request and all pending requests for the WebView that had the error.
         * @public
         * @since 2
         */
        public cancel(): void;
    }
}
export = WebViewHandler;
