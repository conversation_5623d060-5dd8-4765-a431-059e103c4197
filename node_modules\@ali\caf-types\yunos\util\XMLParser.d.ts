import YObject = require("../core/YObject");
/**
 * <p>XMLParser is a parser utility converting XML to JSON. It parses xml by sax mode.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 3
 */
declare class XMLParser extends YObject {
    /**
     * <p>Parse xml file asynchronized and return json by callback.</p>
     * @example
     *  var filePath = __dirname + "/sample.xml";
     *  XMLParser.parse(filePath, function(err, json) {
     *      if(err){
     *          console.error(err);
     *      }
     *      console.dir(json);
     *  });
     *
     *  //handle parsing process
     *  var filePath = __dirname + "/sample.xml";
     *  XMLParser.parse(filePath, {
     *      onStartElement: function(name, attrs) {
     *          //name is name of element
     *          //attrs is attribute map of element
     *      },onEndElement: function(name) {
     *          //name is name of element
     *      },onText: function(text) {
     *          //text is text content of element
     *      }
     *  }, function(err, json) {
     *      if(err){
     *          console.error(err);
     *      }
     *      console.dir(json);
     *  });
     * @param {string} filePath - file path of xml.
     * @param {Object} [options]
     * @param {boolean} [options.needJson] - whether need get JSON result
     * @param {function} [options.onStartElement] - callback when start parsing a element in xml
     * @param {function} [options.onEndElement] - callback when end parsing a element in xml
     * @param {function} [options.onText] - callback when parsing element text content in xml
     * @param {function} cb - the callback.
     * @public
     * @since 3
     */
    public static parse(filePath: string, options: {
        needJson?: boolean;
        onStartElement?: Function;
        onEndElement?: Function;
        onText?: Function;
    }, cb: (...args: Object[]) => void): void;
    /**
     * <p>Parse xml file synchronized and return json.</p>
     * @example
     *  var filePath = __dirname + "/sample.xml"
     *  try{
     *      var json = XMLParser.parseSync(filePath);
     *      //continue by json
     *  }catch(err){
     *      //continue exception case by err
     *  }
     * @param {string} filePath - file path of xml.
     * @param {Object} [options] - the options.
     * @throws {Error} Runtime error.
     * @public
     * @since 3
     */
    public static parseSync(filePath: string, options: Object): Object;
    /**
     * <p>Parse xml plain string asynchronized and return json by callback.</p>
     * @example
     *  var str = "<yunos.ui.view.View id='v2' width='400' height='200' background='#00FF00'/>";
     *  XMLParser.parseString(str, options, function(err, json) {
     *      if(err){
     *          console.error(err);
     *      }
     *      //continue by json
     *  });
     * @param {string} str - string content of xml.
     * @param {Object} options - the options.
     * @param {function} cb - callback.
     * @public
     * @since 3
     */
    public static parseString(str: string, options: {
        needJson?: boolean;
        onStartElement?: Function;
        onEndElement?: Function;
        onText?: Function;
    }, cb: Function): void;
    /**
     * <p>Parse xml plain string synchronized and return json.</p>
     * @example
     *
     *  var str = "<yunos.ui.view.View id='v2' width='400' height='200' background='#00FF00'/>";
     *  try{
     *      var json = XMLParser.parseStringSync(str);
     *      //continue by json
     *  }catch(err){
     *      //continue exception case by err
     *  }
     * @param {string} str - plain string of xml.
     * @param {Object} options - the options.
     * @return {Object} json.
     * @throws {Error} Runtime error.
     * @public
     * @since 3
     */
    public static parseStringSync(str: string, options: Object): Object;
    private static _parseInternal;
}
export = XMLParser;
