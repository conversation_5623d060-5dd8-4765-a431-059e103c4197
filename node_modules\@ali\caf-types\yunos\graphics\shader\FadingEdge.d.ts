import Shader = require("yunos/graphics/shader/Shader");
/**
 * <p>This class provide the edge fade shader support for ui components.</p>
 * @extends yunos.graphics.shader.Shader
 * @memberof yunos.graphics.shader
 * @public
 * @since 6
 */
declare class FadingEdge extends Shader {
    private _leftLength;
    private _rightLength;
    private _topLength;
    private _bottomLength;
    private _leftOffset;
    private _rightOffset;
    private _topOffset;
    private _bottomOffset;
    private _orientation;
    /**
     * Defines how much top region will fade.
     * @name yunos.graphics.shader.EdgeFade#topLength
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If topLength is not a number.
     * @public
     * @since 6
     */
    public topLength: number;
    /**
     * Defines how much bottom region will fade.
     * @name yunos.graphics.shader.EdgeFade#bottomLength
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If bottomLength is not a number.
     * @public
     * @since 6
     */
    public bottomLength: number;
    /**
     * Defines how much left region will fade.
     * @name yunos.graphics.shader.EdgeFade#leftLength
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If leftLength is not a number.
     * @public
     * @since 6
     */
    public leftLength: number;
    /**
     * Defines how much right region will fade.
     * @name yunos.graphics.shader.EdgeFade#rightLength
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If rightLength is not a number.
     * @public
     * @since 6
     */
    public rightLength: number;
    /**
     * Defines offset of top region where top fade starts.
     * @name yunos.graphics.shader.EdgeFade#topOffset
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If topOffset is not a number.
     * @public
     * @since 6
     */
    public topOffset: number;
    /**
     * Defines offset of bottom region where bottom fade starts.
     * @name yunos.graphics.shader.EdgeFade#bottomOffset
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If bottomOffset is not a number.
     * @public
     * @since 6
     */
    public bottomOffset: number;
    /**
     * Defines offset of left region where left fade starts.
     * @name yunos.graphics.shader.EdgeFade#leftOffset
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If leftOffset is not a number.
     * @public
     * @since 6
     */
    public leftOffset: number;
    /**
     * Defines offset of right region where right fade starts.
     * @name yunos.graphics.shader.EdgeFade#rightOffset
     * @type {number} range: [0,1]
     * @default 0
     * @throws {TypeError} If rightOffset is not a number.
     * @public
     * @since 6
     */
    public rightOffset: number;
    /**
     * Defines orientation of the fading edge effect.
     * @name yunos.graphics.shader.EdgeFade#rightOffset
     * @type {number} 1 for horizontal, 2 for vertical, 0 for both
     * @default 0
     * @throws {TypeError} If orientation is not a number.
     * @public
     * @since 6
     */
    public orientation: number;
}
export = FadingEdge;
