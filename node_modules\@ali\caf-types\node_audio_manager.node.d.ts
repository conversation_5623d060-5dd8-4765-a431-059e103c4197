declare class AudioManagerNode {
    release(): void;
    getMaxAudioVolumeLevel(streamType: number, zoneId: number): number;
    getMinAudioVolumeLevel(streamType: number, zoneId: number): number;
    isAudioVolumeFixed(): boolean;
    adjustAudioVolumeByStep(streamType: number, direction: number, flags: number, zoneId: number):  void;
    adjustOptionalAudioVolumeByStep(streamType: number, direction: number, flags: number, zoneId: number): void;
    getAudioVolumeLevel(streamType: number, zoneId: number): number;
    setAudioVolumeLevel(streamType: number, index: number, flags: number, zoneId: number): void;
    setAudioMute(streamType: number, isMute: boolean, flags: number, zoneId: number): void;
    setAudioRingerMode(ringerMode: number): void;
    getAudioRingerMode(): number;
    setAudioMode(mode: number): void;
    getAudioMode(): number;
    isMusicPlaying(zoneId: number): boolean;
    isAudioPlaying(streamType: number, zoneId: number): boolean;
    isAudioMute(streamType: number, zoneId: number): boolean;
    getTopSessionClientName(zoneId: number): string;
    registerAudioVolumeListener(callback: (eventType: number, streamType: number, newIndex: number, oldIndex: number, flags: number, isMute: boolean, zoneId: number) => void): number;
    unregisterAudioVolumeListener(): number;
    registerAudioEventListener(callback: (subEventType: number, bmtType: number, newValue: number, subBandIndex: number, flags: number) => void): number;
    unregisterAudioEventListener(): number;
    setEnableSpeakerphone(isEnable: boolean): void;
    isSpeakerphoneEnabled(): boolean;
    setMuteMicrophone(isMute: boolean): number;
    isMicrophoneMute(): boolean;
    requestAudioSession(streamType: number, sessionType: number, clientName: string, callback: (changeType: number, newClientName: string, zoneId: number) => void, flags: number, usecaseType: number, sessionName: {sessionName: string}, zoneId: number): number;
    abandonAudioSession(clientName: string, callback: (changeType: number, newClientName: string, zoneId: number) => void, zoneId: number): number;
    setEnableSafeVolumeAlert(isEnable: boolean): void;
    setEnableBTSco(isEnable: boolean): void;
    isBTScoEnabled(): boolean;
    setAudioStringKvs(keyValuePairs: string): number;
    getAudioStringKvs(keyString: string): string;
    registerAudioRingerModeListener(callback: (newMode: number, flags: number)  => void): number;
    unregisterAudioRingerModeListener(): number;
    registerAudioDeviceStateListener(callback: (eventType: number, deviceType: number)  => void, deviceType: number): number;
    unregisterAudioDeviceStateListener(deviceType: number): number;
    getAudioDeviceForStream(streamType: number, zoneId: number): number;
    isAudioRingerModeRelevant(streamType: number): boolean;
    setAudioRouteConfig(type: number, config: number): number;
    setAudioEffectFade(fade: number): number;
    setAudioEffectBalance(balance: number): number;
    setAudioEffectBMT(filterType: number, enableFlag: boolean, value: number): number;
    setAudioEffectPresetEQ(presetId: number, enableFlag: boolean): number;
    setAudioEffectCustomizedEQ(bandIndex: number, centerFreq: number, bandLevel: number) :number;
    getAudioEffectFade(): object;
    getAudioEffectBalance(): object;
    getAudioEffectBMT(filterType: number): object;
    getAudioEffectPresetEQ(): object;
    getAudioEffectCustomizedEQ(bandIndex: number): object;
    setEnableMusicFadein(isEnable: boolean): number;
    isMusicFadeinEnabled(): boolean;
    setAudioVolumeRange(streamType: number, minLevel: number, maxLevel: number, zoneId: number): number;
    getAudioVolumeRange(streamType: number, zoneId: number): {minLevel: number, maxLevel: number};
    restoreAudioSetting(settingType: number, zoneId: number): number;
    isConnected():boolean;
    setSpectrumBand(band: number, frequency: number): number;
    setSpectrumQ(Q: number): number;
    setSpectrumBeat(streamType: number, duration: number, zoneId: number): number;
    getSpectrumCapacity(): number;
    getSpectrumDuation(streamType : number, zoneId: number): number;
    registerSpectrumInfoListener(callback: (bandInfo: object) => void, streamType: number, zoneId: number): number;
    unregisterSpectrumInfoListener(streamType: number, zoneId: number): number;
    isMasterMute():boolean;
    setMasterMute(isMute: boolean): number;
    setZoneActiveByType(zoneType : number, streamType : number, active : boolean): number;
    getZoneActiveByType(zoneType : number, streamType : number): number;
    getAllZoneState(): number;
    getZoneTypeState(): {zoneTypeInfos:number[], zoneTypeStreamState: number[][]};
    registerZoneEventListener(callback: (zoneEvent: {zoneEventType: number, zoneType: number, zoneTypeActive: boolean, zoneTypeStream: number, zoneTypeStreamActive: boolean}) => void): number;
    unregisterZoneEventListener(): number;
}

export = AudioManagerNode;
