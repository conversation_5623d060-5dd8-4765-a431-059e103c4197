import YObject = require("../../core/YObject");
/**
 * Enum of Paint Stroke Cap
 * @ignore
 */
export declare enum Cap {
    /**
     *  begin/end contours with no extension
     * @ignore
     */
    Butt_Cap = 0,
    /**
     * begin/end contours with a semi-circle extension
     * @ignore
     */
    Round_Cap = 1,
    /**
     * begin/end contours with a half square extension
     * @ignore
     */
    Square_Cap = 2,
    /**
     * Nick name of Square_Cap
     * @ignore
     */
    Last_Cap = 2,
    /**
     * Default Cap value
     * @ignore
     */
    Default_Cap = 0
}
/**
 * Enum Value of Paint Style
 * @ignore
 */
export declare enum Style {
    /**
     * fill the geometry
     * @ignore
     */
    Fill_Style = 0,
    /**
     * stroke the geometry
     * @ignore
     */
    Stroke_Style = 1,
    /**
     * fill and stroke the geometry
     * @ignore
     */
    StrokeAndFill_Style = 2
}
/**
 * Enum Value of Paint TextAlign
 * @ignore
 */
export declare enum TextAlign {
    /**
     * @ignore
     */
    Left_Align = 0,
    /**
     * @ignore
     */
    Center_Align = 1,
    /**
     * @ignore
     */
    Right_align = 2
}
export declare enum Baseline {
    Top = 0,
    Bottom = 1,
    Middle = 2,
    Alphabetic = 3,
    Hanging = 4
}
/**
 * Enum of Paint Stroke Join
 * @ignore
 */
export declare enum Join {
    /**
     * connect path segments with a sharp join
     * @ignore
     */
    Miter_Join = 0,
    /**
     * connect path segments with a round join
     * @ignore
     */
    Round_Join = 1,
    /**
     * connect path segments with a flat bevel join
     * @ignore
     */
    Bevel_Join = 2,
    /**
     * @ignore
     */
    Last_Join = 2,
    /**
     * Default Join Value
     * @ignore
     */
    Default_Join = 0
}
/**
 * Graphic Paint Wrapper all function is just penetrate to native Paint addon binding
 * @ignore
 */
export declare class Paint extends YObject {
}
