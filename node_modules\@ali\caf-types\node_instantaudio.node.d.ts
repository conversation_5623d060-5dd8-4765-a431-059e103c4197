declare namespace InstantaudioNode {
  export class NodeInstantAudio {
      constructor(maxChannels: number);
      setListener(v: Function): void;
      release(): void;
      setPriority(channelID: number, priority: number): void;
      setRate(channelID: number, rate: number): void;
      setLoop(channelID: number, loop: number): void;
      setVolume(channelID: number, volumeLeft: number, volumeRight: number): void;
      stopAll(): void;
      stop(channelID: number): void;
      resumeAll(): void;
      resume(channelID: number): void;
      pauseAll(): void;
      pause(channelID: number): void;
      play(sampleID: number, volumeLeft: number, volumeRight: number, loop: number,
        rate: number, priority: number, streamType: number): void;
      unloadAll(): void;
      unload(sampleId: number): void;
      loadURI(uri: string): void;
  }
}

export = InstantaudioNode;
