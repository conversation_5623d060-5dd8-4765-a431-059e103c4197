export interface Meta {
    theme: {
        name: string;
        extend: string;
        supportMode: {
            [key: string]: {
                [key: string]: string;
            };
        };
        root?: string;
        compatiple?: boolean;
    };
}
export interface AssetsPkg {
    style: {
        [key: string]: object;
    };
    color: {
        [key: string]: string;
    };
    image: {
        [key: string]: string;
    };
    string: {
        [key: string]: object;
    };
    setter: {
        [key: string]: object;
    };
    dimen: {
        [key: string]: string;
    };
    meta: Meta;
    config: {
        extendStyleNames: string[];
    };
    mode: {
        [key: string]: object;
    };
}
export declare function dp2Pixel(dp: number): number;
export declare function sDp2Pixel(sDp: number): number;
export declare function createReferenceMap(arr: string[], upperCase?: boolean): {
    [key: string]: string;
};
export declare function getAllBundleFromSystemResource(): {
    bundles: {
        [key: string]: AssetsPkg;
    };
    themes: string[];
};
export declare function getSystemBundleInfo(): string[];
export declare function getBundle(bundleName?: string): AssetsPkg;
export declare function simpleClone(obj: Object): Object;
