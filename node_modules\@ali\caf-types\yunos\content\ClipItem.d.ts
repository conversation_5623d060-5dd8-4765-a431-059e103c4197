import YObject = require("yunos/core/YObject");
/**
 * <p>Use this class to describe a single item in a clipped content.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.content
 * @public
 * @since 1
 * @hiddenOnPlatform auto
 */
declare class ClipItem extends YObject {
    private mType: number;
    private mText: string | null;
    private mHtmlText: string | null;
    private mUri: string | null;
    private mData: Object;
    /**
     * <p>Constructor</p>
     * @example
     * let content = new ClipContent();
     * let item = new ClipItem(ClipContent.CONTENT_TYPE_TEXT, "test text");
     * content.addItem(item);
     * @param {number} type one of values:
     *                 ClipContent.CONTENT_TYPE_TEXT,
     *                 ClipContent.CONTENT_TYPE_HTML,
     *                 ClipContent.CONTENT_TYPE_URI,
     *                 ClipContent.CONTENT_TYPE_DATA
     * @param {Object} value - the value of item.
     * @public
     * @since 1
     */
    public constructor(type?: number, value?: Object);
    /**
     * <p>Set value to the item to add a new value or replace the current one.</p>
     * @example
     * let content = new ClipContent();
     * let item = new ClipItem(ClipContent.CONTENT_TYPE_TEXT, "test text");
     * item.setValue(ClipContent.CONTENT_TYPE_HTML, "<div></div>");
     * item.setValue(ClipContent.CONTENT_TYPE_URI, "https://www.yunos.com");
     * content.addItem(item);
     * @param {number} type one of values:
     *                 ClipContent.CONTENT_TYPE_TEXT,
     *                 ClipContent.CONTENT_TYPE_HTML,
     *                 ClipContent.CONTENT_TYPE_URI,
     *                 ClipContent.CONTENT_TYPE_DATA
     * @param {Object} value - the value of item.
     * @public
     * @since 1
     */
    public setValue(type: number, value: Object): void;
    /**
     * <p>Check if the item has plain text.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasText()) {
     *                 console.log(item.getText());
     *             }
     *         }
     *     }
     * });
     * @return {boolean} if item has plain text true otherwise false.
     * @public
     * @since 1
     */
    public hasText(): boolean;
    /**
     * <p>Check if the item has html text.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasHtmlText()) {
     *                 console.log(item.getHtmlText());
     *             }
     *         }
     *     }
     * });
     * @return {boolean} if item has html text true otherwise false.
     * @public
     * @since 1
     */
    public hasHtmlText(): boolean;
    /**
     * <p>Check if the item has uri text.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasUri()) {
     *                 console.log(item.getUri());
     *             }
     *         }
     *     }
     * });
     * @return {boolean} if item has uri text true otherwise false.
     * @public
     * @since 1
     */
    public hasUri(): boolean;
    /**
     * <p>Check if the item has raw data.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasData()) {
     *                 console.log(item.getData());
     *             }
     *         }
     *     }
     * });
     * @return {boolean} if item has raw data true otherwise false.
     * @public
     * @since 1
     */
    public hasData(): boolean;
    /**
     * <p>Get the plain text from clipped item.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasText()) {
     *                 console.log(item.getText());
     *             }
     *         }
     *     }
     * });
     * @return {string} plain text in the item.
     * @public
     * @since 1
     */
    public getText(): string | null;
    /**
     * <p>Get the html text from clipped item.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasHtmlText()) {
     *                 console.log(item.getHtmlText());
     *             }
     *         }
     *     }
     * });
     * @return {string} html text in the item.
     * @public
     * @since 1
     */
    public getHtmlText(): string | null;
    /**
     * <p>Get the uri text from clipped item.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasUri()) {
     *                 console.log(item.getUri());
     *             }
     *         }
     *     }
     * });
     * @return {string} uri text in the item.
     * @public
     * @since 1
     */
    public getUri(): string | null;
    /**
     * <p>Get the raw data element from clipped item.</p>
     * @example
     * const ClipboardManager = require("yunos/content/ClipboardManager");
     * let clipboardManager = ClipboardManager.getInstance();
     * clipboardManager.getContent(function(r, res) {
     *     if (r) {
     *         let cnt = res.getItemCount();
     *         for (let i = 0; i < cnt; ++i) {
     *             let item = res.getItemAt(i);
     *             if (item.hasData()) {
     *                 console.log(item.getData());
     *             }
     *         }
     *     }
     * });
     * @return {Object} Raw data element in the item, composed of:
     *                  mimeType {String}, size {Number}, buffer {Object(Node Buffer)}.
     * @public
     * @since 1
     */
    public getData(): Object;
    private readFromNative(item: Object): void;
}
export = ClipItem;
