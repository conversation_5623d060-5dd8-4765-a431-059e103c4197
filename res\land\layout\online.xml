<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    layout="{layout.online}"
    propertySetName="online">

    <NavigationBar
        id="id_nav"
        title="{string.VIDEO_TITLE}"/>

    <CompositeView
        id="id_cp_info"
        height="{config.ONLINE_CP_INFO_HEIGHT}"
        layout="{layout.online_cp_info}">
        <ImageView
            id="id_category_icon"
            width="{sdp(56)}"
            height="{sdp(56)}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"
            multiState="{config.ITEM_MULTISTATE}"/>
        <TextView
            id="id_category_title"
            text="{string.ONLINE_TITLE}"
            propertySetName="extend/hdt/FontBody1"/>

        <ImageView
            id="id_cp_logo"
            width="{sdp(90)}"
            height="{sdp(60)}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"
            multiState="{config.ITEM_MULTISTATE}"/>
    </CompositeView>

    <ListView
        id="id_list_grid"
        height="{config.ONLINE_ITEM_HEIGHT}"
        orientation="{enum.ListView.Orientation.Horizontal}"
        spacing="{config.ITEM_SPACE}"
        scrollBarCustomized="true"
        horizontalFadingEdgeEnabled="true"
        focusable="false"/>

    <ScrollBar
        id="id_scrollbar"
        height="{config.SCROLL_BAR_SIZE}"
        autoHidden="true"/>

    <LoadingPageBM id="id_loading_page"/>

    <include
        id="id_empty"
        visibility="{enum.View.Visibility.None}"
        markup="empty"/>
</CompositeView>
