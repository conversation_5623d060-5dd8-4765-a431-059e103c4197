import Matrix4 = require("./Matrix4");
import Quaternion = require("./Quaternion");
/**
 * <p>This class represents a 4D vector.</p>
 * <p>A 4D vecotr is an ordered pair of numbers(labeled x, y, z and w.</p>
 * @memberof yunos.graphics.3d.math
 * @public
 * @since 5
 * @hiddenOnPlatform auto
 */
declare class Vector4 {
    private _out;
    /**
     * Constructor that create a 4D vector.
     * @param {number} num1 - the first number of the 3D vector.
     * @param {number} num2 - the second number of the 3D vector.
     * @param {number} num3 - the third number of the 3D vector.
     * @param {number} num4 - the fourth number of the 3D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public constructor(a?: number, b?: number, c?: number, d?: number);
    /**
     * Destructor that destroy this 4D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public destroy(): void;
    /**
     * Defines the first value of this 4D vector.
     * @name yunos.graphics.3d.math.Vector4#x
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public x: number;
    /**
     * Defines the first value of this 4D vector.
     * @name yunos.graphics.3d.math.Vector4#y
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public y: number;
    /**
     * Defines the first value of this 4D vector.
     * @name yunos.graphics.3d.math.Vector4#z
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public z: number;
    /**
     * Defines the first value of this 4D vector.
     * @name yunos.graphics.3d.math.Vector4#w
     * @type {number}
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public w: number;
    /**
     * Return the Float32Array of this 3D vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public data(): Float32Array;
    /**
     * Add a new vector to this vector.
     * @param {yunos.graphics.3d.math.Vector4} v - the vector to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public add(v: Vector4): void;
    /**
     * Add the scalar value s to this vector's x, y, z and w values.
     * @param {number} s - the scalar to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addScalar(s: number): void;
    /**
     * Add the multiple of v and s to this vector.
     * @param {yunos.graphics.3d.math.Vector4} v - the vector to add
     * @param {number} s - the number to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addScaledVector(v: Vector4, s: number): void;
    /**
     * Sets this vector to a + b.
     * @param {yunos.graphics.3d.math.Vector4} a - the vector to add
     * @param {yunos.graphics.3d.math.Vector4} b - the vector to add
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public addVectors(a: Vector4, b: Vector4): void;
    /**
     * Multiplies this vector by Matrix4.
     * @param {yunos.graphics.3d.math.Matrix4} m - the matrix to multiply
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public applyMatrix4(m: Matrix4): void;
    /**
     * The x, y, z and w components of the vector are rounded up to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public ceil(): void;
    /**
     * <p>If this vector's x, y, z or w value is greater than the max vector's x, y, z or w value, it is replaced by the corresponding value.</p>
     * <p>If this vector's x, y, z or w value is less than the min vector's x, y, z or w value, it is replaced by the corresponding value.</p>
     * @param {yunos.graphics.3d.math.Vector4} min - the minimum x, y, z and w values
     * @param {yunos.graphics.3d.math.Vector4} max - the maximum x, y, z and w values
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clamp(min: Vector4, max: Vector4): void;
    /**
     * <p>If this vector's length is greater than the max value, it is replaced by the max value.
     * <p>If this vector's length is less than the min value, it is replaced by the min value.
     * @param {number} min - the minimum value the length will be clamped to
     * @param {number} max - the maximum value the length will be clamped to
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clampLength(min: number, max: number): void;
    /**
     * <p>If this vector's x, y, z or w values are greater than the max value, they are replaced by the max value.</p>
     * <p>If this vector's x, y, z or w values are less than the min value, they are replaced by the min value.</p>
     * @param {number} min - the minimum value the components will be clamped to.
     * @param {number} max - the maximum value the components will be clamped to.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clampScalar(min: number, max: number): void;
    /**
     * Returns a new Vector4 with the same x, y, z and w values as this one.
     * @return {yunos.graphics.3d.math.Vector4} return the vector4
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public clone(): Vector4;
    /**
     * Copies the values of the passed Vector4's x, y, z and w properties to this Vector4.
     * @param {yunos.graphics.3d.math.Vector4} vec - the vector to copy
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public copy(v: Vector4): void;
    /**
     * Divides this vector by scalar s.
     * @param {number} s - the number to divide.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public divideScalar(s: number): void;
    /**
     * Calculates the dot product of this vector and v.
     * @param {yunos.graphics.3d.math.Vector4} v - the vector to dot
     * @return {number} the result of dot
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public dot(v: Vector4): number;
    /**
     * Checks for strict equality of this vector and v.
     * @param {yunos.graphics.3d.math.Vector4} v - the vector to equal
     * @return {boolean} the result of equals
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public equals(v: Vector4): boolean;
    /**
     * The components of the vector are rounded down to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public floor(): void;
    /**
     * Sets this vector's x value to be array[ offset + 0 ], y value to be array[ offset + 1 ] z value to be array[ offset + 2 ] and w value to be array[ offset + 3 ].
     * @param {number[]} array - the source array
     * @param {number} offset - offset into the array. Default is 0.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public fromArray(array: Array<number>, offset: number): void;
    /**
     * <p>If index equals 0 return the x value.</p>
     * <p>If index equals 1 return the y value.</p>
     * <p>If index equals 2 return the z value.</p>
     * <p>If index equals 3 return the w value.</p>
     * @param {number} index - the index of the components.
     * @return {number} return the component
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public getComponent(index: number): number;
    /**
     * Computes the Euclidean length (straight-line length) from (0, 0, 0, 0) to (x, y, z, w).
     * @return {number} return the length of the vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public length(): number;
    /**
     * Computes the Manhattan length of this vector.
     * @return {number} return the manhattan length of the vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public manhattanLength(): number;
    /**
     * Computes the square of the Euclidean length (straight-line length) from (0, 0, 0, 0) to (x, y, z, w)
     * @return {number} return the square of the length.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lengthSq(): number;
    /**
     * Linearly interpolates between this vector and v, where alpha is the distance along the line - alpha = 0 will be this vector, and alpha = 1 will be v.
     * @param {yunos.graphics.3d.math.Vector4} v - Vector4 to interpolate towards.
     * @param {number} alpha - interpolation factor in the closed interval [0, 1].
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lerp(v: Vector4, alpha: number): void;
    /**
     * Sets this vector to be the vector linearly interpolated between v1 and v2.
     * @param {yunos.graphics.3d.math.Vector4} v1 - the starting Vector4
     * @param {yunos.graphics.3d.math.Vector4} v2 - Vector4 to interpolate towards
     * @param {number} alpha - interpolation factor in the closed interval [0, 1].
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public lerpVectors(v1: Vector4, v2: Vector4, alpha: number): void;
    /**
     * Inverts this vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public negate(): void;
    /**
     * Converts this vector to a unit vector.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public normalize(): void;
    /**
     * If this vector's x, y, z or w value is less than v's x, y, z or w value, replace that value with the corresponding max value.
     * @param {yunos.graphics.3d.math.Vector4} v - the max vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public max(v: Vector4): void;
    /**
     * If this vector's x, y, z or w value is greater than v's x, y, z or w value, replace that value with the corresponding min value.
     * @param {yunos.graphics.3d.math.Vector4} v - the min vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public min(v: Vector4): void;
    /**
     * Multiplies this vector by scalar s.
     * @param {number} s - the scalar to multiply.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public multiplyScalar(s: number): void;
    /**
     * The components of the vector are rounded to the nearest integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public round(): void;
    /**
     * The components of the vector are rounded towards zero to an integer value.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public roundToZero(): void;
    /**
     * Sets the x, y, z and w components of this vector.
     * @param {number} x - the x components of this vector
     * @param {number} y - the y components of this vector
     * @param {number} z - the z components of this vector
     * @param {number} w - the w components of this vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public set(x: number, y: number, z: number, w: number): void;
    /**
     * Sets the x, y and z components of this vector to the quaternion's axis and w to the angle.
     * @param {yunos.graphics.3d.math.Quaternion} quaternion - a normalized Quaternion
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setAxisAngleFromQuaternion(q: Quaternion): void;
    /**
     * Sets the x, y and z to the axis of rotation and w to the angle.
     * @param {yunos.graphics.3d.math.Matrix4} m -  a Matrix4 of which the upper left 3x3 matrix is a pure rotation matrix.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setAxisAngleFromRotationMatrix(m: Matrix4): void;
    /**
     * <p>If index equals 0 set x to value.</p>
     * <p>If index equals 1 set y to value.</p>
     * <p>If index equals 2 set z to value.</p>
     * <p>If index equals 3 set w to value.</p>
     * @param {number} index - 0, 1, 2 or 3
     * @param {number} value - set the component
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setComponent(index: number, value: number): void;
    /**
     * Sets this vector to the vector with the same direction as this one, but length l.
     * @param {number} l - set the length
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setLength(l: number): void;
    /**
     * Sets the x, y, z and w values of this vector both equal to scalar.
     * @param {number} scalar - set the number.
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public setScalar(scalar: number): void;
    /**
     * Subtracts v from this vector.
     * @param {yunos.graphics.3d.math.Vector4} v - the vector to sub
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public sub(v: Vector4): void;
    /**
     * Subtracts s from this vector's x, y, z and w compnents.
     * @param {number} s - the scalar to sub
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public subScalar(s: number): void;
    /**
     * Set this vector to a - b
     * @param {yunos.graphics.3d.math.Vector4} a - the first vector
     * @param {yunos.graphics.3d.math.Vector4} b - the second vector
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public subVectors(a: Vector4, b: Vector4): void;
    /**
     * Returns an array [x, y, z, w], or copies x, y, z and w into the provided array.
     * @param {number[]} array - array to store the vector to.
     * @param {number} offset - offset into the array.
     * @return {number[]} return the array
     * @public
     * @since 5
     * @hiddenOnPlatform auto
     */
    public toArray(array: Array<number>, offset: number): Array<number>;
}
export = Vector4;
