import YObject = require("../core/YObject");
import FenceInfo = require("./FenceInfo");
import FenceGroup = require("./FenceGroup");
import { AddFenceRequestObj } from "./TypeInner";
/**
 * <p>A data object that contains request parameters to FenceClient.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.fenceclient
 * @relyon YUNOS_SYSCAP_LOCATION
 * @public
 * @since 5
 */
declare class AddFenceRequest extends YObject {
    public _native: AddFenceRequestObj;
    public constructor();
    /**
     * <p>When the value is true, it means that a cloud integrated fence is created.
     * When the value is false, a local fence is created.</p>
     * @name yunos.fenceclient.AddFenceRequest#cloudFence
     * @type {yunos.fenceclient.AddFenceRequest}
     * @public
     * @since 5
     */
    public cloudFence: boolean;
    /**
     * <p>Fence group definition information,
     * these definitions are effective for a fence group including one or more fences.</p>
     * @name yunos.fenceclient.AddFenceRequest#fenceGroup
     * @type {yunos.fenceclient.AddFenceRequest}
     * @public
     * @since 5
     */
    public fenceGroup: FenceGroup;
    /**
     * <p>List of fence data to be created.</p>
     * @name yunos.fenceclient.AddFenceRequest#fenceList
     * @type {yunos.fenceclient.AddFenceRequest}
     * @public
     * @since 5
     */
    public fenceList: FenceInfo[];
}
export = AddFenceRequest;
