import AlertDialog = require("./AlertDialog");
import ListView = require("../view/ListView");
import { StringObjectKV } from "../util/TypeHelper";
/**
 * <p>The ListDialog Synthesized by ListView and AlertDialog. If you only want to<br>
 * display a radio group or checkBox group in this dialog box, use the setSingleChoiceItems()<br>
 * or setMultiChoiceItems() method. If you want to display an other type of listView,<br>
 * you can get listView content by the property of listView, then you can custom your listView's adpater.</p>
 * @example
 * const listDialog = new ListDialog();
 * listDialog.title = "please choose";
 * listDialog.setMultiChoiceItems(["orange", "banana", "tomato", "peach", "pear", "cantaloupe"], [1, 2, 3]);
 * listDialog.on("groupcheckedchange", () => {
 *     console.log(listDialog.choiceItems);
 * });
 * @extends yunos.ui.widget.AlertDialog
 * @memberof yunos.ui.widget
 * @public
 * @since 2
 */
declare class ListDialog extends AlertDialog {
    private _disabledList;
    private _group;
    private _listView;
    private _message;
    private _minVerticalMargin;
    private _defaultListSpacing;
    private _defaultVerticalFadingEdgeEnabled;
    private _adapterInstance;
    /**
     * Create a list dialog.
     * @public
     * @since 2
     */
    /**
     * <p>Destructor that destroy this ListDialog.</p>
     * @param {boolean} recursive - destroy the children in the ListDialog if the value is true.
     * @public
     * @override
     * @since 2
     */
    public destroy(recursive?: boolean): void;
    /**
     * Message of the dialog, it's not display in the list dialog.
     * @name yunos.ui.widget.ListDialog#message
     * @type {string}
     * @public
     * @override
     * @since 2
     */
    public message: string;
    /**
     * <p>Get the listView of the Dialog, if you want the listView's items not limited to radio or checkBox,<br>
     * you can get it and custom it's adpater.</p>
     * @name yunos.ui.widget.ListDialog#listView
     * @type {yunos.ui.view.ListView}
     * @readonly
     * @public
     * @since 2
     */
    public readonly listView: ListView;
    /**
     * <p>Get the choiced items. it is worked when set the setSingleChoiceItems()<br>
     * or setMultiChoiceItems() method in this listDialog. it will return index of the choiceItem when you<br>
     * set the setSingleChoiceItems() method, return index array of the choiceItem when you<br>
     * set the setMultiChoiceItems() method</p>
     * @name yunos.ui.widget.ListDialog#choiceItems
     * @type {number|number[]}
     * @readonly
     * @public
     * @since 2
     */
    public readonly choiceItems: Object;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.ListDialog#defaultStyleName
     * @type {string}
     * @default "ListDialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: "ListDialog" | "ListDialogAuto";
    /**
     * <p>Set radio list of items to be displayed in the dialog as the content.<br>
     * Clicking on an item in the list will not dismiss the dialog.</p>
     * @param {string[]} items - radio list of items
     * @param {number} choiceItem - index of choiced item, do not pass this parameter if is not choiced
     * @throws {TypeError} If first parameter is not an array.
     * @throws {TypeError} If first parameter is not a string array.
     * @throws {TypeError} If second parameter is not an integer, null or undefined.
     * @public
     * @since 2
     */
    public setSingleChoiceItems(items: string[], choiceItem: number): void;
    /**
     * <p>Set checkbox list of items to be displayed in the dialog as the content.<br>
     * Clicking on an item in the list will not dismiss the dialog.</p>
     * @param {string[]} items - checkbox list of items
     * @param {number[]} choiceList - array index of choiced items, do not pass this parameter if is not choiced
     * @throws {TypeError} If first parameter is not an array.
     * @throws {TypeError} If first parameter is not a string array.
     * @throws {TypeError} If second parameter is not an array, null or undefined.
     * @throws {TypeError} If second parameter is not an integer array, null or undefined.
     * @public
     * @since 2
     */
    public setMultiChoiceItems(items: string[], choiceList?: number[]): void;
    /**
     * <p>Set the property of enabled by index. it's meaningful when use setMultiChoiceItems.</p>
     * @param {number} index - the index of item to set
     * @param {boolean} enabled - the enabled state to set
     * @throws {TypeError} If first parameter is not an integer.
     * @throws {TypeError} If second parameter is not a boolean.
     * @public
     * @since 2
     */
    public setEnabledByIndex(index: number, enabled: boolean): void;
    /**
     * <p>Apply theme style for dialog.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 2
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: StringObjectKV): void;
    /**
     * Return the content view of the dialog
     * @function ListDialog#getContentView
     * @override
     * @protected
     * @since 2
     */
    /**
     * Return the content view of the dialog
     * @returns {yunos.ui.view.CompositeView} the content view.
     * @override
     * @public
     * @since 6
     */
    public getContentView(): ListView;
    private onGroupCheckedChange(id: string, checked: boolean): void;
    private emptyGroup(): void;
    private makeChoiceAdapter(styleName?: number): object;
    private changeIdToIndex(id: string): number;
    private changeIndexToId(index: number): string;
    private updateData(data: string[], choiceItem: number[]): Object[];
}
export = ListDialog;
