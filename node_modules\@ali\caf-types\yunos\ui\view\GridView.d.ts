/// <reference types="node" />
import View = require("./View");
import ScrollableView = require("./ScrollableView");
import Point = require("../../graphics/Point");
import Timer = NodeJS.Timer;
import GestureEvent = require("../event/GestureEvent");
import TouchEvent = require("../event/TouchEvent");
import VoiceEvent = require("../event/VoiceEvent");
import KeyEvent = require("../event/KeyEvent");
import HeaderListAdapter = require("../adapter/HeaderListAdapter");
import GroupAdapter = require("../adapter/GroupAdapter");
import TapRecognizer = require("../gesture/TapRecognizer");
import LongPressRecognizer = require("../gesture/LongPressRecognizer");
import VoiceCommand = require("../voice/VoiceCommand");
import BaseAdapter = require("../adapter/BaseAdapter");
import CursorAdapter = require("../adapter/CursorAdapter");
/**
 * Grid View that shows items in a two-dimensional, scrollable grid.
 * @extends yunos.ui.view.ScrollableView
 * @memberof yunos.ui.view
 * @public
 * @since 2
 */
declare class GridView extends ScrollableView {
    private _requestedCellWidth: number;
    private _cellWidth: number;
    private _requestedCellHeight: number;
    private _cellHeight: number;
    private _rows: number;
    private _requestedRows: number;
    private _requestedColumns: number;
    private _columns: number;
    private _columnSpacing: number;
    private _rowSpacing: number;
    private _spacing: number;
    private _adapter: BaseAdapter | CursorAdapter | GroupAdapter | HeaderListAdapter;
    private _firstPosition: number;
    private _layoutMode: string;
    private _specificPositon: number;
    private _specificStart: number;
    private _itemCount: number;
    private _lastScrollY: number;
    private _recycler;
    private _headers: View[];
    private _footers: View[];
    private _groupHeaders;
    private _contentYTimer: Timer;
    private _headerCount: number;
    private _tapRecognizer: TapRecognizer;
    private _longPressRecognizer: LongPressRecognizer;
    private _onTouchEndFunc: (e: TouchEvent) => void;
    private _onDataChangeFunc: (...args: Object[]) => void;
    private _selectPageCommand: VoiceCommand;
    private _lastScrollX: number;
    private _nativeFrameId: number;
    private _dataChange: boolean;
    private _recordFocusIdx: number;
    private isNeedPropagation: boolean;
    private _canEmitGridItemUpdateEvent: boolean;
    private _groupHeight: number;
    private _defaultRowSpacing: number;
    private _defaultColumnSpacing: number;
    /**
     * Create a GridView.
     * @public
     * @since 2
     */
    public constructor(...args: Object[]);
    /**
     * <p>Destroy this GridView.</p>
     * @param {boolean} recursive - destroy the children in the CompositeView if the value is true.
     * @public
     * @since 2
     */
    public destroy(recursive?: boolean): void;
    /**
     * Defines the data adapter of the GridView.
     * @name yunos.ui.view.GridView#adapter
     * @type {yunos.ui.adapter.BaseAdapter}
     * @public
     * @since 2
     */
    public adapter: BaseAdapter | GroupAdapter | CursorAdapter;
    private resetOffset(): void;
    /**
     * Defines cell width of this item.
     * @name yunos.ui.view.GridView#cellWidth
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 2
     */
    public cellWidth: number;
    /**
     * Defines cell height of this item.
     * @name yunos.ui.view.GridView#cellHeight
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 2
     */
    public cellHeight: number;
    /**
     * Defines number items of column only effective in orientation - GridView.Orientation.Vertical.
     * @name yunos.ui.view.GridView#columns
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 2
     */
    public columns: number;
    /**
     * Defines number items of row only effective in orientation - GridView.Orientation.Horizontal.
     * @name yunos.ui.view.GridView#rows
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 3
     *
     */
    public rows: number;
    /**
     * Defines column spacing.
     * @name yunos.ui.view.GridView#columnSpacing
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 2
     */
    public columnSpacing: number;
    /**
     * Defines row spacing.
     * @name yunos.ui.view.GridView#rowSpacing
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 2
     */
    public rowSpacing: number;
    /**
     * Defines spacing, both row spacing and column spacing.
     * @name yunos.ui.view.GridView#spacing
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not a positive number.
     * @public
     * @since 2
     */
    public spacing: number;
    /**
     * Left padding of GridView.
     * @name yunos.ui.view.GridView#paddingLeft
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingLeft: number;
    /**
     * Right padding of GridView.
     * @name yunos.ui.view.GridView#paddingRight
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingRight: number;
    /**
     * Top padding of GridView.
     * @name yunos.ui.view.GridView#paddingTop
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingTop: number;
    /**
     * Bottom padding of GridView.
     * @name yunos.ui.view.GridView#paddingBottom
     * @type {number}
     * @default 0
     * @throws {TypeError} If parameter is not a number.
     * @private
     */
    private paddingBottom: number;
    private focusPosition: number;
    private focusToPosition(pos: number): void;
    /**
     * Defines the scrolled top position of this view.
     * This is the top edge of the displayed part of your view.
     * You do not need to draw any pixels above it, since those are outside of the frame of your view on screen.
     * @name yunos.ui.view.GridView#scrollY
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @override
     * @public
     * @since 2
     */
    public scrollY: number;
    /**
     * Defines the scrolled top position of this view.
     * This is the top edge of the displayed part of your view.
     * You do not need to draw any pixels above it, since those are outside of the frame of your view on screen.
     * @name yunos.ui.view.GridView#scrollX
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @override
     * @public
     * @since 3
     *
     */
    public scrollX: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.view.GridView#defaultStyleName
     * @type {string}
     * @default "GridView"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * Finds the child view at the point in this GridView.
     * @param {yunos.graphics.Point} point - the point.
     * @return {yunos.ui.view.View} return a child view which contains the specified point and is on the top of all children,
     * otherwise return null.
     * @public
     * @since 2
     */
    public findChildAtPoint(point: Point): View;
    /**
     * Maps a point to a position in the GridView.
     * @param {yunos.graphics.Point} point - local coordinate point
     * @return {number} The position of the item
     * @public
     * @since 2
     */
    public pointToPosition(point: Point): number;
    /**
     * <p>Add a fixed view to appear at the top of the GridView.</p>
     * <p>If this method is called more than once, the views will appear in the order they were added.</p>
     * @param {yunos.ui.view.View} view - The view to add.
     * @throws {TypeError} If parameter is not a instance of View.
     * @public
     * @since 2
     */
    public addHeader(view: View): void;
    /**
     * Removes a previously-added header view.
     * @param {yunos.ui.view.View} view - The view to remove
     * @return {boolean} true if the view was removed, false if the view was not a header
     *         view
     * @public
     * @since 2
     */
    public removeHeader(view: View): boolean;
    /**
     * <p>Add a fixed view to appear at the end of the GridView.</p>
     * <p>If this method is called more than once, the views will appear in the order they were added.</p>
     * @param {yunos.ui.view.View} view - The footer view to add.
     * @throws {TypeError} If parameter is not a instance of View.
     * @public
     * @since 2
     */
    public addFooter(view: View): void;
    /**
     * Removes a previously-added footer view.
     * @param {yunos.ui.view.View} view - The view to remove
     * @return {boolean} true if the view was removed, false if the view was not a footer
     *         view
     * @public
     * @since 2
     */
    public removeFooter(view: View): boolean;
    /**
     * Gets header view count.
     * @return {number} header view count
     * @public
     * @since 2
     */
    public getHeaderViewsCount(): number;
    /**
     * Gets footer view count.
     * @return {number} footer view count
     * @public
     * @since 2
     */
    public getFooterViewsCount(): number;
    /**
     * <p>Arrives the postion item in this GridView.</p>
     * <p>The position item will be positioned appropriately.</p>
     * <p>If the specified  position is less than 0, then the item at position 0 will be located.</p>
     * <p>If the specified  position is more than adapter data array length, then the last item will be located.</p>
     * @param {number} position - The position within the adapter's data set
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 2
     */
    public arriveAt(position: number): void;
    /**
     * Returns the position within the adapter's data set for the first item
     * displayed on screen.
     * @return {number} The position within the adapter's data set
     * @public
     * @since 2
     */
    public getFirstVisiblePosition(): number;
    /**
     * Implement this to apply style
     * @method applyStyle
     * @protected
     * @since 2
     */
    /**
     * Implement this to apply style
     * @param {Object} style - Style config from theme.
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: Object): void;
    /**
     * <p>Implement this method to update style when style properties changed.</p>
     * @param {Object} style - New style config from theme.
     * @param {Object} diffStyle - Object contains difference values compare with the preview style.
     * @override
     * @protected
     * @since 4
     *
     */
    protected updateStyle(style: Object, diffStyle: Object): void;
    private tryEmitGridItemUpdateEvent(): void;
    private getHeadersHight(length: number): int;
    private getHeadersWidth(length: number): int;
    private getGroupsHeight(length: number): int;
    private getGroupsWidth(length: number): int;
    private computeTargetY(position: number): int;
    private computeTargetX(position: number): int;
    private updateContentHeight(): void;
    private updateContentWidth(): void;
    private fillDown(pos: number, nextTop: number): void;
    private fillRight(pos: number, nextLeft: number): void;
    private fillUp(pos: number, nextBottom: number): void;
    private fillLeft(pos: number, nextRight: number): void;
    private obtainView(position: number): View;
    private _handleVoiceSelect(view: View, e: VoiceEvent, pos: number): void;
    private obtainGridItem(adjPos: number): View;
    private getChildAt(position: number): View;
    private getChildByPosition(dataPosition: number): View;
    private getChildCount(): number;
    private determineColumns(): void;
    private determineRows(): void;
    private setupChild(position: number, y: number, flowDown: boolean, vertical: boolean): {
        isEnd: boolean;
        child: View;
    };
    private setupHeaderAndFooter(child: View, y: number, flowDown: boolean, vertical: boolean): void;
    private setupChildDown(child: View, position: number, y: number, isVertical: boolean): boolean;
    private setupChildUp(child: View, position: number, y: number, isVertical: boolean): boolean;
    private fillGap(down: boolean): void;
    private fillSpecific(position: number, start: number): void;
    private adjustViewsUpOrDown(): void;
    private adjustViewsLeftOrRight(): void;
    private keepScreenState(): void;
    private detachViewsFromParent(start: number, count: number): void;
    private detachAllChildren(destroy?: boolean): void;
    private layoutChildren(): void;
    /**
     * Get the focus for this view.
     * @fires yunos.ui.view.View#propertychange
     * @override
     * @public
     * @since 3
     *
     */
    public focus(): void;
    private focusFromDirection(direction: String): void;
    private _focusToIndex(index: number): void;
    /**
     * Handle the key up event.
     * @param {yunos.ui.event.KeyEvent} e - the keyevent info
     * @protected
     * @since 3
     *
     */
    protected onKeyUp(e: KeyEvent): boolean;
    private scrollCellSizeForward(): void;
    private scrollCellSizeBackward(): void;
    private onTap(e: GestureEvent): void;
    private onLongPress(e: GestureEvent): void;
    private onDataChange(): void;
    private dispatchGroupEvent(position: number, child: View): void;
    private determinSize(): void;
    private clearRecycler(): void;
    private detachHeaderAndFooter(): void;
    private destroyHeader(): void;
    private destroyGroupHeader(): void;
    private destroyFooter(): void;
    /**
     * Defined gridview handle voice event, default handle page next and page previous.
     * @name yunos.ui.view.GridView#voiceEnabled
     * @type {boolean}
     * @public
     * @override
     * @since 4
     *
     */
    public voiceEnabled: boolean;
    private onSelectPage(index: number): void;
    private static readonly VIEW_TYPE_HEADER = "header";
    private static readonly VIEW_TYPE_FOOTER = "footer";
    /**
     * Enum for GridView ScrollState.
     * @enum {number}
     * @readonly
     * @public
     * @since 5
     */
    public static readonly ScrollState: {
        /**
         * GridView scroll idle and this is the default value.
         * @public
         * @since 5
         */
        IDLE: int;
        /**
         * GridView touch scroll.
         * @public
         * @since 5
         */
        SCROLL: int;
        /**
         * GridView on fling.
         * @public
         * @since 5
         */
        FLING: int;
        /**
         * GridView touch over sroll.
         * @public
         * @since 5
         */
        OVERSCROLL: int;
        /**
         * GridView over fling.
         * @public
         * @since 5
         */
        OVERFLING: int;
    };
}
export = GridView;
