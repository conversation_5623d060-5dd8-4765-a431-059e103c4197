import YObject = require("../core/YObject");
interface IConstructable {
    new (...args: Object[]): {};
}
/**
 * TypeHelper provides you a list of static methods to check and identify the type of given objects.
 * @extends yunos.core.YObject
 * @memberof yunos.util
 * @public
 * @since 2
 */
declare class Type<PERSON>el<PERSON> extends YObject {
    /**
     * Get the type of a given object
     * @param {*} obj - the object
     * @return {string} the type of the object
     * @public
     * @since 2
     */
    public static getType(obj: Object): string;
    /**
     * Get the constructor name of a given object.
     * @param {*} obj - the object
     * @return {string} the class name of the object
     * @public
     * @since 2
     */
    public static getClassName(obj: Object): string;
    /**
     * Check if the given object is an instance of the given class.
     * Primitive values will be considered as an instance of its constructor class.
     * @param {*} obj - the object
     * @param {function} aClass - a class
     * @return {boolean} boolean result indicates whether the object is an instance of the given class
     * @public
     * @since 2
     */
    public static isInstanceOfClass(obj: Object, aClass: IConstructable): boolean;
    /**
     * Check if two given objects are exactly the same.
     * This is functionally the same with Object.is().
     * TypeHelper.exact(NaN, NaN) will return true, while TypeHelper.exact(+0, -0) will return false.
     * @param {*} obj1 - the first object
     * @param {*} obj2 - the second object
     * @return {boolean} boolean result indicates whether the given two objects are exactly the same
     * @public
     * @since 2
     */
    public static exact(obj1: Object, obj2: Object): boolean;
    /**
     * Check if the given object is NaN.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is NaN
     * @public
     * @since 2
     */
    public static isNaN(obj: Object): boolean;
    /**
     * Check if the given object is null.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is Null
     * @public
     * @since 2
     */
    public static isNull(obj: Object): boolean;
    /**
     * Check if the given object is undefined.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is undefined
     * @public
     * @since 2
     */
    public static isUndefined(obj: Object): boolean;
    /**
     * Check if the given object is Null or Undefined.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is Null or undefined
     * @public
     * @since 2
     */
    public static isNone(obj: Object): boolean;
    /**
     * Check if the given object is empty.
     * Following conditions will be considered as empty:
     * 1, object is an empty string. (Note: this function will not trim the string for you)
     * 2, object is an empty array. (Note: Array-like objects are considered arrays here)
     * 3, object is an plain object and has no own properties.
     * 4, object is an empty set.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is empty
     * @public
     * @since 2
     */
    public static isEmpty(obj: Object): boolean;
    /**
     * Check if the given object is primitive.
     * For example, 56, true, "hello" are primitive values.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is primitive
     * @public
     * @since 2
     */
    public static isPrimitive(obj: Object): boolean;
    /**
     * Check if the given number is finite.
     * This function simply invokes Number.isFinite() for the given object.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is finite
     * @public
     * @since 2
     */
    public static isFinite(obj: Object): boolean;
    /**
     * Check if the given object is a Set object.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is a Set instance
     * @public
     * @since 2
     */
    public static isSet(obj: Object): boolean;
    /**
     * Check if the given object is symbol
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is a Symbol object
     * @public
     * @since 2
     */
    public static isSymbol(obj: Object): boolean;
    /**
     * Check if the given object is boolean.
     * If you set allowObject to false, invoke this with an object-wrapped boolean value will return false.
     * @param {*} obj - the object
     * @param {boolean} [allowObject = true] - whether should allow object-wrapped boolean values
     * @return {boolean} boolean result indicates whether the object is Boolean
     * @public
     * @since 2
     */
    public static isBoolean(obj: Object, allowObject?: boolean): boolean;
    /**
     * Check if the given object is an array.
     * If you set allowArrayLikeObject to true, invoke this with an array-like object will return true.
     * Note: Array-like objects may not comforts to Symbol.iterator protocol, which may makes your "for of" loop not working.
     * @param {*} obj - the object
     * @param {boolean} [allowArrayLikeObject = false] - whether should allow array-like objects
     * @return {boolean} boolean result indicates whether the object is an array
     * @public
     * @since 2
     */
    public static isArray(obj: Object, allowArrayLikeObject?: boolean): boolean;
    /**
     * Check if the given object is integer.
     * Will return false if the object is not a number.
     * If you set allowObject to false, invoke this with an object-wrapped number value will always return false.
     * @param {*} obj - the object
     * @param {boolean} [allowObject = true] - whether should allow object-wrapped integers
     * @return {boolean} boolean result indicates whether the object is an integer
     * @public
     * @since 2
     */
    public static isInteger(obj: Object, allowObject?: boolean): boolean;
    /**
     * Check if the given object is string.
     * If you pass allowObject as false, object-wrapped strings are not considered as strings.
     * @param {*} obj - the object
     * @param {boolean} [allowObject = true] - whether should allow object-wrapped strings
     * @return {boolean} boolean result indicates whether the object is a string
     * @public
     * @since 2
     */
    public static isString(obj: Object, allowObject?: boolean): boolean;
    /**
     * Check if the given object is an instance of RegExp.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is a RegExp object
     * @public
     * @since 2
     */
    public static isRegExp(obj: Object): boolean;
    /**
     * Check if the given object is a Date object.
     * If you set allowInvalidDate as false, invalidate dates are not allowed here.
     * Note: if you pass milliseconds here, it will not be considered as a Date object.
     * @param {*} obj - the object
     * @param {boolean} [allowInvalidDate = true] - whether should allow invalid Date object
     * @return {boolean} boolean result indicates whether the object is a Date object
     * @public
     * @since 2
     */
    public static isDate(obj: Object, allowInvalidDate?: boolean): boolean;
    /**
     * Check if the given object is a number.
     * If you set allowObject as false, object-wrapped numbers will not be considered as numbers.
     * @param {*} obj - the object
     * @param {boolean} [allowObject = true] - whether should allow object-wrapped numbers
     * @return {boolean} boolean result indicates whether the object is a number
     * @public
     * @since 2
     */
    public static isNumber(obj: Object, allowObject?: boolean): boolean;
    /**
     * Check if the given object is a function.
     * Note: Classes are considered as functions as well.
     * @param {*} obj - the object
     * @return {boolean} boolean result indicates whether the object is a function
     * @public
     * @since 2
     */
    public static isFunction(obj: Object): boolean;
    /**
     * Check if the given object is an object
     * If you pass mustBePlain as false, some objects like RegExp, Date, Object-wrapped primitives are not considered objects.
     * @param {*} obj - the object
     * @param {boolean} [mustBePlain = false] - whether should require the object to be a plain object
     * @return {boolean} boolean result indicates whether the item is an object
     * @public
     * @since 2
     */
    public static isObject(obj: Object, mustBePlain?: boolean): boolean;
    /**
     * Check if the given object is string and has length equals to 1.
     * If you specify utf8Length to true, this function will check the UTF-8 length of the given string.
     * @param {*} obj - the object
     * @param {boolean} [utf8Length = false] - whether should check the UTF-8 length of the string
     * @param {boolean} [allowObject = true] - whether should allow object-wrapped strings
     * @return {boolean} boolean result indicates whether the object is a char
     * @public
     * @since 2
     */
    public static isChar(obj: Object, utf8Length?: boolean, allowObject?: boolean): boolean;
    /**
     * Check if given objects have same type.
     * @param {...*} objs - the objects
     * @return {boolean} boolean result indicates whether the given objects are of the same type
     * @public
     * @since 2
     */
    public static equalsType(...objs: Object[]): boolean;
}
export = TypeHelper;
