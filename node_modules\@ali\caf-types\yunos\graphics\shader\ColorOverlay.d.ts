import Shader = require("./Shader");
/**
 * Alters the colors of the source item by applying an overlay color.
 * The effect is similar to what happens when a colorized glass is put on top of a grayscale image. The color for the overlay is given in the RGBA format.
 * @extends yunos.graphics.shader.Shader
 * @memberof yunos.graphics.shader
 * @public
 * @since 2
 */
declare class ColorOverlay extends Shader {
    private _color;
    /**
     * Defines the RGBA color value which is used to colorize the source.
     * @name yunos.graphics.shader.ColorOverlay#color
     * @type {string}
     * @throws {TypeError} If parameter is not a valid color string.
     * @public
     * @since 2
     */
    public color: string;
}
export = ColorOverlay;
