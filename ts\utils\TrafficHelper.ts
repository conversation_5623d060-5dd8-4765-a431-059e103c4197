/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */
"use strict";
// jscs:disable requireDotNotation
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import AlertDialog = require("yunos/ui/widget/AlertDialog");
const iRes = require("yunos/content/resource/Resource").getInstance();
import Settings = require("yunos/content/Settings");
import DataResolver = require("yunos/provider/DataResolver");
import DataObserver = require("yunos/provider/DataObserver");
import NetQuery = require("yunos/net/NetQuery");
const TrafficThreshold = NetQuery.TrafficThreshold;
const iVideoModel = require("../model/VideoModel").getInstance();
const iUserTrackHelper = require("../utils/UserTrackHelper").getInstance();
import log = require("../utils/log");
import Utils = require("../utils/Utils");
const iNetworkState = require("../monitor/NetworkState").getInstance();
import Consts = require("../Consts");
const iPageInstance = require("../index").getInstance();
const TAG = "TrafficHelper";

const REMAIN_DATA = 101; // 101:>100M

interface IPageInfo {
    page_name: string;
    video_id: string;
    video_title: string;
    cpId: string;
}

interface IAlertDialog extends AlertDialog {
    type: number;
}

/**
 * 监听设置视频流量开关状态变化
 */
class NetworkEnableChangeObserver extends DataObserver {
    private _mUri: Object;
    private _mCallback: () => void;
    constructor(uri: string, callback: () => void) {
        log.I(TAG, "NetworkEnableChangeObserver constructor", uri);
        super(uri);
        this._mUri = uri;
        this._mCallback = callback;
    }

    onChange(uri: Object) {
        log.I(TAG, "NetworkEnableChangeObserver onChange", uri);
        if (this._mUri !== uri) {
            return;
        }

        if (this._mCallback) {
            this._mCallback();
        }
    }
}

class TrafficHelper {
    private static _instance: TrafficHelper;
    private _trafficChangedCallback: (arg0?: boolean, arg1?: boolean) => boolean;
    private _dialog: IAlertDialog;
    private _trafficType: string;
    private _query: any;

    // 设置-流量管理，“付费流量权限”和“视频”两个开关需要监听
    private _allNetworkDataEnableInitCompleted: boolean;
    private _allNetworkDataEnableValue: number;
    private _videoNetworkDataEnableInitCompleted: boolean;
    private _videoNetworkDataEnableValue: number;
    private _networkRemainingData: number;
    private _failReason: string;
    private _pageInfo: IPageInfo;

    constructor() {
        log.I(TAG, "TrafficHelper");
        this._query = new NetQuery();
        this._trafficType = "";
        this._allNetworkDataEnableInitCompleted = false;
        this._allNetworkDataEnableValue = 1;
        this._videoNetworkDataEnableInitCompleted = false;
        this._videoNetworkDataEnableValue = 1;
        this._networkRemainingData = REMAIN_DATA;
        this._failReason = Consts.FailReason.NETWORK;
        this._pageInfo = {
            page_name: "NA",
            video_id: "NA",
            video_title: "NA",
            cpId: iUserTrackHelper.CP_ID
        };
        this._initTrafficType(null);
        this._initTrafficSwitch();
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new TrafficHelper();
        }
        return this._instance;
    }

    _initTrafficType(callback: () => void) {
        this._query.getExtendedTrafficType(Consts.VIDEO_PAGE_URI, (error: number, trafficType: string) => {
            if (error !== NetQuery.ErrorCode.SUCCESS) {
                log.I(TAG, "getExtendedTrafficType failed", error);
                return;
            }

            log.I(TAG, "getExtendedTrafficType", trafficType);
            if (trafficType) {
                this._trafficType = trafficType;
                if (callback) {
                    callback();
                }
            }
        });
    }

    _initTrafficSwitch() {
        let resolver = DataResolver.getInstance(iPageInstance);
        this._getAllNetworkDataEnableValue(resolver);
        this._getVideoNetworkDataEnableValue(resolver);

        let allEnableUri = Settings.Secure.getUriFor(Settings.Secure.NETWORK_DATA_ENABLE_ALL);
        let allEnableChangeObserver = new NetworkEnableChangeObserver(allEnableUri, () => {
            this._getAllNetworkDataEnableValue(resolver);
        });
        resolver.registerObserver(allEnableChangeObserver, null);

        let videoEnableUri = Settings.Secure.getUriFor(Settings.Secure.NETWORK_DATA_ENABLE_VIDEO);
        let videoEnableChangeObserver = new NetworkEnableChangeObserver(videoEnableUri, () => {
            this._getVideoNetworkDataEnableValue(resolver);
        });
        resolver.registerObserver(videoEnableChangeObserver, null);
    }

    _getAllNetworkDataEnableValue(resolver: DataResolver) {
        Settings.Secure.getNumberWithDefault(resolver, Settings.Secure.NETWORK_DATA_ENABLE_ALL, 1, (value: number) => {
            log.D(TAG, "Settings.Secure.NETWORK_DATA_ENABLE_ALL", value);
            this._allNetworkDataEnableInitCompleted = true;
            this._allNetworkDataEnableValue = value;
            this._handleNetworkDataEnableChanged();
        });
    }

    _getVideoNetworkDataEnableValue(resolver: DataResolver) {
        Settings.Secure.getNumberWithDefault(resolver, Settings.Secure.NETWORK_DATA_ENABLE_VIDEO, 1, (value: number) => {
            log.D(TAG, "Settings.Secure.NETWORK_DATA_ENABLE_VIDEO", value);
            this._videoNetworkDataEnableInitCompleted = true;
            this._videoNetworkDataEnableValue = value;
            this._handleNetworkDataEnableChanged();
        });
    }

    _handleNetworkDataEnableChanged() {
        if (iNetworkState.isWiFi()) {
            log.I(TAG, "_handleNetworkDataEnableChanged, wifi connected");
            return;
        }

        if (!this._allNetworkDataEnableInitCompleted || !this._videoNetworkDataEnableInitCompleted) {
            return;
        }

        log.I(TAG, "_handleNetworkDataEnableChanged", this._allNetworkDataEnableValue, this._videoNetworkDataEnableValue);
        let isNeedShowDialog = false;
        if (this._allNetworkDataEnableValue === 0 || this._videoNetworkDataEnableValue === 0) {
            this._failReason = Consts.FailReason.SWITCH;
            if (this._trafficChangedCallback) {
                isNeedShowDialog = this._trafficChangedCallback();
                log.I(TAG, "isNeedShowDialog run into:", isNeedShowDialog);
            }
        } else {
            if (this._trafficChangedCallback) {
                isNeedShowDialog = this._trafficChangedCallback(true, true);
                log.I(TAG, "isNeedShowDialog run into:", isNeedShowDialog);
            }
            this._closeDialog();
            this._checkRemainingData(isNeedShowDialog);
        }
    }

    /**
     * 检查流量状态
     * 1.若当前连接的是WiFi则返回true
     * 2.若当前连接的是4G，检查剩余流量，剩余流量大于0返回true，反之则返回false
     */
    checkTrafficState(info: IPageInfo, showDialog = true) {
        log.I(TAG, "checkTrafficState", this._allNetworkDataEnableInitCompleted, this._allNetworkDataEnableValue,
            this._videoNetworkDataEnableInitCompleted, this._videoNetworkDataEnableValue, this._networkRemainingData,
            iNetworkState.trafficCtrlEnabled, showDialog);
        if (info) {
            this._pageInfo = info;
            this._pageInfo.cpId = iUserTrackHelper.CP_ID;
        }

        if (iNetworkState.isWiFi()) {
            log.I(TAG, "checkTrafficState, wifi connected");
            return true;
        }

        if (this._allNetworkDataEnableValue === 0 || this._videoNetworkDataEnableValue === 0 || !iNetworkState.trafficCtrlEnabled) {
            if (showDialog) {
                this._showDialog(Consts.TrafficPromptType.OPEN);
            }
            this._failReason = Consts.FailReason.SWITCH;
            return false;
        }

        if (this._allNetworkDataEnableInitCompleted && this._videoNetworkDataEnableInitCompleted) {
            if (this._networkRemainingData === 0) {
                this._checkRemainingData(false);
                if (showDialog) {
                    this._showDialog(Consts.TrafficPromptType.EXHAUSTION);
                }
                this._failReason = Consts.FailReason.TRAFFIC;
                return false;
            } else {
                this._checkRemainingData(showDialog);
            }
        }

        // for usertrack
        this._failReason = Consts.FailReason.NETWORK;
        return true;
    }

    /**
     * 检查剩余流量
     */
    _checkRemainingData(showDialog = true) {
        log.I(TAG, "_checkRemainingData", this._trafficType, showDialog);
        if (!this._trafficType) {
            this._initTrafficType(() => {
                this._getExtendedRemainingTraffic(showDialog);
            });
        } else {
            this._getExtendedRemainingTraffic(showDialog);
        }
    }

    _getExtendedRemainingTraffic(showDialog = true) {
        this._query.getExtendedRemainingTraffic(this._trafficType, TrafficThreshold.THRESHOLD_ANY, (error: number, value: number) => {
            if (error !== NetQuery.ErrorCode.SUCCESS) {
                log.I(TAG, "getExtendedRemainingTraffic failed", error);
                return;
            }

            log.I(TAG, "getExtendedRemainingTraffic", value);
            if (value > 0) {
                this._closeDialog();
                if (this._trafficChangedCallback) {
                    this._trafficChangedCallback(true);
                }
            } else {
                if (showDialog) {
                    this._showDialog(Consts.TrafficPromptType.EXHAUSTION);
                }
                this._failReason = Consts.FailReason.TRAFFIC;
                if (this._trafficChangedCallback) {
                    this._trafficChangedCallback();
                }
            }
            this._networkRemainingData = value;
        });
    }

    /**
     * 显示流量提示信息（按进程）
     * 1.第一次提示显示对话框方式
     * 2.第二次开始使用toast方式
     */
    _showDialog(type: number) {
        if (this._dialog && this._dialog.isShowing()) {
            log.I(TAG, "_showDialog, dialog is showing");
            return;
        }

        log.I(TAG, "_showDialog", type);
        if (iVideoModel.isTrafficPrompt(type)) {
            Utils.showToast(type === Consts.TrafficPromptType.OPEN ?
                iRes.getString("TRAFFIC_OPEN") :
                iRes.getString("TRAFFIC_EXHAUSTION"));
            return;
        }
        iVideoModel.saveTrafficPrompt(type, true);

        let operateType: string = undefined;
        let dialog: IAlertDialog = <IAlertDialog> new AlertDialog();
        dialog.once("result", (index) => {
            if (dialog.type === Consts.TrafficPromptType.OPEN) {
                if (index === 0) {
                    this._jumpToSetting();
                }
                operateType = index === 0 ? "open" : "cancel";
            } else if (dialog.type === Consts.TrafficPromptType.EXHAUSTION) {
                if (index === 0) {
                    this._jumpToPersonalCenter();
                }
                operateType = index === 0 ? "buy" : "cancel";
            }
        });
        dialog.once("close", () => {
            let paramObj = {
                operate_type: operateType ? operateType : "close"
            };
            if (dialog.type === Consts.TrafficPromptType.OPEN) {
                iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SWITCH_SET_POP,
                    iUserTrackHelper.SWITCH_SET_POP_CLICK, paramObj);
                iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_SWITCH_SET_POP, this._pageInfo);
            } else if (dialog.type === Consts.TrafficPromptType.EXHAUSTION) {
                iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_BUY_TRAFFIC_POP,
                    iUserTrackHelper.BUY_TRAFFIC_POP_CLICK, paramObj);
                iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_BUY_TRAFFIC_POP, this._pageInfo);
            }
            dialog.destroy(true);
            this._dialog = null;
        });
        this._setContentView(dialog, type);
        dialog.show();
        this._dialog = dialog;
    }

    _setContentView(dialog: IAlertDialog, type: number) {
        dialog.type = type;
        switch (type) {
            case Consts.TrafficPromptType.OPEN:
                dialog.title = iRes.getString("TRAFFIC_TITLE");
                dialog.message = iRes.getString("TRAFFIC_OPEN");
                dialog.buttons = [
                    {
                        text: iRes.getString("TRAFFIC_BTN_OPEN"),
                        style: AlertDialog.ButtonStyle.Positive
                    },
                    {
                        text: iRes.getString("TRAFFIC_BTN_CANCEL"),
                        style: AlertDialog.ButtonStyle.Normal
                    }
                ];
                iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_SWITCH_SET_POP);
                break;
            case Consts.TrafficPromptType.EXHAUSTION:
                dialog.title = iRes.getString("TRAFFIC_TITLE");
                dialog.message = iRes.getString("TRAFFIC_EXHAUSTION");
                dialog.buttons = [
                    {
                        text: iRes.getString("TRAFFIC_BTN_BUY"),
                        style: AlertDialog.ButtonStyle.Positive
                    },
                    {
                        text: iRes.getString("TRAFFIC_BTN_CANCEL")
                    }
                ];
                iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_BUY_TRAFFIC_POP);
                break;
        }
        for (let i = 0; i < dialog.buttons.length; i++) {
            (<{ defaultVoiceCommand: { keepEffective: boolean } }><object>
                dialog.getButton(i)).defaultVoiceCommand.keepEffective = false;
            dialog.getButton(i).focusable = false;
        }
    }

    _closeDialog() {
        log.I(TAG, "_closeDialog");
        if (this._dialog) {
            this._dialog.destroy(true);
            this._dialog = null;
        }
    }

    isDialogShowing() {
        log.I(TAG, "isDialogShowing");
        if (this._dialog && this._dialog.isShowing()) {
            return true;
        }
        return false;
    }

    /**
     * 流量开关关闭时，跳转到设置
     */
    _jumpToSetting() {
        log.I(TAG, "_jumpToSetting");
        const PageLink = require("yunos/page/PageLink");
        const link = new PageLink("page://systemsetting.ivi.com/systemsetting");
        link.eventName = "network";
        link.data = "video.alios.cn";
        link.needActive = true;
        link.inGroup = false;
        iPageInstance.sendLink(link, (err: Object, result: Object) => {
            if (err) {
                log.I(TAG, "_jumpToSetting, err ", err);
            } else {
                log.I(TAG, "_jumpToSetting, result ", result);
            }
        });
    }

    /**
     * 流量不足时，跳转到个人中心
     */
    _jumpToPersonalCenter() {
        log.I(TAG, "_jumpToPersonalCenter", this._trafficType);
        const PageLink = require("yunos/page/PageLink");
        const link = new PageLink("page://personalcenter.ivi.com/personalcenter");
        link.eventName = "flowMallViewLink";
        let orderType = {channelId: this._trafficType};
        link.data = JSON.stringify(orderType);
        iPageInstance.sendLink(link, (err: Object, result: Object) => {
            if (err) {
                log.I(TAG, "_jumpToPersonalCenter, err ", err);
            } else {
                log.I(TAG, "_jumpToPersonalCenter, result ", result);
            }
        });
    }

    get failReason() {
        return this._failReason;
    }

    registerTrafficChangeListener(callback: (arg0?: boolean) => boolean) {
        this._trafficChangedCallback = callback;
    }

    unRegisterTrafficChangeListener() {
        this._trafficChangedCallback = null;
    }
}

export = TrafficHelper;
