import YObject = require("yunos/core/YObject");
declare class PhoneServiceAdapter extends YObject {
    private _iface;
    private bus;
    private service;
    private _permission;
    constructor(permission?: string);
    registerphoneDial(cb: (number: string) => Promise<number>): void;
    emitsignal_line_incoming(callName: string, callNumber: string, callNumberType: number): boolean;
    emitsignal_line_count_changed(count: number): boolean;
    destroy(): void;
}
export = PhoneServiceAdapter;
