import YObject = require("../../core/YObject");
/**
 * Enum of Path.ArcSize
 * @ignore
 */
export declare enum ArcSize {
    /**
     * smaller of arc pair
     * @ignore
     */
    mall_ArcSize = 0,
    /**
     * larger of arc pair
     * @ignore
     */
    Large_ArcSize = 1
}
/**
 * Enum of Path.Direction
 * describes whether contour is clockwise or counterclockwise.
 * @ignore
 */
export declare enum Direction {
    /**
     * contour travels clockwise
     * @ignore
     */
    CW_Direction = 0,
    /**
     * contour travels counterclockwise
     * @ignore
     */
    CCW_Direction = 1
}
/**
 * Enum of Path.FillType
 * FillType determines whether overlaps are filled or form holes.
 * @ignore
 */
export declare enum FillType {
    /**
     * enclosed by a non-zero sum of contour
     * @ignore
     */
    Winding = 0,
    /**
     * enclosed by an odd number of contours
     * @ignore
     */
    EvenOdd = 1,
    /**
     * enclosed by a zero sum of contour directions
     * @ignore
     */
    InverseWinding = 2,
    /**
     * enclosed by an even number of contours
     * @ignore
     */
    InverseEvenOdd = 3
}
/**
 * Wrapper class for SkPath addon binding
 * A Path may contain geometry or empty.
 * When used to draw a filled area, Path describe whether the fill is inside or outside the geometry.
 * It also describes the winding rule used to fill overlapping contours.
 * @ignore
 */
export declare class Path extends YObject {
}
