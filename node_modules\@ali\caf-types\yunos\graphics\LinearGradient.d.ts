import Gradient = require("./Gradient");
/**
 * <p>LinearGradient the color rect.</p>
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 1
 */
/**
 * <p>Create a LinearGradient that draws a linear gradient along a line.</p>
 * @example
 *
 * class MyView extends View {
 *   onDraw(ctx) { // override
 *     let linearGradient = ctx.createLinearGradient(0, 0, 0, 300);
 *     // let linearGradient = new LinearGradient(0, 0, 0, 300);
 *     linearGradient.addColorStop(0, "#ffffff");
 *     linearGradient.addColorStop(1, "#000000");
 *     ctx.fillStyle = linearGradient;
 *     ctx.fillRect(0, 0, 300, 300);
 *   }
 * }
 *
 * @extends yunos.graphics.Gradient
 * @memberof yunos.graphics
 * @public
 * @since 3
 *
 */
declare class LinearGradient extends Gradient {
    private x0: number;
    private y0: number;
    private x1: number;
    private y1: number;
    /**
     * <p>Create a linear gradient.</p>
     * @param {number} x0 - The x axis of the coordinate of the start point.
     * @param {number} y0 - The y axis of the coordinate of the start point.
     * @param {number} x1 - The x axis of the coordinate of the end point.
     * @param {number} y1 - The y axis of the coordinate of the end point.
     * @public
     * @since 1
     */
    public constructor(x0: number, y0: number, x1: number, y1: number);
}
export = LinearGradient;
