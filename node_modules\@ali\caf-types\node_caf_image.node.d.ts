export class CafBitmap {
    constructor(v1?: Object, v2?: number, v3?: number);

    getWidth(): number;

    getHeight(): number;

    setImageBuffer(buffer: Buffer): boolean;

    destroy(): void;

    reset(): void;

    getStride(): number;

    getImageBuffer(): Buffer;

    setPixels(v0: Buffer, v1: number, v2: number, v3: number, v4: number,
              v5: number, v6: number, v7: number, v8: number): Buffer;

    setPixel(v1: number, v2: number, v3: Object): void;

    getPixel(v: number, v2: number): number[];

    getRawData(): Buffer;

    getPixels(v: number, v2: number, v3: number, v4: number): Buffer;

    getFormat(): number;

    clone(): number | Buffer;

    setNativeBitmapHandle(v: number): void;

    getNativeBitmapHandle(): number;

    getNativeBitmapRefHandle(): number;
}

export interface CafBuffer {
    data: CafBuffer,
    size: number
}

export class Image {
    width: number;
    height: number;

    constructor(width?: number, height?: number);

    getImageInfoFromFile(key: string): { width: number, height: number };

    loadFromBuffer(buffer: Buffer, start?: number, end?: number, callback?: Function): void;

    loadFromFile(file: string, callback: Function): void;

    copyFromImage(src: Image, x: number, y: number, width: number, height: number): void;

    fillColor(red: number, green: number, blue: number, alpha: number, callback?: Function): void;

    fillColorSync(red: number, green: number, blue: number, alpha: number): void;

    drawImage(img: Image, x: number, y: number, callback?: Function): void;

    toBuffer(type: string | number, config: Object, callback?: Function): { data: Buffer };

    rotate(degree: number, callback?: Function): void;

    rotateSync(degree: number): void;

    save(file: string, type: string | number, config: Object, callback: Function): void;

    getRawData(callback?: Function): Object;

    getPixelsBufferHandle(): number;

    setPixelsBufferHandle(v: number): void;

    setAlphaPremul(): void;

    resize(width: number, height: number, filter: Function | string,
           callback?: Function | string): void;

    destroy(): void;
}

export class ScreenShot {
    captureScreenByID( id: number ): number | Buffer;

    captureScreenByIDAsync( id: number, callback: Function ): Object;

    captureScreen(v1: number, v2: number): number | Buffer;

    captureScreenAsync(v1: number, v2: number, callback: Function): Object;

    connectDisplay(): boolean;
}

export class ImageFilter {
    static FilterType: {
        UNKNOWN: 0x0,
        BLUR: 0x1,
        BRIGHTNESS: 0x2,
        CONTRAST: 0x3,
        DROP_SHADOW: 0x4,
        GRAYSCALE: 0x5,
        HUE_ROTATE: 0x6,
        INVERT: 0x7,
        OPACITY: 0x8,
        SATURATE: 0x9,
        SEPIA: 0xa,
    };

    processBitmap(h1: number, h2: number, filters: Object[],
                  callback: (error: Error) => void): void;

    processBitmapSync(h1: number, h2: number, filters: Object[]): void;
}

export const TYPE_PNG: number;
export const TYPE_JPEG: number;
export const TYPE_GIF: number;
export const TYPE_BMP: number;
export const TYPE_RAW: number;
export const TYPE_WBMP: number;
export const TYPE_WEBP: number;
