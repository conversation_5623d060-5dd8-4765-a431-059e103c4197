import AlertDialog = require("./AlertDialog");
import DatePicker = require("./DatePicker");
import KeyEvent = require("yunos/ui/event/KeyEvent");
import { StringObjectKV } from "../util/TypeHelper";
/**
 * <p>A simple dialog containing an DatePicker.</p>
 * @extends yunos.ui.widget.AlertDialog
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class DatePickerDialog extends AlertDialog {
    private _datePicker;
    private _pickerWidth;
    private _pickerHeight;
    private _pickerHorizontalPadding;
    private _pickerVerticalPadding;
    private _isAutoTheme;
    private _pickerOkColor;
    /**
     * <p>Create a DatePickerDialog.</p>
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this DatePickerDialog.</p>
     * @param {boolean} recursive - destroy the children in the DatePickerDialog if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * <p>Destructor that destroy this DatePickerDialog.</p>
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>Get the DatePicker contained in this dialog.</p>
     * @name yunos.ui.widget.DatePickerDialog#datePicker
     * @type {yunos.ui.widget.DatePicker}
     * @readonly
     * @public
     * @since 2
     */
    public readonly datePicker: DatePicker;
    /**
     * <p>Sets the current date include year, month and day.</p>
     * @method yunos.ui.widget.DatePickerDialog#updateTime
     * @public
     * @since 1
     */
    /**
     * <p>Sets time with given year, month and day, if year, month or day does not exist, will have the current date instead.</p>
     * @method yunos.ui.widget.DatePickerDialog#updateTime
     * @param {number} year - need to set the year
     * @param {number} month - need to set the month
     * @param {number} day - need to set the day
     * @throws {TypeError} If year, month or day is invalid.
     * @public
     * @since 2
     */
    public updateTime(year: number, month: number, day: number): void;
    /**
     * <p>Get the content view. Use a TimePickerDialog as the custom view.</p>
     * @returns {yunos.ui.view.View} the content view.
     * @protected
     * @since 1
     */
    /**
     * <p>Get the content view. Use a DatePicker as the custom view.</p>
     * @returns {yunos.ui.view.CompositeView} the content view.
     * @override
     * @public
     * @since 6
     */
    public getContentView(): DatePicker;
    /**
     * <p>Listener function for button tap event.</p>
     * @param {number} index - the button index.
     * @protected
     * @since 1
     */
    protected onTap(index: number): void;
    /**
     * <p>This property always returns the empty string.</p>
     * @name yunos.ui.widget.DatePickerDialog#message
     * @type {string}
     * @public
     * @override
     * @since 4
     *
     */
    public message: string;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.DatePickerDialog#defaultStyleName
     * @type {string}
     * @default "DatePickerDialog"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: "DatePickerDialog" | "DatePickerDialogAuto";
    /**
     * <p>Apply theme style for DatePickerDialog.</p>
     * @method applyStyle
     * @override
     * @protected
     * @since 1
     */
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: StringObjectKV): void;
    /**
     * Handle the key up event.
     * @method NumberPickerDialog#onKeyUp
     * @param {yunos.ui.event.KeyEvent} e - the keyevent info
     * @protected
     * @since 1
     */
    protected onKeyUp(e: KeyEvent): boolean;
}
export = DatePickerDialog;
