import Page = require("yunos/page/Page");
import View = require("yunos/ui/view/View");
/**
 * <p>InputMethod is the base class of yunos input method application. <br>
 * Developer should implement a class derived from this class InputMethod, <br>
 * which takes charge of create input method type page, communication with <br>
 * InputMethod framework(include input method api interface and callbacks).</p>
 * @example
 * var InputMethod = require("yunos/inputmethod/InputMethod");
 * class MyInputMethod extends InputMethod {
 *      onCreate() {
 *          super.onCreate();
 *          ......
 *      }
 *
 *      onCandidateUpdate(type, candidates, attribute) {
 *          ......
 *      }
 *      .......
 * }
 * module.exports = MyInputMethod;
 * @extends yunos.page.Page
 * @memberof yunos.inputmethod
 * @friend
 *
 */
declare class InputMethod extends Page {
    mImeView: View;
    /**
     * <p>The InputMethod is also a Page but whith the type Page.PageType.SERVICE</p>
     * @friend
     * @ignore
     * @override
     */
    readonly type: string;
    /**
     * @friend
     */
    constructor();
    /**
     * <p>the method is called when candidates updated. </p>
     * <PERSON>elo<PERSON> should override this method to update candidates
     * @example
     * var InputMethod = require("yunos/inputmethod/InputMethod");
     * class MyInputMethod extends InputMethod {
     *      onCandidateUpdate(type, candidates, attribute) {
     *          // update your candidates here
     *          ......
     *      }
     * }
     * @param {yunos.inputmethod.InputMethod.CandidateType} type - the type of candidates updated
     * @param {string[]} candidates - the candidates list
     * @param {string[]} attribute - the candidates attribute list
     * @friend
     */
    onCandidateUpdate(type: number, candidates: string[], attribute: number[]): void;
    /**
     * <p>the method is called when candidates reset. </p>
     * Developer should override this method to update candidates
     * @example
     * var InputMethod = require("yunos/inputmethod/InputMethod");
     * class MyInputMethod extends InputMethod {
     *      onCandidateReset() {
     *          // update your candidates here
     *          ......
     *      }
     * }
     * @friend
     */
    onCandidateReset(): void;
    /**
     * <p>the method is called when preedit string updated. </p>
     * Developer should override this method to update preedit string
     * @example
     * var InputMethod = require("yunos/inputmethod/InputMethod");
     * class MyInputMethod extends InputMethod {
     *      onPreeditUpdate(preedit, attribute) {
     *          // update your preedit string here
     *          ......
     *      }
     * }
     * @param {string} preedit - the preedit string
     * @param {string} attribute - the preedit string attribute
     * @friend
     */
    onPreeditUpdate(preedit: string, attribute: number[]): void;
    /**
     * <p>the method is called when preedit string reset. </p>
     * Developer should override this method to update preedit string
     * @example
     * var InputMethod = require("yunos/inputmethod/InputMethod");
     * class MyInputMethod extends InputMethod {
     *      onPreeditUpdate() {
     *          // update your preedit string here
     *          ......
     *      }
     * }
     * @friend
     */
    onPreeditReset(): void;
    /**
     * <p>the method is called when the input context reset. </p>
     * @friend
     */
    onContextReset(): void;
    /**
     * <p>the method is called when preedit path updated. </p>
     * Developer should override this method to update preedit path
     * @example
     * var InputMethod = require("yunos/inputmethod/InputMethod");
     * class MyInputMethod extends InputMethod {
     *      onPreeditPathUpdate(preeditPath) {
     *          // update your preedit path here
     *          ......
     *      }
     * }
     * @param {string} preeditPath - the preedit path
     * @friend
     */
    onPreeditPathUpdate(preeditPath: string[]): void;
    /**
     * <p>the method is called when engine has data to sync. </p>
     * @param {string} data - the data to sync
     * @friend
     */
    onEngineDataSync(data: string): void;
    /**
     * <p>the method is called when need update cloud data. </p>
     * @param {number} data - the cloud data to update
     * @friend
     */
    onCloudUpdate(data: string[]): void;
    /**
     * <p>the method is called when mode updated. </p>
     * @param {number} mode - the mode to update
     * @friend
     */
    onModeUpdate(mode: number): void;
    /**
     * <p>the method is called when key event updated. </p>
     * @param {string} keyevent - the key event
     * @friend
     */
    onKeyEvent(keyevent: string): void;
    /**
     * <p>the method is called when host package changed. </p>
     * @param {string} name - the new package name
     * @friend
     */
    onPackageName(name: string): void;
    /**
     * <p>the method is called when display changed. </p>
     * @param {number} displayid - the new display id
     * @friend
     */
    onDisplayId(displayid: number): void;
    /**
     * <p>the method is called when host package changed. </p>
     * @param {number} hint - the new hint
     * @param {number} purpose - the new purpose
     * @param {number} options - the new options
     * @friend
     */
    onContentType(hint: number, purpose: number, options: number): void;
    /**
     * <p>the method is called when package name updated. </p>
     * @param {string} packagename - the package name updated
     * @friend
     */
    onPackageNameUpdate(packagename: string): void;
    /**
      * <p>Override the onCreate function to create input method's main window, with <br>
      * the type Window.SurfaceType.InputMethod. And also register the callback and start <br>
      * Input Method listener thread. The subclass should **MUST** call super.onCreate() first</p>
      * @example
      * class MyInputMethod extends InputMethod {
      *     onCreate() {
      *          super.onCreate();
      *          .......
      *     }
      * }
      * @protected
      */
    onCreate(): void;
    /**
     * <p>the method is called when receiving message. </p>
     * @param {string} name
     * @private
     */
    processNodeMsg(): void;
    /**
     * Start the input method work thread
     * @friend
     */
    startInputMethodService(): void;
    /**
     * Notify to -> ims -> weston, ime has been finished layouting
     * @friend
     */
    finishLayout(): void;
    /**
     * <p> has navigation bar or not <p>
     * @friend
     */
    onImeViewHeightChanged(value: Object): void;
    /**
     * <p> create Input View <p>
     * @friend
     */
    onCreateInputView(): null | View;
    /**
     * <p> rotation changed <p>
     * @param {number} rotation 0 means portrait and 1 means landscape, 3 means 270 landscape
     * @friend
     */
    onRotationChanged(rotation: number): void;
    /**
     * <p> environment changed, inluding rotation, etc <p>
     * @protected
     */
    onEnvironment(env: {
        rotation: number;
    }): boolean | void;
    /**
     * <p> Set the IMEngine mode <p>
     * @param {string} mode - The mode to set
     * @friend
     */
    setIMEngineMode(mode: number): void;
    /**
     * <p> Set the IMEngine status <p>
     * @param {string} cmd - The cmd to set
     * @param {string} mode - The mode to set
     * @friend
     */
    setIMEngineStatus(cmd: number, mode: string): void;
    /**
     * <p> Send a KeyEvent to an IMEngineInstance<p>
     * @param {yunos.inputmethod.InputMethod.KeyCode} key - The keyevent to send
     * @friend
     */
    sendKeyEvent(key: string): void;
    /**
     * <p> Forward a KeyEvent to a client application directly <p>
     * @param {string} key - The keyevent to forward
     * @friend
     */
    forwardKeyEvent(key: string): void;
    /**
     * <p> Select a candidate in current candidate list <br>
     * When user click a candidate this method should be called<p>
     * @param {number} index - The index of the selected candidate
     * @friend
     */
    selectCandidate(index: number): void;
    /**
     * <p> Select a preedit in current preedit path <br>
     * When user click a preedit, this method should be called<p>
     * @param {number} index - The index of selected preedit
     * @friend
     */
    selectPreedit(index: number): void;
    /**
     * <p> Commit a string to client application directly <p>
     * @param {string} text - The text to commit
     * @param {string} mode - The mode to commit
     * @friend
     */
    commitString(text: string, mode: number): void;
    /**
     * <p> Set the Input mode <p>
     * @param {string} mode - The mode to set "EN"|"CH"
     * @friend
     */
    setInputMode(mode: string): void;
    /**
     * <p> Sync the data with IMEngine <p>
     * @param {string} str - The data to sync
     * @friend
     */
    syncEngineData(str: string): void;
    /**
     * <p> Reset the IMEngine <p>
     * @friend
     */
    resetEngineIse(): void;
    /**
     * <p> Commit the preedit string <p>
     * @param {string} preedit - The preedit to commit
     * @friend
     */
    commitPreeditString(preedit: string): void;
    /**
     * <p> Request to stop the work thread, should be called when page exit<p>
     * @friend
     */
    exit(): void;
    /**
     * <p> Enum of the input mode EN/CH</p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly InputMode: {
        /**
         * Set the input mode, english mode
         * @friend
         */
        EN: string;
        /**
         * Set the input mode, chinese mode
         * @friend
         */
        CH: string;
    };
    /**
     * <p> Enum of the IMEngine status</p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly IMEngineStatus: {
        /**
         * the predict engine status
         * @friend
         */
        PREDICT_CMD: int;
        /**
         * the zici engine status
         * @friend
         */
        ZICI_CMD: int;
        /**
         * the jiafan engine status
         * @friend
         */
        JIANFAN_CMD: int;
        /**
         * the mohuyin engine status
         * @friend
         */
        MOHUYIN_CMD: int;
        /**
         * add hot words
         * @friend
         */
        HOTWORD_CMD: int;
    };
    /**
     * <p> Enum of the IMEngine Mode</p>
     * @enum {number}
     * @friend
     */
    static readonly IMEngineMode: {
        /**
         * the chinese engine mode
         * @friend
         */
        MODE_CHINESE_PATTERN: int;
        /**
         * the english engine mode
         * @friend
         */
        MODE_ENGLISH_PREDICT_PATTERN: int;
        /**
         * the chinese predict engine mode
         * @friend
         */
        MODE_CHINESE_PREDICT_PATTERN: int;
        /**
         * the jiugongge chinese engine mode
         * @friend
         */
        MODE_JIUGONGGE_CHINESE_PATTERN: int;
        /**
         * the jiugongge english engine mode
         * @friend
         */
        MODE_JIUGONGGE_ENGLISH_PATTERN: int;
        /**
         * the hand write engine mode
         * @friend
         */
        MODE_HAND_WRITE_PATTERN: int;
        /**
         * the cang jie engine mode
         * @friend
         */
        MODE_CANGJIE_PATTERN: int;
        /**
         * the zhuyin engine mode
         * @friend
         */
        MODE_ZHUYIN_PATTERN: int;
        /**
         * the english normal engine mode
         * @friend
         */
        MODE_ENGLISH_NORMAL_PATTERN: int;
    };
    /**
     * <p> Enum of the input method layout type </p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly ContentLayout: {
        /**
         * the normal layout
         * @friend
         */
        LAYOUT_NORMAL: int;
        /**
         * the alpha layout
         * @friend
         */
        LAYOUT_ALPHA: int;
        /**
         * the digits layout
         * @friend
         */
        LAYOUT_DIGITS: int;
        /**
         * the number layout
         * @friend
         */
        LAYOUT_NUMBER: int;
        /**
         * the phone layout
         * @friend
         */
        LAYOUT_PHONE: int;
        /**
         * the url layout
         * @friend
         */
        LAYOUT_URL: int;
        /**
         * the email layout
         * @friend
         */
        LAYOUT_EMAIL: int;
        /**
         * the name layout
         * @friend
         */
        LAYOUT_NAME: int;
        /**
         * the password layout
         * @friend
         */
        LAYOUT_PASSWORD: int;
        /**
         * the data layout
         * @friend
         */
        LAYOUT_DATA: int;
        /**
         * the time layout
         * @friend
         */
        LAYOUT_TIME: int;
        /**
         * the date time layout
         * @friend
         */
        LAYOUT_DATETIME: int;
        /**
         * the terminal layout
         * @friend
         */
        LAYOUT_TERMINAL: int;
    };
    /**
     * <p> Enum of the input type </p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly InputType: {
        /**
         * the input type not specified
         * @friend
         */
        HINT_NONE: int;
        /**
         * the default input type
         * @friend
         */
        HINT_DEFAULT: int;
        /**
         * the pass word input type
         * @friend
         */
        HINT_PASSWORD: int;
        /**
         * the auto completion input type
         * @friend
         */
        HINT_AUTO_COMPLETION: int;
        /**
         * the auto correction input type
         * @friend
         */
        HINT_AUTO_CORRECTION: int;
        /**
         * the auto capitalization input type
         * @friend
         */
        HINT_AUTO_CAPITALIZATION: int;
        /**
         * the lower case input type
         * @friend
         */
        HINT_LOWERCASE: int;
        /**
         * the upper case input type
         * @friend
         */
        HINT_UPPERCASE: int;
        /**
         * the title case input type
         * @friend
         */
        HINT_TITLECASE: int;
        /**
         * the hidden text input type
         * @friend
         */
        HINT_HIDDEN_TEXT: int;
        /**
         * the sensitive data input type
         * @friend
         */
        HINT_SENSITIVE_DATA: int;
        /**
         * the latin input type
         * @friend
         */
        HINT_LATIN: int;
        /**
         * the multiline input type
         * @friend
         */
        HINT_MULTILINE: int;
    };
    /**
     * <p> Enum of the input method options </p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly ImeOptions: {
        /**
         * the ime option is not specified
         * @friend
         */
        OPTIONS_UNSPECIFIED: int;
        /**
         * the none ime option
         * @friend
         */
        OPTIONS_NONE: int;
        /**
         * the go ime option
         * @friend
         */
        OPTIONS_GO: int;
        /**
         * the search ime option
         * @friend
         */
        OPTIONS_SEARCH: int;
        /**
         * the send ime option
         * @friend
         */
        OPTIONS_SEND: int;
        /**
         * the next ime option
         * @friend
         */
        OPTIONS_NEXT: int;
        /**
         * the done ime option
         * @friend
         */
        OPTIONS_DONE: int;
        /**
         * the previous ime option
         * @friend
         */
        OPTIONS_PREVIOUS: int;
        /**
         * the join option
         * @friend
         */
        OPTIONS_JOIN: int;
    };
    /**
     * <p> Enum of the candidate type </p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly CandidateType: {
        /**
         * the chinese normal candidate type
         * @friend
         */
        CH_NORMAL: int;
        /**
         * the english normal candidate type
         * @friend
         */
        EN_NORMAL: int;
        /**
         * the prediction candidate type
         * @friend
         */
        PREDICTION: int;
        /**
         * the chinese part candidate type
         * @friend
         */
        CH_PART: int;
        /**
         * the symbol candidate type
         * @friend
         */
        SYMBOL: int;
    };
    /**
     * <p> Enum of the candidate attributes </p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly CandidateAttributes: {
        /**
         * the candidate attribute is none
         * @friend
         */
        ATTR_NONE: int;
        /**
         * the decorate candidate attribute
         * @friend
         */
        ATTR_DECORATE: int;
        /**
         * the foreground candidate attribute
         * @friend
         */
        ATTR_FOREGROUND: int;
        /**
         * the background candidate attribute
         * @friend
         */
        ATTR_BACKGROUND: int;
        /**
         * the kernel candidate attribute
         * @friend
         */
        ATTR_KERNEL_DICT: int;
        /**
         * the user dict candidate attribute
         * @friend
         */
        ATTR_USER_DICT: int;
        /**
         * the english decoder candidate attribute
         * @friend
         */
        ATTR_ENGLISH_DECODER: int;
        /**
         * the hand write candidate attribute
         * @friend
         */
        ATTR_HAND_WRITE: int;
        /**
         * the contact dict candidate attribute
         * @friend
         */
        ATTR_CONTACT_DICT: int;
        /**
         * the hotword dict candidate attribute
         * @friend
         */
        ATTR_HOTWORD_DICT: int;
        /**
         * the cellword dict candidate attribute
         * @friend
         */
        ATTR_CELLWORD_DICT: int;
        /**
         * the suggest word candidate attribute
         * @friend
         */
        ATTR_SUGGESTWORD_DICT: int;
        /**
         * the cangjie dict candidate attribute
         * @friend
         */
        ATT_CANGJIE_DICT: int;
        /**
         * the zhuyin dict candidate attribute
         * @friend
         */
        ATTR_ZHUYIN_DICT: int;
        /**
         * the correction candidate attribute
         * @friend
         */
        ATTR_CORRECTION: int;
        /**
         * the  correction decode candidate attribute
         * @friend
         */
        ATTR_CORRECTION_DECODE: int;
        /**
         * the  emotion dict candidate attribute
         * @friend
         */
        ATTR_EMOTION_DICT: int;
        /**
         * the english dict candidate attribute
         * @friend
         */
        ATTR_ENGLISH_DICT: int;
        /**
         * the symbol dict candidate attribute
         * @friend
         */
        ATTR_SYMBOL_DICT: int;
        /**
         * the stroke dict candidate attribute
         * @friend
         */
        ATTR_STROKE_DICT: int;
        /**
         * the whole match candidate attribute
         * @friend
         */
        ATTR_WHOLE_MATCH: int;
    };
    /**
     * <p> Enum of the key event code </p>
     * @enum {number}
     * @readonly
     * @friend
     */
    static readonly KeyCode: {
        /**
         * KEY_CLEAR
         * @friend
         */
        KEY_CLEAR: string;
        /**
         * KEY_SPACE
         * @friend
         */
        KEY_SPACE: string;
        /**
         * KEY_TAB
         * @friend
         */
        KEY_TAB: string;
        /**
         * KEY_EXCLAM
         * @friend
         */
        KEY_EXCLAM: string;
        /**
         * KEY_QUOTEDBL
         * @friend
         */
        KEY_QUOTEDBL: string;
        /**
         * KEY_NUMBERSIGN
         * @friend
         */
        KEY_NUMBERSIGN: string;
        /**
         * KEY_DELLAR
         * @friend
         */
        KEY_DELLAR: string;
        /**
         * KEY_PERCENT
         * @friend
         */
        KEY_PERCENT: string;
        /**
         * KEY_AMPERSAND
         * @friend
         */
        KEY_AMPERSAND: string;
        /**
         * KEY_APOSTROPHE
         * @friend
         */
        KEY_APOSTROPHE: string;
        /**
         * KEY_QUOTERIGHT
         * @friend
         */
        KEY_QUOTERIGHT: string;
        /**
         * KEY_PARENLEFT
         * @friend
         */
        KEY_PARENLEFT: string;
        /**
         * KEY_PARENRIGHT
         * @friend
         */
        KEY_PARENRIGHT: string;
        /**
         * KEY_ASTERISK
         * @friend
         */
        KEY_ASTERISK: string;
        /**
         * KEY_PLUS
         * @friend
         */
        KEY_PLUS: string;
        /**
         * KEY_COMMA
         * @friend
         */
        KEY_COMMA: string;
        /**
         * KEY_MINUS
         * @friend
         */
        KEY_MINUS: string;
        /**
         * KEY_PERIOD
         * @friend
         */
        KEY_PERIOD: string;
        /**
         * KEY_SLASH
         * @friend
         */
        KEY_SLASH: string;
        /**
         * KEY_LEFTSINGLEQUOTE
         * @friend
         */
        KEY_LEFTSINGLEQUOTE: string;
        /**
         * KEY_KP_SEPARATOR
         * @friend
         */
        KEY_KP_SEPARATOR: string;
        /**
         * KEY_0
         * @friend
         */
        KEY_0: string;
        /**
         * KEY_1
         * @friend
         */
        KEY_1: string;
        /**
         * KEY_2
         * @friend
         */
        KEY_2: string;
        /**
         * KEY_3
         * @friend
         */
        KEY_3: string;
        /**
         * KEY_4
         * @friend
         */
        KEY_4: string;
        /**
         * KEY_5
         * @friend
         */
        KEY_5: string;
        /**
         * KEY_6
         * @friend
         */
        KEY_6: string;
        /**
         * KEY_7
         * @friend
         */
        KEY_7: string;
        /**
         * KEY_8
         * @friend
         */
        KEY_8: string;
        /**
         * KEY_9
         * @friend
         */
        KEY_9: string;
        /**
         * KEY_SHARP
         * @friend
         */
        KEY_SHARP: string;
        /**
         * KEY_LEFT
         * @friend
         */
        KEY_LEFT: string;
        /**
         * KEY_UP
         * @friend
         */
        KEY_UP: string;
        /**
         * KEY_DOWN
         * @friend
         */
        KEY_DOWN: string;
        /**
         * KEY_RIGHT
         * @friend
         */
        KEY_RIGHT: string;
        /**
         * KEY_A
         * @friend
         */
        KEY_A: string;
        /**
         * KEY_B
         * @friend
         */
        KEY_B: string;
        /**
         * KEY_C
         * @friend
         */
        KEY_C: string;
        /**
         * KEY_D
         * @friend
         */
        KEY_D: string;
        /**
         * KEY_E
         * @friend
         */
        KEY_E: string;
        /**
         * KEY_F
         * @friend
         */
        KEY_F: string;
        /**
         * KEY_G
         * @friend
         */
        KEY_G: string;
        /**
         * KEY_H
         * @friend
         */
        KEY_H: string;
        /**
         * KEY_I
         * @friend
         */
        KEY_I: string;
        /**
         * KEY_J
         * @friend
         */
        KEY_J: string;
        /**
         * KEY_K
         * @friend
         */
        KEY_K: string;
        /**
         * KEY_L
         * @friend
         */
        KEY_L: string;
        /**
         * KEY_M
         * @friend
         */
        KEY_M: string;
        /**
         * KEY_N
         * @friend
         */
        KEY_N: string;
        /**
         * KEY_O
         * @friend
         */
        KEY_O: string;
        /**
         * KEY_P
         * @friend
         */
        KEY_P: string;
        /**
         * KEY_Q
         * @friend
         */
        KEY_Q: string;
        /**
         * KEY_R
         * @friend
         */
        KEY_R: string;
        /**
         * KEY_S
         * @friend
         */
        KEY_S: string;
        /**
         * KEY_T
         * @friend
         */
        KEY_T: string;
        /**
         * KEY_U
         * @friend
         */
        KEY_U: string;
        /**
         * KEY_V
         * @friend
         */
        KEY_V: string;
        /**
         * KEY_W
         * @friend
         */
        KEY_W: string;
        /**
         * KEY_X
         * @friend
         */
        KEY_X: string;
        /**
         * KEY_Y
         * @friend
         */
        KEY_Y: string;
        /**
         * KEY_Z
         * @friend
         */
        KEY_Z: string;
        /**
         * KEY_a
         * @friend
         */
        KEY_a: string;
        /**
         * KEY_b
         * @friend
         */
        KEY_b: string;
        /**
         * KEY_c
         * @friend
         */
        KEY_c: string;
        /**
         * KEY_d
         * @friend
         */
        KEY_d: string;
        /**
         * KEY_e
         * @friend
         */
        KEY_e: string;
        /**
         * KEY_f
         * @friend
         */
        KEY_f: string;
        /**
         * KEY_g
         * @friend
         */
        KEY_g: string;
        /**
         * KEY_h
         * @friend
         */
        KEY_h: string;
        /**
         * KEY_i
         * @friend
         */
        KEY_i: string;
        /**
         * KEY_j
         * @friend
         */
        KEY_j: string;
        /**
         * KEY_k
         * @friend
         */
        KEY_k: string;
        /**
         * KEY_l
         * @friend
         */
        KEY_l: string;
        /**
         * KEY_m
         * @friend
         */
        KEY_m: string;
        /**
         * KEY_n
         * @friend
         */
        KEY_n: string;
        /**
         * KEY_o
         * @friend
         */
        KEY_o: string;
        /**
         * KEY_p
         * @friend
         */
        KEY_p: string;
        /**
         * KEY_q
         * @friend
         */
        KEY_q: string;
        /**
         * KEY_r
         * @friend
         */
        KEY_r: string;
        /**
         * KEY_s
         * @friend
         */
        KEY_s: string;
        /**
         * KEY_t
         * @friend
         */
        KEY_t: string;
        /**
         * KEY_u
         * @friend
         */
        KEY_u: string;
        /**
         * KEY_v
         * @friend
         */
        KEY_v: string;
        /**
         * KEY_w
         * @friend
         */
        KEY_w: string;
        /**
         * KEY_x
         * @friend
         */
        KEY_x: string;
        /**
         * KEY_y
         * @friend
         */
        KEY_y: string;
        /**
         * KEY_z
         * @friend
         */
        KEY_z: string;
        /**
         * KEY_BACK
         * @friend
         */
        KEY_BACK: string;
        /**
         * KEY_BACKSPACE
         * @friend
         */
        KEY_BACKSPACE: string;
        /**
         * KEY_BACKSPACE_SHIFT
         * @friend
         */
        KEY_BACKSPACE_SHIFT: string;
        /**
         * KEY_ENTER
         * @friend
         */
        KEY_ENTER: string;
        /**
         * KEY_CAPSLOCK
         * @friend
         */
        KEY_CAPSLOCK: string;
        /**
         * KEY_QUESTION
         * @friend
         */
        KEY_QUESTION: string;
        /**
         * KEY_ALT
         * @friend
         */
        KEY_ALT: string;
        /**
         * KEY_DOT
         * @friend
         */
        KEY_DOT: string;
        /**
         * KEY_PAGE_UP
         * @friend
         */
        KEY_PAGE_UP: string;
        /**
         * KEY_PAGE_DOWN
         * @friend
         */
        KEY_PAGE_DOWN: string;
    };
}
export = InputMethod;
