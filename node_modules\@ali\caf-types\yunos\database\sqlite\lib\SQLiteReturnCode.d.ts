declare const SQLiteReturnCode: {
    /** Constant of return value for success */
    SQLiteOk: int;
    /** Constant of return value for: SQL error or missing database */
    SQLiteError: int;
    /** Constant of return value for: Internal logic error in SQLite */
    SQLiteInternal: int;
    /** Constant of return value for: Access permission denied in SQLite */
    SQLitePerm: int;
    /** Constant of return value for: Callback routine requested an abort */
    SQLiteAbort: int;
    /** Constant of return value for: The database file is locked */
    SQLiteBusy: int;
    /** Constant of return value for: A table in the database is locked */
    SQLiteLocked: int;
    /** Constant of return value for: A malloc() failed */
    SQLiteNomem: int;
    /** Constant of return value for: Attempt to write a readonly database */
    SQLiteReadonly: int;
    /** Constant of return value for: Operation was interrupted */
    SQLiteInterrupt: int;
    /** Constant of return value for: Some kind of disk I/O error occurred */
    SQLiteIoErr: int;
    /** Constant of return value for: The database disk image is malformed */
    SQLiteCorrupt: int;
    /** Constant of return value for: Unknown opcode in sqlite */
    SQLiteNotFound: int;
    /** Constant of return value for: Insertion failed because database is full */
    SQLiteFull: int;
    /** Constant of return value for: Unable to open the database file */
    SQLiteCantOpen: int;
    /** Constant of return value for: Database lock protocol error */
    SQLiteProtocol: int;
    /** Constant of return value for: Database is empty */
    SQLiteEmpty: int;
    /** Constant of return value for: The database schema changed */
    SQLiteSchema: int;
    /** Constant of return value for: String or BLOB exceeds size limit */
    SQLiteTooBig: int;
    /** Constant of return value for: Abort due to constraint violation */
    SQLiteConstraint: int;
    /** Constant of return value for: Data type mismatch */
    SQLiteMismatch: int;
    /** Constant of return value for: Library used incorrectly */
    SQLiteMisuse: int;
    /** Constant of return value for: Uses OS features not supported on host */
    SQLiteNoLFS: int;
    /** Constant of return value for: Authorization denied */
    SQLiteAuth: int;
    /** Constant of return value for: Auxiliary database format error */
    SQLiteFormat: int;
    /** Constant of return value for: Parameter to bind out of range */
    SQLiteRange: int;
    /** Constant of return value for: File opened that is not a database file */
    SQLiteNotADb: int;
    /** Constant of return value for: move/query which has another row ready */
    SQLiteRow: int;
    /** Constant of return value for: executeCommand() has finished executing */
    SQLiteDone: int;
};
export = SQLiteReturnCode;
