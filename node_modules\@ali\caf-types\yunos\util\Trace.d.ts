import YObject = require("../core/YObject");
/**
 * Helper class to write trace event to system's trace, and can be collected
 * by atrace tool. Used to diagnose performance of certain section of your code.
 * @extends yunos.core.YObject
 * @memberof yunos.util.
 * @public
 * @since 3
 *
 */
declare class Trace extends YObject {
    /**
     * Write a trace event to system's trace buffer, indicate that the method has begun,
     * and can be collected and shown by atrace tools.
     * Must be followed by a call to endAppTrace using the same tag.
     * @param {string} methodName - method name of your trace event, will be shown in trace file when collected.
     * @public
     * @since 3
     *
     */
    public static beginAppTrace(methodName: string): void;
    /**
     * Write a trace event to system's trace buffer and indicate that the previous beginAppTrace has ended.
     * @public
     * @since 3
     *
     */
    public static endAppTrace(): void;
    private static isTagEnabled(tag: number): boolean;
    private static beginTrace(tag: number, methodName: string): void;
    private static endTrace(tag: number): void;
    private static getTraceInitedForTest(): Object;
    private static getEnabledTagsForTest(): Object;
    private static setTagsUpdateCallbackForTest(cb: (...args: Object[]) => void): void;
    private static testJSTagValid(): Object;
    private static Tag: {
        ALWAYS: number;
        GRAPHICS: number;
        INPUT: number;
        VIEW: number;
        WEBVIEW: number;
        WINDOW_MANAGER: number;
        PAGE_MANAGER: number;
        SYNC_MANAGER: number;
        AUDIO: number;
        VIDEO: number;
        CAMERA: number;
        AGILNG: number;
        PERF: number;
        HAL: number;
        APP: number;
        RESOURCES: number;
        VM: number;
        DALVIK: number;
        SEED: number;
        PAGE_START_UP: number;
        DATABASE: number;
        NETWORK: number;
        UBUS: number;
        PAGE: number;
        ASR: number;
        TTS: number;
        MAGICCUBED: number;
        XIAOYUN: number;
        MAP: number;
    };
}
export = Trace;
