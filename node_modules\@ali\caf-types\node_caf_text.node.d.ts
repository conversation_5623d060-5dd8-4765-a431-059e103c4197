import Size = require("yunos/graphics/Size");


declare class CafText {
    static measureText(v1: CafText.CafFont, v2: string, v3: number, v4: number, v5: number, v6: number, v7: number): {
        width: number, height: number, lineCount: number
    };

    static loadTextGlyphs(v1: CafText.CafFont, v2: string): Size;

    static glyphsToBitmap(v1: CafText.CafFont, v2: Object[], v3: number): { nativeBitmapHandle: number };

    static releaseBuffer(v: Object): void;
}

declare namespace CafText {
    export interface Metrics {
        width: number;
        height?: number;
        lineCount?: number;
        ascent?: number;
        descent?: number;
    }

    export class CafFont {
        constructor(v1: string, v2: number, v3: number, v4: number);

        destroy(): void;

        getMetrics(): Metrics;
    }
}

export = CafText;
