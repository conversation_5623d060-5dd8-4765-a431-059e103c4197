import CompositeView = require("../view/CompositeView");
import View = require("../view/View");
import PropertyAnimation = require("../animation/PropertyAnimation");
import TouchEvent = require("../event/TouchEvent");
import GestureEvent = require("../event/GestureEvent");
interface IStyle {
    maskColor: string;
    maskOpacity: number;
    edgeSize: number;
    drawerWidth: number;
}
/**
 * <p>DrawerView acts as a top-level container for window content that allows for <br>
 * interactive "drawer" views to be pulled out from one or both vertical edges of the window.</p>
 * <p>The user can set one of left drawer and right drawer, or set all of them,<br>
 * but will open one at the same time.</p>
 * @example
 * const drawerView = new DrawerView();
 * drawerView.contentView = new CompositeView();
 * drawerView.leftView = new ListView();
 * drawerView.rightView = new ListView();
 * @extends yunos.ui.view.CompositeView
 * @memberof yunos.ui.widget
 * @public
 * @since 1
 */
declare class DrawerView extends CompositeView {
    private _contentView;
    private _leftDrawer;
    private _rightDrawer;
    private _animationLeft;
    private _animationRight;
    private _leftDrawerLock;
    private _rightDrawerLock;
    private _maskColor;
    private _maskView;
    private _maskOpacity;
    private _edgeSize;
    private _drawerWidth;
    private _animationGroup;
    private _isRightDrawerOpen;
    private _isLeftDrawerOpen;
    private _singlePoint;
    private _moveHandle;
    private _swiped;
    private _rightSlider;
    private _leftSlider;
    private _touchXStart;
    private _touchYStart;
    private _moveIn;
    private _moveHorizontal;
    private _touchLeftDistance;
    private _startDistance;
    private _touchRightDistance;
    private _animationMask;
    /**
     * Create a drawerView.
     * @public
     * @since 1
     */
    /**
     * <p>Destructor that destroy this DrawerView.</p>
     * @param {boolean} recursive - destroy the children in the DrawerView if the value is true.
     * @public
     * @override
     * @since 2
     */
    /**
     * Destroy this drawerView.
     * @public
     * @since 1
     */
    public destroy(recursive?: boolean): void;
    /**
     * <p>Primary content,<br>
     * as the first child of the drawerView with width and height equal to parent.</p>
     * <p>The default background is transparent.</p>
     * @name yunos.ui.widget.DrawerView#contentView
     * @type {yunos.ui.view.View}
     * @throws {TypeError} If parameter is not instance of view.
     * @public
     * @since 1
     */
    public contentView: View;
    /**
     * <p>Drawer view emerge from left, with height equal to parent,<br>
     * the width can customize, not recommended for more than three quarters of his father.</p>
     * <p>The default background is transparent.</p>
     * @name yunos.ui.widget.DrawerView#leftView
     * @type {yunos.ui.view.View}
     * @throws {TypeError} If parameter is not instance of view.
     * @public
     * @since 1
     */
    public leftView: View;
    /**
     * <p>Drawer view emerge from right, with height equal to parent,<br>
     * the width can customize, not recommended for more than three quarters of his father.</p>
     * <p>The default background is transparent.</p>
     * @name yunos.ui.widget.DrawerView#rightView
     * @type {yunos.ui.view.View}
     * @throws {TypeError} If parameter is not instance of view.
     * @public
     * @since 1
     */
    public rightView: View;
    /**
     * <p>Enable or disable interaction with the left drawer,<br>
     * This allows the application to restrict the user's ability to open or close the left drawer.</br>
     * DrawerLayout will still respond to calls to openDrawer, closeDrawer.</p>
     * <p>you can choose DrawerView.LockMode.UnLock, DrawerView.LockMode.Open,<br>
     * or DrawerView.LockMode.Close.</p>
     * @name yunos.ui.widget.DrawerView#leftDrawerLock
     * @type {yunos.ui.widget.DrawerView.LockMode}
     * @default yunos.ui.widget.DrawerView.LockMode.UnLock
     * @throws {TypeError} If type of parameter is not DrawerView.LockMode.
     * @public
     * @since 1
     */
    public leftDrawerLock: number;
    /**
     * <p>Enable or disable interaction with the right drawer,<br>
     * This allows the application to restrict the user's ability to open or close the right drawer.</br>
     * DrawerLayout will still respond to calls to openDrawer, closeDrawer.</p>
     * <p>you can choose DrawerView.LockMode.UnLock, DrawerView.LockMode.Open,<br>
     * or DrawerView.LockMode.Close.</p>
     * @name yunos.ui.widget.DrawerView#rightDrawerLock
     * @type {yunos.ui.widget.DrawerView.LockMode}
     * @default yunos.ui.widget.DrawerView.LockMode.UnLock
     * @throws {TypeError} If type of parameter is not DrawerView.LockMode.
     * @public
     * @since 1
     */
    public rightDrawerLock: number;
    /**
     * Set a color to use for the scrim that obscures primary content while a drawer is open.
     * @name yunos.ui.widget.DrawerView#maskColor
     * @type {string}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public maskColor: string;
    /**
     * Set a opacity to use for the scrim that obscures primary content while a drawer is open.
     * @name yunos.ui.widget.DrawerView#maskOpacity
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @throws {RangeError} If parameter is not between 0 and 1.
     * @public
     * @since 1
     */
    public maskOpacity: number;
    /**
     * Defined edge sliding region of the drawerView.
     * @name yunos.ui.widget.DrawerView#edgeSize
     * @type {number}
     * @throws {TypeError} If parameter is not a number.
     * @public
     * @since 1
     */
    public edgeSize: number;
    /**
     * <p>Get the default style name of this view.</p>
     * @name yunos.ui.widget.DrawerView#defaultStyleName
     * @type {string}
     * @default "DrawerView"
     * @readonly
     * @public
     * @override
     * @since 4
     *
     */
    public readonly defaultStyleName: string;
    /**
     * Open the specified drawer view, will close others open before.
     * @param {yunos.ui.view.View} view - left drawer or right drawer to open
     * @param {boolean} animation - Whether opening of the drawer should be animated
     * @fires yunos.ui.widget.DrawerView#draweropen
     * @public
     * @since 1
     */
    public openDrawer(view: View, animation?: boolean): void;
    /**
     * Close the specified drawer view.
     * @param {yunos.ui.view.View} view - left drawer or right drawer to close
     * @param {boolean} animation - Whether closing of the drawer should be animated
     * @fires yunos.ui.widget.DrawerView#drawerclose
     * @public
     * @since 1
     */
    public closeDrawer(view: View, animation?: boolean): void;
    /**
     * Check if the given drawer view is currently in an open state.
     * @param {yunos.ui.view.View} view - drawer view to check
     * @return {boolean} returns whether the view is opened
     * @public
     * @since 1
     */
    public isDrawerOpen(view: View): boolean;
    /**
     * Remove left or right drawer, but not destroy.
     * @param {yunos.ui.view.View} view - Drawer view to check
     * @public
     * @since 1
     */
    public removeDrawer(view: View): void;
    /**
     * Handle the touch start event processing.
     * @param {yunos.ui.event.TouchEvent} ev - the touch event info
     * @protected
     * @since 1
     */
    protected onTouchStart(ev: TouchEvent): void;
    /**
     * Handle the touch move event processing.
     * @param {yunos.ui.event.TouchEvent} ev - the touch event info
     * @protected
     * @since 1
     */
    protected onTouchMove(ev: TouchEvent): void;
    /**
     * Handle the touch end event processing.
     * @protected
     * @since 1
     */
    protected onTouchEnd(ev: TouchEvent): void;
    /**
     * Handle the touch cancel event processing.
     * @protected
     * @since 1
     */
    protected onTouchCancel(ev: TouchEvent): void;
    /**
     * Handle the swipe event processing.
     * @param {yunos.ui.gesture.GestureEvent} ev - the swipe gesture event info.
     * @protected
     * @since 1
     */
    protected onSwipe(ev: GestureEvent): void;
    private doLayout(): void;
    private createMaskView(): void;
    private removeMaskView(): void;
    private initAnimationGroup(): void;
    private removeAnimationGroup(): void;
    /**
     * Implement this to apply style
     * @param {Object} style - style config
     * @override
     * @protected
     * @since 4
     *
     */
    protected applyStyle(style?: IStyle): void;
    private initDrawer(view: View, position: number): void;
    private onMaskTap(): void;
    private onAnimationComplete(): void;
    private checkTouchOutDrawer(view: View, distance: number, distanceY: number): boolean;
    private handleEnd(ev: TouchEvent): void;
    private setDrawerLock(view: View, lockMode: number): void;
    private checkDrawerViewAbsoluteGravity(view: View, checkFor: number): boolean;
    private doAnimationGroup(drawerAnimation: PropertyAnimation, drawerEndLeft: number, maskOpacity: number, drawerEndState: number, duration: number): void;
    private doEnableView(view: View, enable: boolean): void;
    private static readonly Position: {
        Left: int;
        Right: int;
    };
    private static readonly AnimationState: {
        ShowLeft: int;
        HideLeft: int;
        ShowRight: int;
        HideRight: int;
    };
    /**
     * Enum for drawer Lock Mode.
     * @enum {number}
     * @readonly
     * @public
     * @since 1
     */
    public static readonly LockMode: {
        /**
         * (default) - The drawer is unlocked.
         * @public
         * @since 1
         */
        UnLock: int;
        /**
         * The drawer is locked open. The user may not close it, though the app may close it programmatically.
         * @public
         * @since 1
         */
        Open: int;
        /**
         * The drawer is locked closed. The user may not open it, though the app may open it programmatically.
         * @public
         * @since 1
         */
        Close: int;
    };
}
export = DrawerView;
