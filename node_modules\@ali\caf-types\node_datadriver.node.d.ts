declare namespace DataDriver {
    interface Result {
        err: number;
        msg: string;
    }

    type GetDataCallback = (err: Result, data: Object) => void;
    type SignalListener = (err: Result, signalName: string, data: Object) => void;
}

/* eslint-disable no-redeclare */
declare class DataDriver {
    static configure(key: string, options?: Object) : DataDriver.Result;
    static setAccount(accountName: string, accountId: string, options?: Object) : DataDriver.Result;
    static setSessionProperties(options?: Object) : DataDriver.Result;
    static setPageProperties(pageName: string, options?: Object) : DataDriver.Result;
    static enterPage(pageName: string, options?: Object) : DataDriver.Result;
    static leavePage(pageName: string, options?: Object) : DataDriver.Result;
    static sendEvent(pageName: string, eventName: string, options?: Object, eventValue?: number) :
        DataDriver.Result;
    static clickButton(pageName: string, buttonName: string, options?: Object) : DataDriver.Result;
    static selectItem(pageName: string, controlName: string, selectedIndex: number,
        options?: Object) : DataDriver.Result;
    static enterView(pageName: string, viewName: string, options?: Object) : DataDriver.Result;
    static leaveView(pageName: string, viewName: string, options?: Object) : DataDriver.Result;
    static sendCrashEvent(target: string, callstack: string, options?: Object) : DataDriver.Result;
    static inputText(pageName: string, controlName: string, content: string, options?: Object) :
        DataDriver.Result;

    static getVehicleInfo(filter: [string], callback: DataDriver.GetDataCallback) :
        DataDriver.Result;
    static getVehicleInfoSync(filter: [string]) : Object;
    static getUserInfo(callback: DataDriver.GetDataCallback) : DataDriver.Result;
    static getUserInfoSync() : Object;
    static getSceneInfo(filter: [string], callback: DataDriver.GetDataCallback) : DataDriver.Result;
    static getSceneInfoSync(filter: [string]) : Object;
    static getLocationInfo(callback: DataDriver.GetDataCallback) : DataDriver.Result;
    static getLocationInfoSync() : Object;
    static subscribeSignal(signalNames: [string], listener: DataDriver.SignalListener) :
        DataDriver.Result;
    static unsubscribeSignal(signalNames: [string]) : DataDriver.Result;
    static releaseDataboard() : DataDriver.Result;
}

export = DataDriver;
