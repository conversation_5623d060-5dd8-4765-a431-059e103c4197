export interface MapServiceResult {
    status: string;
    funcName: string;
    result: {
        statusType: string;
        data: Object;
    };
}
export interface INavigateOptions {
    currentQuickNavi?: boolean;
    name: string;
    lng: number;
    lat: number;
}
export declare enum ErrorCode {
    RESP_SUCCESS = "success",
    RESP_FAILURE = "failure",
    RESP_UN_AUTHORIZED = "unAuthorized",
    RESP_UN_SUPPORTED = "unSupported",
    RESP_NO_RESULT = "noResult"
}
export declare enum StatusType {
    NetWorkError = "networkError",
    ParameterError = "parameterError",
    UnKnownError = "unKnownError",
    Success = "success",
    Failure = "failure"
}
export declare enum NaviStatusInfo {
    NoNavi = "noNavi",
    Routing = "routing",
    SelectRoute = "selectRoute",
    Naving = "naving"
}
