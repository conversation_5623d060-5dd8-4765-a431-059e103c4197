export declare class PublisherAddon {
    constructor(topic:string, typename:string, isReliable:boolean, id:number,
            cb:(status:number, guid:string)=>void, participant:Object);
    isConnected:()=>number
    write:(data:Object, typename:string)=>boolean
}

export declare class ParticipantAddon {
    constructor();
    new (): ParticipantAddon
    remove:()=>boolean
    removeSubscriber:(sub:Object)=>boolean
    removePublisher:(pub:Object)=>boolean
    registerType:(type:Object, typename:string)=>boolean
    unregisterType:(typeName:string)=>boolean
}

export declare class SerializerAddon {
    constructor(serialize:Object, cb:()=>number);
    writeUint8:(value:number)=>void
    writeUint16:(value:number)=>void
    writeUint32:(value:number)=>void
    writeInt8:(value:number)=>void
    writeInt16:(value:number)=>void
    writeInt32:(value:number)=>void
    writeInt64:(value:number)=>void
    writeFloat:(value:number)=>void
    writeDouble:(value:number)=>void
    writeBool:(value:boolean)=>void
    writeString:(value:string)=>void
    writeInt32Array:(value:Array<number>)=>void
    writeFloatArray:(value:Array<number>)=>void
    writeDoubleArray:(value:Array<number>)=>void
    readUint8:()=>number
    readUint16:()=>number
    readUint32:()=>number
    readInt8:()=>number
    readInt16:()=>number
    readInt32:()=>number
    readInt64:()=>number
    readFloat:()=>number
    readDouble:()=>number
    readBool:()=>boolean
    readString:()=>string
    readInt32Array:()=>Array<number>
    readFloatArray:()=>Array<number>
    readDoubleArray:()=>Array<number>
}

export declare class SubscriberAddon {
    constructor(topic:string, typename:string, isReliable:boolean, id:number,
            cb:(status:number, guid:string)=>void, participant:Object);
    registerNewDataCallback:(cb:(sub:Object)=>void)=>boolean
    takeNextData:(serializer:Object, typename:string)=>Object
    isConnected:()=>number
    getUnreadCount:()=>number
    waitForUnreadMessage:()=>void
}

export declare class TopicDataTypeAddon {
    constructor(cb:(name:string, value:Object)=>void, size:number);
    getName:()=>string
    setName:(name:string)=>void
}

export declare class ImageTypeAddon {
    constructor();
    new (): ImageTypeAddon
    getName:()=>string
    setName:(name:string)=>void
}

export declare class ImageAddon {
    constructor(sec:number, nanosec:number, frame_id:string, height:number,
            width:number, encoding:string, is_bigendian:number, step:number,
            data:Buffer);
    new (): ImageAddon
    getSec:()=>number
    setSec:(value:number)=>void
    getNanosec:()=>number
    setNanosec:(value:number)=>void
    getFrameId:()=>string
    setFrameId:(value:string)=>void
    getHeight:()=>number
    setHeight:(value:number)=>void
    getWidth:()=>number
    setWidth:(value:number)=>void
    getEncoding:()=>string
    setEncoding:(value:string)=>void
    getIsBigendian:()=>number
    setIsBigendian:(value:number)=>void
    getStep:()=>number
    setStep:(value:number)=>void
    getData:()=>Buffer
    setData:(value:Buffer)=>void
}