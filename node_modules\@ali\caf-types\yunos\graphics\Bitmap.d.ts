/// <reference types="node" />
import YObject = require("../core/YObject");
import ImageData = require("./ImageData");
import Color = require("./Color");
/**
 * <p>The Bitmap class is used to save and handle image.</p>
 *
 * <p>To draw an [Bitmap]{@link yunos.graphics.Bitmap}, use one of the methods on the [Context]{@link yunos.graphics.Context} class, such as [Context.drawImage]{@link yunos.graphics.Context.drawImage}.
 * Also an [Bitmap]{@link yunos.graphics.Bitmap} can be used as the source of [ImageView]{@link yunos.ui.view.ImageView}.</p>
 *
 * @example
 *
 * const Image = require("yunos/multimedia/Image");
 * var iv = new ImageView();
 * var image = new Image();
 * image.loadFile(uri, (err) => {
 *   if (!err) {
 *     image.getRawData((err, buffer) => {
 *       if (!err) {
 *         let bitmap = new Bitmap(buffer.data);
 *         log.D("Bitmap", "width&height", bitmap.width, bitmap.height);
 *         // iv.src = bitmap;
 *         // ctx.drawImage(bitmap);
 *         bitmap.destroy();
 *       }
 *       image.destroy();
 *     });
 *   }
 * });
 *
 *
 * @extends yunos.core.YObject
 * @memberof yunos.graphics
 * @public
 * @since 2
 */
declare class Bitmap extends YObject {
    private _ownNative;
    /**
     * <p>Constructor that create a Bitmap.</p>
     * @param {number} width - width of bitmap
     * @param {number} height - height of bitmap
     * @throws {TypeError} If width or height is not number
     * @public
     * @since 2
     */
    /**
     * <p>Constructor that create a Bitmap.</p>
     * @param {number} width - width of bitmap
     * @param {number} height - height of bitmap
     * @param {yunos.graphics.Bitmap.Format} format - pixel format of bitmap
     * @throws {TypeError} If width or height is not number
     * @public
     * @since 3
     *
     */
    public constructor(width: number | Buffer | object, height?: number, format?: number);
    /**
     * <p>Destroy that destroy the Bitmap.</p>
     * @public
     * @since 2
     */
    public destroy(): void;
    /**
     * <p>Create a clone of the Bitmap.</p>
     * @return {yunos.graphics.Bitmap} Return a clone of the Bitmap object.
     * @override
     * @public
     * @since 3
     *
     */
    public clone(): Bitmap;
    /**
     * <p>Width of current bitmap.</p>
     * @name  yunos.graphics.Bitmap#width
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly width: number;
    /**
     * <p>Height of current bitmap.</p>
     * @name  yunos.graphics.Bitmap#height
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly height: number;
    /**
     * <p>The bytes of scanline in the Bitmap.</p>
     * @name  yunos.graphics.Bitmap#stride
     * @type {number}
     * @readonly
     * @public
     * @since 2
     */
    public readonly stride: number;
    /**
     * <p>The Pixel format of current Bitmap.</p>
     * @name  yunos.graphics.Bitmap#pixelFormat
     * @type {yunos.graphics.Bitmap.Format}
     * @readonly
     * @public
     * @since 3
     *
     */
    public readonly pixelFormat: number;
    /**
     * <p>Save image to target path as bmp format.</p>
     * @param {string} filepath - target path
     * @param {string} filename - save image's name
     * @throws {TypeError} If filepath or filename is not string
     * @throws {Error} If save path not support
     * @public
     * @since 2
     * @deprecated 3
     *
     */
    public saveImage(filepath: string, filename: string): void;
    /**
     * <p>Get the buffer from Bitmap.</p>
     * @return {Buffer} Return the buffer of current bitmap.
     * @public
     * @since 2
     */
    public getImageBuffer(): Buffer;
    /**
     * <p>Set the buffer to the bitmap. Only support RGBA32 format.</p>
     * @param {Buffer} buffer - set buffer to current bitmap
     * @throws {TypeError} If buffer is not Buffer type
     * @throws {Error} If set buffer is fail, maybe buffer size is not fit
     * @public
     * @since 2
     */
    public setImageBuffer(buffer: Buffer): void;
    /**
     * <p>Get pixels from bitmap.</p>
     * @param {number} x - the pixel horizantal position
     * @param {number} y - the pixel vertical position
     * @param {number} width - The number of pixels to read from each row
     * @param {number} height - The number of rows to read
     * @return {yunos.graphics.ImageData} the ImageData object to save pixels
     * @throws {TypeError} If x,y,width or height is not number
     * @throws {RangeError} If x add width larger than bitmap's width or y add height larger than bitmap's height
     * @public
     * @since 2
     */
    public getImageData(x: number, y: number, width: number, height: number): ImageData;
    /**
     * <p>Set pixels from ImageData.<p>
     * @param {yunos.graphics.ImageData} img - source image data
     * @param {number} x - The index of the row to read from image
     * @param {number} y - The index of the column to read from image
     * @param {number} dstX - The x coordinate of the first pixel to write to in the bitmap
     * @param {number} dstY - The y coordinate of the first pixel to write to in the bitmap
     * @param {number} width - copy width , offsetX + width must less than img.width and less than this.width
     * @param {number} height - copy height , offsetY + height must less than img.height and less than this.height
     * @public
     * @since 2
     */
    public putImageData(img: ImageData, x: number, y: number, dstX: number, dstY: number, width: number, height: number): void;
    /**
     * <p>Return the pixel from bitmap.</p>
     * @param {number} x - the pixel horizantal position
     * @param {number} y - the pixel vertical position
     * @return {yunos.graphics.Color} return color
     * @public
     * @since 2
     */
    public getPixel(x: number, y: number): Color;
    /**
     * <p>Set a color to bitmap.</p>
     * @param {number} x - the pixel horizantal position
     * @param {number} y - the pixel vertical position
     * @param {yunos.graphics.Color} color - set the pixel color
     * @public
     * @since 2
     */
    public setPixel(x: number, y: number, color: Color): void;
    /**
     * <p>This callback is called by compress method.</p>
     * @callback yunos.graphics.Bitmap~compressCallback
     * @param {Buffer} buffer - The filled buffer
     * @public
     * @since 2
     */
    /**
     * <p>Compress the bitmap to target type.</p>
     * @param {yunos.graphics.Bitmap.CompressType} type - the compress type support JPEG and PNG
     * @param {number} quality - the quality to compress between 1 and 100
     * @param {yunos.graphics.Bitmap.compressCallback} callback - the compress callback
     * @public
     * @since 2
     */
    public compress(type: string, quality: number, callback: (buffer: Buffer) => void): void;
    /**
     * <p>Compress the bitmap to target type.</p>
     * @param {yunos.graphics.Bitmap.CompressType} type - the compress type support JPEG and PNG
     * @param {number} quality - the quality to compress between 1 and 100
     * @return {Buffer} the buffer has been filled
     * @public
     * @since 2
     */
    public compressSync(type: string, quality: number): Buffer;
    /**
     * <p>Create a new bitmap from the source bitmap with new width and height.</p>
     * @param {yunos.graphics.Bitmap} bitmap - source bitmap
     * @param {number} width - the scaled bitmap width
     * @param {number} height - the scaled bitmap height
     * @return {yunos.graphics.Bitmap} the bitmap has been scaled
     * @public
     * @since 2
     */
    public static createScaledBitmapSync(bitmap: Bitmap, width: number, height: number): Bitmap;
    /**
     * <p>This callback is called by createScaledBitmap method.</p>
     * @callback yunos.graphics.Bitmap~createScaleBitmapCallback
     * @param {yunos.graphics.Bitmap} bitmap - new bimap with new width an height.
     * @public
     * @since 2
     */
    /**
     * <p>Create a new bitmap from the source bitmap with new width and height.</p>
     * @param {yunos.graphics.Bitmap} bitmap - source bitmap
     * @param {number} width - the scaled bitmap width
     * @param {number} height - the scaled bitmap height
     * @param {yunos.graphics.Bitmap.createScaledBitmapCallback} callback - the callback function after create new bitmap
     * @throws {Error} throw the error when bitmap create fail
     * @public
     * @since 2
     */
    public static createScaledBitmap(bitmap: Bitmap, width: number, height: number, callback: (bitmap: Bitmap) => void): void;
    /**
     *
     * @param {yunos.graphics.Bitmap} source - The original source Bitmap
     * @param {number} X - Initial X coordinate
     * @param {number} Y - Initial Y coordinate
     * @param {number} width - Intercepts the width of the bitmap
     * @param {number} height - Intercepts the height of the bitmap
     * @throws {Error} throw the error when bitmap create fail
     * @public
     * @since 6
     */
    public static createCropBitmapSync(source: Bitmap, x: number, y: number, width: number, height: number): Bitmap;
    private readonly head: Buffer;
    private getRawData(): Buffer;
    private getNativeBitmapHandle(): number;
    private getNativeBitmapRefHandle(): number;
    /**
     * <p>Enum for Bitmap Compress type.</p>
     * <p> Bitmap Compress type support JPEG and PNG</p>
     * @enum {string}
     * @readonly
     * @public
     * @since 2
     */
    public static readonly CompressType: {
        /**
         * Compress to JPEG
         * @public
         * @since 2
         */
        JPEG: string;
        /**
         * Compress to PNG
         * @public
         * @since 2
         */
        PNG: string;
    };
    /**
     * <p>Enum for Bitmap Format type.</p>
     * <p> Bitmap Color format type</p>
     * @enum {number}
     * @readonly
     * @public
     * @since 3
     *
     */
    public static readonly Format: {
        /**
         * Invalid Bitmap format.
         * @public
         * @since 3
         *
         */
        Invalid: int;
        /**
         * Bitmap format of RGB888.
         * @public
         * @since 3
         *
         */
        RGB24: int;
        /**
         * Bitmap format of RGBX8888.
         * @public
         * @since 3
         *
         */
        RGBX32: int;
        /**
         * Bitmap format of RGBA8888.
         * @public
         * @since 3
         *
         */
        RGBA32: int;
        /**
         * Bitmap format of ARGB8888.
         * @public
         * @since 3
         *
         */
        ARGB32: int;
        /**
         * Bitmap format of RGB565.
         * @public
         * @since 3
         *
         */
        RGB16: int;
        /**
         * Bitmap format of BGRA8888
         *  For Canvas On Bitmap
         * @ignore
         */
        BGRA32: int;
    };
}
export = Bitmap;
