/// <reference types="node" />
import EventEmitter = require("../../../core/EventEmitter");
import Bitmap = require("yunos/graphics/Bitmap");
import LinearGradient = require("yunos/graphics/LinearGradient");
import RadialGradient = require("yunos/graphics/RadialGradient");
import ConicalGradient = require("yunos/graphics/ConicalGradient");
import { Pattern } from "yunos/graphics/Context";
declare class Gradient {
    private _type;
    private _color;
    private _position;
    private _args;
    public constructor(type: number, ...args: number[]);
    private addCommand(canvas: number, fillOrStroke: number): void;
    private addColorStop(position: number, color: string): this;
}
declare class ContextNode extends EventEmitter {
    private bitmapcount;
    private _canvas;
    private _pointInStroke;
    private _pointInPath;
    private _rImageData;
    private _measureTextWidth;
    private _measureTextResult;
    private _fontFamily;
    private _fontSize;
    public constructor(canvas: number);
    private setProperty(name: string, value: Object): void;
    private drawImage(image: string | Bitmap, sx?: int, sy?: int, sw?: int, sh?: int, dx?: int, dy?: int, dw?: int, dh?: int): void;
    private loadImage(image: string | Bitmap): string;
    private initLinearGradient(value: LinearGradient, fillOrStroke: number): void;
    private initRadialGradient(value: RadialGradient, fillOrStroke: number): void;
    private initConicalGradient(value: ConicalGradient, fillOrStroke: number): void;
    private initPattern(pattern: Pattern, fillOrStroke: number): void;
    private createLinearGradient(x0: number, y0: number, x1: number, y1: number): Gradient;
    private createRadialGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number): Gradient;
    private createConicalGradient(x: number, y: number, angle: number): Gradient;
    private strokeRect(x: number, y: number, w: number, h: number): void;
    private fillRect(x: number, y: number, w: number, h: number): void;
    private rect(x: number, y: number, w: number, h: number): void;
    private ellipse(x: number, y: number, w: number, h: number): void;
    private clearRect(x: number, y: number, w: number, h: number): void;
    private fill(evenodd: boolean): void;
    private stroke(): void;
    private beginPath(): void;
    private moveTo(x: number, y: number): void;
    private closePath(): void;
    private lineTo(x: number, y: number): void;
    private clip(evenodd: boolean): void;
    private quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): void;
    private bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void;
    private arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, anticlockwise: boolean): void;
    private arcTo(x1: number, y1: number, x2: number, y2: number, r: number): void;
    private scale(w: number, h: number): void;
    private rotate(angle: number): void;
    private translate(x: number, y: number): void;
    private transform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    private resetTransform(): void;
    private setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    private setLineDash(arr: number[]): void;
    private reset(): void;
    private save(): void;
    private restore(): void;
    private text(text: string, x: number, y: number): void;
    private fillText(text: string, x: number, y: number): void;
    private strokeText(text: string, x: number, y: number): void;
    private measureTextWidth(text: string): number;
    private measureText(text: string): Object[];
    private roundedRect(x: number, y: number, width: number, height: number, xr: number, yr: number): void;
    private startAnimation(path: string, loop: number, invert: number, speed: number): void;
    private updateAnimationProgress(progress: number): void;
    private pauseAnimation(): void;
    private resumeAnimation(): void;
    private stopAnimation(): void;
    private getImageData(x: number, y: number, width: number, height: number): Buffer;
    private isPointInPath(x: number, y: number, evenodd: number): boolean;
    private isPointInStroke(x: number, y: number): boolean;
    private handleEvent(cid: number, h: number, ...args: Object[]): void;
}
export = ContextNode;
