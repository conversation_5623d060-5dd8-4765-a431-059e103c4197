/*
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";

import VideoModel = require("../model/VideoModel");
const iVideoModel = VideoModel.getInstance();

class AccountHelper {
    private static _instance: AccountHelper;
    private _accountName: string;
    private _token: string;
    private _vin: string;

    constructor() {
        this._init();
    }

    static getInstance() {
        if (!this._instance) {
            this._instance = new AccountHelper();
        }
        return this._instance;
    }

    _init() {
        this._accountName = "";
        this._token = "";
        this._vin = "";
    }

    get account() {
        if (this._accountName) {
            return this._accountName;
        } else {
            let accountInfo = iVideoModel.getAccountInfo();
            if (accountInfo && accountInfo.accountName) {
                this._accountName = accountInfo.accountName;
                return accountInfo.accountName;
            } else {
                return "default_account";
            }
        }
    }

    get token() {
        if (this._token) {
            return this._token;
        } else {
            let accountInfo = iVideoModel.getAccountInfo();
            if (accountInfo && accountInfo.token) {
                this._token = accountInfo.token;
                return accountInfo.token;
            } else {
                return "test";
            }
        }
    }

    get vin() {
        return this._vin;
    }
}
export = AccountHelper;
