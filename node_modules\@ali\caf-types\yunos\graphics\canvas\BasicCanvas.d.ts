import YObject = require("../../core/YObject");
/**
 * Color composite strategy for canvas draw command
 * @private
 */
export declare enum BlendMode {
    /**
     * [0, 0]
     * @ignore
     */
    kClear = 0,
    /**
     * [<PERSON>, <PERSON>]
     * @ignore
     */
    kSrc = 1,
    /**
     * [<PERSON>, Dc]
     * @ignore
     */
    kDst = 2,
    /**
     * [Sa + Da * (1 - Sa), Sc + Dc * (1 - Sa)]
     * @ignore
     */
    kSrcOver = 3,
    /**
     * [<PERSON> + Sa * (1 - Da), Dc + <PERSON> * (1 - Da)]
     * @ignore
     */
    kDstOver = 4,
    /**
     * [Sa * Da, Sc * Da]
     * @ignore
     */
    kSrcIn = 5,
    /**
     * [Da * Sa, Dc * Sa]
     * @ignore
     */
    kDstIn = 6,
    /**
     * [Sa * (1 - Da), Sc * (1 - Da)]
     * @ignore
     */
    kSrcOut = 7,
    /**
     * [Da * (1 - Sa), Dc * (1 - Sa)]
     * @ignore
     */
    kDstOut = 8,
    /**
     * [<PERSON>, <PERSON> * Da + Dc * (1 - Sa)]
     * @ignore
     */
    kSrcATop = 9,
    /**
     * [<PERSON>, <PERSON>c * Sa + Sc * (1 - Da)]
     * @ignore
     */
    kDstATop = 10,
    /**
     * [<PERSON> + Da - 2 * Sa * Da, Sc * (1 - Da) + Dc * (1 - Sa)]
     * @ignore
     */
    kXor = 11,
    /**
     * [Sa + Da, Sc + Dc]
     * @ignore
     */
    kPlus = 12,
    /**
     * multiplies all components (= alpha and color)
     * @ignore
     */
    kModulate = 13,
    /**
     * @ignore
     */
    kScreen = 14,
    /**
     * @ignore
     */
    kLastCoeffMode = 14,
    /**
     * @ignore
     */
    kOverlay = 15,
    /**
     * @ignore
     */
    kDarken = 16,
    /**
     * @ignore
     */
    kLighten = 17,
    /**
     * @ignore
     */
    kColorDodge = 18,
    /**
     * @ignore
     */
    kColorBurn = 19,
    /**
     * @ignore
     */
    kHardLight = 20,
    /**
     * @ignore
     */
    kSoftLight = 21,
    /**
     * @ignore
     */
    kDifference = 22,
    /**
     * @ignore
     */
    kExclusion = 23,
    /**
     * @ignore
     */
    kMultiply = 24,
    /**
     * @ignore
     */
    kLastSeparableMode = 24,
    /**
     * @ignore
     */
    kHue = 25,
    /**
     * @ignore
     */
    kSaturation = 26,
    /**
     * @ignore
     */
    kColor = 27,
    /**
     * @ignore
     */
    kLuminosity = 28,
    /**
     * @ignore
     */
    kLastMode = 28
}
/**
 * <p>A low level Canvas Wrap class, all function is just penetrate to native Canvas addon </p>
 * @example
 *  let bitmap = new Bitmap(400, 300, Bitmap.Format.BGRA32);
 *  let canvas = new BasicCanvas(bitmap);
 * @ignore
 */
export declare class BasicCanvas extends YObject {
}
