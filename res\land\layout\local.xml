<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    layout="{layout.local}"
    propertySetName="local">

    <NavigationBar id="id_nav"/>

    <GridView
        id="id_list_grid"
        orientation="{enum.GridView.Orientation.Horizontal}"
        rows="{config.LOCAL_ROWS_NUM}"
        rowSpacing="{config.ITEM_SPACE}"
        columnSpacing="{config.ITEM_SPACE}"
        dividerHeight="{config.ITEM_SPACE}"
        scrollBarCustomized="true"
        horizontalFadingEdgeEnabled="true"
        focusable="false"
        visibility="{enum.View.Visibility.Hidden}"/>

    <ScrollBar
        id="id_scrollbar"
        height="{config.SCROLL_BAR_SIZE}"
        autoHidden="true"/>

    <QuickIndex
        id="id_quickindex"
        height="{config.PAGE_MARGIN}"
        fontSize="{sdp(20)}"
        visibility="{enum.View.Visibility.Hidden}"/>

    <CompositeView
        id="id_empty"
        layout="{layout.local_empty}">
        <ImageView
            id="id_icon"
            height="{config.BG_VIDEO_HEIGHT}"
            scaleType="{enum.ImageView.ScaleType.Center}"/>
        <TextView
            id="id_info"
            text=""
            propertySetName="extend/hdt/FontTitle2"
            align="{enum.TextView.Align.Center}"/>
        <TextView
            id="id_support_format_tip_1"
            width="{config.LOCAL_SUPPORT_MEDIA_FORMAT_TIP_WIDTH}"
            text="{string.SUPPORT_MEDIA_FORMAT_TIP_1}"
            propertySetName="extend/hdt/FontBody4"
            align="{enum.TextView.Align.Center}"/>
        <TextView
            id="id_support_format_tip_2"
            width="{config.LOCAL_SUPPORT_MEDIA_FORMAT_TIP_WIDTH}"
            text="{string.SUPPORT_MEDIA_FORMAT_TIP_2}"
            propertySetName="extend/hdt/FontBody4"
            align="{enum.TextView.Align.Center}"/>
        <TextView
            id="id_support_format_tip_3"
            width="{config.LOCAL_SUPPORT_MEDIA_FORMAT_TIP_WIDTH}"
            text="{string.SUPPORT_MEDIA_FORMAT_TIP_3}"
            propertySetName="extend/hdt/FontBody4"
            align="{enum.TextView.Align.Center}"/>
    </CompositeView>

    <CompositeView
        id="id_loading"
        width="{sdp(240)}"
        height="{sdp(202)}"
        layout="{layout.local_loading}">
        <LoadingBM
            id="id_loading_icon"
            sizeStyle="{enum.LoadingBM.SizeStyle.M}"/>
        <TextView
            id="id_loading_text"
            text="{string.SCANING}"
            propertySetName="extend/hdt/FontBody2"/>
    </CompositeView>
</CompositeView>
