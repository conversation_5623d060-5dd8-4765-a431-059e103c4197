/**
 * Copyright (C) 2018-2019 Alibaba Group Holding Limited. All Rights Reserved.
 */

"use strict";
// jscs:disable disallowSpacesInsideObjectBrackets
// jscs:disable requireCamelCaseOrUpperCaseIdentifiers

import Presenter = require("yunos/appmodel/Presenter");
const iRes = require("yunos/content/resource/Resource").getInstance();
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import FlowLayout = require("yunos/ui/layout/FlowLayout");
const {Visible, Hidden, None} = require("yunos/ui/view/View").Visibility;
import View = require("yunos/ui/view/View");
import ListView = require("yunos/ui/view/ListView");
import GridView = require("yunos/ui/view/GridView");
import Cursor = require("yunos/provider/Cursor");
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
import TextField = require("yunos/ui/view/TextField");
import BaseAdapter = require("yunos/ui/adapter/BaseAdapter");
import CompositeView = require("yunos/ui/view/CompositeView");
import NavigationBar = require("yunos/ui/view/NavigationBar");
import Button = require("yunos/ui/widget/Button");
import LoadingPage = require("extend/hdt/control/LoadingPageBM");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import ScrollBar = require("yunos/ui/widget/ScrollBar");
const RecognitionMode = VoiceCommand.RecognitionMode;
import OnlineAdapter = require("../../adapter/OnlineAdapter");
import LocalAdapter = require("./adapter/LocalAdapter");
import VideoInfo = require("../../model/VideoInfo");
const iLocalModel = require("../../model/LocalModel").getInstance();
const iOnlineModel = require("../../model/OnlineModel").getInstance();
const iSearchModel = require("../../model/SearchModel").getInstance();
const iVideoModel = require("../../model/VideoModel").getInstance();
const iAccount = require("../../utils/AccountHelper").getInstance();
const iUserTrackHelper = require("../../utils/UserTrackHelper").getInstance();
const iNetworkState = require("../../monitor/NetworkState").getInstance();
import Consts = require("../../Consts");
const RoutePath = Consts.RoutePath;
import Features = require("../../Features");
import log = require("../../utils/log");
import Utils = require("../../utils/Utils");
const TAG = "SearchPresenter";

const State = {
    WAIT_INPUT: 0,
    SHOW_RESULT: 1
};
const LoadingPageType = {
    LOADING: 0,
    ERROR: 1,
    EMPTY: 2,
    NOTHING: 3,
    NO_HISTORY: 4
};

interface IViews {
    navigationBar?: NavigationBar;
    loadingPage?: LoadingPage;
    errorDlg?: View;
    emptyInfo?: TextView;
    emptyIcon?: ImageView;
    empty?: View;
    searchInput?: TextField;
    historyContainer?: CompositeView;
    historyContent?: CompositeView;
    resultContainer?: CompositeView;
    onlineContainer?: View;
    onlineList?: ListView;
    onlineScrollbar?: ScrollBar;
    localContainer?: View;
    localBanner?: CompositeView;
    localMore?: CompositeView;
    localMoreTips?: TextView;
    localGrid?: GridView;
    localScrollbar?: ScrollBar;
}

interface IConfig {
    ITEM_SPACE?: number;
    LOCAL_ITEM_WIDTH?: number;
    SEARCH_INPUT_LEFT_MARGIN?: number;
    SEARCH_RESULT_SPACE?: number;
    SEARCH_LAYOUT_LINE_SPACING?: number;
    SEARCH_LAYOUT_ITEM_SPACING?: number;
    SEARCH_HISTORY_ITEM_WIDTH?: number;
    SEARCH_HISTORY_ITEM_HEIGHT?: number;
    BORDER_RADIUS_SMALL?: number;
}
const Config: IConfig = {};

class SearchPresenter extends Presenter {
    private _searchLocalResult: {
        completed?: boolean,
        error?: Object,
        data?: Cursor,
    };
    private _searchOnlineResult: {
        completed?: boolean,
        error?: Object,
        data?: Object[],
    };
    private _highlightUrlChanged: (path: string, url: string, index: number) => void;
    private _navBackListener: () => void;
    private _retryBtnListener: () => void;
    private _destroyed: boolean;
    private _pageNo: number;
    private _views: IViews;
    private _searchWord: string;
    private _hidden: boolean;
    private _state: number;
    private _tapAbleViews: View[];
    private _loadingMoreView: View;
    private _loadingMore: boolean;
    private _loadingMoreTimeout: NodeJS.Timer;
    private _isNarrowScreen: boolean;

    onCreate() {
        log.I(TAG, "onCreate");
        this._destroyed = false;
        this._searchLocalResult = {};
        this._searchOnlineResult = {};
        this._pageNo = 1;
        this._isNarrowScreen = Utils.isNarrowScreen();
        Config.ITEM_SPACE = <number>Utils.getDimen("ITEM_SPACE");
        Config.LOCAL_ITEM_WIDTH = <number>Utils.getDimen("LOCAL_ITEM_WIDTH");
        Config.SEARCH_INPUT_LEFT_MARGIN = <number>Utils.getDimen("SEARCH_INPUT_LEFT_MARGIN");
        Config.SEARCH_RESULT_SPACE = <number>Utils.getDimen("SEARCH_RESULT_SPACE");
        Config.SEARCH_LAYOUT_LINE_SPACING = <number>Utils.getDimen("SEARCH_LAYOUT_LINE_SPACING");
        Config.SEARCH_LAYOUT_ITEM_SPACING = <number>Utils.getDimen("SEARCH_LAYOUT_ITEM_SPACING");
        Config.SEARCH_HISTORY_ITEM_WIDTH = <number>Utils.getDimen("SEARCH_HISTORY_ITEM_WIDTH");
        Config.SEARCH_HISTORY_ITEM_HEIGHT = <number>Utils.getDimen("SEARCH_HISTORY_ITEM_HEIGHT");
        Config.BORDER_RADIUS_SMALL = <number>Utils.getDimen("BORDER_RADIUS_SMALL");
        this.attachView("search");
        this._initListener();
    }

    /**
     * 初始化监听器，监听U盘、高亮的变化消息
     */
    _initListener() {
        this._highlightUrlChanged = (path: string, url: string, index: number) => {
            if (this._destroyed) {
                log.W(TAG, "_highlightUrlChanged, presenter is destroyed");
                return;
            }

            if (!path) {
                log.W(TAG, "_highlightUrlChanged, path is null");
                return;
            }

            if (!url) {
                log.W(TAG, "_highlightUrlChanged, url is null");
                return;
            }

            log.D(TAG, "_highlightUrlChanged", path, url, index);
            if (path === Consts.FromType.ONLINE) {
                if (this._views.onlineList) {
                    let adapter = <OnlineAdapter> this._views.onlineList.adapter;
                    if (adapter && adapter.data) {
                        for (let i = 0; i < adapter.data.length; i++) {
                            let item = <VideoInfo> adapter.data[i];
                            item.lastPlayed = item.url === url ? true : false;
                            adapter.update(i, item);
                        }
                    }
                }
            } else {
                if (this._views.localGrid) {
                    let adapter = <LocalAdapter> this._views.localGrid.adapter;
                    if (adapter && adapter.cursor && !adapter.cursor.isClosed()) {
                        adapter.onDataChange();
                    }
                }
            }
        };
        iVideoModel.on(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
    }

    onShow() {
        log.I(TAG, "onShow");
        this._hidden = false;
        iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_SEARCH);
    }

    onHide() {
        log.I(TAG, "onHide");
        this._hidden = true;
        iUserTrackHelper.leavePage(iUserTrackHelper.PAGE_SEARCH);
    }

    onPageShow() {
        log.I(TAG, "onPageShow");
        if (this._hidden) {
            this.onShow();
        }
    }

    onPageHide() {
        log.I(TAG, "onPageHide");
        if (!this._hidden) {
            this.onHide();
        }
    }

    onViewAttached(parentView: View) {
        log.I(TAG, "onViewAttached");
        this._setupViews(parentView);
        this._setupTapHandler();
        this._state = State.WAIT_INPUT;

        this._views.searchInput.on("textchange", (text: string) => {
            log.D(TAG, "textchange, search word:", text);
            if (!text || text.length === 0 || text.trim().length === 0) {
                this._backToWaitingInput();
            }
        });

        this._views.searchInput.on("accept", () => {
            log.D(TAG, "accept, search word:", this._views.searchInput.text);
            let word = this._views.searchInput.text;
            word = word ? word.trim() : word;
            if (word && word.length > 0) {
                this._startSearch(this._views.searchInput.text);
            } else {
                this._views.searchInput.text = "";
                Utils.showToast(iRes.getString("SEARCH_INPUT_TIP"));
            }
        });
        this._views.searchInput.on("clear", () => {
            log.D(TAG, "clear");
            this._backToWaitingInput();
        });

        this._showSearchHistory();
        this._addVoiceCommands();
    }

    _backToWaitingInput() {
        this._showLoadingPage(LoadingPageType.NOTHING);
        this._views.searchInput.visibility = Visible;
        this._views.resultContainer.visibility = Hidden;
        this._views.localContainer.visibility = Hidden;
        this._views.onlineContainer.visibility = Hidden;
        this._searchWord = null;
        this._showSearchHistory();
        this._state = State.WAIT_INPUT;
    }

    _setupViews(parentView: View) {
        this._views = {};
        this._views.navigationBar = <NavigationBar> parentView.findViewById("id_nav");
        this._views.navigationBar.leftItem.visibility = Visible;
        this._views.navigationBar.titleItem.visibility = None;
        this._views.navigationBar.preTitleItem.visibility = None;
        this._views.navigationBar.rightItem.visibility = Visible;
        let rightItem = <CompositeView> LayoutManager.loadSync("search_nav_right_item");
        this._views.searchInput = <TextField> rightItem.findViewById("id_search_input");
        this._views.navigationBar.rightItem = rightItem;
        this._views.navigationBar.layout.setLayoutParam("id_right_item", "align", {
            left: "parent",
            middle: "parent"
        });
        this._views.navigationBar.layout.setLayoutParam("id_right_item", "margin", {
            left: Config.SEARCH_INPUT_LEFT_MARGIN
        });

        this._views.navigationBar.addEventListener("back", this._navBackListener = () => {
            log.I(TAG, "back button pressed!");
            this._onBack();
        });

        this._views.historyContainer = <CompositeView> parentView.findViewById("id_search_history");
        this._views.historyContent = <CompositeView> this._views.historyContainer.findViewById("id_sh_content");
        this._views.resultContainer = <CompositeView> parentView.findViewById("id_result_container");
        this._views.localContainer = parentView.findViewById("id_local_container");
        this._views.localGrid = <GridView> parentView.findViewById("id_local_grid");
        this._views.localBanner = <CompositeView> parentView.findViewById("id_local_banner");
        this._views.localMore = <CompositeView> parentView.findViewById("id_local_more");
        this._views.localMoreTips = <TextView> parentView.findViewById("id_local_more_tips");
        this._views.localScrollbar = <ScrollBar> parentView.findViewById("id_local_scrollbar");
        this._views.localGrid.horizontalScrollBar = this._views.localScrollbar;
        this._views.onlineContainer = <CompositeView> parentView.findViewById("id_online_container");
        this._views.onlineList = <ListView> parentView.findViewById("id_online_list");
        this._views.onlineScrollbar = <ScrollBar> parentView.findViewById("id_online_scrollbar");
        this._views.onlineList.horizontalScrollBar = this._views.onlineScrollbar;
        this._views.empty = parentView.findViewById("id_empty");
        this._views.emptyIcon = <ImageView> this._views.empty.findViewById("id_empty_icon");
        this._views.emptyInfo = <TextView> this._views.empty.findViewById("id_empty_info");
        this._views.errorDlg = parentView.findViewById("id_error_dlg");
        this._views.loadingPage = <LoadingPage> parentView.findViewById("id_loading_page");
        this._views.loadingPage.addEventListener("RetryButtonReleased", this._retryBtnListener = () => {
            log.I(TAG, "retry button pressed!");
            if (iNetworkState.networkConnected) {
                this._startSearch(this._searchWord);
            }
        });
    }

    _onBack() {
        iVideoModel.removeHighlightUrl(Consts.FromType.SEARCH_LOCAL, "");
        this.context.router.back();
    }

    /**
     * 注册语音指令
     */
    _addVoiceCommands() {
        const cmdKeys = [
            "VOICECMD_BACK_1",
            "VOICECMD_BACK_2"
        ];

        Utils.registerVoiceCommand(this._views.navigationBar, cmdKeys, RecognitionMode.Both, (cmdKey, index) => {
            if (this._destroyed) {
                log.W(TAG, "voice command, presenter is destroyed");
                return;
            }

            log.I(TAG, "voice command", cmdKey, index);
            this._onBack();

            const iPageInstance = require("../index").getInstance();
            Utils.cancelSpeech(iPageInstance);
        });
    }

    _showSearchHistory() {
        log.D(TAG, "_showSearchHistory", iAccount.account);
        let searchHistoryData = <string[]> iSearchModel.getSearchHistory(iAccount.account);
        log.I(TAG, "_showSearchHistory", searchHistoryData.length);
        if (searchHistoryData && searchHistoryData.length > 0) {
            this._views.historyContainer.visibility = Visible;
            this._showLoadingPage(LoadingPageType.NOTHING);
            this._views.historyContent.removeAllChildren();
            for (let i = 0; i < searchHistoryData.length; i++) {
                let view = new Button();
                view.width = Config.SEARCH_HISTORY_ITEM_WIDTH;
                view.height = Config.SEARCH_HISTORY_ITEM_HEIGHT;
                view.propertySetName = Consts.SEARCH_HISTORY_ITEM;
                view.borderRadius = Config.BORDER_RADIUS_SMALL;
                view.multiState = {
                    normal: {
                        opacity: 1
                    },
                    pressed: {
                        opacity: 0.4
                    }
                };
                view.text = searchHistoryData[i];
                if (view.contentWidth + Config.SEARCH_LAYOUT_ITEM_SPACING * 2 > Config.SEARCH_HISTORY_ITEM_WIDTH) {
                    view.width = view.contentWidth + Config.SEARCH_LAYOUT_ITEM_SPACING * 2;
                }
                view.addEventListener("tap", () => {
                    log.D(TAG, "_searchHistory item tap, view.text: " + view.text);
                    let paramObj = {
                        search_keyword: view.text
                    };
                    iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SEARCH,
                        iUserTrackHelper.SEARCH_CLICK_HISTROY, paramObj);

                    this._views.searchInput.text = view.text;
                    this._views.searchInput.blur();
                    this._views.searchInput.hideSoftKeyboard();
                    this._startSearch(view.text);
                });
                this._views.historyContent.addChild(view);
            }
            let view = new Button();
            view.id = "id_sh_clear";
            view.width = Config.SEARCH_HISTORY_ITEM_WIDTH;
            view.height = Config.SEARCH_HISTORY_ITEM_HEIGHT;
            view.propertySetName = Consts.SEARCH_HISTORY_ITEM_WARNING;
            view.borderRadius = Config.BORDER_RADIUS_SMALL;
            view.multiState = {
                normal: {
                    opacity: 1
                },
                pressed: {
                    opacity: 0.4
                }
            };
            view.text = iRes.getString("SEARCH_CLEAR_HISTORY");
            view.addEventListener("tap", () => {
                log.I(TAG, "clear button pressed!");
                let paramObj = {
                    search_history_list: this._getHistoryList()
                };
                iUserTrackHelper.clickButton(
                    iUserTrackHelper.PAGE_SEARCH,
                    iUserTrackHelper.SEARCH_CLEAR_HISTROY,
                    paramObj
                );
                iSearchModel.clearSearchHistory(iAccount.account);
                this._views.historyContainer.visibility = Hidden;
                this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("SEARCH_HISTORY_EMPTY"));
            });
            this._views.historyContent.addChild(view);

            let layout = new FlowLayout();
            layout.align = FlowLayout.Align.Left;
            layout.lineSpacing = Config.SEARCH_LAYOUT_LINE_SPACING;
            layout.itemSpacing = Config.SEARCH_LAYOUT_ITEM_SPACING;
            this._views.historyContent.layout = layout;
        } else {
            this._views.historyContainer.visibility = Hidden;
            this._showLoadingPage(LoadingPageType.NO_HISTORY,
                iRes.getString("SEARCH_HISTORY_EMPTY"));
        }
    }

    _setupTapHandler() {
        this._tapAbleViews = [];
        Utils.setOnTapListener(this._views.localMore, () => {
            log.I(TAG, "local more button pressed!");
            this._enterSearchMore(false);
        });
        this._tapAbleViews.push(this._views.localMore);
    }

    _enterSearchMore(isOnline: boolean) {
        let launchData = {
            from: isOnline ? Consts.FromType.SEARCH_ONLINE : Consts.FromType.SEARCH_LOCAL,
            keyword: this._searchWord
        };
        this._navigate(RoutePath.SEARCH_MORE, launchData);
    }

    /**
     * 搜索视频
     * 1.搜索本地视频
     * 2.搜索在线视频
     */
    _startSearch(word: string) {
        log.D(TAG, "_startSearch", word);
        if (typeof word !== "string" || word.trim() === "") {
            return;
        }
        let paramObj = {
            search_keyword: word
        };
        iUserTrackHelper.clickButton(
            iUserTrackHelper.PAGE_SEARCH,
            iUserTrackHelper.SEARCH_GO_SEARCH,
            paramObj
        );
        this._initSearchView();

        word = word ? word.trim() : word;
        if (word && word.length > 0) {
            iUserTrackHelper.enterPage(iUserTrackHelper.PAGE_SEARCH_RESULT);
            this._searchWord = word;
            this._showLoadingPage(LoadingPageType.LOADING);
            this._searchLocalVideo(word);
            this._searchOnlineVedio(word);
            iSearchModel.addSearchHistory(iAccount.account, word);
            this._state = State.SHOW_RESULT;
        }
    }

    _initSearchView() {
        this._showLoadingPage(LoadingPageType.NOTHING);
        this._views.searchInput.blur();
        this._views.historyContainer.visibility = Hidden;
        this._views.localContainer.visibility = Hidden;
        this._views.onlineContainer.visibility = Hidden;
        if (this._views.onlineList.adapter) {
            (<BaseAdapter> this._views.onlineList.adapter).clear();
        }
        this._searchLocalResult = {};
        this._searchOnlineResult = {};
    }

    /**
     * 搜索本地视频
     */
    _searchLocalVideo(keyword: string) {
        log.D(TAG, "_searchLocalVideo", keyword);
        if (!Features.SUPPORT_USB) {
            this._searchLocalResult = {
                error: null,
                data: null,
                completed: true
            };
            return;
        }

        iLocalModel.searchVideo(keyword, 0, (error: Object, cursor: Cursor) => {
            if (this._destroyed || !this._views) {
                return;
            }

            this._searchLocalResult = {
                error: error,
                data: cursor,
                completed: true
            };
            this._checkSearchResult();
        });
    }

    /**
     * 搜索在线视频
     */
    _searchOnlineVedio(keyword: string) {
        log.D(TAG, "_searchOnlineVedio", keyword);
        iOnlineModel.searchVideo(keyword, 1, (error: Object, data: Object[]) => {
            if (this._destroyed || !this._views) {
                return;
            }

            this._searchOnlineResult = {
                error: error,
                data: data,
                completed: true
            };
            this._checkSearchResult();
        });
    }

    /**
     * 根据本地视频和在线视频搜索结果来显示UI
     */
    _checkSearchResult() {
        log.I(TAG, "_checkSearchResult", this._searchLocalResult.completed, this._searchOnlineResult.completed);
        if (!this._searchLocalResult.completed || !this._searchOnlineResult.completed) {
            return;
        }

        if (this._state === State.WAIT_INPUT) {
            log.I(TAG, "_checkSearchResult", this._state);
            this._views.empty.visibility = None;
            this._views.resultContainer.visibility = None;
            return;
        }

        let isLocalEmpty = !this._searchLocalResult.data || this._searchLocalResult.data.count === 0;
        let isOnlineEmpty = !this._searchOnlineResult.data || this._searchOnlineResult.data.length === 0;
        log.I(TAG, "_checkSearchResult", this._searchLocalResult.error, isLocalEmpty,
            this._searchOnlineResult.error, isOnlineEmpty);
        if (this._searchOnlineResult.error && this._searchLocalResult.error) {
            this._views.resultContainer.visibility = None;
            this._showLoadingPage(LoadingPageType.ERROR);
        } else if (!this._searchOnlineResult.error && this._searchLocalResult.error) {
            if (isOnlineEmpty) {
                this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("ERR_QUERY"));
                this._views.resultContainer.visibility = None;
            } else {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(null, this._searchOnlineResult.data);
            }
        } else if (this._searchOnlineResult.error && !this._searchLocalResult.error) {
            if (isLocalEmpty) {
                this._views.resultContainer.visibility = None;
                this._showLoadingPage(LoadingPageType.ERROR);
            } else {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(this._searchLocalResult.data, null);
            }
        } else {
            if (isOnlineEmpty && isLocalEmpty) {
                this._showLoadingPage(LoadingPageType.EMPTY, iRes.getString("SEARCH_RESULT_EMPTY"));
            } else if (!isOnlineEmpty && isLocalEmpty) {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(null, this._searchOnlineResult.data);
            } else if (isOnlineEmpty && !isLocalEmpty) {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(this._searchLocalResult.data, null);
            } else {
                this._showLoadingPage(LoadingPageType.NOTHING);
                this._trySetList(this._searchLocalResult.data, this._searchOnlineResult.data);
            }
        }
    }

    _trySetList(cursor?: Cursor, data?: Object[]) {
        let hasLocalList = cursor && !cursor.isClosed() && cursor.count > 0;
        let hasOnlineList = data && data.length > 0;

        this._views.resultContainer.visibility = Visible;
        if (hasLocalList) {
            this._views.localContainer.visibility = Visible;
            let adapter = <LocalAdapter> this._views.localGrid.adapter;
            if (!adapter) {
                adapter = new LocalAdapter(true);
                adapter.registerVoiceSelectListener(this._localSelectHandler.bind(this));
                this._views.localGrid.adapter = adapter;
                this._views.localGrid.on("itemselect", this._localSelectHandler.bind(this));
            }
        } else {
            this._views.localContainer.visibility = None;
            this._views.localScrollbar.visibility = None;
        }

        if (hasOnlineList) {
            this._views.onlineContainer.visibility = Visible;
            this._views.onlineScrollbar.visibility = Visible;
            let adapter = this._views.onlineList.adapter;
            if (!adapter) {
                adapter = new OnlineAdapter(true);
                this._views.onlineList.adapter = adapter;
                this._views.onlineList.on("itemselect", this._onlineSelectHandler.bind(this));
                this._views.onlineList.on("reachend", this._onReachend.bind(this));
            }
        } else {
            this._views.onlineContainer.visibility = None;
            this._views.onlineScrollbar.visibility = None;
        }

        if (hasLocalList && hasOnlineList) {
            let maxNum = this._isNarrowScreen ? Consts.LAND_NARROW_MAX_SEARCH_LOCAL_VIDEO_NUM : Consts.LAND_MAX_SEARCH_LOCAL_VIDEO_NUM;
            let num = cursor.count;
            if (num > maxNum) {
                this._views.localMore.visibility = Visible;
                let rowNum = Math.ceil(maxNum / 2);
                this._views.localGrid.width = rowNum * Config.LOCAL_ITEM_WIDTH + (rowNum - 1) * Config.ITEM_SPACE;
                this._views.localBanner.width = this._views.localGrid.width;
                this._views.localContainer.width = this._views.localGrid.width + Config.SEARCH_RESULT_SPACE;
                if (this._isNarrowScreen) {
                    this._views.localMore.width = Utils.getDimen("SEARCH_MORE_NARROW_WIDTH");
                    this._views.localMore.propertySetName = Consts.SEARCH_MORE_NARROW_MULTISTATE;
                    this._views.localMoreTips.visibility = None;
                } else {
                    this._views.localMore.width = Utils.getDimen("SEARCH_MORE_WIDTH");
                    this._views.localMore.propertySetName = Consts.SEARCH_MORE_MULTISTATE;
                    this._views.localMoreTips.visibility = Visible;
                }

                iLocalModel.searchVideo(this._searchWord, maxNum, (error: Object, c: Cursor) => {
                    log.I(TAG, "searchVideo, error", error);
                    if (this._destroyed || !this._views || !c) {
                        return;
                    }
                    (<LocalAdapter> this._views.localGrid.adapter).cursor = c;
                });
            } else {
                (<LocalAdapter> this._views.localGrid.adapter).cursor = cursor;
                this._views.localMore.visibility = None;
                let rowNum = Math.ceil(num / 2);
                this._views.localGrid.width = rowNum * Config.LOCAL_ITEM_WIDTH + (rowNum - 1) * Config.ITEM_SPACE;
                this._views.localBanner.width = this._views.localGrid.width;
                this._views.localContainer.width = this._views.localGrid.width + Config.SEARCH_RESULT_SPACE;
            }

            (<OnlineAdapter> this._views.onlineList.adapter).data = data;
            this._views.onlineContainer.width = this._views.resultContainer.width - this._views.localContainer.width;
            this._views.onlineList.width = this._views.onlineContainer.width;
            this._views.onlineScrollbar.width = this._views.onlineList.width;
        } else if (hasLocalList) {
            (<LocalAdapter> this._views.localGrid.adapter).cursor = cursor;
            this._views.localMore.visibility = None;
            this._views.localContainer.width = this._views.resultContainer.width;
            this._views.localGrid.width = this._views.resultContainer.width;
            this._views.localGrid.arriveAt(0);
            this._views.localScrollbar.visibility = Visible;
        } else if (hasOnlineList) {
            (<OnlineAdapter> this._views.onlineList.adapter).data = data;
            this._views.onlineList.width = this._views.resultContainer.width;
            this._views.onlineContainer.width = this._views.onlineList.width;
            this._views.onlineList.arriveAt(0);
            this._views.onlineScrollbar.width = this._views.resultContainer.width;
        }
    }

    _localSelectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_localSelectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        let adapter = <LocalAdapter> this._views.localGrid.adapter;
        if (!adapter || !adapter.cursor || adapter.cursor.isClosed()) {
            return;
        }

        let videoInfo = <VideoInfo> iLocalModel.getVideoInfo(adapter.cursor, position);
        if (!videoInfo) {
            return;
        }

        let paramObj = {
            video_from: "usb",
            video_id: videoInfo.id,
            video_title: videoInfo.title,
            video_size: videoInfo.videoSize,
            video_category: "NA",
            video_tags: "NA",
            video_duration: videoInfo.duration
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SEARCH_RESULT, iUserTrackHelper.SEARCH_RESULT_PICK, paramObj);

        let launchData = {
            from: Consts.FromType.SEARCH_LOCAL,
            index: position,
            list: adapter
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _onlineSelectHandler(itemView: View, position: number, point: object, voice: boolean) {
        log.I(TAG, "_onlineSelectHandler", position, point, voice);
        if (!itemView || position < 0) {
            return;
        }

        let adapter = <OnlineAdapter> this._views.onlineList.adapter;
        if (!adapter) {
            return;
        }

        let videoInfo = <VideoInfo> adapter.data[position];
        if (!videoInfo) {
            return;
        }

        let paramObj = {
            video_from: Consts.FromType.ONLINE,
            video_id: videoInfo.id,
            video_title: videoInfo.title,
            video_size: videoInfo.videoSize,
            video_category: videoInfo.category,
            video_tags: videoInfo.tags,
            video_duration: videoInfo.duration
        };
        iUserTrackHelper.clickButton(iUserTrackHelper.PAGE_SEARCH_RESULT, iUserTrackHelper.SEARCH_RESULT_PICK, paramObj);

        let launchData = {
            from: Consts.FromType.SEARCH_ONLINE,
            index: position,
            list: adapter.data,
            pageNo: this._pageNo,
            keyword: this._searchWord
        };
        this._navigate(RoutePath.PLAYER, launchData, {launchMode: "single"});
    }

    _navigate(path: string, launchData?: Object, newConfig = {}) {
        if (path === RoutePath.BACK) {
            this._onBack();
        } else {
            if (!this._hidden) {
                if (!launchData) {
                    launchData = {};
                }
                this.context.router.navigate(path, launchData, newConfig);
            } else {
                log.W(TAG, "_navigate negtive", path);
            }
        }
    }

    _onReachend() {
        log.D(TAG, "_onReachend");
        if (!this._loadingMoreView) {
            this._loadingMoreView = LayoutManager.loadSync("online_loading_more");
        }
        if (!this._loadingMore) {
            this._loadingMore = true;
            this._views.onlineList.addFooter(this._loadingMoreView);

            let pageNo = this._pageNo + 1;
            iOnlineModel.searchVideo(this._searchWord, pageNo, (error: Object, ret: object[]) => {
                this._removeLoadingMoreView();
                this._removeTimeout(this._loadingMoreTimeout);
                if (!error) {
                    if (ret && ret.length > 0) {
                        this._pageNo++;
                        (<BaseAdapter> this._views.onlineList.adapter).addAll(ret);
                    } else {
                        Utils.showToast(iRes.getString("LOAD_MORE_NO_MORE"));
                    }
                } else {
                    Utils.showToast(iRes.getString("LOAD_MORE_FAILED"));
                }
            });

            this._removeTimeout(this._loadingMoreTimeout);
            this._loadingMoreTimeout = setTimeout(() => {
                this._removeLoadingMoreView();
            }, Consts.LOADING_MORE_TIMEOUT);
        }
    }

    _removeLoadingMoreView() {
        this._views.onlineList.removeFooter(this._loadingMoreView);
        this._loadingMore = false;
        this._removeTimeout(this._loadingMoreTimeout);
    }

    /**
     * 显示loading、查询错误、无网络，无数据、无搜索历史等信息
     */
    _showLoadingPage(type: number, info?: string) {
        if (!this._views || this._destroyed) {
            log.W(TAG, "_showLoadingPage, presenter is destroyed");
            return;
        }

        log.I(TAG, "_showLoadingPage", type);
        if (type === LoadingPageType.LOADING) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.LoadingMode;
            this._views.loadingPage.errorTitleVisible = false;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = false;
            this._views.loadingPage.retryButtonVisible = false;
            this._views.loadingPage.loadingText = iRes.getString("LOADING");
        } else if (type === LoadingPageType.ERROR) {
            this._views.empty.visibility = None;
            this._views.loadingPage.visibility = Visible;
            this._views.loadingPage.displayMode = LoadingPage.DisplayMode.ErrorMode;
            this._views.loadingPage.errorTitleVisible = true;
            this._views.loadingPage.errorTextVisible = false;
            this._views.loadingPage.errorImageVisible = true;
            this._views.loadingPage.retryButtonVisible = true;
            this._views.loadingPage.retryButtonText = iRes.getString("RELOAD");
            this._views.loadingPage.errorTitle = iRes.getString("NETWORK_ERROR");
        } else if (type === LoadingPageType.EMPTY) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = Visible;
            this._views.emptyIcon.propertySetName = Consts.SRC_NO_VIDEO;
            if (info) {
                this._views.emptyInfo.text = info;
            }
        } else if (type === LoadingPageType.NO_HISTORY) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = Visible;
            this._views.emptyIcon.propertySetName = Consts.SRC_NO_HISTORY;
            if (info) {
                this._views.emptyInfo.text = info;
            }
        } else if (type === LoadingPageType.NOTHING) {
            this._views.loadingPage.visibility = None;
            this._views.empty.visibility = None;
        }
    }

    /**
     * 获取视频列表id
     */
    _getVideoIdList(data: VideoInfo[]) {
        let list = "";
        for (let i = 0; i < data.length; i++) {
            list += data[i].id;
            if (i !== data.length - 1) {
                list += ",";
            }
        }
        return list;
    }

    /**
     * 获取搜索历史
     */
    _getHistoryList() {
        let data = <Object[]>iSearchModel.getSearchHistory(iAccount.account);
        if (!data || data.length === 0) {
            return "NA";
        }
        let list = "";
        if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                list += data[i];
                if (i !== data.length - 1) {
                    list += ",";
                }
            }
        }
        if (!list) {
            list = "NA";
        }
        return list;
    }

    /**
     * 移除语音指令
     */
    _removeVoiceCommands() {
        if (!this._views) {
            log.W(TAG, "_removeVoiceCommands, views is null.");
            return;
        }

        if (this._views.navigationBar) {
            Utils.removeVoiceCommand(this._views.navigationBar);
        }
    }

    /**
     * 移除所有监听器
     */
    _removeAllListeners() {
        if (this._tapAbleViews) {
            for (let i = 0; i < this._tapAbleViews.length; i++) {
                let view = this._tapAbleViews[i];
                Utils.setOnTapListener(view, null);
            }
        }
        this._tapAbleViews = [];

        if (this._views.localGrid) {
            this._views.localGrid.removeAllListeners("itemselect");
            let adapter = <LocalAdapter> this._views.localGrid.adapter;
            if (adapter) {
                adapter.registerVoiceSelectListener(null);
                adapter.destroy();
            }
        }

        if (this._views.onlineList) {
            this._views.onlineList.removeAllListeners("itemselect");
            this._views.onlineList.removeAllListeners("reachend");
            let adapter = this._views.onlineList.adapter;
            if (adapter) {
                adapter.destroy();
            }
        }

        if (this._views.searchInput) {
            this._views.searchInput.removeAllListeners("textchange");
            this._views.searchInput.removeAllListeners("clear");
            this._views.searchInput.removeAllListeners("accept");
        }
        if (this._loadingMoreView) {
            this._loadingMoreView.destroy();
            this._loadingMoreView = null;
        }

        if (this._views.navigationBar) {
            this._views.navigationBar.removeEventListener("back", this._navBackListener);
            this._navBackListener = null;
        }

        if (this._views.loadingPage) {
            this._views.loadingPage.removeEventListener("RetryButtonReleased", this._retryBtnListener);
            this._retryBtnListener = null;
        }

        this._views = null;
        iVideoModel.off(Consts.EV_HIGHLIGHT_URL_CHANGED, this._highlightUrlChanged);
        log.I(TAG, "_removeAllListeners done");
    }

    /**
     * 移除所有timer
     */
    _removeAllTimeout() {
        this._removeTimeout(this._loadingMoreTimeout);
    }

    _removeTimeout(timeout: NodeJS.Timer) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
    }

    onDestroy() {
        log.I(TAG, "onDestroy");
        this._destroyed = true;
        this._removeVoiceCommands();
        this._removeAllTimeout();
        this._removeAllListeners();
    }
}

export = SearchPresenter;
